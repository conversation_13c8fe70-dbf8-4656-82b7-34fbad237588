import { Maybe } from '@pylot-data/pylotschema'
import { useRouter } from 'next/router'

export const usePromoCampaignMessage = (messageMap?: Maybe<string>) => {
    const { locale = 'en' } = useRouter()
    const language = locale?.split('-')?.[0]
    const promoMessageMap = messageMap ? JSON.parse(messageMap) : { en: '' }
    const promoMessage = promoMessageMap?.[language] ?? ''

    return promoMessage
}
