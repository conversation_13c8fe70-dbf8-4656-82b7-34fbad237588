import { setUserAddressInput } from '@components/forked-from-pylot/Address/AdressHelper'
import { AddressInputVariableInterfaceWithRegion } from '@components/forked-from-pylot/Address/AdressHelper'
import { useBaseUrl } from '@config/hooks/useBaseUrl'
import { ToastType, useUI } from '@corsairitshopify/pylot-ui/context'
import { GraphqlFetchOutput } from '@pylot-data/graphqlFetch'
import {
    AddressInputVariableInterface,
    isUSAddressLine2MistakenInput,
    useAddressValidation
} from '@pylot-data/hooks/melissa/use-address-validation'
import { useTranslation } from 'next-i18next'
import { useRouter } from 'next/router'
import { Dispatch, SetStateAction, useState } from 'react'

type UseMelissaReturn = {
    modalOpen: boolean
    melissaSuccessResponse: boolean | undefined
    formattedMelissaResponse: AddressInputVariableInterface | undefined
    userSelectedAddressInput: AddressInputVariableInterface | undefined
    isInvalidAddressLine2: boolean
    onSubmit: (input: AddressInputVariableInterface) => Promise<void>
    handleSubmitClick: (
        formattedMelissaData: AddressInputVariableInterface | undefined
    ) => Promise<null | undefined>
    setModalOpen: Dispatch<SetStateAction<boolean>>
}

export const event = {
    ADDRESS_UPDATE: 'accountAddressUpdate'
}
export const responseData = ['updateCustomerAddress', 'createCustomerAddress']
export const defaultRegionData = { regionCode: null, regionId: null }

export const useMelissa = <T>(
    onSubmitHandler: (
        formattedMelissaResponse: AddressInputVariableInterface
    ) => Promise<GraphqlFetchOutput<T>>
): UseMelissaReturn => {
    const router = useRouter()
    const { t } = useTranslation(['account'])
    const { openToast } = useUI()

    const { requestMelissaData, transformMelissaData } = useAddressValidation()
    const { region: currentRegion } = useBaseUrl(
        typeof window !== 'undefined' ? window.location.href : ''
    )

    const [formattedMelissaResponse, setFormattedMelissaResponse] = useState<
        AddressInputVariableInterface | undefined
    >()
    const [userSelectedAddressInput, setUserSelectedAddressInput] = useState<
        AddressInputVariableInterfaceWithRegion | undefined
    >()
    const [modalOpen, setModalOpen] = useState(false)
    const [
        melissaSuccessResponse,
        setMelissaSuccessResponse
    ] = useState<boolean>()
    const [isInvalidAddressLine2, setIsInvalidAddressLine2] = useState(false)

    const handleSubmitClick = async (
        formattedMelissaResponse:
            | AddressInputVariableInterfaceWithRegion
            | undefined
    ) => {
        if (!formattedMelissaResponse) return null

        setModalOpen(false)

        delete formattedMelissaResponse.regionName

        const response = await onSubmitHandler(formattedMelissaResponse)

        if (response?.errors) {
            //Error page cannot be used inside a toast
            openToast(
                t('account||account|Something went wrong. Please try again'),
                ToastType.Danger
            )
        }

        const hasResponse = responseData.some((data) =>
            Object.prototype.hasOwnProperty.call(response?.data, data)
        )

        if (hasResponse) {
            router.push('/account/address').then(() => {
                openToast(
                    t('account||account|You saved the address.'),
                    ToastType.Success
                )
            })
        }
    }

    const onSubmit = async (input: AddressInputVariableInterface) => {
        try {
            if (isUSAddressLine2MistakenInput(currentRegion, input.street[1])) {
                setModalOpen(true)
                setIsInvalidAddressLine2(true)
                return
            }
            setIsInvalidAddressLine2(false)

            const {
                address: melissaData,
                isAddressAccuracy,
                isValidAddress
            } = await requestMelissaData(input)

            if (melissaData) {
                if (!isValidAddress) {
                    const ErrorMessage = t(
                        'account||This type of address is not allowed because uses a Military address. Please use another address.'
                    )
                    setModalOpen(false)
                    openToast(ErrorMessage, ToastType.Danger)
                    return
                }

                setMelissaSuccessResponse(isAddressAccuracy)
                setUserAddressInput(
                    input,
                    melissaData,
                    setUserSelectedAddressInput
                )
                setModalOpen(true)

                const transformedInput = transformMelissaData(
                    melissaData,
                    input,
                    currentRegion
                )

                setFormattedMelissaResponse(transformedInput)
            }
        } catch (e) {
            //Error page cannot be used inside a toast
            openToast(
                t('account||account|Something went wrong. Please try again'),
                ToastType.Danger
            )
        }
    }

    return {
        modalOpen,
        melissaSuccessResponse,
        formattedMelissaResponse,
        isInvalidAddressLine2,
        userSelectedAddressInput,
        onSubmit,
        handleSubmitClick,
        setModalOpen
    }
}
