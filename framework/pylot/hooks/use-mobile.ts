import {
    BREAKPOINT_MOBILE_MAX,
    BREAKPOINT_TABLET_MAX
} from '@components/layouts/MainLayout/breakpoints'
import { useEffect, useState } from 'react'

type UseMobile = (
    mobileBreakpoint?: number,
    tabletBreakpoint?: number
) => {
    isMobile?: boolean
    isTablet?: boolean
}
//optional breakpoint parameters
export const useMobile: UseMobile = (
    mobileBreakpoint = BREAKPOINT_MOBILE_MAX,
    tabletBreakpoint = BREAKPOINT_TABLET_MAX
) => {
    const [isTablet, setIsTablet] = useState<boolean>()
    const [isMobile, setIsMobile] = useState<boolean>()

    useEffect(() => {
        const handleResize = () => {
            setIsMobile(window.innerWidth <= mobileBreakpoint + 1)
            setIsTablet(window.innerWidth <= tabletBreakpoint + 1)
        }

        handleResize()
        window.addEventListener('resize', handleResize)

        return () => {
            window.removeEventListener('resize', handleResize)
        }
    }, [mobileBreakpoint, tabletBreakpoint])

    return { isTablet, isMobile }
}
