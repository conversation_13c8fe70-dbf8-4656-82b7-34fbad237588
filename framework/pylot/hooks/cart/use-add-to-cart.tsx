import { useRouter } from 'next/router'
import { useEffect, useState } from 'react'
import { graphqlFetch, GraphqlFetchOutput } from '@pylot-data/graphqlFetch'
import type {
    AddProductsToCartOutput as AddProductsToCartOutputRaw,
    Cart,
    CartItemFrom,
    CartItemInput,
    ProductInterface
} from '@pylot-data/pylotschema'
import { addToCartMutation } from '@lib/cart-manager/graphql/addToCartMutation'
import { CartData, useCart, getCartId } from '@lib/cart-manager'
import { getIsSignedIn } from '@lib/cart-manager/utils/getIsSignedIn'
import { getOptimisticAddToCartData } from '@lib/cart-manager/utils/getOptimisticAddToCartData'
import { useUI } from '@corsairitshopify/pylot-ui/context'
import { CartItemInterface, Maybe } from '@pylot-data/pylotschema'
import { useStoreConfig } from '@config/hooks/useStoreConfig'
import { orderCart, setCookie } from 'helpers'
import { orderCartFromLocalStorageSkuList } from '@components/common/CorsairCart/CartSidebarView/utils/cartOrderHelper'
import { useCartState } from '@components/common/CorsairCart/CartContext'
import { cartItemsFrom } from '@corsairitshopify/pylot-tag-manager'
import { useClickFrom } from '@components/corra/AddToCart/AddToCart'
import { useCartItemHelper } from 'helpers/cartItemHelper'
import {
    PIXEL_TRACKING_EVENT,
    sendTrackingData
} from '@pylot-data/api/operations/send-tracking-event'
import {
    ACCEPTED_CONSENT_VALUE,
    CONSENT_OPTION_COOKIE_NAME,
    META_CONVERSION_HEADER_NAME,
    META_FBC_COOKIE_NAME,
    META_FBP_COOKIE_NAME
} from '@config/static'
import { getCookie } from 'helpers/cookiesHelper'

export interface PersonalizationDataInput {
    uid: string
    value: string
}

export interface AddProductsToCartInput {
    cartId: string
    cartItems: CartItemInput[]
    isSignedIn: boolean
}

export interface AddProductsToCartOutput {
    addProductsToCart: AddProductsToCartOutputRaw
}

interface AddToCartOptions {
    ignoreSidebar?: boolean
    isMainItemElgatoBundleAndSave?: boolean
}

export interface UseAddToCartReturn {
    addToCart: (
        cartItems: CartItemInput[],
        products?: ProductInterface[],
        excludeItems?: string[],
        options?: AddToCartOptions
    ) => Promise<CartData | undefined>
    isAdding: boolean
}

const getMetaConversionValues = () => {
    const _fbp = getCookie(META_FBP_COOKIE_NAME)
    const _fbc = getCookie(META_FBC_COOKIE_NAME)

    return { _fbp, _fbc }
}

const parseOptanonConsent = (consentString: string): Record<string, any> => {
    const params: Record<string, any> = {}
    const pairs = consentString.split('&')

    pairs.forEach((pair) => {
        const [key, value] = pair.split('=')
        if (key && value) {
            // Decode the value to handle any URL encoded characters
            params[decodeURIComponent(key)] = decodeURIComponent(value)
        }
    })

    return params
}

const isCustomerAcceptedConsent = () => {
    const consentCookie = getCookie(CONSENT_OPTION_COOKIE_NAME)

    if (!consentCookie) {
        return false // No consent cookie found
    }

    const consentString = consentCookie
        .split('&')
        .find(
            (ck) =>
                ck.startsWith('groups') &&
                decodeURIComponent(ck).includes(ACCEPTED_CONSENT_VALUE)
        )

    if (!consentString) {
        return false
    }
    // Parse the consent string into an object
    const parsedConsent = parseOptanonConsent(consentString)

    // Check if the 'groups' contains 'C0004:1'
    const groups = parsedConsent.groups || ''
    return groups.includes(ACCEPTED_CONSENT_VALUE)
}

const formatData = (
    output: GraphqlFetchOutput<AddProductsToCartOutput>
): ((oldData?: CartData) => CartData) | CartData => {
    const outputData = output.data.addProductsToCart
    const errors = output.errors

    if (errors?.length || outputData?.user_errors?.length) {
        // populate cart data using the old data and append it with new errors
        return (oldData) => ({
            errors: errors,
            data: oldData?.data ?? outputData,
            user_errors: outputData?.user_errors
        })
    }

    return {
        data: { cart: outputData.cart },
        errors: errors
    }
}

// During 'editing cart item' we remove an old item and add a new item simultaneously
// So, we have to ignore the outdated cart item
const filterExcluded = (cart: Cart, excludeItems?: string[]): Cart => {
    if (cart.items && excludeItems) {
        cart.items = cart.items.filter(
            (item) => item && !excludeItems.includes(item.uid)
        )
    }
    return cart
}

export const useAddToCart = (): UseAddToCartReturn => {
    const { locale } = useRouter()
    const { data, mutate } = useCart()
    const { openSidebar, setOptimisticCart } = useUI()
    const clickFrom = useClickFrom(1, { isMainATC: true })
    const { setCartError, setIsLoading } = useCartState()
    const updateCartItemsLocalStorage = useCartItemHelper()
    const [isAdding, setIsAdding] = useState(false)
    const [optimisticItems, setOptimisticItems] = useState<
        Maybe<Array<Maybe<CartItemInterface>>>
    >()
    const {
        base: {
            product: { default_max_quantity }
        }
    } = useStoreConfig()

    // If cart data is being updated during ATC request - restore optimistic items
    useEffect(() => {
        if (isAdding && data && optimisticItems) {
            data.data.cart.items = optimisticItems
            mutate(data, false)
        }
    }, [data])

    /**
     * validate if the product exists in the cart and validate
     * the max quantity the can added to cart
     * @param products
     * @returns
     */
    const validate = (products: ProductInterface[]) => {
        return products.map((product) => {
            //check if the product has max qty attribute or default max quantity
            const maxQty = product?.max_allowed_quantity || default_max_quantity
            const items = data?.data?.cart?.items
            const cartItem = items?.filter(
                (item) => item?.product?.url_key === product?.url_key
            )
            if (!cartItem?.length) return false

            return cartItem[0]?.quantity === maxQty
                ? cartItem[0]?.product?.url_key
                : null
        })
    }

    const checkMaxQtyOutdated = (addToCartItem: CartItemInput[]) => {
        const addingMaxQty =
            addToCartItem[0]?.max_quantity || default_max_quantity
        const { items } = data?.data.cart!
        const currentCartItem = items?.find(
            (item) => item?.product?.sku === addToCartItem[0]?.sku
        )

        if (!currentCartItem || currentCartItem.qty_limit === addingMaxQty)
            return false

        const { quantity: currentQty } = currentCartItem
        if (currentQty + 1 <= addingMaxQty) return false
        else {
            addToCartItem[0].quantity = addingMaxQty - currentQty
        }
        return true
    }

    return {
        addToCart: async (
            cartItems: CartItemInput[],
            products?: ProductInterface[],
            excludeItems?: string[],
            options?: AddToCartOptions
        ) => {
            const promoHeaders: HeadersInit = {}
            const metaHeaders: HeadersInit = {}
            let optimisticData
            const cardID = await getCartId(locale || '')
            const hasLimitReached = validate(products!)
            //If max limit reached then prevent adding product to cart
            const errors = hasLimitReached.filter((f) => f)

            cartItems = cartItems.map((cartItem) => {
                const productInCartItem = products?.find(
                    (product) => product.uid === cartItem.uid
                )
                const isError = errors.includes(productInCartItem?.url_key)
                // Does not increase qty for error items
                return !isError
                    ? cartItem
                    : Object.assign(cartItem, { quantity: 0 })
            })

            // Set error message for error items
            errors.forEach((error) => setCartError(error || ''))
            checkMaxQtyOutdated(cartItems)
            // Does not call api if all items in cart reach max qty
            if (errors.length === cartItems.length || !!hasLimitReached[0]) {
                const cartItemsFromLocalStorage: string | null =
                    localStorage.getItem(cartItemsFrom) || null

                let parseCartItemsFrom: CartItemFrom[] = []
                try {
                    parseCartItemsFrom = cartItemsFromLocalStorage
                        ? JSON.parse(cartItemsFromLocalStorage)
                        : []
                } catch (e) {
                    console.error(e)
                }
                const prevItemApplyPromo = parseCartItemsFrom.find(
                    (i) => i.sku === products![0].sku
                )?.prev_apply_promo
                if (!prevItemApplyPromo) {
                    products![0].promo_apply_product = undefined
                } else {
                    products![0].promo_apply_product = prevItemApplyPromo
                }
                updateCartItemsLocalStorage(products![0], clickFrom)
                return
            }
            setIsLoading(true)
            setIsAdding(true)
            if (products) {
                optimisticData = getOptimisticAddToCartData(
                    data!,
                    cartItems,
                    products
                )
                optimisticData.data.cart = filterExcluded(
                    optimisticData.data.cart,
                    excludeItems
                )

                optimisticData.data.cart.items = orderCartFromLocalStorageSkuList(
                    optimisticData.data.cart.items
                )
                setOptimisticItems(optimisticData.data.cart.items)
                setOptimisticCart(optimisticData)
                mutate(optimisticData, false)
                if (!options?.ignoreSidebar) {
                    openSidebar()
                }
            }

            if (
                products?.length &&
                (products[0]?.show_third_party_promo ||
                    (products[0]?.promo_campaigns || [])[0]?.campaign) &&
                products[0]?.promo_apply_product
            ) {
                const promoes = JSON.parse(
                    products[0]?.promo_apply_product || '{}'
                )
                if (
                    products[0]?.show_third_party_promo ||
                    promoes?.mh ||
                    promoes?.campaign
                ) {
                    promoHeaders['x-promo-campaign'] = encodeURI(
                        JSON.stringify(promoes)
                    )
                }
            }

            const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone
            const url = new URL(window.location.href)
            const clickIdParam = url.searchParams.get('clickid') || ''
            const clickId = sessionStorage.getItem('clickid') || clickIdParam
            const mediaIdParam =
                url.searchParams.get('utm_medium') == 'Affiliate'
                    ? url.searchParams.get('utm_source') || ''
                    : null
            const mediaId = sessionStorage.getItem('mediaid') || mediaIdParam

            if (isCustomerAcceptedConsent()) {
                const metaConversionValue = getMetaConversionValues()
                metaHeaders[META_CONVERSION_HEADER_NAME] = encodeURIComponent(
                    JSON.stringify(metaConversionValue)
                )
            }

            const fetchOptions = {
                headers: {
                    timezone,
                    ...(options?.isMainItemElgatoBundleAndSave &&
                        products && {
                            'x-bundle-and-save': products[0]?.elgato_bundle_and_save_skus?.join(
                                '|'
                            ),
                            'x-bds-main-sku': cartItems[0]?.sku
                        }),
                    ...promoHeaders,
                    ...metaHeaders,
                    ...(clickId && { clickid: clickId }),
                    ...(mediaId && { mediaid: mediaId })
                }
            }

            const response = await graphqlFetch<
                AddProductsToCartInput,
                AddProductsToCartOutput
            >({
                fetchOptions,
                query: addToCartMutation,
                variables: {
                    cartId: cardID,
                    cartItems,
                    isSignedIn: getIsSignedIn()
                },
                locale
            })

            const graphqlErrors = response.errors
            if (graphqlErrors?.length) {
                const errorMetaInfo = {
                    cartId: cardID,
                    query: addToCartMutation,
                    sku: cartItems[0].sku
                }
                if (typeof window !== 'undefined' && window.Rollbar) {
                    window.Rollbar.error('Graphql Add to Cart Error', {
                        graphqlErrors,
                        ...errorMetaInfo
                    })
                    console.error(
                        'Graphql Add to Cart Error: ',
                        graphqlErrors,
                        errorMetaInfo
                    )
                }
            }

            if (response?.data?.addProductsToCart?.cart?.items) {
                response.data.addProductsToCart.cart = filterExcluded(
                    response.data.addProductsToCart.cart,
                    excludeItems
                )
            }

            if (response?.data?.addProductsToCart?.cart?.items) {
                response.data.addProductsToCart.cart.items = orderCart(
                    optimisticData?.data?.cart?.items,
                    response.data.addProductsToCart.cart.items
                )
            }

            setOptimisticItems(null)
            setOptimisticCart(false)

            const itemATC = response.data.addProductsToCart.cart?.items?.find(
                (item) => item?.product?.sku === cartItems[0]?.sku
            )
            sendTrackingData(PIXEL_TRACKING_EVENT.ADD_TO_CART, {
                content_type: 'product',
                currency:
                    response.data.addProductsToCart.cart?.prices?.grand_total
                        ?.currency,
                value: itemATC?.prices?.price?.value || 0,
                content_ids: [cartItems[0]?.sku],
                ...(itemATC
                    ? {
                          contents: [
                              {
                                  id: itemATC?.product?.sku,
                                  quantity: itemATC?.quantity
                              }
                          ]
                      }
                    : {})
            })

            const result = await mutate(formatData(response), false)
            // If there were errors - revalidate cart data
            if (
                result?.errors?.length ||
                response?.data?.addProductsToCart?.user_errors?.length
            ) {
                await mutate(undefined, true)
            }
            setIsAdding(false)
            setIsLoading(false)
            const cartItemsResult =
                result?.data?.cart?.items?.map((items) => items) || []
            const productUrls = cartItemsResult.map((item) => {
                const sku = item?.product.sku || ''
                const url_key = item?.product.url_key || ''
                const pathnameNodes = location.pathname.split('/')
                const lastPathnameNode = pathnameNodes.pop()
                const productUrl = pathnameNodes.includes(url_key)
                    ? location.pathname
                    : lastPathnameNode
                    ? location.pathname.replace(lastPathnameNode, url_key)
                    : ''
                return {
                    sku,
                    productUrl
                }
            })
            setCookie('productUrls', JSON.stringify(productUrls))
            return result
        },
        isAdding
    }
}
