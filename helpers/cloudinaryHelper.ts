const cloudinaryVideoExtendsion: string[] = [
    'mp4',
    'mov',
    'avi',
    '3gp',
    '3g2',
    'wmv',
    'mpeg',
    'mkv',
    'webm',
    'ogv',
    'flv',
    'ts'
]
const findSpecialFormats = (url: string): string => {
    const regex = /\/upload\/([^/]+)\/products/
    const match = url?.match(regex)
    if (match) {
        return match[1]
    }
    return ''
}
const paramsConcat = (param1: string, param2: string) => {
    const completedParams = new Set(`${param1},${param2}`.split(','))
    return Array.from(completedParams).join(',')
}

const convertUrlDomain = (url: string) => {
    if (!url) return ''
    if (url.includes('assets.corsair.com')) return url
    return url.replace('res.cloudinary.com/corsair-pwa', 'assets.corsair.com')
}
export const swatchImageUrlTransformation = (url: string): string => {
    const splitedUrl = url.split('/')
    let isReplaced = false
    const transformedUrl = splitedUrl.map((urlPath: string) => {
        if (
            urlPath.includes(
                'c_scale' ||
                    urlPath.includes('q_auto') ||
                    urlPath.includes('w_96')
            ) &&
            !isReplaced
        ) {
            isReplaced = true
            return paramsConcat(
                urlPath.replaceAll('%2C', ','),
                'f_auto,q_auto,w_250,h_250,c_pad'
            )
        }
        return urlPath
    })
    return transformedUrl.join('/')
}

const correctMediaTransformParams = (url: string, insertIndex: number) => {
    const urlSplitedByDots = url.split('.')
    const mediaType = urlSplitedByDots[urlSplitedByDots.length - 1]
    const isCloudinaryVideo = cloudinaryVideoExtendsion.includes(mediaType)
    if (
        !url?.includes('assets.corsair.com') || isCloudinaryVideo
            ? url.includes('f_auto') &&
              url.includes('q_auto') &&
              url.includes('ac_none')
            : url.includes('f_auto') && url.includes('q_auto')
    ) {
        return url
    }
    const isAC_noneIncluded = url.includes('ac_none')
    const splitedUrl = url.split('/')
    if (url.includes('f_auto')) {
        if (isCloudinaryVideo && !isAC_noneIncluded) {
            splitedUrl[insertIndex] += ',ac_none,q_auto'
        } else {
            splitedUrl[insertIndex] += '/q_auto'
        }
    } else if (url.includes('q_auto')) {
        if (isCloudinaryVideo && !isAC_noneIncluded) {
            splitedUrl[insertIndex] += ',ac_none,f_auto'
        } else {
            splitedUrl[insertIndex] += ',f_auto'
        }
    } else {
        if (isCloudinaryVideo && !isAC_noneIncluded) {
            splitedUrl[insertIndex - 1] += '/ac_none,q_auto,f_auto'
        } else {
            splitedUrl[insertIndex - 1] += '/q_auto,f_auto'
        }
    }
    return splitedUrl.join('/')
}

export const convertImageFormat = (
    url: string,
    format: string,
    ext: string
): string => {
    url = convertUrlDomain(url)
    if (!url?.includes('assets.corsair.com')) return url
    const replaceFormat1 = /c_scale%2Cq_auto%2Cw_96/g
    const replaceFormat2 = /c_scale,f_auto,h_96,q_auto,w_96/g
    const otherFormat = findSpecialFormats(url)
    if (otherFormat) {
        return url?.replace(otherFormat, format)?.replace(/\.png/g, `.${ext}`)
    }

    return url
        ?.replaceAll(replaceFormat1, format)
        ?.replaceAll(replaceFormat2, format)
        ?.replace(/\.png/g, `.${ext}`)
}
export const convertUrlFormat = (url: string, insertIndex = 5): string => {
    url = convertUrlDomain(url)
    return correctMediaTransformParams(url, insertIndex)
}
