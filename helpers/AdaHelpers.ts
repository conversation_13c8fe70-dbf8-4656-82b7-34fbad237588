import React from 'react'

const focusableSelectors = [
    'a[href]', // Links
    'area[href]', // Area elements
    'input', // Input elements
    'select', // Select elements
    'textarea', // Textarea elements
    'button:not(:disabled)', // Button elements
    '[tabindex]', // Elements with tabindex
    'iframe', // Iframe elements
    'object', // Object elements
    'embed' // Embed elements
]

/**
 * Utility function to observe and wait for an element to exist in the DOM.
 * @param {string} selector - CSS selector for the element to observe.
 * @param {Function} callback - Function to execute once the element is found.
 */
const checkForElementToExist = (
    selector: string,
    callback: (element: Element) => void
): void => {
    const observer = new MutationObserver((mutationsList, observer) => {
        for (const mutation of mutationsList) {
            if (mutation.type === 'childList') {
                const element = document.querySelector(selector)
                if (element) {
                    callback(element)
                    observer.disconnect() // Stop observing once the element is found
                }
            }
        }
    })
    // Start observing the document body for changes
    observer.observe(document.body, { childList: true, subtree: true })
}

/**
 *
 * @param {string} triggerElmentSelector - could be id or class name to use inside document.querySelector
 * @param {string} wrapperElementSelector - could be id or class name to use inside document.querySelector
 * @param {string} closeButtonSelector - could be id or class name to use inside document.querySelector
 */
export const focusController = (
    triggerElmentSelector: string,
    wrapperElementSelector: string,
    closeButtonSelector?: string,
    customFocusableElements?: string[],
    secondaryFocusElementSelector?: string
): void => {
    const triggerElement = document.querySelector(
        triggerElmentSelector
    ) as HTMLButtonElement
    if (closeButtonSelector) {
        const closeBtn = document.querySelector(
            closeButtonSelector
        ) as HTMLButtonElement
        closeBtn?.focus()
        closeBtn.addEventListener('click', () => {
            if (secondaryFocusElementSelector) {
                const secondaryFocusElement = document.querySelector(
                    secondaryFocusElementSelector
                ) as HTMLButtonElement
                secondaryFocusElement?.focus()
            } else {
                if (triggerElement) {
                    ;(triggerElement as HTMLElement).focus()
                } else {
                    checkForElementToExist(
                        triggerElmentSelector,
                        (triggerElement) => {
                            ;(triggerElement as HTMLElement).focus()
                        }
                    )
                }
            }
        })
    }
    const diaglog = document.querySelector(
        wrapperElementSelector
    ) as HTMLDivElement
    const allFocusableElements = diaglog?.querySelectorAll(
        customFocusableElements?.length
            ? customFocusableElements.join(',')
            : focusableSelectors.join(',')
    ) as NodeListOf<HTMLButtonElement>
    if (!allFocusableElements) return
    diaglog.addEventListener('keydown', function (event: KeyboardEvent) {
        if (event.key === 'Escape') {
            if (closeButtonSelector) {
                const closeBtn = document.querySelector(
                    closeButtonSelector
                ) as HTMLButtonElement
                closeBtn?.click()
            }
            if (secondaryFocusElementSelector) {
                const secondaryFocusElement = document.querySelector(
                    secondaryFocusElementSelector
                ) as HTMLButtonElement
                secondaryFocusElement?.focus()
            } else {
                if (triggerElement) {
                    ;(triggerElement as HTMLElement).focus()
                } else {
                    checkForElementToExist(
                        triggerElmentSelector,
                        (triggerElement) => {
                            ;(triggerElement as HTMLElement).focus()
                        }
                    )
                }
            }
        }
        if (event.key === 'Tab') {
            if (event.shiftKey) {
                // Shift + Tab
                if (document.activeElement === allFocusableElements[0]) {
                    event.preventDefault()
                    allFocusableElements[
                        allFocusableElements?.length - 1
                    ].focus()
                }
            } else {
                // Tab
                if (
                    document.activeElement ===
                    allFocusableElements[allFocusableElements?.length - 1]
                ) {
                    event.preventDefault()
                    allFocusableElements[0].focus()
                }
            }
        }
    })
}

export const adaVerifyH1Heading = (): void => {
    if (document) {
        const h1 = document.querySelector('h1')
        //return if there is a h1 tag
        if (h1) return
        const h2AsH1 = document.querySelector('h2.h1')
        //if there is any h2 tag with class contains h2, update that tag to h1
        if (h2AsH1) {
            h2AsH1.setAttribute('role', 'heading')
            h2AsH1.setAttribute('aria-level', '1')
            return
        }
        //create a h1 tag if there is no h1
        const newH1 = document.createElement('h1')
        newH1.textContent = document.title
        newH1.classList.add('sr-only')
        document.querySelector('main')?.prepend(newH1)
    }
}

export const verifyAnchorTags = (
    translatedText: string,
    containerSelector: string
): void => {
    if (document) {
        const textContainerElement = document?.querySelector(containerSelector)
        if (textContainerElement && document) {
            document.querySelectorAll('a').forEach((anchor) => {
                anchor.setAttribute(
                    'aria-label',
                    `${anchor.textContent} - ${translatedText}`
                )
            })
        }
    }
}

/**
 * Handles focusing on the sidebar cart element once it becomes available.
 * @param {string} triggerElementSelector - The selector for the trigger element that will regain focus (e.g., button or link).
 * @param {string} [secondaryFocusElementSelector] - Optional selector for an alternative element to focus if the trigger element is not available.
 */
export const handleSidebarCartFocus = (
    triggerElementSelector: string,
    secondaryFocusElementSelector?: string
): void => {
    checkForElementToExist('.sidebar-container .minicart-items', () => {
        focusController(
            triggerElementSelector,
            '#cart-sidebar-root',
            '.cartSidebar-close-btn',
            ['select', 'button'],
            secondaryFocusElementSelector
        )
    })
}

/**
 *
 * @param {string} text - the text need to get width
 * @param {number} additionalPadding - additional padding for x-direction
 * @returns {number}
 */
export function getTextWidth(text: string, additionalPadding = 0): number {
    // Get the body font style
    const bodyFont = window.getComputedStyle(document.body).font

    // Create a temporary canvas element
    const canvas = document.createElement('canvas')
    const context = canvas.getContext('2d')
    if (!context) return 0

    // Set the context's font to match the body's font
    context.font = bodyFont

    // Measure and return the width of the text
    return context.measureText(text).width + additionalPadding
}
