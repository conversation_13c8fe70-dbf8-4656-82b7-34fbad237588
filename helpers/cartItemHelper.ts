import { useStoreConfig } from '@config/index'
import { cartItemsFrom } from '@corsairitshopify/pylot-tag-manager'
import { CartItemFrom, Maybe, ProductInterface } from '@pylot-data/pylotschema'
import {
    calculateDeliveryDateLocal,
    getFinalDateFormatByRegion
} from 'helpers/dateHelper'
import { useCallback } from 'react'

export type PromoApplied = {
    mh: boolean
    campaign: string
    limit?: boolean
}

export const parsePromoOnItem = (
    promoData: Maybe<string> | undefined
): PromoApplied => {
    return JSON.parse(promoData || `{}`)
}

const saveCartItemsFrom = (cartItems: CartItemFrom[]) => {
    localStorage.setItem(cartItemsFrom, JSON.stringify(cartItems))
}

// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
export const useCartItemHelper = () => {
    const {
        base: { dateFormatByRegion }
    } = useStoreConfig()

    const updateCartItemsLocalStorage = useCallback(
        (variantProduct: ProductInterface, clickFromTab = '') => {
            // 'variant' of a SimpleProduct is the same as 'product'
            const cartItemsFromLocalStorage: string | null =
                localStorage.getItem(cartItemsFrom) || null
            const parseCartItemsFrom: CartItemFrom[] = cartItemsFromLocalStorage
                ? JSON.parse(cartItemsFromLocalStorage)
                : []
            const cartItemFrom: CartItemFrom = {
                sku: variantProduct.sku,
                from: clickFromTab!
            }
            if (variantProduct?.deliverBy || variantProduct?.bundle_products) {
                let deliverByDate: string | null = null
                if (variantProduct?.bundle_products?.length) {
                    for (const bundle of variantProduct?.bundle_products) {
                        const date = calculateDeliveryDateLocal(
                            getFinalDateFormatByRegion(dateFormatByRegion),
                            Number(bundle?.deliverBy),
                            bundle?.excludedDeliveryDate || []
                        )
                        if (
                            !deliverByDate ||
                            new Date(date) > new Date(deliverByDate)
                        ) {
                            deliverByDate = date
                        }
                    }
                } else {
                    deliverByDate = calculateDeliveryDateLocal(
                        getFinalDateFormatByRegion(dateFormatByRegion),
                        Number(variantProduct?.deliverBy),
                        variantProduct?.excludedDeliveryDate || []
                    )
                    if (variantProduct?.deliverBy == null) {
                        deliverByDate = ''
                    }
                }
                cartItemFrom.deliver_by = deliverByDate || ''
            }
            const promoes = parsePromoOnItem(
                variantProduct?.promo_apply_product
            )
            if (variantProduct?.promo_apply_product) {
                const promoApply = parsePromoOnItem(
                    variantProduct?.promo_apply_product
                )
                if (promoApply.limit) delete promoApply.limit
                cartItemFrom.promo_apply_product = JSON.stringify(promoApply)
                cartItemFrom.is_apply_3rd_party_promo =
                    promoes.mh || !!promoes.campaign
                cartItemFrom.promo_message = variantProduct?.promo_message
                cartItemFrom.promo_campaign = { campaign: promoes.campaign }
                cartItemFrom.cart_item_promo_message =
                    variantProduct?.promo_message
            }
            const isCartItemFromExist = parseCartItemsFrom.some(
                (element: CartItemFrom) => element.sku === variantProduct.sku
            )

            const isUpdateCartItemFrom = parseCartItemsFrom.some(
                (element: CartItemFrom) => {
                    if (!isCartItemFromExist) return false
                    const elementPromo = parsePromoOnItem(
                        element.promo_apply_product
                    )
                    return (
                        isCartItemFromExist &&
                        (elementPromo.mh !== promoes.mh ||
                            elementPromo.campaign !== promoes.campaign ||
                            variantProduct?.is_updated_qty)
                    )
                }
            )

            if (!isCartItemFromExist) {
                parseCartItemsFrom.push(cartItemFrom)
                saveCartItemsFrom(parseCartItemsFrom)
                return
            } else {
                const dataUpdateCartItemFrom = parseCartItemsFrom?.map<CartItemFrom>(
                    (element) => {
                        if (element.sku === cartItemFrom.sku) {
                            element.prev_apply_promo =
                                element?.promo_apply_product
                            if (cartItemFrom?.deliver_by) {
                                element.deliver_by = cartItemFrom.deliver_by
                            }
                            if (isUpdateCartItemFrom) {
                                element = variantProduct?.promo_apply_product
                                    ? cartItemFrom
                                    : element
                                if (
                                    !variantProduct?.is_updated_qty &&
                                    !promoes.mh &&
                                    !promoes.campaign
                                ) {
                                    delete element.is_apply_3rd_party_promo
                                    delete element.promo_apply_product
                                } else if (
                                    variantProduct?.is_updated_qty &&
                                    !element.is_apply_3rd_party_promo
                                ) {
                                    delete element.promo_message
                                }
                            } else {
                                element.promo_message =
                                    cartItemFrom?.promo_message
                            }
                            element.from = clickFromTab
                            return element
                        }
                        return element
                    }
                )
                saveCartItemsFrom(dataUpdateCartItemFrom)
                return
            }
        },
        [dateFormatByRegion]
    )

    return updateCartItemsLocalStorage
}
