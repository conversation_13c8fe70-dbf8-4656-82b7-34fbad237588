import { useEffect, useState } from 'react'

export const useIsMobileDevice = (): boolean => {
    const [isMobileDevice, setIsMobileDevice] = useState(false)

    useEffect(() => {
        const mobileDevice = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
            navigator.userAgent
        )
        setIsMobileDevice(mobileDevice)
    }, [])

    return isMobileDevice
}
