import {
    <PERSON>ginForm,
    RegisterForm,
    useLoginRedirect
} from '@corsairitshopify/corsair-login-view'
import serverSideTranslations from '@pylot-data/serverSideTranslations'
import { GetStaticProps, GetStaticPropsContext } from 'next'
import {
    ONE_HOUR_IN_SECONDS,
    TEN_MINUTES_IN_SECONDS
} from '@lib/utils/pageTtls'
import { useTranslation } from 'next-i18next'
import Head from 'next/head'
import LoginLayout from '@components/Login/LoginLayout'

export const getStaticProps: GetStaticProps = async ({
    locale
}: GetStaticPropsContext) => {
    try {
        return {
            props: {
                ...(await serverSideTranslations(locale!, ['common']))
            },
            revalidate: ONE_HOUR_IN_SECONDS
        }
    } catch (e) {
        console.log(`Error: Generating welcome message ${e}`)
        return {
            props: {
                ...(await serverSideTranslations(locale!, ['common']))
            },
            revalidate: TEN_MINUTES_IN_SECONDS
        }
    }
}

export default function Login(): JSX.Element {
    const { t } = useTranslation(['common'])
    useLoginRedirect()

    return (
        <>
            <Head>
                <title>{t('login|Log in')}</title>
            </Head>
            <div className="w-full grid grid-cols-12 gap-x-6 gap-y-16 md:gap-x-16 md:gap-y-10">
                <div className="col-span-12 md:col-span-6 h-full">
                    <LoginForm />
                </div>
                <div className="col-span-12 md:col-span-6 h-full">
                    <RegisterForm />
                </div>
            </div>
        </>
    )
}

Login.Layout = LoginLayout
