/* eslint-disable @typescript-eslint/no-explicit-any */
import '@styles/chrome-bug.css'
import '@styles/main.css'
import 'slick-carousel/slick/slick-theme.css'
import 'slick-carousel/slick/slick.css'
import '../node_modules/swiper/swiper-bundle.css'
// Default theme
import { Head } from '@components/common'
import { LoadingIndicator } from '@components/LoadingIndicator'
import { ManagedAuthContext } from '@corsairitshopify/pylot-auth-manager'
import { useGTM } from '@corsairitshopify/pylot-gtm'
import { TagContextInit } from '@corsairitshopify/pylot-tag-manager'
import { Toast } from '@corsairitshopify/pylot-ui'
import { ManagedUIContext } from '@corsairitshopify/pylot-ui/context'
import { appWithTranslation } from 'next-i18next'
import nextI18NextConfig from 'next-i18next.config.js'
import type { AppProps } from 'next/app'
import { FC, useEffect, useMemo } from 'react'
import '../node_modules/@splidejs/react-splide/dist/css/splide-core.min.css'
import '../node_modules/@splidejs/splide-extension-auto-scroll/dist/js/splide-extension-auto-scroll'
import '../node_modules/@splidejs/splide-extension-auto-scroll/dist/js/splide-extension-auto-scroll.min'
import '../node_modules/@splidejs/splide/dist/js/splide'
// import { LiveChat } from '@components/common/LiveChat'
import { AnimationAndVideoToggleProvider } from '@components/common/AnimationAndVideosToggle/AnimationAndVideoContext'
import { CartContextView } from '@components/common/CorsairCart/CartContext'
import { PSDStateProvider } from '@components/common/PSDStateProvider/PSDStateProvider'
import { ContextResetter } from '@components/layouts/MainLayout/ContextResetter'
import { LayoutContextProvider } from '@components/layouts/MainLayout/LayoutContext'
import { LinkContextProvider } from '@components/layouts/MainLayout/LinkContext'
import { useBaseUrl } from '@config/hooks/useBaseUrl'
import { LoginView } from '@corsairitshopify/corsair-login-view'
import { ContentGroupContext } from '@corsairitshopify/pylot-tag-manager/src/TrackContentGroup'
import { useCart } from '@lib/cart-manager/use-cart'
import { sendWebVitalsToGoogleAnalytics } from '@lib/gtm/webVitals'
import s from '@pagestyles/Login.module.scss'
import {
    PIXEL_TRACKING_EVENT,
    sendTrackingData,
    TrackingParam
} from '@pylot-data/api/operations/send-tracking-event'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/dist/ScrollTrigger'
import { TextPlugin } from 'gsap/dist/TextPlugin'
import { setCookie } from 'helpers'
import {
    ATCFromURL,
    isATCFromURL,
    isSameCartId
} from 'helpers/addToCartFromUrlHelper'
import { removeCartIdCookie } from 'helpers/removeCartIdCookie'
import { useRouter } from 'next/router'
require('polyfill-object.fromentries')

gsap.registerPlugin(ScrollTrigger, TextPlugin)

const Noop: FC = ({ children }) => <>{children}</>

function MyApp({ Component, pageProps }: AppProps) {
    const Layout = (Component as any).Layout || Noop
    const authRequired = (Component as any).Protected || false
    const router = useRouter()
    const { data } = useCart()
    const trackingCartData = useMemo(() => {
        if (!data?.data.cart) return {}
        return {
            currency: data?.data.cart?.prices?.grand_total?.currency || '',
            value: data?.data.cart?.prices?.grand_total?.value || '',
            content_ids: data?.data.cart?.items?.map(
                (item) => item?.product?.sku
            ),
            content_type: 'product_group',
            contents: data?.data.cart?.items?.map((item) => ({
                id: item?.product?.sku,
                quantity: item?.quantity
            }))
        } as TrackingParam
    }, [data?.data?.cart])

    const { region: currentRegion } = useBaseUrl(
        typeof window !== 'undefined' ? window.location.href : ''
    )
    useEffect(() => {
        sendWebVitalsToGoogleAnalytics()
        sendTrackingData(PIXEL_TRACKING_EVENT.VIEW_CONTENT)
        document.body.classList?.remove('loading')
        if (window.location.search.includes('clickid')) {
            try {
                const url = new URL(window.location.href)
                const clickId = url.searchParams.get('clickid') || ''
                if (clickId) {
                    sessionStorage.setItem('clickid', clickId)
                    setCookie('clickid', clickId)
                }
            } catch (error) {
                console.log(error)
            }
        }
        if (
            window.location.search.includes('utm_source') &&
            window.location.search.includes('utm_medium')
        ) {
            try {
                const url = new URL(window.location.href)
                const utmMedium = url.searchParams.get('utm_medium') || ''
                const mediaId = url.searchParams.get('utm_source') || ''
                if (utmMedium == 'Affiliate' && mediaId) {
                    sessionStorage.setItem('mediaid', mediaId)
                    setCookie('mediaid', mediaId)
                }
            } catch (error) {
                console.log(error)
            }
        }
    }, [])

    useEffect(() => {
        const handleResize = () => {
            document.documentElement.style.setProperty(
                '--innerHeight',
                `${window.innerHeight}px`
            )
        }
        document.documentElement.style.setProperty(
            '--initialInnerHeight',
            `${window.innerHeight}px`
        )
        handleResize()
        window.addEventListener('resize', handleResize)
        return () => window.removeEventListener('resize', handleResize)
    }, [])

    //handle add to cart from url
    if (isATCFromURL(router)) {
        if (!isSameCartId()) {
            localStorage.removeItem('cart_id')
            localStorage.removeItem(`cartId_${currentRegion}`)
            localStorage.removeItem('skuCartOrder')
            localStorage.removeItem('pylot_token')
            localStorage.removeItem('gigya_uid')
            removeCartIdCookie()
            const url = new URL(window.location.href)
            window.localStorage.setItem(ATCFromURL, url.href)
        }
    }

    const { gtmInitScript } = useGTM()

    return (
        <ContentGroupContext>
            <LoginView styling={s}>
                <Head />
                {/* Disabled for the moment because is on top the new add to cart button */}
                {/* <LiveChat /> */}
                <TagContextInit scripts={[gtmInitScript]}>
                    <ManagedUIContext LoadingIndicator={LoadingIndicator}>
                        <AnimationAndVideoToggleProvider>
                            <ManagedAuthContext>
                                <CartContextView
                                    beforeCheckoutCb={() => {
                                        sendTrackingData(
                                            PIXEL_TRACKING_EVENT.INITIATE_CHECKOUT,
                                            trackingCartData
                                        )
                                    }}
                                >
                                    <LayoutContextProvider>
                                        <ContextResetter dummyVar={Component} />
                                        <LinkContextProvider>
                                            <Layout
                                                pageProps={pageProps}
                                                authRequired={authRequired}
                                            >
                                                <Toast />
                                                <PSDStateProvider>
                                                    <Component {...pageProps} />
                                                </PSDStateProvider>
                                            </Layout>
                                        </LinkContextProvider>
                                    </LayoutContextProvider>
                                </CartContextView>
                            </ManagedAuthContext>
                        </AnimationAndVideoToggleProvider>
                    </ManagedUIContext>
                </TagContextInit>
            </LoginView>
        </ContentGroupContext>
    )
}

//@ts-ignore
export default appWithTranslation(MyApp, nextI18NextConfig)
