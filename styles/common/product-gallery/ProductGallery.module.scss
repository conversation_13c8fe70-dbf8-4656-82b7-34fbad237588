@mixin bg-gradient-gallery() {
    background: #373e44;
    background: -webkit-radial-gradient(center, #373e44, #0a0d0f);
    background: -moz-radial-gradient(center, #373e44, #0a0d0f);
    background: radial-gradient(ellipse at center, #373e44, #0a0d0f);
}

@mixin rounded-button-base($icon, $width, $height) {
    @apply absolute flex z-10 bg-white rounded-full shadow-xl transition-all duration-150 outline-none cursor-pointer;
    background: white url($icon) no-repeat center;
    width: 40px;
    height: 40px;

    @screen md {
        width: $width;
        height: $height;
    }

    &:hover {
        transform: scale(1.1);
    }
}

.pdp-gallery {
    @apply w-full relative overflow-hidden;
    @include bg-gradient-gallery();
    border-radius: 8px;

    aspect-ratio: 343/408;

    @media (min-width: 768px) {
        aspect-ratio: 16/9;
    }

    @media (min-width: 1080px) {
        aspect-ratio: unset;
    }

    @media (min-width: 1080px) {
        border-radius: 0px;
    }

    .desktop-gallery {
        @apply w-full relative;

        > div {
            @apply w-full;
        }
        :global {
            .gallery-controls {
                @apply absolute w-full left-0 z-5;
                height: 40px;
                top: -50px;
            }
            .slider-gallery .gallery-container .gallery-slider {
                .zoom-in-btn {
                    &:focus {
                        border: blue 3px solid !important;
                    }
                }
            }
        }
    }
    :global {
        .mobile-gallery {
            > .gallery-controls {
                @apply justify-end pr-8;
            }

            .slider-gallery .gallery-container .gallery-slider {
                .swiper-slide-image-container {
                    &.zoom-disabled {
                        @apply cursor-move;
                    }
                }

                .swiper .zoom-in-btn {
                    @apply top-auto left-auto bottom-2 right-2;
                    &:focus {
                        border: darkblue 2px solid;
                    }
                }
            }
        }
    }

    :global {
        .slider-gallery {
            @apply w-full py-0 md:p-0 md:m-0;

            .gallery-container {
                @apply flex w-full h-full justify-center relative;
                aspect-ratio: 343/408;

                @media (min-width: 768px) {
                    aspect-ratio: 16/9;
                }

                @media (min-width: 1080px) {
                    aspect-ratio: unset;
                    height: calc(
                        100vh - 64px - var(--geolocationBannerHeight, 0px)
                    );
                }

                @media screen and (min-width: 1200px) {
                    height: calc(
                        100vh - 125px - var(--geolocationBannerHeight, 0px)
                    );
                }

                .swiper-slide {
                    width: 100% !important;
                    margin-bottom: 16px !important;
                    &:focus {
                        border: darkblue 2px solid;
                    }
                }

                .gallery-thumbnails {
                    @apply box-border p-0 max-h-full flex justify-center flex-col absolute inset-y-0 left-0;
                    z-index: 20;
                    padding: 10px 0;
                    margin-left: 65px;
                    width: 150px;

                    > div {
                        @apply flex flex-col items-center max-h-full;
                        width: 96px;
                    }

                    .swiper-thumbs {
                        @apply max-h-full overflow-visible;
                        transform: translateX(-91px) scale(0.1);
                        width: 104px;
                        transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    }

                    .thumbnails-container {
                        @apply h-full flex items-center justify-center;
                    }

                    .swiper-slide-image-container img {
                        opacity: 0;
                    }

                    .swiper-wrapper {
                        transform: none !important;
                    }
                }

                &:hover .gallery-thumbnails {
                    .swiper-thumbs {
                        transform: none;
                    }
                    .swiper-slide-image-container img {
                        opacity: 1;
                    }

                    .swiper-slide {
                        margin-bottom: 16px !important;
                    }
                }

                &.gallery-container-focus .gallery-thumbnails {
                    .swiper-thumbs {
                        transform: none;
                    }
                    .swiper-slide-image-container img {
                        opacity: 1;
                    }

                    .swiper-slide {
                        margin-bottom: 16px !important;
                    }
                }

                .gallery-slider {
                    @apply w-full;

                    .swiper-slide-image-container {
                        @apply flex max-h-full overflow-visible w-full h-full;
                        cursor: zoom-in;

                        &.video-thumbnail {
                            cursor: pointer;
                        }

                        > span {
                            @apply flex max-h-full w-full;
                            height: auto;

                            > span {
                                min-width: 0 !important;
                                min-height: 0 !important;
                                max-height: none !important;
                                object-fit: contain;
                                width: 100% !important;
                                height: unset !important;
                                position: unset !important;
                                padding-top: 0 !important;
                            }
                        }

                        // img {
                        //     @apply w-full;
                        //     height: calc(100vh - 88px);
                        // }
                    }

                    .swiper {
                        @apply h-full overflow-visible;

                        @screen md {
                            @apply overflow-hidden;
                        }

                        .swiper-slide {
                            @apply flex items-center justify-center;
                            &:hover .zoom-in-btn {
                                @include rounded-button-base(
                                    '/icons/zoom-in.svg',
                                    52px,
                                    52px
                                );
                                @apply absolute z-10 bg-white rounded-full shadow-xl transition-all duration-150 hidden;
                                right: -3.7rem;
                                bottom: -3.7rem;

                                @screen md {
                                    @apply left-6/12 top-6/12 block;
                                    margin: -26px 0 0 -26px;
                                }
                            }
                        }
                        // For Screen Reader
                        .swiper-slide {
                            &:hover {
                                .zoom-in-btn {
                                    @apply absolute z-10 bg-white rounded-full shadow-xl transition-all duration-150;
                                    clip: auto;
                                }
                            }
                            .zoom-in-btn {
                                @apply sr-only;
                            }
                        }
                    }
                }
            }
        }

        .thumbnail-container {
            height: inherit;

            img,
            span {
                border-radius: 0.6rem;
            }

            .btn-play-video {
                @include rounded-button-base(
                    '/icons/play-video.svg',
                    52px,
                    52px
                );
                @apply absolute left-6/12 top-6/12 cursor-pointer;
                margin: -26px 0 0 -26px;
            }
        }

        .video-container {
            @apply w-full h-full relative;

            video {
                @apply absolute top-0 left-0 w-full h-full;
            }

            .btn-play-video {
                @include rounded-button-base(
                    '/icons/play-video.svg',
                    52px,
                    52px
                );
                @apply absolute left-6/12 top-6/12 cursor-pointer;
                margin: -26px 0 0 -26px;
            }
        }

        .fs-slider-gallery {
            @apply md:px-20;
            padding-top: 120px;
            padding-bottom: 100px;
            background: #121212;

            @screen md {
                padding: 0px;
                @include bg-gradient-gallery();
            }

            .fs-gallery-close {
                @apply z-10 right-6 md:right-20 top-8;
                @include rounded-button-base('/icons/close.svg', 52px, 52px);
                right: 3rem;
                width: 52px;
                height: 52px;
            }

            .close-icon {
                @apply z-10 right-6 md:right-20 top-8 flex justify-center items-center;
                color: var(--black);
                background: rgba(255, 255, 255, 0.9);
                border-radius: 9999px;
                cursor: pointer;
                display: flex;
                outline: 2px solid transparent;
                outline-offset: 2px;
                position: absolute;
                box-shadow: 0 20px 25px -5px rgb(0 0 0 / 10%),
                    0 10px 10px -5px rgb(0 0 0 / 4%);
                z-index: 10;
                transition-property: all;
                transition-duration: 150ms;
                right: 3rem;
                width: 52px;
                height: 52px;

                svg {
                    width: 24px;
                    height: 24px;
                }

                &:focus {
                    outline: auto;
                }
            }

            h1 {
                @apply flex w-full h-full justify-center items-center;
            }

            .fs-gallery-container {
                @apply h-full w-full hidden;

                &.show-fs-gallery {
                    @apply flex;
                    @include bg-gradient-gallery();

                    @screen md {
                        background: none;
                    }
                }

                .fs-gallery-slider {
                    @apply w-full relative;
                    padding: 30px 30px 70px;

                    @screen md {
                        padding: 0px 0px;
                    }

                    > .swiper {
                        @apply h-full w-full;
                    }

                    .swiper-slide {
                        @apply flex justify-center items-center;

                        .react-transform-component,
                        .react-transform-wrapper {
                            @apply relative w-full h-full;

                            > span {
                                cursor: zoom-in;

                                &.zoom-disabled,
                                &.zoom-active {
                                    @apply cursor-move;
                                }
                            }
                        }

                        .zoom-in-out-btn {
                            @include rounded-button-base(
                                '/icons/zoom-in.svg',
                                52px,
                                52px
                            );
                            @apply block top-2/12 left-2/12;

                            &.zoom-out {
                                background-image: url(/icons/zoom-out.svg);
                            }
                        }
                    }
                }

                .fs-gallery-thumbnails {
                    @apply hidden box-border p-0 justify-center flex-col md:flex absolute inset-y-0;
                    z-index: 20;
                    padding: 10px 0;
                    width: 150px;
                    margin: auto 0 auto 65px;
                    height: calc(95% - 140px);

                    > div {
                        @apply flex flex-col items-center justify-center max-h-full;
                        width: 110px;
                    }

                    .swiper-thumbs {
                        @apply h-full max-h-full;
                        width: 104px;
                    }

                    .custom-swiper-button-prev,
                    .custom-swiper-button-next {
                        @apply relative flex-none;
                        background-color: transparent;
                        width: 24px;
                        height: 24px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                    }

                    .custom-swiper-button-prev {
                        margin-bottom: 16px;
                    }

                    .custom-swiper-button-next {
                        margin-bottom: 0;
                        margin-top: 16px;
                    }

                    .thumbnails-container {
                        @apply h-auto relative;
                        /* 80px for the arrow buttons and maximum of 950 px for max 5 thumbs*/
                        max-height: min(calc(100% - 80px), 590px);

                        &.no-slider-controls {
                            max-height: 100%;
                        }
                    }
                }
            }

            .video-container {
                @apply p-0 relative h-full max-h-full overflow-hidden;

                video {
                    @apply absolute top-0 left-0 w-full h-full;
                }
            }
        }

        .slider-gallery .gallery-thumbnails,
        .fs-slider-gallery .fs-gallery-thumbnails {
            .swiper-slide {
                width: 96px;
                height: 96px;

                .swiper-slide-image-container-overlay {
                    display: none;
                }

                .swiper-slide-image-container:not(.video-thumbnail) {
                    @include bg-gradient-gallery();
                }

                .swiper-slide-image-container {
                    @apply border border-transparent transition-all duration-300 rounded-lg overflow-visible opacity-100 relative;
                    width: 96px;
                    height: 96px;
                    opacity: 0.4;

                    > span {
                        @apply relative block w-full h-full;
                    }

                    span,
                    img {
                        border-radius: 0.6rem;
                    }
                }

                &:hover .swiper-slide-image-container {
                    @apply border-cool-gray-500;
                }

                &.swiper-slide-thumb-active .swiper-slide-image-container {
                    @apply opacity-100;
                    border-color: #ffff;
                    box-shadow: rgb(255 255 255 / 20%) 0px 8px 21px;
                }

                &:not(.swiper-slide-thumb-active)
                    .swiper-slide-image-container {
                    @apply opacity-100;
                }
            }
        }

        .mobile-gallery .gallery-controls,
        .fs-slider-gallery .gallery-controls {
            @apply absolute w-full left-0;
            height: 40px;
            padding-left: 0;
            top: 33px;

            @screen md {
                padding-left: 50px;
                padding-left: 50px;
            }

            button {
                @apply px-0;
                min-width: 34px;

                &.three-hundred-sixty-btn {
                    @apply hidden;
                }

                img {
                    @apply m-0;
                }
                @screen md {
                    @apply px-4;
                    min-width: 98px;

                    &.three-hundred-sixty-btn {
                        @apply flex;
                    }
                }
                > span {
                    @apply hidden md:block;
                }
            }
        }
    }
}

.mobile-slider-bar {
    @apply z-1 relative bg-light-grey-2;
    height: 2px;
    bottom: 32px;
    margin: 64px auto 0px auto;
    width: calc(100% - 40px);

    @media (min-width: 1080px) {
        display: none;
    }

    > span {
        @apply z-1 absolute h-1 transition-all duration-300 bg-charcoal;
        width: 50px;

        @media (min-width: 1080px) {
            display: none;
        }
    }
}

.slider-container {
    @apply overflow-hidden;
}

.mobile-gallery {
    @apply flex-col flex;

    @media (min-width: 1080px) {
        display: none;
    }
}
.desktop-gallery {
    @apply hidden justify-between flex-wrap;

    @media (min-width: 1080px) {
        display: flex;
    }
}
.pdp-gallery-video {
    :global {
        .video-container video {
            object-fit: cover;
        }
    }
}
.pdp-gallery.page-theme-neo {
    background: transparent;
    border-radius: 40px;

    @media (min-width: 1080px) {
        border-radius: 0;
    }

    :global {
        .video-container video {
            object-fit: cover;
        }

        .thumbnail-container,
        .video-container {
            .btn-play-video {
                background: transparent url('/icons/play-video-neo.svg')
                    no-repeat center;
                box-shadow: none;
            }

            &:hover {
                transform: none;
            }
        }

        .slider-gallery .gallery-thumbnails,
        .fs-slider-gallery .fs-gallery-thumbnails {
            height: inherit;

            img,
            span {
                border-radius: 2.4rem;
            }

            .swiper-slide {
                width: 96px;
                height: 96px;

                .swiper-slide-image-container {
                    border: none;
                    margin: 2px;
                    transition: border-radius 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    position: relative;
                    overflow: hidden;
                    background: none;
                    box-shadow: 4px 4px 8px 0px rgba(0, 0, 0, 0.08);

                    span,
                    img {
                        border-radius: 0;
                    }
                }

                & .swiper-slide-image-container-overlay {
                    display: block;
                    pointer-events: none;
                    position: absolute;
                    z-index: 99;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    background-color: var(--greyscale-black-50);
                    border-color: transparent;
                }

                &.swiper-slide-thumb-active
                    .swiper-slide-image-container-overlay {
                    background-color: var(--white);
                }
            }
        }

        .slider-gallery .gallery-thumbnails {
            .swiper-thumbs {
                transform: translateX(-75px) scale(0.17);
                width: auto;
                transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            }

            .swiper-slide .swiper-slide-image-container {
                border-radius: 100%;
            }

            .swiper-wrapper {
                justify-content: center;
                transform: none !important;
            }

            .swiper-slide {
                margin-bottom: 94px !important;
                transition: margin-bottom 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            }
        }

        .fs-slider-gallery .fs-gallery-container .fs-gallery-thumbnails {
            position: absolute;
            margin: 0;
            top: auto;
            bottom: 0;
            height: auto;
            width: 100%;
            padding: 32px 0;

            > div {
                flex-direction: row;
                width: 100%;
                height: 98px;
            }

            .thumbnails-container {
                max-height: 100%;
                max-width: min(calc(100% - 80px), 570px);
            }

            .swiper-thumbs {
                width: 100%;
                height: 104px !important;
            }

            .custom-swiper-button-prev,
            .custom-swiper-button-next {
                @apply relative flex-none;
                width: 56px;
                height: 56px;
                display: flex;
                justify-content: center;
                align-items: center;
                border-radius: 100%;
                background-color: var(--black);
                color: var(--white);

                svg:first-child {
                    transform: rotate(270deg) translateX(0);
                    opacity: 1;
                    transition: all 0.2s linear;
                }

                svg:nth-child(2) {
                    opacity: 0;
                    transform: rotate(270deg) translateX(100%);
                    transition: all 0.2s linear;
                    position: absolute;
                }

                &:hover {
                    color: var(--primaries-dark-blue);
                    background: var(--primaries-light-blue);

                    svg:first-child {
                        transform: rotate(270deg) translateX(-100%);
                        opacity: 0;
                    }

                    svg:nth-child(2) {
                        transform: rotate(270deg) translateX(0%);
                        opacity: 1;
                    }
                }
            }

            .custom-swiper-button-prev {
                margin: 0 24px 0 0;
            }

            .custom-swiper-button-next {
                margin: 0 0 0 24px;
            }
        }

        .gallery-container.gallery-container-focus .gallery-thumbnails,
        .slider-gallery:hover .gallery-thumbnails,
        .fs-slider-gallery .fs-gallery-thumbnails {
            .swiper-slide-image-container {
                border-radius: 2.4rem;
            }

            .swiper-slide-thumb-active .swiper-slide-image-container {
                margin: 0;
                border: 2px solid var(--black);
            }

            .swiper-slide .swiper-slide-image-container-overlay {
                opacity: 0.5;
            }

            .swiper-slide.swiper-slide-thumb-active
                .swiper-slide-image-container-overlay {
                background-color: transparent;
            }
        }

        .fs-slider-gallery .close-icon {
            width: 56px;
            height: 56px;
            color: var(--white);
            background: var(--black);

            svg:first-child {
                transform: translateY(0);
                opacity: 1;
                transition: all 0.2s linear;
            }

            svg:nth-child(2) {
                opacity: 0;
                transform: translateY(-100%);
                transition: all 0.2s linear;
                position: absolute;
            }

            &:hover {
                color: var(--primaries-dark-blue);
                background: var(--primaries-light-blue);

                svg:first-child {
                    transform: translateY(100%);
                    opacity: 0;
                }

                svg:nth-child(2) {
                    transform: translateY(0%);
                    opacity: 1;
                }
            }
        }
    }
}

.mobile-slider-dots {
    padding: 20px 16px;
    display: flex;
    flex-direction: row;
    justify-content: center;
    gap: 8px;

    @media (min-width: 1080px) {
        display: none;
    }

    .dot {
        width: 8px;
        height: 8px;
        flex-shrink: 0;
        border-radius: 9999px;
        background-color: var(--greyscale-black-10);

        &--active {
            background-color: var(--greyscale-black-65);
        }
    }
}
