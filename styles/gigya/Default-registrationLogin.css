.gigya-screen h1,
.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-header-text {
    font-family: 'Univers67BoldCondensed', sans-serif;
    font-size: 13px !important;
    color: #111111 !important;
    text-transform: uppercase !important;
    text-align: center !important;
}

.gigya-screen-caption {
    color: #111111;
    font-family: 'Univers67BoldCondensed', sans-serif;
    font-size: 20px;
    line-height: 1.1em;
    margin: 1em 5% 0 5%;
    padding-right: 0;
    text-align: center;
    text-transform: uppercase;
}

.gigya-screen,
.gigya-screen * {
    margin: 0 auto;
    padding: 0;
    border: none;
    text-decoration: none;
    width: auto;
    float: none;
    border-radius: 0;
    font-size: 12px;
    color: #111111;
    text-align: left;
    box-sizing: content-box;
}

.gigya-locale-ar .gigya-checkbox-text,
.gigya-locale-ar .gigya-composite-control-header,
.gigya-locale-ar .gigya-composite-control-label,
.gigya-locale-ar .gigya-composite-control-link,
.gigya-locale-ar .gigya-label,
.gigya-locale-ar .gigya-legend,
.gigya-locale-ar .gigya-multi-choice-item label,
.gigya-locale-ar input,
.gigya-locale-ar select,
.gigya-locale-he .gigya-checkbox-text,
.gigya-locale-he .gigya-composite-control-header,
.gigya-locale-he .gigya-composite-control-label,
.gigya-locale-he .gigya-composite-control-link,
.gigya-locale-he .gigya-label,
.gigya-locale-he .gigya-legend,
.gigya-locale-he .gigya-multi-choice-item label,
.gigya-locale-he input,
.gigya-locale-he select {
    direction: rtl;
}

.gigya-screen {
    box-sizing: border-box;
    padding-bottom: 32px;
}

.gigya-screen .gigya-label,
.gigya-screen .gigya-legend {
    display: block;
    font-weight: 700;
    font-size: 15px;
    color: #4e515e;
}

.gigya-screen .gigya-label-text,
.gigya-screen .gigya-legend-text,
.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-code-remember-label,
.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-header-text,
.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-label,
.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-phone-code-header-subtext,
.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-phone-code-header-text,
.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-phone-code-phonenumber,
.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-phone-edit-header-text,
.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-phone-edit-number,
.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-phone-register-example,
.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-phone-register-type span,
.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-phone-resend-header-text,
.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-phone-resend-type,
.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-phone-resend-type span,
.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-phone-resend-yourphone {
    display: inline-block;
    font-weight: lighter;
    font-size: 12px;
    font-family: "Univers55Roman", sans-serif;
    color: #111111;
}

.gigya-screen .gigya-checkbox-text {
    display: inline;
}

.gigya-screen .gigya-message {
    font-size: 15px;
    color: #525252;
    display: block;
    text-align: center;
}

.gigya-screen .gigya-message.left {
    text-align: left;
    padding-bottom: 40px;
}

.gigya-screen .gigya-social-login-container {
    margin: auto;
}

.gigya-screen .gigya-forgotPassword,
.gigya-screen .gigya-forgotPassword:link {
    float: right;
}

.gigya-composite-control.gigya-composite-control-link[data-switch-screen="gigya-register-screen"] {
    float: right;
}

.gigya-screen .gigya-reset-password-form .gigya-composite-control.gigya-composite-control-label.main-text {
    padding-bottom: 8px
}

.gigya-reset-password-form .gigya-composite-control.gigya-composite-control-link {
    float: right;
    margin-top: -30px;
}

.gigya-screen .gigya-keep-me-logged-in {
    display: block;
}

.gigya-screen .gigya-input-password,
.gigya-screen .gigya-input-text,
.gigya-screen .gigya-textarea,
.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-code-textbox {
    padding-left: 7px;
    box-sizing: border-box;
    width: 100%;
    text-indent: 5px;
    border-color: #eaeaea;
    color: #111111;
    outline: none;
    border-radius: 0;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, .05);
}

.gigya-screen .gigya-input-password:focus,
.gigya-screen .gigya-input-text:focus,
.gigya-screen .gigya-textarea:focus,
.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-code-textbox:focus {
    outline: none;
    border-color: #204cfe !important;
}

.gigya-screen.portrait div.gigya-tfa .gig-tfa-container input.gig-tfa-code-textbox,
.gigya-screen input.gigya-input-password,
.gigya-screen input.gigya-input-text {
    border: 1px solid #eaeaea;
}

.gigya-screen.portrait div.gigya-tfa .gig-tfa-container input.gig-tfa-code-textbox:disabled,
.gigya-screen input.gigya-input-password:disabled,
.gigya-screen input.gigya-input-text:disabled,
.gigya-screen select.gigya-country-code-select:disabled {
    background-color: #ffffff;
    border-color: #eaeaea;
    color: #111111;
}

.gigya-screen.portrait div.gigya-tfa .gig-tfa-container input.gigya-error.gig-tfa-code-textbox,
.gigya-screen input.gigya-input-password.gigya-error,
.gigya-screen input.gigya-input-text.gigya-error,
.gigya-screen select.gigya-country-code-select.gigya-error {
    border-radius: 0;
    border: 1px solid #e12a40;
}

.gigya-screen.portrait div.gigya-tfa .gig-tfa-container input.gig-tfa-code-textbox,
.gigya-screen.portrait div.gigya-tfa .gig-tfa-container input.gig-tfa-code-textbox:focus,
.gigya-screen.portrait div.gigya-tfa .gig-tfa-container input.gigya-error.gig-tfa-code-textbox,
.gigya-screen.portrait div.gigya-tfa .gig-tfa-container input.gigya-error.gig-tfa-code-textbox:focus,
.gigya-screen.portrait div.gigya-tfa .gig-tfa-container input.gigya-pending.gig-tfa-code-textbox,
.gigya-screen.portrait div.gigya-tfa .gig-tfa-container input.gigya-pending.gig-tfa-code-textbox:focus,
.gigya-screen.portrait div.gigya-tfa .gig-tfa-container input.gigya-valid.gig-tfa-code-textbox,
.gigya-screen.portrait div.gigya-tfa .gig-tfa-container input.gigya-valid.gig-tfa-code-textbox:focus,
.gigya-screen input.gigya-input-password,
.gigya-screen input.gigya-input-password.gigya-error,
.gigya-screen input.gigya-input-password.gigya-error:focus,
.gigya-screen input.gigya-input-password.gigya-pending,
.gigya-screen input.gigya-input-password.gigya-pending:focus,
.gigya-screen input.gigya-input-password.gigya-valid,
.gigya-screen input.gigya-input-password.gigya-valid:focus,
.gigya-screen input.gigya-input-password:focus,
.gigya-screen input.gigya-input-text,
.gigya-screen input.gigya-input-text.gigya-error,
.gigya-screen input.gigya-input-text.gigya-error:focus,
.gigya-screen input.gigya-input-text.gigya-pending,
.gigya-screen input.gigya-input-text.gigya-pending:focus,
.gigya-screen input.gigya-input-text.gigya-valid,
.gigya-screen input.gigya-input-text.gigya-valid:focus,
.gigya-screen input.gigya-input-text:focus,
.gigya-screen select.gigya-country-code-select,
.gigya-screen select.gigya-country-code-select.gigya-error,
.gigya-screen select.gigya-country-code-select.gigya-error:focus,
.gigya-screen select.gigya-country-code-select.gigya-pending,
.gigya-screen select.gigya-country-code-select.gigya-pending:focus,
.gigya-screen select.gigya-country-code-select.gigya-valid,
.gigya-screen select.gigya-country-code-select.gigya-valid:focus,
.gigya-screen select.gigya-country-code-select:focus {
    padding-left: 7px;
}

.gigya-screen .gigya-input-password,
.gigya-screen .gigya-input-text,
.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-code-textbox,
.gigya-screen select {
    height: 43px;
    border: 1px solid #b6bdc5;
}

.gigya-screen .gigya-required-display {
    color: #e12a40;
    display: inline-block;
    padding: 0 5px;
}

.gigya-screen .gigya-error-display {
    display: none;
}

.gigya-screen .gigya-error-display.gigya-error-display-active {
    display: block;
}

.gigya-screen .gigya-error-msg,
.gigya-screen .gigya-error-msg-active.gigya-form-error-msg {
    line-height: 14px;
    font-size: 13px;
    text-align: center;
    font-weight: 400;
    box-sizing: border-box;
}

.gigya-screen .gigya-error-msg-active.gigya-form-error-msg,
.gigya-screen .gigya-error-msg.gigya-error-msg-active {
    padding-top: 8px;
    padding-bottom: 0;
    color: #e12a40;
    font-weight: 700;
    font-size: 13px;
    line-height: 15px;
}

.gigya-screen .gigya-error-msg-active.gigya-form-error-msg {
    font-size: 12px;
    text-align: center;
    margin-top: 1px;
    font-weight: 700;
    padding: 10px;
}

.gigya-screen .gigya-login-provider-row:first-child {
    padding-top: 0
}

.gigya-screen .gigya-container {
    text-align: inherit;
    text-decoration: inherit;
    font-size: inherit;
    color: inherit;
}

.gigya-screen div.gigya-container.gigya-visible-when .gigya-composite-control {
    display: block;
}

.gigya-screen .gigya-myPhoto-profile-box-wrapper {
    margin-left: 0;
    margin-top: 0;
}

.gigya-screen .label-divider {
    display: block;
    width: 100%;
    height: 80px;
    background-repeat: repeat-x;
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAIAAACQd1PeAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAAMSURBVBhXY7h8+TIABPYCeicnV7oAAAAASUVORK5CYII=);
    background-position: 50%;
    box-sizing: border-box;
    padding: 0 !important;
}

.gigya-screen .label-divider span {
    display: inline-block;
    background-color: #ffffff;
    margin-top: 31px;
    padding-left: 10px;
    padding-right: 10px;
}

.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-phone-code-resend,
.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-phone-code-resend:active,
.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-phone-code-resend:link,
.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-phone-code-resend:visited,
.gigya-screen a,
.gigya-screen a:active,
.gigya-screen a:link,
.gigya-screen a:visited {
    color: #111111;
    text-decoration: none;
    font-size: 12px;
}

.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-phone-code-resend:hover,
.gigya-screen a:hover {
    text-decoration: underline;
}

.gigya-screen a.gigya-terms-of-use, .gigya-screen a.gigya-term-of-use:visited {
    text-decoration: underline;
}

.gigya-screen h1,
.gigya-screen h2 {
    line-height: 1em;
    font-size: 15px;
    color: #111111;
    font-weight: 400;
    text-align: center;
}

.gigya-screen h1 {
    border-bottom: 1px solid #dbdbdb;
    font-weight: 700;
}

.gigya-screen fieldset {
    border: none;
    padding: 0;
    margin: 0;
}

.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gigya-button.gig-tfa-phone-code-resend,
.gigya-screen a.gigya-button,
.gigya-screen input[type=email],
.gigya-screen input[type=password],
.gigya-screen input[type=text],
.gigya-screen select,
.gigya-screen textarea {
    width: 100%;
    border-radius: 0;
    background-color: #f9f9f9;
    box-sizing: border-box;
}

.gigya-screen input:-ms-input-placeholder {
    color: #a9a9a9;
}

.gigya-screen .gig-tfa-button-submit,
.gigya-screen .gigya-button,
.gigya-screen .gigya-composite-control-link,
.gigya-screen .gigya-composite-control-submit,
.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-button-container .gig-tfa-button,
.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-button-container .gig-tfa-button-submit,
.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-phone-edit-link,
.gigya-screen input[type=button],
.gigya-screen input[type=submit] {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-button-container .gig-tfa-button,
.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-button-container .gig-tfa-button-submit,
.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-phone-edit-link,
.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gigya-button.gig-tfa-phone-code-resend,
.gigya-screen a.gigya-button,
.gigya-screen input[type=button],
.gigya-screen input[type=submit] {
    cursor: pointer;
    font-family: "Univers55Roman", sans-serif;
    font-weight: 500;
    height: 43px;
    box-shadow: none;
    color: #ffffff;
    -ms-flex-line-pack: center;
    text-align: center;
    padding: 8px 20px;
    gap: 8px;
    min-width: 120px;
    background-color: #204cfe;
    box-sizing: border-box;
    min-height: 43px;
    font-size: 16px;
    letter-spacing: 0.025em;
    border-radius: 6px;
    border-style: none;
    transition-property: background-color, border-color, color, fill, stroke;
    transition-duration: .3s;
}

.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-button-container .gig-tfa-button-submit:active,
.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-button-container .gig-tfa-button-submit:hover,
.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-button-container .gig-tfa-button:active,
.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-button-container .gig-tfa-button:hover,
.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-phone-edit-link:active,
.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-phone-edit-link:hover,
.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gigya-button.gig-tfa-phone-code-resend:active,
.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gigya-button.gig-tfa-phone-code-resend:hover,
.gigya-screen a.gigya-button:active,
.gigya-screen a.gigya-button:hover {
    background-color: #1231ac;
    border-radius: 4px;
}

.gigya-screen input[type=button]:active,
.gigya-screen input[type=button]:hover,
.gigya-screen input[type=submit]:active,
.gigya-screen input[type=submit]:hover {
    background-color: #1231ac;
    border-radius: 4px;
}

.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-button-container .gig-tfa-button-submit:active,
.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-button-container .gig-tfa-button:active,
.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-phone-edit-link:active,
.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gigya-button.gig-tfa-phone-code-resend:active,
.gigya-screen a.gigya-button:active,
.gigya-screen input[type=button]:active,
.gigya-screen input[type=submit]:active {
    box-shadow: none;
}

.gigya-screen [data-gigya-api-link] {
    cursor: pointer;
}

.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-code-remember-checkbox,
.gigya-screen input[type=checkbox],
.gigya-screen input[type=radio] {
    width: auto;
    margin: 4px 0 0 0;
    padding: 0;
    border-radius: 0;
    border: none;
    vertical-align: middle;
}

.gigya-screen option {
    width: 100%;
    padding: 5px;
}

.gigya-screen option:hover {
    background-color: #204cfe;
}

.gigya-screen i.gigya-pencil {
    content: url(https://cdns.gigya.com/gs/i/gm/pencil.png);
    width: 14px;
    height: 14px;
    margin: 0 3px;
}

.gigya-screen label.letter-spacing-s {
    letter-spacing: -.1px;
}

.gigya-screen .gigya-composite-control-phone-number-input select.gigya-country-code-select {
    display: inline-block;
    width: 116px;
    margin: 0;
}

.gigya-screen .gigya-composite-control-phone-number-input select.gigya-country-code-select.gigya-error {
    padding: 0;
}

.gigya-screen .gigya-composite-control-phone-number-input input.gigya-subscriber-phone-number-input {
    display: inline-block;
    width: calc(100% - 121px);
    margin: 0;
}

.gigya-screen .gigya-layout-row {
    zoom: 1;
    display: block;
    text-align: left;
}
.gigya-screen .gigya-otp-update-form .gigya-layout-row {
    margin-bottom: 8px;
}
.gigya-screen .gigya-otp-update-form .gigya-composite-control.gigya-composite-control-textbox {
    padding-bottom: 0;
}

.gigya-screen .gigya-layout-row:after {
    content: "";
    display: table;
    clear: both;
}

.gigya-screen .gigya-layout-row.with-two-inputs .gigya-composite-control {
    padding-right: 8px
}

.gigya-screen .gigya-layout-row.with-two-inputs > .gigya-layout-cell + .gigya-layout-cell > .gigya-composite-control {
    padding-right: 0;
    padding-left: 8px;
}

.gigya-screen .gigya-layout-cell {
    text-align: center;
    float: left;
    width: 50%;
}

.gigya-screen .gigya-layout-cell .gigya-error-cell {
    width: 100%;
    max-width: none;
    min-width: 0;
}

.gigya-screen .gigya-layout-cell .gigya-layout-cell {
    max-width: 50%;
}

.gigya-screen .gigya-layout-cell.gigya-no-float {
    float: none;
}

.gigya-screen .gigya-layout-footer {
    text-align: right;
    clear: both;
}

.gigya-screen.portrait .gigya-layout-row .submit-button {
    padding-bottom: 24px;
}

.gigya-screen.portrait .gigya-layout-row .gigya-layout-cell.responsive,
.gigya-screen.portrait .gigya-layout-row.with-divider .gigya-layout-cell.responsive {
    float: none;
    min-width: 100%;
    width: 100%;
}

.gigya-screen-content div.gigya-screen.landscape {
    padding-left: 32px;
    padding-right: 32px;
    width: 920px;
}

.gigya-screen.landscape {
    padding-top: 10px;
}

.gigya-screen.landscape .gigya-composite-control.gigya-composite-control-social-login {
    position: relative;
    right: 25px;
}

.gigya-screen.portrait {
    width: 500px;
    font-size: 15px;
    padding-left: 32px;
    padding-right: 32px;
    padding-top: 10px;
}

.gigya-screen.portrait .sub-title-text {
    font-size: 15px;
}

.gigya-screen.portrait .gigya-error-msg-active.gigya-form-error-msg,
.gigya-screen.portrait .gigya-error-msg.gigya-error-msg-active {
    font-size: 13px;
}

.gigya-screen.portrait .text-field-disabled {
    background-color: #f2f2f2;
    border: 1px solid #d6dee4;
    border-radius: 2px;
    color: #4e515e;
}

.gigya-screen.portrait .gigya-composite-control {
    padding-left: 0;
    padding-right: 0
}

.gigya-screen.portrait .gigya-composite-control.gigya-composite-control-social-login {
    width: calc(100% + 50px);
    position: relative;
    right: 27px;
}

.gigya-screen.portrait.mobile {
    width: 100%;
    padding-top: 30px;
    padding-bottom: 15px;
    padding-left: 20px;
    padding-right: 20px;
}

.gigya-screen.portrait.mobile:before {
    display: block;
    width: 375px;
    content: "";
}

.gigya-screen.portrait.mobile .text-field-disabled {
    color: #4e515e;
}

.gigya-screen.portrait.mobile .forgotpassword,
.gigya-screen.portrait.mobile .gigya-composite-control-label,
.gigya-screen.portrait.mobile .gigya-error-msg-active.gigya-form-error-msg,
.gigya-screen.portrait.mobile .gigya-error-msg.gigya-error-msg-active,
.gigya-screen.portrait.mobile .gigya-forgotPassword,
.gigya-screen.portrait.mobile .gigya-label,
.gigya-screen.portrait.mobile .gigya-legend,
.gigya-screen.portrait.mobile .gigya-legend-text,
.gigya-screen.portrait.mobile .gigya-login-footer-text-wrapper,
.gigya-screen.portrait.mobile .gigya-message,
.gigya-screen.portrait.mobile .gigya-terms-of-use,
.gigya-screen.portrait.mobile .main-header,
.gigya-screen.portrait.mobile .site-title,
.gigya-screen.portrait.mobile .sub-title-text,
.gigya-screen.portrait.mobile a,
.gigya-screen.portrait.mobile div.gigya-tfa .gig-tfa-container .gig-tfa-code-remember-label,
.gigya-screen.portrait.mobile div.gigya-tfa .gig-tfa-container .gig-tfa-header-text,
.gigya-screen.portrait.mobile div.gigya-tfa .gig-tfa-container .gig-tfa-label,
.gigya-screen.portrait.mobile div.gigya-tfa .gig-tfa-container .gig-tfa-phone-code-header-subtext,
.gigya-screen.portrait.mobile div.gigya-tfa .gig-tfa-container .gig-tfa-phone-code-header-text,
.gigya-screen.portrait.mobile div.gigya-tfa .gig-tfa-container .gig-tfa-phone-code-phonenumber,
.gigya-screen.portrait.mobile div.gigya-tfa .gig-tfa-container .gig-tfa-phone-code-resend,
.gigya-screen.portrait.mobile div.gigya-tfa .gig-tfa-container .gig-tfa-phone-edit-header-text,
.gigya-screen.portrait.mobile div.gigya-tfa .gig-tfa-container .gig-tfa-phone-edit-number,
.gigya-screen.portrait.mobile div.gigya-tfa .gig-tfa-container .gig-tfa-phone-register-example,
.gigya-screen.portrait.mobile div.gigya-tfa .gig-tfa-container .gig-tfa-phone-register-type span,
.gigya-screen.portrait.mobile div.gigya-tfa .gig-tfa-container .gig-tfa-phone-resend-header-text,
.gigya-screen.portrait.mobile div.gigya-tfa .gig-tfa-container .gig-tfa-phone-resend-type,
.gigya-screen.portrait.mobile div.gigya-tfa .gig-tfa-container .gig-tfa-phone-resend-type span,
.gigya-screen.portrait.mobile div.gigya-tfa .gig-tfa-container .gig-tfa-phone-resend-yourphone,
.gigya-screen.portrait.mobile h1,
.gigya-screen.portrait.mobile h2 {
    font-size: 12px;
}

.gigya-screen.portrait.mobile .gigya-label-text {
    font-size: 12px;
    letter-spacing: 0;
}

.gigya-screen.portrait.mobile input[type=email],
.gigya-screen.portrait.mobile input[type=password],
.gigya-screen.portrait.mobile input[type=text],
.gigya-screen.portrait.mobile select {
    height: 36px;
    font-size: 12px;
}

.gigya-screen.portrait.mobile .gigya-error-msg-active.gigya-form-error-msg,
.gigya-screen.portrait.mobile .gigya-error-msg.gigya-error-msg-active {
    font-size: 12px;
}

.gigya-screen.portrait.mobile .gigya-composite-control-social-login {
    width: calc(100% + 40px);
    position: relative;
    right: 20px;
}

.gigya-screen.portrait.mobile div.gigya-tfa .gig-tfa-container .gig-tfa-code-remember-checkbox,
.gigya-screen.portrait.mobile input[type=checkbox] {
    margin-bottom: 2px;
}

.gigya-screen.portrait.mobile label.letter-spacing-s {
    letter-spacing: 0;
}

.gigya-screen .gigya-composite-control.gigya-composite-control-label,
.gigya-screen .gigya-composite-control.gigya-composite-control-link {
    display: inline-block;
    color: #111111;
    line-height: 18px;
    font-size: 12px;
}

a.gigya-composite-control.gigya-composite-control-link:last-child {
    margin-top: 13px;
    float: right;
}

.gigya-screen .gigya-composite-control.gigya-composite-control-label.main-text,
.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-header-text {
    padding-bottom: 20px;
    display: block;
    text-align: left;
}

.gigya-screen .gigya-composite-control.gigya-composite-control-label.main-text.inlined,
.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .inlined.gig-tfa-header-text {
    display: inline-block;
    text-align: center;
}

.gigya-screen .gigya-composite-control.gigya-composite-control-label.social-login-title {
    padding-bottom: 26px;
    display: inline-block;
    text-align: center;
}

.gigya-screen .gigya-composite-control.gigya-composite-control-header {
    padding-bottom: 26px;
}

.gigya-screen .gigya-composite-control.gigya-composite-control-dropdown,
.gigya-screen .gigya-composite-control.gigya-composite-control-fieldset,
.gigya-screen .gigya-composite-control.gigya-composite-control-password,
.gigya-screen .gigya-composite-control.gigya-composite-control-textbox {
    padding-bottom: 5px;
}

.gigya-screen .gigya-composite-control.gigya-composite-control-dropdown .gigya-legend,
.gigya-screen .gigya-composite-control.gigya-composite-control-fieldset .gigya-legend,
.gigya-screen .gigya-composite-control.gigya-composite-control-password .gigya-legend,
.gigya-screen .gigya-composite-control.gigya-composite-control-textbox .gigya-legend {
    margin: 0;
}

.gigya-screen .gigya-composite-control.gigya-composite-control-dropdown .gigya-label,
.gigya-screen .gigya-composite-control.gigya-composite-control-dropdown .gigya-legend,
.gigya-screen .gigya-composite-control.gigya-composite-control-fieldset .gigya-label,
.gigya-screen .gigya-composite-control.gigya-composite-control-fieldset .gigya-legend,
.gigya-screen .gigya-composite-control.gigya-composite-control-password .gigya-label,
.gigya-screen .gigya-composite-control.gigya-composite-control-password .gigya-legend,
.gigya-screen .gigya-composite-control.gigya-composite-control-textbox .gigya-label,
.gigya-screen .gigya-composite-control.gigya-composite-control-textbox .gigya-legend {
    padding-bottom: 6px;
}

.gigya-screen .gigya-composite-control.gigya-composite-control-dropdown input,
.gigya-screen .gigya-composite-control.gigya-composite-control-dropdown select,
.gigya-screen .gigya-composite-control.gigya-composite-control-fieldset input,
.gigya-screen .gigya-composite-control.gigya-composite-control-fieldset select,
.gigya-screen .gigya-composite-control.gigya-composite-control-password input,
.gigya-screen .gigya-composite-control.gigya-composite-control-password select,
.gigya-screen .gigya-composite-control.gigya-composite-control-textbox input,
.gigya-screen .gigya-composite-control.gigya-composite-control-textbox select {
    box-shadow: none;
    margin-bottom: 12px;
    width: 100%;
    border-radius: 6px;
    border-width: 1px;
    border-color: #eaeaea;
    line-height: 1;
    padding: 0 8px !important;
}

.gigya-screen .gigya-terms-error,
.gigya-screen input.gigya-error,
.gigya-screen input[type=text].gigya-error,
.gigya-screen input[type=password].gigya-error,
.gigya-screen input[type=email].gigya-error,
.gigya-screen select.gigya-error {
    border: 1px solid #e12a40 !important;
    border-radius: 6px !important;
    padding: 0 8px !important;
    box-shadow: none;
    width: 100%;
    line-height: 1;
}

.gigya-screen .gigya-error-msg.gigya-error-msg-active {
    color: #e12a40 !important;
    text-align: left !important;
    font-size: 12px;
    margin-top: 0;
    font-family: "Univers55Oblique", sans-serif;
    font-weight: normal;
    padding: 0;
}

.gigya-screen .gigya-composite-control.gigya-composite-control-dropdown .gigya-error-msg-active,
.gigya-screen .gigya-composite-control.gigya-composite-control-fieldset .gigya-error-msg-active,
.gigya-screen .gigya-composite-control.gigya-composite-control-password .gigya-error-msg-active,
.gigya-screen .gigya-composite-control.gigya-composite-control-textbox .gigya-error-msg-active {
    padding-top: 0;
    padding-bottom: 8px;
    margin-top: -6px;
}

.gigya-screen .gigya-composite-control.gigya-composite-control-captcha-widget {
    padding-bottom: 0;
}

.gigya-screen .gigya-composite-control.gigya-composite-control-captcha-widget .gigya-captcha-wrapper {
    margin-bottom: 5px;
}

.gigya-screen .gigya-composite-control.gigya-composite-control-captcha-widget .gigya-captcha-wrapper .gigya-captcha {
    margin: 0 auto 13px auto;
}

.gigya-screen .gigya-composite-control.gigya-composite-control-captcha-widget .gigya-captcha-wrapper .gigya-captcha input {
    height: auto;
}

.gigya-screen .gigya-composite-control.gigya-composite-control-captcha-widget .gigya-captcha-wrapper .gigya-captcha .recaptcha_only_if_privacy a,
.gigya-screen.portrait .gigya-composite-control.gigya-composite-control-captcha-widget .gigya-captcha-wrapper .gigya-captcha .recaptcha_only_if_privacy div.gigya-tfa .gig-tfa-container .gig-tfa-phone-code-resend,
.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gigya-composite-control.gigya-composite-control-captcha-widget .gigya-captcha-wrapper .gigya-captcha .recaptcha_only_if_privacy .gig-tfa-phone-code-resend {
    font-size: 12px;
}

.gigya-screen .gigya-composite-control.gigya-composite-control-captcha-widget .gigya-captcha-wrapper .gigya-error-msg-active {
    padding-top: 0;
    padding-bottom: 8px;
}

.gigya-screen .gigya-composite-control.gigya-composite-control-tfa-widget {
    padding-bottom: 0;
}

.gigya-screen .gigya-composite-control.gigya-composite-control-button,
.gigya-screen .gigya-composite-control.gigya-composite-control-button + .gigya-error-display-active,
.gigya-screen .gigya-composite-control.gigya-composite-control-link.gigya-button-link,
.gigya-screen .gigya-composite-control.gigya-composite-control-link.gigya-button-link + .gigya-error-display-active,
.gigya-screen .gigya-composite-control.gigya-composite-control-submit,
.gigya-screen .gigya-composite-control.gigya-composite-control-submit + .gigya-error-display-active {
    padding-bottom: 0;
    float: left;
}

.gigya-screen .gigya-composite-control.gigya-composite-control-link.gigya-button-link {
    display: block;
}

.gigya-screen .gigya-composite-control.gigya-composite-control-social-login {
    padding: 0;
}

.gigya-screen .gigya-composite-control.gigya-composite-control-multi-choice .gigya-label {
    padding-bottom: 8px;
}

.gigya-screen .gigya-composite-control.gigya-composite-control-multi-choice .gigya-multi-choice-item {
    padding-top: 10px;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: start;
    align-items: flex-start;
}

.gigya-screen .gigya-composite-control.gigya-composite-control-multi-choice .gigya-multi-choice-item .gigya-input-radio {
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
}

.gigya-screen .gigya-composite-control.gigya-composite-control-multi-choice .gigya-multi-choice-item label {
    -ms-flex: 0 auto;
    flex: 0 auto;
    margin-left: 8px;
    width: 100%;
    display: block;
}

.gigya-screen .gigya-composite-control.gigya-composite-control-checkbox {
    display: -ms-flexbox !important;
    display: flex !important;
    -ms-flex-align: start;
    align-items: flex-start;
    margin-bottom: 20px;
}

.gigya-screen .gigya-composite-control.gigya-composite-control-checkbox .gigya-input-checkbox {
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    cursor: pointer;
}

.gigya-screen .gigya-composite-control.gigya-composite-control-checkbox .gigya-label {
    -ms-flex: 0 auto;
    flex: 0 auto;
    margin-left: 8px;
    width: 100%;
    display: block;
    cursor: pointer;
    font-size: 16px;
    line-height: 100%;
}

.gigya-screen .gigya-composite-control.gigya-terms-error {
    position: relative;
    width: 100%;
    border: none !important;
    padding: 0 !important;
}

.gigya-composite-control.gigya-composite-control-checkbox.privacy-policy-checkbox.gigya-terms-error::before {
    content: "";
    width: 11px;
    height: 12px;
    position: absolute;
    left: 0;
    top: 4px;
    border: 1px solid #e12a40;
    border-radius: 2px;
    background-color: white;
    z-index: 10;
}

.gigya-screen div.gigya-loginID-availability-available {
    margin-bottom: 18px;
}

.gigya-screen form.gigya-form-error .gigya-composite-control.gigya-composite-control-button,
.gigya-screen form.gigya-form-error .gigya-composite-control.gigya-composite-control-link.gigya-button-link,
.gigya-screen form.gigya-form-error .gigya-composite-control.gigya-composite-control-submit {
    padding-bottom: 10px;
}

.gigya-screen form.gigya-form-error .gigya-composite-control.gigya-composite-control-form-error.gigya-error-display-active {
    height: 45px;
    display: flex;
    justify-content: center;
    align-items: center;
    clear: both;
    padding-bottom: 0;
}

.gigya-screen.landscape .gigya-layout-row.with-divider {
    height: 0;
}

.gigya-screen.landscape .gigya-layout-row.with-divider .gigya-composite-control.gigya-composite-control-button,
.gigya-screen.landscape .gigya-layout-row.with-divider .gigya-composite-control.gigya-composite-control-link.gigya-button-link,
.gigya-screen.landscape .gigya-layout-row.with-divider .gigya-composite-control.gigya-composite-control-submit {
    padding-bottom: 0;
    text-align: center;
}

.gigya-screen.landscape form.gigya-form-error .gigya-layout-row.with-divider,
.gigya-screen.landscape form.gigya-form-error .gigya-layout-row.with-divider .gigya-composite-control.gigya-composite-control-button,
.gigya-screen.landscape form.gigya-form-error .gigya-layout-row.with-divider .gigya-composite-control.gigya-composite-control-link.gigya-button-link,
.gigya-screen.landscape form.gigya-form-error .gigya-layout-row.with-divider .gigya-composite-control.gigya-composite-control-submit {
    padding-bottom: 10px;
}

.gigya-screen.landscape form.gigya-form-error .gigya-layout-row.with-divider .gigya-composite-control.gigya-composite-control-form-error.gigya-error-display-active {
    padding-bottom: 0;
}

.gigya-screen .gigya-spacer-divider {
    border-bottom: 1px solid #d3d3d3;
    margin-bottom: 15px;
    padding-bottom: 5px;
    display: none;
}

.gigya-screen .gigya-layout-row.with-divider > .gigya-layout-cell {
    min-width: 48%;
    width: 48%;
}

.gigya-screen .gigya-layout-row.with-divider > .gigya-layout-cell.with-divider {
    min-width: 4%;
    width: 4%;
}

.gigya-screen .gigya-layout-row.with-divider > .gigya-layout-cell.with-divider > .gigya-divider-content {
    padding: 0;
    position: relative;
    line-height: 16px;
    background: #ffffff;
    text-align: center;
    min-width: 10px;
}

.gigya-screen.portrait .gigya-layout-row.with-divider > .gigya-layout-cell.with-divider {
    height: 10px;
    background-repeat: repeat-x;
    position: relative;
}

.gigya-screen.portrait .gigya-layout-row.with-divider > .gigya-layout-cell.with-divider > .gigya-divider-content {
    padding: 0 10px 2px 10px;
    position: absolute;
    display: inline-block;
    top: 50%;
    left: 50%;
    transform: translateX(-50%) translateY(-50%);
}

.gigya-screen.landscape .gigya-layout-row .gigya-layout-cell.under-site-login {
    width: 370px;
    text-align: center;
    min-width: 0;
    margin-left: 460px;
}

.gigya-screen.landscape .gigya-layout-row.with-divider {
    display: -ms-flexbox;
    display: flex;
}

.gigya-screen.landscape .gigya-layout-row.with-divider > .gigya-layout-cell.with-divider {
    margin-top: 41px;
    width: 87px;
    background-repeat: repeat-y;
}

.gigya-screen.landscape .gigya-layout-row.with-divider > .gigya-layout-cell.with-divider > .gigya-divider-content {
    display: block;
    line-height: 15px;
    padding: 10px 0;
    top: 83px;
    left: 0;
}

.gigya-screen.landscape .gigya-layout-row.with-divider > .gigya-layout-cell.with-site-login,
.gigya-screen.landscape .gigya-layout-row.with-divider > .gigya-layout-cell.with-social-login {
    min-width: 0;
    width: 370px;
}

.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-link {
    margin-left: 24%;
    margin-top: 8%;
}

.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-email-text {
    font-weight: 700;
    width: 207px;
    margin: 0 auto;
    font-size: 15px;
    padding-bottom: 25px;
}

.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-email-code-header-text {
    width: 240px;
    margin: 0 auto;
    padding-top: 6px;
    padding-bottom: 5px;
    font-size: 15px;
}

.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-phone-code-header-subtext,
.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-phone-code-header-text,
.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-phone-code-phonenumber {
    display: inline;
}

.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-code-header-subtext + .gig-tfa-label {
    margin-top: 40px;
}

.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-phone-register-example {
    display: block;
}

.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-label {
    padding-bottom: 6px;
    display: block;
    margin: 0;
}

.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-code-remember-label,
.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-code-textbox,
.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-phone-register-example,
.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-phone-register-select,
.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-phone-resend-header-text,
.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-phone-resend-yourphone-label {
    margin: 0 0 18px 0;
    padding: 0;
}

.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-header-text {
    margin: 0;
}

.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-code-textbox {
    font-size: 15px;
}

.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-error {
    text-align: center;
    margin-bottom: 18px;
    margin-top: -10px;
    font-size: 15px;
}

.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-error:empty {
    display: none;
}

.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-phone-register-type span,
.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-phone-resend-type span {
    display: inline;
}

.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-phone-prefix {
    display: none;
    height: 43px;
    width: 10%;
    margin-top: 0;
    margin-right: 16px;
    text-align: center;
    background-color: #f9f9f9;
}

.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-phone-number {
    margin-top: 0;
    height: 43px;
    width: 100%;
}

.gigya-screen.portrait div.gigya-tfa .gig-tfa-container label {
    display: inline;
}

.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-phone-register-type,
.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-phone-register-type-label,
.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-phone-resend-type,
.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-phone-resend-type-label {
    display: inline-block;
    width: 33.33333333%;
    margin-bottom: 18px;
    margin-top: 18px;
    vertical-align: top;
}

.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-phone-register-type-label,
.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-phone-resend-type-label {
    padding: 5px 0;
    width: 30.33333333%;
}

.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-phone-register-type,
.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-phone-resend-type {
    margin-right: 0;
    margin-left: 0;
    width: 33.33333333%;
}

.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-phone-register-type-sms,
.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-phone-resend-type-sms {
    width: 38.33333333%;
}

.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-phone-register-type-voice-call,
.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-phone-resend-type-voice-call {
    width: 31.33333333%;
}

.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-button-container {
    margin: 0;
}

.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-button-container .gig-tfa-button,
.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-button-container .gig-tfa-button-submit {
    background-image: none;
    filter: none;
    font-weight: 400;
    line-height: normal;
    padding-top: 10px;
    margin-bottom: 25px;
}

.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-phone-edit-link {
    width: 70px;
    line-height: 43px;
    float: right;
    margin-top: -10px;
}

.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-phone-edit-numbers {
    margin-top: 26px;
    line-height: 31px;
}

.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-phone-edit-numbers:last-child {
    padding-bottom: 25px;
}

.gigya-screen.portrait.mobile div.gigya-tfa .gig-tfa-container input.gig-tfa-phone-prefix,
.gigya-screen.portrait div.gigya-tfa .gig-tfa-container input.gig-tfa-phone-prefix {
    display: none;
}

.gigya-screen.portrait.mobile div.gigya-tfa .gig-tfa-container .gig-tfa-phone-edit-link {
    font-size: 19px;
    padding: 0;
    margin-top: 0;
}

.gigya-screen.portrait.mobile div.gigya-tfa .gig-tfa-container .gig-tfa-phone-prefix {
    display: none;
    width: 10%;
    margin-right: 8px;
}

.gigya-screen.portrait.mobile div.gigya-tfa .gig-tfa-container .gig-tfa-phone-number {
    width: 100%;
}

.gigya-screen.portrait.mobile div.gigya-tfa .gig-tfa-container .gig-tfa-code-remember-label {
    margin-left: 0;
}


.gigya-screen.portrait.mobile div.gigya-tfa .gig-tfa-container .gig-tfa-error,
.gigya-screen.portrait.mobile div.gigya-tfa .gig-tfa-container .gig-tfa-phone-code-resend {
    font-size: 13px;
}

.gigya-screen.portrait.mobile div.gigya-tfa .gig-tfa-container .gig-tfa-phone-edit-header-text {
    font-size: 13px;
    color: #4e515e;
}

.gigya-screen.portrait.mobile div.gigya-tfa .gig-tfa-container .gig-tfa-phone-edit-numbers {
    margin-top: 30px;
}

.gigya-screen.portrait.mobile div.gigya-tfa .gig-tfa-container .gig-tfa-phone-edit-numbers .gig-tfa-phone-edit-number {
    font-size: 13px;
    margin-top: 8px;
    color: #4e515e;
}

.gigya-screen.portrait.mobile div.gigya-tfa .gig-tfa-container .gig-tfa-code-textbox,
.gigya-screen.portrait.mobile div.gigya-tfa .gig-tfa-container .gig-tfa-phone-number,
.gigya-screen.portrait.mobile div.gigya-tfa .gig-tfa-container .gig-tfa-phone-prefix {
    font-size: 13px;
    height: 36px;
}

.gigya-screen.portrait.mobile div.gigya-tfa .gig-tfa-container .gig-tfa-button-container .gig-tfa-button,
.gigya-screen.portrait.mobile div.gigya-tfa .gig-tfa-container .gig-tfa-button-container .gig-tfa-button-submit {
    margin-bottom: 15px;
}

.gigya-screen.portrait.mobile div.gigya-tfa .gig-tfa-container .gig-tfa-phone-resend-header-text {
    display: block;
}

.gigya-screen-dialog-mobile .gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-phone-number {
    width: 100%;
}

#gigya-login-screen.gigya-screen.landscape .gigya-layout-row.with-divider > .gigya-layout-cell.with-divider > .gigya-divider-content {
    top: 83px;
}

#gigya-login-screen.gigya-screen.portrait.mobile .gigya-prefered-content-width {
    width: 375px;
}

#gigya-register-screen.gigya-screen.landscape .gigya-layout-row.with-divider > .gigya-layout-cell.with-divider > .gigya-divider-content {
    top: 127px;
}

#gigya-change-password-screen .error-wrapper {
    width: 100%;
}

#gigya-change-password-screen.portrait.mobile input[password] {
    width: 260px;
}

#gigya-change-password-screen.portrait.mobile input[submit] {
    width: 276px;
}

.gigya-screen div.gigya-composite-control-my-photo-widget.update-profile-my-photo {
    padding-right: 20px;
}

.gigya-screen .link-accounts-main-header,
.gigya-screen .reauthentication-main-header {
    margin-bottom: 10px;
}

.gigya-subscribe-with-email-form .gigya-header {
    line-height: 1.5em;
    font-weight: bolder;
    text-align: center;
}

.subscribe-thank-you {
    line-height: 1.2em;
    font-size: 13px;
    text-align: center;
}

.subscribe-thank-you p {
    margin-bottom: .5em;
    text-align: center;
    font-size: inherit;
}

#gigya-org-register-screen.gigya-screen.landscape .gigya-layout-row.with-divider > .gigya-layout-cell.with-divider > .gigya-divider-content {
    top: 127px;
}

.gigya-mac .gigya-screen label.letter-spacing-s {
    letter-spacing: -.3px;
}

.gigya-ios .gigya-screen.landscape .gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-code-remember-checkbox,
.gigya-ios .gigya-screen.landscape input[type=checkbox],
.gigya-ios .gigya-screen.portrait.mobile div.gigya-tfa .gig-tfa-container .gig-tfa-code-remember-checkbox,
.gigya-ios .gigya-screen.portrait.mobile input[type=checkbox],
.gigya-ios .gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-code-remember-checkbox,
.gigya-ios .gigya-screen.portrait input[type=checkbox],
.gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gigya-ios .gigya-screen.landscape .gig-tfa-code-remember-checkbox {
    margin-bottom: 5px;
}

.gigya-ios .gigya-screen#gigya-change-password-screen form label:first-child {
    letter-spacing: -.1px;
}

.gigya-ios .gigya-screen#gigya-forgot-password-screen form label:first-child {
    letter-spacing: -.3px;
}

.gigya-native-mobile-app.gigya-android .gigya-screen.portrait.mobile .gigya-composite-control.gigya-composite-control-social-login {
    right: 28px;
}

.gigya-edge .gigya-screen label.letter-spacing-s,
.gigya-ie10 .gigya-screen.portrait label {
    letter-spacing: -.3px;
}

.gigya-ie10 .gigya-screen.mobile label {
    letter-spacing: 0;
}

.gigya-ie10 .gigya-screen #gigya-change-password-screen.portrait .gigya-message {
    letter-spacing: -.2px;
}

.gigya-ie10 .gigya-screen #gigya-change-password-screen.mobile {
    letter-spacing: 0;
}

.gigya-ie9 .gigya-screen#gigya-login-screen.landscape .gigya-layout-row.with-divider .gigya-layout-cell.with-divider {
    height: 202px;
}

.gigya-ie9 .gigya-screen#gigya-org-register-screen.landscape .gigya-layout-row.with-divider .gigya-layout-cell.with-divider,
.gigya-ie9 .gigya-screen#gigya-register-screen.landscape .gigya-layout-row.with-divider .gigya-layout-cell.with-divider {
    height: 300px;
}

.gigya-ie9 .gigya-screen select {
    font-size: 13px;
}

.gigya-ie9 .gigya-screen .gigya-input-password:not(.gigya-placeholder),
.gigya-ie9 .gigya-screen .gigya-input-text:not(.gigya-placeholder),
.gigya-ie9 .gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-code-textbox:not(.gigya-placeholder) {
    line-height: 40px;
}

.gigya-ie9 .gigya-screen .gigya-composite-control-checkbox .gigya-label,
.gigya-ie9 .gigya-screen .gigya-composite-control-multi-choice .gigya-multi-choice-item label {
    display: inline;
}

.gigya-ie8 .gigya-screen .gigya-layout-cell {
    margin-left: 0 !important;
}

.gigya-ie8 .gigya-screen .gigya-layout-cell.under-site-login {
    margin-left: 460px !important;
}

.gigya-ie8 .gigya-screen .gigya-input-password,
.gigya-ie8 .gigya-screen .gigya-input-text,
.gigya-ie8 .gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-code-textbox {
    padding-top: 11px;
}

.gigya-ie8 .gigya-screen.portrait .gigya-layout-row.with-divider .gigya-layout-cell.with-divider .gigya-divider-content {
    top: 31px;
    left: 45%;
}

.gigya-ie8 .gigya-screen.portrait .gigya-input-password,
.gigya-ie8 .gigya-screen.portrait .gigya-input-text,
.gigya-ie8 .gigya-screen.portrait div.gigya-tfa .gig-tfa-container .gig-tfa-code-textbox {
    padding-top: 11px;
}

.gigya-ie8 .gigya-screen.portrait gigya-layout-cell {
    margin-left: 0 !important;
}

.gigya-ie8 .gigya-screen.portrait #gigya-change-password-screen.portrait .gigya-message {
    letter-spacing: -.2px;
}

.gigya-ie8 .gigya-screen.portrait #gigya-change-password-screen.mobile {
    letter-spacing: 0;
}

.gigya-ie8 .gigya-screen.portrait #gigya-forgot-password-screen.portrait label {
    letter-spacing: -.3px;
}

.gigya-ie8 .gigya-screen.portrait #gigya-forgot-password-screen.mobile label {
    letter-spacing: 0;
}

.gigya-ie8 .gigya-screen#gigya-login-screen.landscape .gigya-layout-row.with-divider .gigya-layout-cell.with-divider {
    height: 202px;
}

.gigya-ie8 .gigya-screen#gigya-org-register-screen.landscape .gigya-layout-row.with-divider .gigya-layout-cell.with-divider,
.gigya-ie8 .gigya-screen#gigya-register-screen.landscape .gigya-layout-row.with-divider .gigya-layout-cell.with-divider {
    height: 300px;
}


/* OVERRIDES */

.gigya-screen-dialog.gigya-style-modern div.gigya-screen-dialog-main {
    box-shadow: none;
}

.gigya-screen-dialog.gigya-style-modern div.gigya-screen-dialog-top {
    background-color: #ffffff;
    padding-left: 20px;
    padding-right: 20px;
    padding-top: 20px;
}

@media screen and (min-width: 550px) {
    .gigya-screen-dialog.gigya-style-modern div.gigya-screen-dialog-top {
        padding-left: 32px;
        padding-right: 32px;
        padding-top: 32px;
    }
}

.gigya-screen-dialog.gigya-style-modern div.gigya-screen-dialog-caption {
    color: #111111;
    font-family: 'Univers67BoldCondensed', sans-serif;
    font-size: 24px;
    line-height: 1.2em;
    text-transform: uppercase;
}

.gigya-style-modern.gigya-screen-dialog-mobile div.gigya-screen-dialog-close > a,
.gigya-screen-dialog.gigya-style-modern div.gigya-screen-dialog-close > a {
    line-height: 0;
    overflow: hidden;
    position: absolute;
    right: 18px;
    top: 18px;
    transition: 500ms border-radius;
}

@media screen and (min-width: 550px) {
    .gigya-style-modern.gigya-screen-dialog-mobile div.gigya-screen-dialog-close > a,
    .gigya-screen-dialog.gigya-style-modern div.gigya-screen-dialog-close > a {
        right: 30px;
        top: 30px;
    }
}

.gigya-style-modern.gigya-screen-dialog-mobile div.gigya-screen-dialog-close > a:hover,
.gigya-screen-dialog.gigya-style-modern div.gigya-screen-dialog-close > a:hover {
    border-radius: 0;
}

.gigya-style-modern.gigya-screen-dialog-mobile div.gigya-screen-dialog-close > a::before,
.gigya-style-modern.gigya-screen-dialog-mobile div.gigya-screen-dialog-close > a::after,
.gigya-screen-dialog.gigya-style-modern div.gigya-screen-dialog-close > a::before,
.gigya-screen-dialog.gigya-style-modern div.gigya-screen-dialog-close > a::after {
    background: #111111;
    content: '';
    height: 2px;
    left: 6px;
    position: absolute;
    top: 12px;
    transform-origin: 50%;
    width: 15px;
}

.gigya-style-modern.gigya-screen-dialog-mobile div.gigya-screen-dialog-close > a::before,
.gigya-screen-dialog.gigya-style-modern div.gigya-screen-dialog-close > a::before {
    transform: rotate(45deg);
}

.gigya-style-modern.gigya-screen-dialog-mobile div.gigya-screen-dialog-close > a::after,
.gigya-screen-dialog.gigya-style-modern div.gigya-screen-dialog-close > a::after {
    transform: rotate(-45deg);
}

.gigya-style-modern.gigya-screen-dialog-mobile div.gigya-screen-dialog-close > a > img,
.gigya-screen-dialog.gigya-style-modern div.gigya-screen-dialog-close > a > img {
    height: 18px;
    margin-top: 0;
    opacity: 0;
    width: 18px;
}

.gigya-screen .gigya-composite-control.gigya-composite-control-checkbox.terms-and-conditions-checkbox.terms-and-conditions-checkbox {
    visibility: hidden;
    position: absolute;
    display: none;
}

.gigya-screen .gigya-composite-control.gigya-composite-control-phone-number-widget a:hover {
    font-size: inherit;
    color: #204cfe;
    cursor: pointer;
}

.gigya-screen .gigya-composite-control.gigya-composite-control-phone-number-widget .gigya-country-code-select {
    width: 116px;
}

.gigya-screen .gigya-composite-control.gigya-composite-control-phone-number-widget input.gigya-input-text.gigya-phone-number-verification-code,
.gigya-screen .gigya-composite-control.gigya-composite-control-phone-number-widget input.gigya-input-text.gigya-subscriber-phone-number-input,
.gigya-screen .gigya-composite-control.gigya-composite-control-phone-number-widget select.gigya-country-code-select {
    margin: 0;
}

.gigya-screen .gigya-composite-control.gigya-composite-control-phone-number-widget .gigya-phone-number-verification-code-wrapper {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: center;
    align-items: center;
    margin: 0;
}

.gigya-screen .gigya-composite-control.gigya-composite-control-phone-number-widget .gigya-phone-number-widget-wrapper {
    margin: 0 0 13px 0;
}

.gigya-screen .gigya-composite-control.gigya-composite-control-phone-number-widget .gigya-fieldset {
    display: none;
    -ms-flex-align: center;
    align-items: center;
}

.gigya-screen .gigya-composite-control.gigya-composite-control-phone-number-widget .gigya-phone-number-preview {
    -ms-flex-negative: 0;
    flex-shrink: 0;
    margin: 0 8px 0 0;
}

.gigya-screen .gigya-composite-control.gigya-composite-control-phone-number-widget .gigya-phone-number-verification {
    -ms-flex-pack: justify;
    justify-content: space-between;
}

.gigya-screen .gigya-composite-control.gigya-composite-control-phone-number-widget .gigya-phone-number-verification-code {
    width: 80px;
}

.gigya-screen .gigya-composite-control.gigya-composite-control-phone-number-widget .gigya-fieldset.gigya-phone-number-input {
    display: -ms-flexbox;
    display: flex;
}

.gigya-screen .gigya-composite-control.gigya-composite-control-phone-number-widget .gigya-phone-verified-status-image {
    margin-right: 4px;
}

.gigya-screen .gigya-composite-control.gigya-composite-control-phone-number-widget select.gigya-country-code-select {
    width: 95px;
    margin-right: 16px;
}

.gigya-screen .gigya-composite-control.gigya-composite-control-phone-number-widget .gigya-country-code-select.gigya-error {
    padding: 0;
}

.gigya-screen .gigya-composite-control.gigya-composite-control-phone-number-widget .gigya-input-text.gigya-subscriber-phone-number-input {
    box-sizing: border-box;
}

.gigya-screen .gigya-composite-control.gigya-composite-control-phone-number-widget .gigya-phone-verify-button {
    margin: 0;
    display: none;
}

.gigya-screen .gigya-composite-control.gigya-composite-control-phone-number-widget .gigya-phone-resend-button {
    margin: 0;
}

.gigya-screen .gigya-composite-control.gigya-composite-control-phone-number-widget .gigya-phone-resend-blocked {
    margin: 0;
    text-align: center;
}

.gigya-screen .gigya-composite-control.gigya-composite-control-phone-number-widget .gigya-phone-verified-status,
.gigya-screen .gigya-composite-control.gigya-composite-control-phone-number-widget .gigya-phone-verify-button {
    margin-left: 32px;
}

.gigya-screen .gigya-composite-control.gigya-composite-control-phone-number-widget .gigya-phone-verified-status {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: center;
    align-items: center;
}

.gigya-screen .gigya-composite-control.gigya-composite-control-phone-number-widget .gigya-phone-verified-status .gigya-show-checkmark {
    margin-right: 4px;
}

.gigya-screen .gigya-composite-control.gigya-composite-control-phone-number-widget.gigya-composite-control-phone-number-widget-identifier[data-widget-code-state=resend] .gigya-phone-resend-button {
    display: block;
}

.gigya-screen .gigya-composite-control.gigya-composite-control-phone-number-widget.gigya-composite-control-phone-number-widget-identifier[data-widget-code-state=resend] .gigya-phone-resend-blocked,
.gigya-screen .gigya-composite-control.gigya-composite-control-phone-number-widget.gigya-composite-control-phone-number-widget-identifier[data-widget-code-state=sent] .gigya-phone-resend-button {
    display: none;
}

.gigya-screen .gigya-composite-control.gigya-composite-control-phone-number-widget.gigya-composite-control-phone-number-widget-identifier .gigya-phone-verify-button,
.gigya-screen .gigya-composite-control.gigya-composite-control-phone-number-widget.gigya-composite-control-phone-number-widget-identifier[data-widget-code-state=sent] .gigya-phone-resend-blocked {
    display: block;
}

.gigya-screen .gigya-composite-control.gigya-composite-control-phone-number-widget.gigya-composite-control-phone-number-widget-identifier .gigya-phone-number-input {
    display: none;
}

.gigya-screen .gigya-composite-control.gigya-composite-control-phone-number-widget.gigya-composite-control-phone-number-widget-identifier[data-widget-state=mobile] .gigya-phone-number-input,
.gigya-screen .gigya-composite-control.gigya-composite-control-phone-number-widget.gigya-composite-control-phone-number-widget-identifier[data-widget-state=verification] .gigya-phone-number-verification,
.gigya-screen .gigya-composite-control.gigya-composite-control-phone-number-widget.gigya-composite-control-phone-number-widget-identifier[data-widget-state=verified] .gigya-phone-number-verified {
    display: -ms-flexbox;
    display: flex;
}

.gigya-composite-control-link:focus,
.gigya-composite-control select:focus,
.gigya-composite-control input[type=checkbox]:focus,
.gigya-myPhoto-delete-button:focus,
.gigya-myPhoto-uploadForm input:focus,
.gigya-dummyPhoto-upload:focus {
    box-shadow: none !important;
}


/* Mobile style overwrites */
.gigya-style-modern.gigya-screen-dialog-mobile div.gigya-screen-dialog-top {
    background: none;
    color: #111111;
    position: relative;
    max-width: 500px;
    margin: 0 auto;
    padding: 20px 20px 0;
}

.gigya-style-modern.gigya-screen-dialog-mobile div.gigya-screen-dialog-inner > div {
    max-width: 500px !important;
    margin: 0 auto;
}

@media screen and (min-width: 550px) {
    .gigya-style-modern.gigya-screen-dialog-mobile div.gigya-screen-dialog-top {
        padding: 32px 32px 0;
    }
}

.gigya-style-modern.gigya-screen-dialog-mobile div.gigya-screen-dialog-caption {
    color: #111111;
    font-family: 'Univers67BoldCondensed', sans-serif;
    font-size: 20px;
    line-height: 1.2em;
    text-transform: uppercase;
    padding-right: 0;
    text-align: center;
}

.gigya-style-modern.gigya-screen-dialog-mobile div.gigya-screen-dialog-inner {
    margin-top: 0;
}

div.gigya-screen-dialog-mobile .gigya-screen.portrait.mobile {
    padding-top: 10px;
}

/* Password strength bubble */
.gigya-passwordStrength-text .gigya-passwordStrength-scoreText {
    font-family: "Univers65Bold", sans-serif;
    font-weight: normal;
}

.gig-balloon.gigya-password-strength-bubble,
.gigya-password-strength .gigya-reset, .gig-balloon-body,
.gigya-passwordStrength-text-requirements, .gigya-passwordStrength-bar-very_strong, .gigya-passwordStrength-bar,
.gigya-passwordStrength-text .gigya-passwordStrength-scoreText, .gigya-passwordStrength-text, .gigya-passwordStrength-very_strong,
.gig-balloons *,
div.gig-balloons,
.gig-balloons span,
.gig-balloons a:hover,
.gig-balloons a:visited,
.gig-balloons a:link,
.gig-balloons a:active {
    font-family: "Univers55Roman", sans-serif;
    color: #111111;
    font-weight: normal;
}

div.gig-balloon-frame {
    border: 1px solid #eaeaea !important;
}

div.gig-balloon-caption {
    font-family: "Univers55Roman", sans-serif !important;
    color: #111111 !important;
    font-weight: normal !important;
}

/* Profile details on account pages */
#gigyaAccountWrapper .gigya-screen.portrait {
    margin-left: 0;
    margin-right: auto;
    padding: 0;
}

#gigyaAccountWrapper .gigya-screen .gigya-composite-control.gigya-composite-control-label {
    font-family: "Univers67BoldCondensed", sans-serif;
    font-size: 24px;
    line-height: 1.2em;
    text-transform: uppercase;
    margin-bottom: 30px;
    padding-bottom: 0;
}

#gigyaAccountWrapper a.gigya-composite-control.gigya-composite-control-link:last-child {
    margin-top: -26px;
}

@media screen and (min-width: 768px) {
    #gigyaAccountWrapper .gigya-screen .gigya-composite-control.gigya-composite-control-label {
        font-size: 32px;
        margin-bottom: 30px;
    }
}

