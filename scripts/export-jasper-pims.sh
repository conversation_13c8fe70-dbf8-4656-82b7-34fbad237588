#!/bin/bash
# This script exports product entries from Contentful and extracts the SKU and Jasper PIM ID fields.
set -euo pipefail
# Ensure the contentful CLI is installed and configured
if ! command -v contentful &> /dev/null; then
  echo "Contentful CLI is not installed. Please install it first."
  exit 1
fi
if ! contentful space list &> /dev/null; then
  echo "Contentful CLI is not configured. Please run 'contentful login' first."
  exit 1
fi
# Export product entries from both master and staging environments
# print on the screen what is being done
# print the message in bold and with a green color
echo "\033[1;32mExporting product entries from Contentful master environment...\033[0m"
# only run the export command if the export-master.json file does not exist
if [ -f export-master.json ]; then
  echo "export-master.json already exists. Skipping export for master environment."
else
  echo "Exporting product entries from Contentful master environment..."
  contentful space export --environment-id=master --query-entries "content_type=product" --content-only=true --query-assets="fields.title=null" --content-file="export-master.json" && jq '[
    .entries[]
    | select(.fields.sku and .fields.sku.en and .fields.jasperPimId and .fields.jasperPimId.en)
    | {key: .fields.sku.en, value: .fields.jasperPimId.en}
    ] | sort_by(.key) | from_entries' ./export-master.json > export-master-pims.json
fi

rm -f export-master.json

echo "\033[1;32mExporting product entries from Contentful staging environment...\033[0m"
# only run the export command if the export-staging.json file does not exist
if [ -f export-staging.json ]; then
  echo "export-staging.json already exists. Skipping export for staging environment."
else
  echo "Exporting product entries from Contentful staging environment..."
  contentful space export --environment-id=staging --query-entries "content_type=product" --content-only=true --query-assets="fields.title=null" --content-file="export-staging.json" && jq '[
    .entries[]
    | select(.fields.sku and .fields.sku.en and .fields.jasperPimId and .fields.jasperPimId.en)
    | {key: .fields.sku.en, value: .fields.jasperPimId.en}
    ] | sort_by(.key) | from_entries' ./export-staging.json > export-staging-pims.json
fi

rm -f export-staging.json

# print only the differences between the two files
echo "\033[1;32mDifferences between master and staging environments:\033[0m"
# bash -c 'diff <(jq -S . export-master-pims.json) <(jq -S . export-staging-pims.json)' > json-diff.txt
# print the differences in a readable format
bash -c 'diff <(jq -S . export-staging-pims.json) <(jq -S . export-master-pims.json) | colordiff'

# rm -f export-master-pims.json
# rm -f export-staging-pims.json