// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
export const getPopUpFollowingEvent = (
    eventName: string,
    eventGroup: string,
    eventText: string | undefined,
    eventAction: string,
    event_click_URL: string | undefined
) => {
    return {
        event: 'popUpFollowing',
        popUpFollowing: {
            eventName: eventName,
            eventGroup: eventGroup,
            eventText: eventText,
            eventAction: eventAction,
            event_click_URL: event_click_URL
        }
    }
}
