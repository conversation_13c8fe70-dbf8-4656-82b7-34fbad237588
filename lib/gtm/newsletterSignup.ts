import { getLocation } from '@lib/gtm/utils/location'

export type NewsletterSignupEventProps = {
    customerEmail: string
    registrationLocation: string
    marketingConsent: boolean
    softwareTitle?: string
    softwareOS?: string
    softwareVersion?: string
    firstName?: string
    lastName?: string
}

export const getNewsletterSignupEvent = ({
    customerEmail,
    registrationLocation,
    marketingConsent,
    softwareTitle,
    softwareOS,
    softwareVersion,
    firstName,
    lastName
}: NewsletterSignupEventProps): any => {
    return {
        event: 'newsletterSignup',
        newsletterSignup: {
            customerEmail,
            ...(firstName &&
                lastName && { firstName: firstName, lastName: lastName }),
            registrationLocation,
            marketingConsent,
            ...(softwareTitle && { softwareTitle }),
            ...(softwareOS && { softwareOS }),
            ...(softwareVersion && { softwareVersion })
        },
        location: getLocation()
    }
}
