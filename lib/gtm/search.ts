import { ProductInterface, Products } from '@pylot-data/fwrdschema'
import {
    getVariantId,
    getProductPrices,
    getVariantTitle,
    getVariantURL
} from '@lib/gtm/utils/product'

const getProducts = (items?: ProductInterface[]) => {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const products: any[] = []
    items?.forEach((p, index) => {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const product: any = {
            position: index, //position of product in array
            productTitle: p.name, //product title from product catalog
            productId: p.uid, //product id from product catalog
            variantId: getVariantId(p), //variant id from first available variant in the product catalog, blank if not a variant
            variantTitle: getVariantTitle(p), //variant title from from first available variant in the product catalog, blank if not a variant
            sku: p.sku, //sku from product catalog
            price: getProductPrices(p.price_range).price, //price from product catalog
            msrPrice: getProductPrices(p.price_range).comparedPrice, //compare at price from product catalog
            brand: p.brand, // brand from product catalog TODO
            collections: '', //comma separated list of collections from product catalog
            type: '', // type from product catalog
            bundleSkus: p.bundle_and_save_skus, //array of bundle skus, blank if it is not a bundle
            tags: '', //comma separated list of tags from product catalog
            imageURL: p.image?.url, //default image URL from product catalog, variant image of variant
            productURL: p.url_key, // product URL from product catalog
            variantURL: getVariantURL(p) //variant URL from product catalog, blank if not a variant
        }
        products.push(product)
    })

    return products
}

// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
export const getSearchEvent = (
    visibleProductData: Products,
    searchTerm: string | string[]
) => {
    return {
        event: 'search',
        search: {
            resultsCountAll: visibleProductData.total_count, //all products from the search
            resultsCount: visibleProductData.items?.length, //products from the search that loaded on the page
            searchTerm: searchTerm, //search term used in the search
            products: getProducts(
                visibleProductData.items as ProductInterface[]
            ) //list only the products loaded into the page
        }
    }
}
