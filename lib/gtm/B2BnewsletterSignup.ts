import { getLocation } from '@lib/gtm/utils/location'

// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
export const getB2BnewsletterSignupEvent = (
    customerEmail: string,
    registrationLocation: string,
    marketingConsent: boolean,
    firstName?: string,
    lastName?: string
) => {
    return {
        event: 'B2BnewsletterSignup',
        B2BnewsletterSignup: {
            customerEmail: customerEmail,
            ...(firstName &&
                lastName && { firstName: firstName, lastName: lastName }),
            registrationLocation: registrationLocation,
            marketingConsent: marketingConsent
        },
        location: getLocation()
    }
}
