import { graphqlFetch } from '@pylot-data/graphqlFetch'
import type { GraphqlFetchOutputPromise } from '@pylot-data/graphqlFetch'
import { cartFields } from '@pylot-data/core-queries/cart'
import type { Cart } from '@pylot-data/pylotschema'
import { getIsSignedIn } from './utils/getIsSignedIn'

// We don't pass the CustomerCartID, because Magento will use it automatically
// by checking the user associated with the auth header

export const MERGE_CARTS = /* GraphQL */ `
    mutation mergeCarts($guestCartId: String!, $isSignedIn: Boolean = false) {
        cart: mergeCarts(source_cart_id: $guestCartId) {
            ${cartFields}
        }
    }
`
export const mergeCarts = async ({
    guestCartId,
    locale = ''
}: {
    guestCartId: string
    locale?: string
}): GraphqlFetchOutputPromise<{
    cart: Cart
}> =>
    await graphqlFetch<
        { guestCartId: string; isSignedIn: boolean },
        { cart: Cart }
    >({
        query: MERGE_CARTS,
        variables: {
            guestCartId,
            isSignedIn: getIsSignedIn()
        },
        locale
    })
