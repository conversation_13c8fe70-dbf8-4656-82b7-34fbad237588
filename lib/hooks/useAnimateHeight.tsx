import { FC, ReactNode, useEffect, useRef, useState } from 'react'

type AnimateHeightProps = {
    isOpen: boolean
    children: ReactNode
}

export const AnimateHeight: FC<AnimateHeightProps> = ({ isOpen, children }) => {
    const contentRef = useRef<HTMLDivElement>(null)
    const [height, setHeight] = useState<string>('0px')

    useEffect(() => {
        if (contentRef.current) {
            if (isOpen) {
                const contentHeight = `${contentRef.current.scrollHeight}px`
                setHeight(contentHeight)
            } else {
                setHeight('0px')
            }
        }
    }, [isOpen])

    return (
        <div
            ref={contentRef}
            style={{
                height,
                overflow: 'hidden',
                transition: 'height 0.3s ease'
            }}
        >
            {children}
        </div>
    )
}
