import { ElgatoCurrencyMap } from '@preload/graphql/getConfig.d'
// import { CountryCodeEnum } from '../../framework/pylot/enums/CountryCodeEnum.d'
const priceRegExp = /^(\D+|)(\d+)(,|\.)(\d+)(\D+|)$/gm

// const listCountriesUseEuro: string[] = [
//     CountryCodeEnum.Eu.toLocaleLowerCase(),
//     CountryCodeEnum.Fr.toLocaleLowerCase(),
//     CountryCodeEnum.De.toLocaleLowerCase(),
//     CountryCodeEnum.It.toLocaleLowerCase(),
//     CountryCodeEnum.Es.toLocaleLowerCase(),
//     CountryCodeEnum.Pt.toLocaleLowerCase()
// ]
const defaultRegionMap: ElgatoCurrencyMap = {
    countryCode: ['US'],
    symbol: '$',
    separator: '.',
    position: 'left'
}

const transformPrice = (
    priceValue: string,
    locale: string,
    amount: unknown,
    decimalAmount: unknown,
    countryCode: string,
    currencyMap: ElgatoCurrencyMap[]
): string => {
    const curRegion = currencyMap.find(({ countryCode: regionCode }) =>
        regionCode.includes(countryCode.toUpperCase())
    )

    if (curRegion) {
        const { symbol, separator, position } = curRegion
        return (priceValue =
            position === 'left'
                ? `${symbol}${amount}${separator}${decimalAmount}`
                : `${amount}${separator}${decimalAmount} ${symbol}`)
    }

    return priceValue
}

export const formatPriceSymbol = (
    priceValue?: string,
    symbolCode?: string | null,
    locale?: string,
    currencyMap?: ElgatoCurrencyMap[]
): string => {
    if (priceValue) {
        const priceParts = [...priceValue.matchAll(priceRegExp)]
        const countryCode = locale
            ? locale.split('-')[1].toLocaleLowerCase()
            : 'us'
        if (priceParts.length === 0) {
            const formatPrice = priceValue.replace(/[^0-9.,\s-]+/g, '')
            // if (countryCode === CountryCodeEnum.Pl.toLocaleLowerCase()) {
            //     return (priceValue = `${formatPrice} zł`)
            // } else if (listCountriesUseEuro.includes(countryCode)) {
            //     return (priceValue = `${formatPrice} €`)
            // } else if (
            //     countryCode === CountryCodeEnum.Ca.toLocaleLowerCase() ||
            //     countryCode === CountryCodeEnum.Us.toLocaleLowerCase()
            // ) {
            //     return (priceValue = `$${formatPrice}`)
            // } else if (countryCode === CountryCodeEnum.Uk.toLocaleLowerCase()) {
            //     return (priceValue = `£${formatPrice}`)
            // }
            const curRegion = currencyMap
                ? currencyMap.find(({ countryCode: regionCode }) =>
                      regionCode.includes(countryCode.toUpperCase())
                  )
                : defaultRegionMap
            if (curRegion) {
                return curRegion.position === 'left'
                    ? (priceValue = `${curRegion.symbol}${formatPrice}`)
                    : (priceValue = `${formatPrice} ${curRegion.symbol}`)
            }

            return priceValue
        }
        const [
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            _all,
            symbol1,
            amount,
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            _separator,
            decimalAmount,
            symbol2
        ] = priceParts[0]

        const symbol1Helper = symbol1.trim()
        const symbol2Helper = symbol2.trim()
        const symbol = symbol1Helper.length > 0 ? symbol1Helper : symbol2Helper
        if (symbol.length === 0) {
            if (!symbolCode || !locale) {
                return priceValue
            }
            const intl = new Intl.NumberFormat(locale, {
                style: 'currency',
                currency: symbolCode
            })
            const num = parseFloat(`${amount}.${decimalAmount}`)
            return intl.format(num)
        }

        return transformPrice(
            priceValue,
            locale ?? '',
            amount,
            decimalAmount,
            countryCode,
            currencyMap || [defaultRegionMap]
        )
    }

    return ''
}
