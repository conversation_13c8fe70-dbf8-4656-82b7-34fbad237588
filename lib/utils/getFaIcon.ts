/* eslint-disable @typescript-eslint/no-var-requires */
import { IconProp } from '@fortawesome/fontawesome-svg-core'
import { camelCase } from 'lodash'

export enum EIconType {
    SOLID = 'Solid',
    REGULAR = 'Regular',
    LIGHT = 'Light',
    DUOTONE = 'Duotone'
}

interface IFaIcon {
    iconName: string
    iconType?: EIconType
    freeVersion?: boolean
}

export const getFaIcon = ({
    iconName,
    iconType = EIconType.SOLID,
    freeVersion = false
}: IFaIcon): IconProp | null => {
    const iconNameFormatted = camelCase(iconName)
    if (freeVersion) {
        return require('@fortawesome/free-solid-svg-icons')[iconNameFormatted]
    }
    switch (iconType) {
        case EIconType.SOLID: {
            return require('@fortawesome/pro-solid-svg-icons')[
                iconNameFormatted
            ]
        }
        case EIconType.REGULAR: {
            return require('@fortawesome/pro-regular-svg-icons')[
                iconNameFormatted
            ]
        }
        case EIconType.LIGHT: {
            return require('@fortawesome/pro-light-svg-icons')[
                iconNameFormatted
            ]
        }
        case EIconType.DUOTONE: {
            return require('@fortawesome/pro-duotone-svg-icons')[
                iconNameFormatted
            ]
        }
        default: {
            return null
        }
    }
}
