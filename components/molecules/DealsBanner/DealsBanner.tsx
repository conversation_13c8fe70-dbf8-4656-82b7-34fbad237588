import s from './DealsBanner.module.scss'
import React, { FC, useState, useEffect } from 'react'
import cn from 'classnames'
import { Button } from '@components/molecules/Button/Button'
import { OverlayCloseButton } from '@components/organisms/Overlay/OverlayCloseButton'
import ElgatoImage from '@components/common/ElgatoImage/ElgatoImage'
import { useContentJson } from '@pylot-data/hooks/contentful/use-content-json'
import { BannerProps } from '@components/molecules/Banner/Banner'
import { pushToDataLayer } from '@corsairitshopify/pylot-gtm/src/utils'

const CONTENT_IDENTIFIER = ['elgatoWeeks']
const CONTENT_TYPE = 'moleculeBanner'
export const PATHS_DEALS_BANNER_HIDDEN = ['/', '/s/elgato-week-2025']

export interface DealsBannerProps {
    onClose?: () => void
    className?: string
}

export const DealsBanner: FC<DealsBannerProps> = ({ onClose, className }) => {
    const { data } = useContentJson<BannerProps>(
        {
            identifier: CONTENT_IDENTIFIER,
            contentType: CONTENT_TYPE,
            options: {
                level: 4
            }
        },
        {
            revalidateOnFocus: false,
            revalidateOnMount: true
        }
    )

    const [isVisible, setIsVisible] = useState(true)
    const [isMounted, setIsMounted] = useState(false)
    const [isClosing, setIsClosing] = useState(false)

    useEffect(() => {
        setIsVisible(true)
        setIsClosing(false)

        setTimeout(() => {
            setIsMounted(true)
        }, 3000)
    }, [])

    const handleClose = () => {
        setIsClosing(true)
        setTimeout(() => {
            setIsVisible(false)
            if (onClose) {
                onClose()
            }
        }, 600)
        pushToDataLayer({
            event: 'deals-banner',
            eventAction: 'close',
            shop: link?.linkTitle,
            link: link?.linkUrl
        })
    }

    if (!isVisible) {
        return null
    }

    if (!data?.[0]?.parsedEntries) {
        return null
    }

    const {
        headline,
        text: description,
        link,
        cloudinaryImage
    } = data[0].parsedEntries

    return (
        <div
            className={cn(s['deals-banner'], className, {
                [s['deals-banner--mounted']]: isMounted && !isClosing,
                [s['deals-banner--closing']]: isClosing
            })}
        >
            <div className={s['deals-banner__content']}>
                <div className={s['deals-banner__textContent']}>
                    <div className={s['deals-banner__text']}>
                        <h2 className={s['deals-banner__headline']}>
                            {headline}
                        </h2>
                        <p className={s['deals-banner__description']}>
                            {description}
                        </p>
                    </div>
                    {link && link.linkUrl && (
                        <div className={s['deals-banner__buttonWrapper']}>
                            <Button
                                href={link.linkUrl}
                                className={cn(s['deals-banner__button'])}
                                tracking
                                dataLayer={link.eventTracking}
                            >
                                {link.linkTitle}
                            </Button>
                        </div>
                    )}
                </div>
                {cloudinaryImage && cloudinaryImage[0] && (
                    <div className={s['deals-banner__imageWrapper']}>
                        <ElgatoImage
                            src={cloudinaryImage[0].secure_url}
                            alt={cloudinaryImage[0].context?.custom?.alt}
                            width={118}
                            height={164}
                        />
                    </div>
                )}
            </div>
            <button
                className={s['deals-banner__closeButton']}
                onClick={handleClose}
                aria-label="Close banner"
                data-layer={link?.eventTracking}
            >
                <OverlayCloseButton />
            </button>
        </div>
    )
}
