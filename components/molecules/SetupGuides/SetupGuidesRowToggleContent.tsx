import React, { FC, useEffect, useState } from 'react'
import AnimateHeight from 'react-animate-height'
import { SetupGuidesRowsProps } from '@components/molecules/SetupGuides/SetupGuidesRows'
import s from './SetupGuidesRows.module.scss'
import { SetupGuidesRow } from '@components/molecules/SetupGuides/SetupGuidesRow'

export interface SetupGuidesRowToggleContentProps extends SetupGuidesRowsProps {
    toggled: boolean
}

const SetupGuidesRowToggleContent: FC<SetupGuidesRowToggleContentProps> = (
    props
) => {
    const { rows, linkLabel, toggled } = props
    const [animateHeight, setAnimateHeight] = useState<
        'auto' | number | `${number}%`
    >(0)
    useEffect(() => {
        if (toggled) {
            setAnimateHeight('auto')
        } else {
            setAnimateHeight(0)
        }
    }, [toggled])
    return (
        <AnimateHeight
            duration={500}
            height={animateHeight} // see props documentation below
        >
            <div className={s['setup-guides-row__toggle-content']}>
                {rows.map((row, i) => {
                    if (i >= 3) {
                        return (
                            <SetupGuidesRow
                                key={i}
                                image={row.image}
                                rowTitle={row.rowTitle}
                                variant={i % 2 == 0 ? 'transparent' : 'light'}
                                downloadUrl={row.downloadUrl}
                                newTab={row.newTab}
                                text={row.text}
                                linkLabel={linkLabel}
                                className={s['setup-guides-rows__row']}
                            />
                        )
                    }
                })}
            </div>
        </AnimateHeight>
    )
}
export default SetupGuidesRowToggleContent
