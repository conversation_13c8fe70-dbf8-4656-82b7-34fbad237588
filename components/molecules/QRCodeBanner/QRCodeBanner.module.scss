.qr-code-banner {
    position: fixed;
    left: 50%;
    right: 0;
    z-index: 10; // Since this should not appear when the navigation is open, we need to set a lower z-index than the navigation
    bottom: 16px;
    background-color: var(--charcoal);
    border: 2px solid var(--mid-grey-1);
    border-radius: 8px;
    padding: 9px 12px 12px;
    width: calc(100% - 32px);
    color: var(--white);
    transform: translate(-50%, 0);
    max-width: 170px;

    display: flex;
    flex-direction: column;
    gap: 8px;

    @screen md-max {
        transition: 0.3s ease-out;
    }

    @screen md {
        z-index: 10;
        position: fixed;
        transform: none;
        width: auto;
        left: auto;
        right: 65px;
        top: calc(75px + 36px);
        bottom: auto;

        @screen lg {
            top: calc(111px + 36px + var(--geolocationBannerHeight));
        }
    }

    &--out {
        @screen md-max {
            pointer-events: none;
            opacity: 0;
            -webkit-transform: translate(-50%, calc(100% + 16px));
            transform: translate(-50%, calc(100% + 16px));
        }
    }

    &__inner {
        display: flex;
        flex-direction: column;
    }

    &__image {
        margin: 0 auto;
        transition: height 0.5s ease;
    }
}
