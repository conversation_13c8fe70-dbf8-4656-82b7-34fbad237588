import React, { FC } from 'react'
import { Icon } from '@components/atoms/Icon/Icon'
import { useTranslation } from 'next-i18next'

export enum CompatibilityLegendItemEnum {
    FULL = 'full',
    NONE = 'none',
    PARTIAL = 'partial'
}

export type CompatibilityLegendItemProps = {
    className?: string
    label?: string
    variant: CompatibilityLegendItemEnum
    size?: 'default' | 'large'
    active?: boolean
}

export const CompatibilityLegendItem: FC<CompatibilityLegendItemProps> = (
    props
) => {
    const { variant, label, size = 'default', active = false } = props
    const { t } = useTranslation(['common'])
    let legendLabel = ''
    let icon = ''
    switch (variant) {
        case CompatibilityLegendItemEnum.NONE: {
            legendLabel = t('None')
            icon = 'compatibilityNone'
            break
        }
        case CompatibilityLegendItemEnum.PARTIAL: {
            legendLabel = t('Partial')
            icon = 'compatibilityPartial'
            break
        }
        default: {
            legendLabel = t('Full')
            icon = 'compatibilityFull'
        }
    }
    legendLabel = label ? label : legendLabel
    let textClass = 'xs-copy'
    if (size === 'large') {
        textClass = active ? 'button-text-bold' : 'button-text'
    }
    return (
        <div className="flex gap-8px items-center">
            <Icon name={icon} />
            <span className={textClass}>{legendLabel}</span>
        </div>
    )
}
