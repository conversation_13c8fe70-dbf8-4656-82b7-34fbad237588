import { urlParamsGenerator } from '@corsairitshopify/corsair-image/src/utils/urlParamsGenerator'
import { NextImageProps } from '@corsairitshopify/corsair-image/src/Image'

const CONTENTFUL_API_BASE_URL = 'https://images.ctfassets.net'

const isContentfulImage = (url: string) =>
    url.startsWith(CONTENTFUL_API_BASE_URL)

/**
 * Construct images url with optimization params from contentful
 * @param imageProps NextImageProps
 * @returns string
 */

export const contentfulImageLoader = (imageProps: NextImageProps): string => {
    const { src, width, quality = 75 } = imageProps
    const url = String(src)

    if (isContentfulImage(url)) {
        const urlConstructor = new URL(url)
        const urlParams = urlParamsGenerator(imageProps)

        Object.entries(urlParams).forEach(([key, value]) => {
            urlConstructor.searchParams.set(String(key), String(value))
        })

        return urlConstructor.toString()
    }

    //fallback should have the width & quality to get next image optimization
    //since we use the custom loader
    return `/_next/image?url=${encodeURIComponent(url)}&w=${width}&q=${quality}`
}
