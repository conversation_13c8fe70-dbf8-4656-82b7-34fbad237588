import React, { FC, useEffect, useState } from 'react'
import s from './Toggles.module.scss'
import cn from 'classnames'
import {
    TextToggle,
    TextToggleProps
} from '@components/atoms/TextToggle/TextToggle'

export type TogglesProps = {
    className?: string
    toggles?: TextToggleProps[]
    activeTab?: number
    variant?: 'three-column' | 'system'
    onChange?: (i: number) => void
    id?: string
}

export const Toggles: FC<TogglesProps> = (props) => {
    const {
        toggles,
        className,
        activeTab = 0,
        variant = 'three-column',
        onChange,
        id
    } = props
    const [activeTabIndex, setActiveTabIndex] = useState(activeTab)
    const textToggleVariant = variant === 'three-column' ? 'tab' : variant
    const setSelectedToggle = (i: number) => {
        if (onChange) {
            onChange(i)
        } else {
            setActiveTabIndex(i)
        }
    }
    useEffect(() => {
        setActiveTabIndex(activeTab)
    }, [activeTab])
    return (
        <div
            className={cn(s['toggles'], s[`toggles--${variant}`], className, {
                [s['toggles__wave-neo']]: id === 'wave-neo-system-requirements'
            })}
        >
            {toggles &&
                toggles.map((toggle, i) => (
                    <TextToggle
                        key={i}
                        text={toggle.text}
                        active={
                            (onChange && activeTab === i) ||
                            (!onChange && activeTabIndex === i)
                        }
                        onChange={() => setSelectedToggle(i)}
                        variant={textToggleVariant}
                        id={id}
                    />
                ))}
        </div>
    )
}
