.table-popup {

    @apply opacity-0 rounded-xxl p-24px flex gap-8 items-start text-white;
    @apply transition transition-opacity duration-150 -z-1;
    overflow-y: auto;
    overflow-x: hidden;
    pointer-events: none;
    max-width: 1490px;
    max-height: 555px;
    width: 100%;
    height: 100%;
    bottom: 0;
    padding: 32px;
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
    justify-content: center;
    gap: 40px;
    transform: translate(0, 0);
    background: var(--primitive-gray-120);
    &__close-button {
        background-color: black;
        color: white;
        border-radius: 6px;
        padding: 8px;
    }
    @screen md-max {
        min-width: 371px;
        width: 90%;
        padding: 16px;
    }
    &--light-theme {
        background-color: white;
        table {
            background-color: var(--primitive-gray-10) !important;
            border: solid 16px var(--primitive-gray-10) !important;
        td {
            color: var(--black) !important;
            }
            tr:not(:first-child) {
                border-top: 1px solid var(--primitive-gray-80) !important;
            }
        }

        
    }
    @media only screen and (max-width: 1399px) {
        max-width: 1010px;
    }

    &--light {
        background: rgba(51, 51, 51, 0.7);
    }

    &--white {
        background: rgba(255, 255, 255, 0.9);
        @apply text-charcoal;
    }

    &--open {
        @apply opacity-100 z-10;
        pointer-events: auto;
    }

    table {
        display: flex;
        align-items: flex-start;
        justify-content: center;
        max-width: 345px;
        border-radius: 6px;
        background: var(--charcoal);
        position: relative;
        border: solid 20px var(--charcoal);
        outline: solid 1px white;
        height: 100%;

        @media only screen and (max-width: 1440px) {
            max-width: 315px;
        }

        tr {
            border-top: 1px solid #767676;
        }

        b:first-child {
            &:before {
                content: "";
                background-color: var(--abbey-green-1);
                width: 8px;
                height: 8px;
                border-radius: 20px;
                justify-content: center;
                align-items: center;
                position: relative;
                top: 4%;
                left: 3%;
                margin-right: 10px;
                display: inline-block;
            }

            td:first-child {
                margin-left: 12px;
                @apply font-univers55Roman;
            }
        }

        tr:first-child {
            height: 2px;
            border-top: none;

            td:nth-of-type(2) {
                display: none;
            }
        }

        tr:nth-of-type(2) {
            border-top: none;
        }

        td {
            font-size: 12px;
            padding-bottom: 8px;
            padding-top: 8px;
            min-width: 140px;
            width: 100%;
        }
    }

    &--positioned {
        @screen md-max {
            top: 10% !important;
            left: 16px !important;
            right: 16px !important;
            transform: translate(0, -50%) !important;
        }
    }
}
