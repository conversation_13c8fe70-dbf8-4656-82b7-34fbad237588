import cn from 'classnames'
import { FC, useEffect, useMemo, useRef, useState } from 'react'

import CloseIcon from '@components/atoms/Icon/general/CloseIcon'
import { Button } from '@components/molecules/Button/Button'
import { Dropdown } from '@components/molecules/Dropdown/Dropdown'
import {
    Container,
    ContainerSize
} from '@components/organisms/Container/Container'
import { TFunction } from 'i18next'
import Cookies from 'js-cookie'
import { REGION_EU_CODES_MAP } from 'localesConfig'
import { useTranslation } from 'next-i18next'
import { useRouter } from 'next/router'
import s from './GeolocationBanner.module.scss'

const getDropdownOptions = (t: TFunction) => {
    return [
        {
            title: t('regions|north_america'),
            options: [
                {
                    label: t(`regions|canada`),
                    value: 'CA'
                },
                {
                    label: t(`regions|united_states`),
                    value: 'US'
                }
            ]
        },
        {
            title: t('regions|europe'),
            options: [
                {
                    label: t(`regions|france`),
                    value: 'FR'
                },
                {
                    label: t(`regions|germany`),
                    value: 'DE'
                },
                {
                    label: t(`regions|italy`),
                    value: 'IT'
                },
                {
                    label: t(`regions|poland`),
                    value: 'PL'
                },
                {
                    label: t(`regions|portugal`),
                    value: 'PT'
                },
                {
                    label: t(`regions|spain`),
                    value: 'ES'
                },
                {
                    label: t(`regions|united_kingdom`),
                    value: 'UK'
                },
                {
                    label: t(`regions|sweden`),
                    value: 'SE'
                },
                {
                    label: t(`regions|switzerland`),
                    value: 'CH'
                },
                { label: t(`regions|rest_of_europe`), value: 'EU' }
            ]
        },
        {
            title: t('regions|asia'),
            options: [
                {
                    label: t(`regions|china`),
                    value: 'CN'
                },
                {
                    label: t(`regions|japan`),
                    value: 'JP'
                },
                {
                    label: t(`regions|korea`),
                    value: 'KR'
                },
                {
                    label: t(`regions|taiwan`),
                    value: 'TW'
                }
            ]
        },
        {
            title: t('regions|global'),
            options: [
                {
                    label: t(`regions|latam`),
                    value: 'LM'
                },
                {
                    label: t(`regions|rest_of_the_world`),
                    value: 'WW'
                }
            ]
        }
    ]
}

interface GeolocationBannerProps {
    className?: string
    openDirection?: 'up' | 'down'
    arrowPosition?: 'left' | 'right'
    isVisible: boolean
    setIsVisible: React.Dispatch<React.SetStateAction<boolean>>
    onClick?: () => void
}

export const GeolocationBanner: FC<GeolocationBannerProps> = ({
    className = '',
    openDirection = 'down',
    arrowPosition = 'right',
    isVisible,
    setIsVisible
}) => {
    const dropdownRef = useRef<HTMLDivElement>(null)
    const { t } = useTranslation(['common'])
    const router = useRouter()
    const { asPath, locale } = router
    const currentLangCode = locale?.substring(0, 2)
    const userCountryCodeCookie = Cookies.get('Country_Code')
    const userRegionFromCountryCodeCookie =
        userCountryCodeCookie === 'GB' ? 'UK' : userCountryCodeCookie

    const geolocationBannerRef = useRef<HTMLDivElement>(null)

    useEffect(() => {
        if (isVisible) {
            document.documentElement.style.setProperty(
                '--geolocationBannerHeight',
                `80px`
            )
        } else {
            document.documentElement.style.setProperty(
                '--geolocationBannerHeight',
                `0px`
            )
        }
    }, [isVisible])

    const sortedDropdownOptions = useMemo(() => {
        const dropdownOptions = getDropdownOptions(t)
        return dropdownOptions.map((option) => {
            return {
                ...option,
                options: option.options.sort((country, nextCountry) => {
                    if (['EU', 'WW'].includes(country.value)) {
                        return 0
                    }
                    return country.label.localeCompare(nextCountry.label)
                })
            }
        })
    }, [t])

    const isPossibleCountry = sortedDropdownOptions.some((option) =>
        option.options.some(
            (country) => country.value === userRegionFromCountryCodeCookie
        )
    )
    const [selectedCountry, setSelectedCountry] = useState(
        isPossibleCountry ? userRegionFromCountryCodeCookie : undefined
    )

    const title = useMemo(() => {
        let titleToReturn = t('Select your country')
        if (userRegionFromCountryCodeCookie) {
            sortedDropdownOptions.forEach((option) =>
                option.options.forEach((country) => {
                    if (
                        country.value === userRegionFromCountryCodeCookie ||
                        (country.value === 'EU' &&
                            Object.values(REGION_EU_CODES_MAP).includes(
                                userRegionFromCountryCodeCookie
                            ))
                    ) {
                        titleToReturn = country.label
                    }
                })
            )
        }
        return titleToReturn
    }, [userRegionFromCountryCodeCookie, sortedDropdownOptions, t])

    const onClose = () => {
        setIsVisible(false)
        Cookies.set('GEOLOCATION_BANNER_HIDE', '1')
    }

    const onContinue = () => {
        if (currentLangCode && selectedCountry) {
            router.push(asPath, undefined, {
                locale: `${selectedCountry.toLowerCase()}/${currentLangCode}`
            })
        }
    }

    if (!isVisible) {
        return null
    }

    return (
        <div
            className={cn(
                s['geolocation-banner'],
                'bg-dark-grey-1',
                'geolocation-banner-wrapper'
            )}
            ref={geolocationBannerRef}
        >
            <Container size={ContainerSize.MEDIUM}>
                <div
                    className={cn(
                        'flex item-center justify-between md-max:flex-col gap-16px',
                        className,
                        {
                            [s['geolocation-banner--open']]: isVisible,
                            [s['geolocation-banner--up']]:
                                openDirection === 'up',
                            [s['geolocation-banner--icon-left']]:
                                arrowPosition === 'left'
                        }
                    )}
                    ref={dropdownRef}
                >
                    <div
                        className={cn(
                            s['geolocation-banner__label'],
                            'text-white'
                        )}
                    >
                        {t(
                            'Choose a different country or region if you want to see the content for your location and shop online.'
                        )}
                    </div>
                    <div
                        className={cn(
                            'flex items-start gap-16px md-max:gap-8px',
                            s['geolocation-banner__country']
                        )}
                    >
                        <Dropdown
                            title={title}
                            variant="primary"
                            // eslint-disable-next-line i18next/no-literal-string
                            buttonColor="dark"
                            // eslint-disable-next-line i18next/no-literal-string
                            dropdownVariant="dark"
                            onChange={(countryValue) =>
                                setSelectedCountry(countryValue)
                            }
                            options={sortedDropdownOptions}
                            asOverlay
                            className={cn(s['geolocation-banner__dropdown'])}
                        />
                        <div className={cn('flex items-center gap-16px')}>
                            <div className={cn('flex items-start')}>
                                <Button
                                    // eslint-disable-next-line i18next/no-literal-string
                                    variant="secondary"
                                    // eslint-disable-next-line i18next/no-literal-string
                                    color="light"
                                    className={cn(
                                        s['continue-button'],
                                        'flex items-start'
                                    )}
                                    onClick={onContinue}
                                    disabled={!selectedCountry}
                                    label={t('Continue')}
                                >
                                    {t('Continue')}
                                </Button>
                            </div>
                            <button
                                className={cn(
                                    s['geolocation-banner__icon'],
                                    'flex items-center justify-center'
                                )}
                                onClick={onClose}
                            >
                                <CloseIcon className={cn('text-white')} />
                            </button>
                        </div>
                    </div>
                </div>
            </Container>
        </div>
    )
}
