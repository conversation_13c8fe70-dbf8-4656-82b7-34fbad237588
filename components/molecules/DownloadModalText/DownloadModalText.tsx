import s from './DownloadModalText.module.scss'
import cn from 'classnames'
import React, { FC } from 'react'
import unescape from 'lodash.unescape'

export enum HorizontalAlignmentEnum {
    LEFT = 'left',
    CENTER = 'center',
    RIGHT = 'right'
}

export enum VerticalAlignmentEnum {
    TOP = 'top',
    CENTER = 'center',
    BOTTOM = 'bottom'
}

export interface DownloadModalTextProps {
    headline?: string
    bodyCopy?: string
}

export const DownloadModalText: FC<DownloadModalTextProps> = ({
    headline,
    bodyCopy
}) => (
    <div className={cn(s['download-modal-text'], 'col-span-12 text-center')}>
        {headline && (
            <h5 className={cn(s['download-modal-text__headline'])}>
                {headline}
            </h5>
        )}
        {bodyCopy && (
            <div
                className={cn(
                    s['download-modal-text__body'],
                    'body-copy',
                    'whitespace-pre-line',
                    'rich-text'
                )}
                dangerouslySetInnerHTML={{ __html: unescape(bodyCopy) }}
            />
        )}
    </div>
)

export default DownloadModalText
