.compatible-products {
    @screen md {
        margin-top: 16px ;
    }
    &__compatible-text {
        font-size: 16px;
        @screen md {
            margin-bottom: 13px;
        }
    }
    &__text-alignment-left {
        align-items: flex-start;
    }
    &__text-alignment-center {
        align-items: center;
    }
    &__text-alignment-right {
        align-items: flex-end;
    }
    
    &__button {
        border: none !important;
        @screen md-max {
            margin-top: 9px;
        }
    }
    & &__swiper-slide {
        width: auto;
        button,
        a{
            color: var(--mid-grey-2);
            background: var(--primitives-gray-gray-20, #EAEAEA);
            @media only screen and (min-width:767px) {
                min-height: 3.3rem;
            }
        }
    }
    &__btn-wrap-driver {
        a{
            border: 1px solid var(--content-blue) !important;
        }
    }

    &__btn-wrap{
        display: flex;
        flex-direction: row;
        justify-content: center;
        a{
            display: flex;
            justify-content: center;
            padding: 8px 20px;
            width: auto !important;
            border: none;
        }
    }
    &_button{
        button,
        a{
            color: var(--mid-grey-2)!important;
            background: var(--primitives-gray-gray-20, #EAEAEA)!important;
            @media only screen and (min-width:767px) {
                min-height: 30px;
            }
        }
    }

    & &__swiper-btn-pre,
    & &__swiper-btn-next {
        transform: none;
        background: #fff;
        position: relative;
        top: 50%;
        transform: translateY(-50%);
        button {
            height: 42px;
            width: 42px;
            &:focus{
                outline: none;
            }
        }
    }
    & &__swiper-btn-next {
        position: absolute;
        &::after{
            position: absolute;
            right: 0;
            content: "";
            display: block;
            background: linear-gradient(to left, #fff 20%, transparent 100%);
            height: 42px;
            width: 42px;
            top: 50%;
            transform: translate(-85%, -50%);
        }
    }
    & &__swiper-btn-pre{
        position: absolute;
        &::after{
            position: absolute;
            left: 0;
            content: "";
            display: block;
            background: linear-gradient(to right, #fff 20%, transparent 100%);
            height: 42px;
            width: 42px;
            top: 50%;
            transform: translate(85%, -50%);
        }
    }

    & &__swiper-btn-pre {
        left: 0;
    }
    & &__swiper-btn-next {
        right: 0;
    }

    &__feature-img {
        max-width: 36px;
    }

    &__downloads-button {
        width: auto;
        justify-content: center !important;
    }
}

.card-downloads {
    &__thank-you {
        @apply text-center m-auto w-full pt-32px;
        @apply whitespace-pre-wrap;
        max-width: 550px;
        border-top: 1px solid black;
    }
}
