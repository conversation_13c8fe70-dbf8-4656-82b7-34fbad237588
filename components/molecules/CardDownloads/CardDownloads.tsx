/* eslint-disable i18next/no-literal-string */
import { Icon } from '@components/atoms/Icon/Icon'
import { Button } from '@components/molecules/Button/Button'
import { Link, LinkResponse } from '@components/molecules/Link/Link'
import {
    DownloadModal,
    useDownloadModal
} from '@components/organisms/DownloadModal/DownloadModal'
import { SwiperSliderTheme } from '@components/organisms/Slider/SwiperSlider'
import CardList, {
    CardListProps
} from '@components/templates/CardList/CardList'
import { FeatureProps } from '@components/templates/FeatureList/FeatureList'
import useSMSRegion from '@config/hooks/useSMSRegion'
import Image from '@corsairitshopify/corsair-image'
import { pushToDataLayer } from '@corsairitshopify/pylot-gtm/src/utils'
import { getDownloadSoftwareEvent } from '@lib/gtm/downloadSoftware'
import { getNewsletterSignupEvent } from '@lib/gtm/newsletterSignup'
import { getSmsSignupEvent } from '@lib/gtm/smsSignup'
import { useMobile } from '@pylot-data/hooks/use-mobile'
import { default as classNames, default as cn } from 'classnames'
import { nanoid } from 'nanoid'
import { useTranslation } from 'next-i18next'
import dynamic from 'next/dynamic'
import { FC, useEffect, useRef, useState } from 'react'
import { A11y, Navigation, SwiperOptions } from 'swiper'
import { SwiperSlide } from 'swiper/react'
import s from './CardDownloads.module.scss'

const SwiperSlider = dynamic(
    () => import('@components/organisms/Slider/SwiperSlider'),
    {
        ssr: false
    }
)

export interface CardDownloadsTextProps {
    system?: 'win' | 'mac'
    compatibleText: string
    compatibleActions?: LinkResponse[]
    additionalLinks?: FeatureProps[]
    downloadAction?: LinkResponse
    modalHeadline?: JSX.Element
    modalContent?: CardListProps
    disclaimerText?: string
    extraLinks?: LinkResponse[]
    requirement?: string
    downloadBtnArialabelledby?: string
}

export const CardDownloadsText: FC<CardDownloadsTextProps> = ({
    compatibleText,
    compatibleActions,
    additionalLinks,
    downloadAction,
    modalHeadline,
    modalContent,
    disclaimerText,
    extraLinks,
    requirement,
    system,
    downloadBtnArialabelledby
}) => {
    const { t } = useTranslation(['common'])
    const modalState = useDownloadModal()
    const availableSMSRegion = useSMSRegion()
    const linkTitle = downloadAction?.linkTitle ?? t('Download')
    const [windowWidth, setWindowWidth] = useState<number | undefined>(
        undefined
    )
    const [shareAvailable, setShareAvailable] = useState<boolean>(false)
    const describeTextId = nanoid()
    const additonalDescribedTextId = nanoid()
    const compatibleActionsTextAlignment =
        additionalLinks?.[0]?.link?.textAlignment
    const downloadLinkRef = useRef<HTMLAnchorElement>(null)
    useEffect(() => {
        const handleResize = () => {
            setWindowWidth(window.innerWidth)
        }

        handleResize()

        window.addEventListener('resize', handleResize)
        setShareAvailable(navigator.share !== undefined)

        return () => {
            window.removeEventListener('resize', handleResize)
        }
    }, [])

    // const isMobile = windowWidth && windowWidth <= BREAKPOINT_MOBILE_MAX
    const { isMobile } = useMobile()
    const buttonText =
        isMobile && shareAvailable ? 'Share download link' : linkTitle

    const sliderSettings: SwiperOptions = {
        slidesPerView: 'auto',
        centeredSlides: false,
        centerInsufficientSlides: true,
        centeredSlidesBounds: false,
        spaceBetween: 8,
        loop: false,
        allowTouchMove: true,
        simulateTouch: true,
        modules: [A11y, Navigation]
    }
    const onDownloadModalRegisterNewsLetter = (
        email: string,
        phone: string | null
    ) => {
        const registrationLocation = 'DownloadModal'

        const filteredAndMappedChildren = modalHeadline?.props?.children
            .filter((item: { type: string }) => item && item.type === 'h5')
            .map((item: { props: { children: any } }) => ({
                children: item.props?.children
            }))

        const softwareTitle =
            filteredAndMappedChildren.length > 0
                ? filteredAndMappedChildren[0].children
                : null
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const softwareSystem = system

        const softwareVersionArr = disclaimerText?.match(/[0-9]+\.[0-9]+/i)

        const softwareVersion = softwareVersionArr
            ? softwareVersionArr[0]
            : disclaimerText

        const newsletterSignupEvent = getNewsletterSignupEvent({
            customerEmail: email,
            registrationLocation,
            marketingConsent: true,
            softwareTitle,
            softwareOS: softwareSystem,
            softwareVersion
        })

        pushToDataLayer(newsletterSignupEvent)

        if (phone && availableSMSRegion) {
            const country = availableSMSRegion?.countryCode
            const smsSignupEvent = getSmsSignupEvent(
                email,
                phone,
                registrationLocation
            )
            pushToDataLayer(smsSignupEvent)
        }
    }

    const onDownloadModalStartDownload = (
        userAction:
            | 'Signup Newsletter'
            | 'Skip Newsletter Signup'
            | 'Close Modal'
    ) => {
        if (isMobile && shareAvailable) {
            navigator
                .share({
                    url: downloadAction!.linkUrl
                })
                .then(() => {
                    console.log('Thanks for sharing!')
                })
                .catch(console.error)
        } else {
            if (downloadAction && downloadAction.linkUrl) {
                downloadLinkRef.current?.click()
            }
        }

        const softwareVersionArr = disclaimerText?.match(/[0-9]+\.[0-9]+/i)

        const softwareVersion = softwareVersionArr
            ? softwareVersionArr[0]
            : disclaimerText

        const filteredAndMappedChildren = modalHeadline?.props?.children
            .filter((item: { type: string }) => item && item.type === 'h5')
            .map((item: { props: { children: any } }) => ({
                children: item.props?.children
            }))

        const softwareTitle =
            filteredAndMappedChildren.length > 0
                ? filteredAndMappedChildren[0].children
                : null
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const softwareSystem = system

        const event = getDownloadSoftwareEvent(
            userAction,
            downloadAction!.linkUrl,
            softwareTitle,
            softwareVersion,
            softwareSystem
        )
        pushToDataLayer(event)
    }

    const requirementAdditionalLink = additionalLinks
        ? additionalLinks.find((feature) => feature.label === 'requirement')
        : null
    return (
        <div
            className={classNames(
                'block items-center flex-col flex-grow',
                s['compatible-products']
            )}
        >
            <div className="flex-grow" />
            {(downloadAction || extraLinks) && (
                <div
                    className={classNames(
                        'flex gap-12px flex-wrap',
                        {
                            [s['compatible-products__btn-wrap-driver']]:
                                extraLinks?.[0]?.linkTitle === 'Driver'
                        },
                        s['compatible-products__btn-wrap']
                    )}
                >
                    {downloadAction && (
                        <>
                            <a
                                href={downloadAction.linkUrl}
                                ref={downloadLinkRef}
                                download
                                style={{ display: 'none' }}
                            >
                                {t('Download')}
                            </a>
                            <Button
                                variant="primary"
                                href={
                                    downloadAction.linkTitle === t('Learn more')
                                        ? downloadAction.linkUrl
                                        : ''
                                }
                                onClick={() => {
                                    if (
                                        downloadAction.linkTitle !==
                                        t('Learn more')
                                    ) {
                                        modalState.open()
                                    }
                                }}
                                download
                                aria-label={buttonText ?? t('Download')}
                                describedBy={downloadBtnArialabelledby}
                                className={cn(
                                    s['compatible-products__downloads-button'],
                                    {
                                        [s[
                                            'compatible-products__downloads-button-externalLinks'
                                        ]]:
                                            extraLinks?.[0]?.linkTitle ===
                                            'Setup Guide'
                                    }
                                )}
                            >
                                {isMobile && shareAvailable ? (
                                    // eslint-disable-next-line i18next/no-literal-string
                                    <Icon name="share" />
                                ) : (
                                    buttonText?.toLowerCase() ===
                                        'download' && (
                                        // eslint-disable-next-line i18next/no-literal-string
                                        <Icon name="download" />
                                    )
                                )}
                                {buttonText ?? t('Download')}
                            </Button>
                            <DownloadModal
                                state={modalState}
                                newsletterPage={
                                    <>
                                        <h3 className="mb-4 text-center">
                                            {t(
                                                'downloadModal|newsletter_title'
                                            )}
                                        </h3>
                                        <p className="text-center">
                                            {!isMobile
                                                ? availableSMSRegion
                                                    ? t(
                                                          'downloadModal|newsletter_description_us'
                                                      )
                                                    : t(
                                                          'downloadModal|newsletter_description'
                                                      )
                                                : availableSMSRegion
                                                ? t(
                                                      'downloadModal|newsletter_description_mobile_us'
                                                  )
                                                : t(
                                                      'downloadModal|newsletter_description_mobile_non-us'
                                                  )}
                                        </p>
                                    </>
                                }
                                downloadPage={modalHeadline}
                                registeredContent={(withPhone) => (
                                    <div
                                        className={
                                            s['card-downloads__thank-you']
                                        }
                                    >
                                        {availableSMSRegion &&
                                            (!withPhone
                                                ? t(
                                                      'downloadModal|newsletter_thank_you_us'
                                                  )
                                                : `${t(
                                                      'downloadModal|newsletter_thank_you_us'
                                                  )}\n${t(
                                                      'downloadModal|newsletter_thank_you_phone_us'
                                                  )}`)}
                                        {!availableSMSRegion &&
                                            t(
                                                'downloadModal|newsletter_thank_you'
                                            )}
                                    </div>
                                )}
                                bottomContent={
                                    modalContent && (
                                        <CardList
                                            content={Object.assign(
                                                {},
                                                modalContent,
                                                {
                                                    variant: 'download-cards'
                                                }
                                            )}
                                        />
                                    )
                                }
                                onRegisterNewsletter={
                                    onDownloadModalRegisterNewsLetter
                                }
                                onStartDownload={onDownloadModalStartDownload}
                            />
                        </>
                    )}
                    {extraLinks &&
                        extraLinks.map((link) => {
                            const color = link.styleColor ?? 'blue'
                            const iconAlignment =
                                link.style === 'tertiary' ? 'right' : 'left'
                            const hasIcon =
                                link.icon ??
                                (link.style === 'secondary' ||
                                    link.style === 'tertiary')
                            const iconName =
                                link.icon ??
                                (link.style === 'secondary'
                                    ? 'download'
                                    : 'chevronRight')
                            return (
                                <Button
                                    key={link.linkUrl}
                                    variant={link.style}
                                    color={color}
                                    iconAlignment={iconAlignment}
                                    href={link.linkUrl}
                                    download={link.style === 'secondary'}
                                    newTab={link.newTab}
                                    label={link.linkTitle}
                                >
                                    {hasIcon && (
                                        <Icon
                                            name={iconName}
                                            aria-hidden="true"
                                        />
                                    )}
                                    {link.linkTitle}
                                </Button>
                            )
                        })}
                </div>
            )}
            {compatibleActions && compatibleActions.length > 0 && (
                <div className="my-16px flex flex-col items-center w-full">
                    <p
                        className={cn(
                            'self-start text-small-copy font-univers55Roman capitalize',
                            s['compatible-products__compatible-text']
                        )}
                    >
                        {compatibleText}
                    </p>
                    {compatibleActions.length > 3 ? (
                        <SwiperSlider
                            loop={false}
                            className="w-full"
                            settings={sliderSettings}
                            navigationTheme={SwiperSliderTheme.LIGHT}
                            buttonClassPre={
                                s['compatible-products__swiper-btn-pre']
                            }
                            buttonClassNext={
                                s['compatible-products__swiper-btn-next']
                            }
                            ariaLabel="compatible product carousel"
                            arrowBtnTabindex={-1}
                        >
                            <p className="sr-only">
                                This is a carousel with auto-rotating slides.
                                Activate any of the buttons to disable rotation.
                                Use Next and Previous buttons to navigate, or
                                jump to a slide with the slide dots.
                            </p>
                            {compatibleActions.map((action) => (
                                <SwiperSlide
                                    key={action.linkTitle}
                                    className={
                                        s['compatible-products__swiper-slide']
                                    }
                                >
                                    <Button
                                        variant="compatible-product"
                                        disabled={!action.linkUrl}
                                        href={action.linkUrl}
                                        className={cn(
                                            'box-border',
                                            s['compatible-products__button']
                                        )}
                                        aria-label={action.linkTitle}
                                    >
                                        {action.linkTitle}
                                    </Button>
                                </SwiperSlide>
                            ))}
                        </SwiperSlider>
                    ) : (
                        <div
                            className={classNames(
                                'flex flex-wrap w-full',
                                s['compatible-products_button']
                            )}
                            role="region"
                            aria-label="compatible product carousel"
                        >
                            <p className="sr-only">
                                This a carousel with compatible products.
                                Activate any of the buttons to discover the
                                product. Use Next and Previous buttons to
                                navigate between the compatible products
                            </p>
                            {compatibleActions.map((action) => (
                                <div key={action.linkTitle}>
                                    <Button
                                        variant="compatible-product"
                                        disabled={!action.linkUrl}
                                        href={action.linkUrl}
                                        className={cn(
                                            'box-border mr-4',
                                            s['compatible-products__button']
                                        )}
                                        aria-label={action.linkTitle}
                                    >
                                        {action.linkTitle}
                                    </Button>
                                </div>
                            ))}
                        </div>
                    )}
                </div>
            )}
            {(additionalLinks || requirement) && (
                <div
                    className={cn(
                        'mt-16px mb-12px flex flex-col gap-4px items-start',
                        s[
                            `compatible-products__text-alignment-${compatibleActionsTextAlignment}`
                        ]
                    )}
                >
                    {requirement && (
                        <div className="flex items-start flex-wrap md-max:justify-center md-max:text-center">
                            <span
                                className="text-xs-copy mr-1"
                                id={describeTextId}
                            >
                                {t(
                                    'cardDownloads|Requires {{requirement}} or later.',
                                    {
                                        requirement: requirement
                                    }
                                )}
                            </span>
                            {/* There is a version history link */}
                            {additionalLinks && requirementAdditionalLink && (
                                <>
                                    {requirementAdditionalLink.link && (
                                        <Link
                                            aria-describedby={describeTextId}
                                            link={
                                                requirementAdditionalLink.link
                                            }
                                            className="font-bold underline text-xs-copy"
                                            aria-label={`${
                                                requirementAdditionalLink.link
                                                    .linkTitle ?? t('Download')
                                            } - ${t(
                                                'ada|Opens in the current Tab'
                                            )}`}
                                        >
                                            {requirementAdditionalLink.link
                                                .linkTitle ?? t('Download')}
                                        </Link>
                                    )}
                                    {requirementAdditionalLink.image && (
                                        <div className="flex items-center justify-center ml-4">
                                            <Image
                                                src={
                                                    requirementAdditionalLink
                                                        .image.file.url
                                                }
                                                alt={
                                                    requirementAdditionalLink
                                                        .image.description || ''
                                                }
                                                width={
                                                    requirementAdditionalLink
                                                        .image.file.details
                                                        .image.width
                                                }
                                                height={
                                                    requirementAdditionalLink
                                                        .image.file.details
                                                        .image.height
                                                }
                                            />
                                        </div>
                                    )}
                                </>
                            )}
                        </div>
                    )}
                    {additionalLinks &&
                        additionalLinks.map((feature) => {
                            // requirement was already rendered before
                            if (requirement && feature.label === 'requirement')
                                return
                            // all other links
                            return (
                                <div
                                    className="flex items-start flex-wrap md-max:justify-start md-max:text-center"
                                    key={`link-${feature.text}-${feature.link?.linkUrl}`}
                                >
                                    {feature.text && (
                                        <span
                                            id={additonalDescribedTextId}
                                            className="text-xs-copy mr-1"
                                        >
                                            {feature.text}
                                        </span>
                                    )}
                                    {feature.link && (
                                        <Link
                                            aria-describedby={
                                                additonalDescribedTextId
                                            }
                                            link={feature.link}
                                            className="font-bold underline text-xs-copy"
                                            aria-label={`${
                                                feature.link.linkTitle ??
                                                t('Download')
                                            } - ${t(
                                                'ada|Opens in the current Tab'
                                            )}`}
                                        >
                                            {feature.link.linkTitle ??
                                                t('Download')}
                                        </Link>
                                    )}
                                    {feature.image && (
                                        <div
                                            className={cn(
                                                'flex items-center justify-center ml-4',
                                                s[
                                                    'compatible-products__feature-img'
                                                ]
                                            )}
                                        >
                                            <Image
                                                src={feature.image.file.url}
                                                alt={
                                                    feature.image.description ||
                                                    ''
                                                }
                                                width={
                                                    feature.image.file.details
                                                        .image.width
                                                }
                                                height={
                                                    feature.image.file.details
                                                        .image.height
                                                }
                                            />
                                        </div>
                                    )}
                                </div>
                            )
                        })}
                </div>
            )}
        </div>
    )
}

export default CardDownloadsText
