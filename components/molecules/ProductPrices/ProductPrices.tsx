import {
    PriceNode,
    ProductPriceType
} from '@components/common/ProductPrice/ProductPrice'
import { Money, PriceRange } from '@pylot-data/pylotschema'
import { formatPrice } from '@corsairitshopify/pylot-price'
import React, { FC } from 'react'
import { useRouter } from 'next/router'
import cn from 'classnames'

type ProductsPriceType = Omit<ProductPriceType, 'priceRange'> & {
    pricesRange: PriceRange[]
    theme?: 'dark' | 'light' | 'blue' | 'red' | 'neo'
}

const getProductPrices = (prices: PriceRange[], locale: string) => {
    const regularPrice: Money = {
        value: 0
    }

    const finalPrice: Money = {
        value: 0
    }

    prices.map((price) => {
        const {
            minimum_price: { final_price, regular_price }
        } = price

        regularPrice.value = regularPrice.value! + (regular_price.value ?? 0)
        finalPrice.value = finalPrice.value! + (final_price.value ?? 0)
    })

    return {
        subtotal: formatPrice(regularPrice, locale),
        total: formatPrice(finalPrice, locale),
        subtotalValue: regularPrice.value as number,
        totalValue: finalPrice.value as number
    }
}

export const ProductsPrice: FC<ProductsPriceType> = ({
    pricesRange,
    className,
    isGiftCard,
    showCurrencyCode = false,
    splitSymbol = false,
    size = 'big',
    overrideSubTotal = false,
    altSubTotal = '',
    theme
}) => {
    const currencyCode = showCurrencyCode
        ? pricesRange[0]?.minimum_price.final_price.currency
        : null

    const { locale = 'en' } = useRouter()

    const { total, subtotal, subtotalValue, totalValue } = getProductPrices(
        pricesRange,
        locale
    )

    if (isGiftCard || !totalValue) return null

    return (
        <div className={cn(['product-price', className])}>
            <PriceNode
                price={total}
                currencyCode={currencyCode}
                splitSymbol={splitSymbol}
                subPrice={subtotal && subtotal !== total ? subtotal : ''}
                size={size}
                showSubtotalText
                subtotalValue={subtotalValue}
                totalValue={totalValue}
                theme={theme}
                variant="below"
            />
        </div>
    )
}
