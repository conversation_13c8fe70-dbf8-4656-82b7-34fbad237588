import React, { FC, ReactNode, useEffect, useRef, useState } from 'react'
import cn from 'classnames'
import s from './TooltipWrapper.module.scss'

export type TooltipWrapperProps = {
    /** the element this tooltip should apply to */
    children: ReactNode
    /** the tooltip that should be shown */
    tooltip: ReactNode
    /** an optional offset to the tooltip, defaults to 8px */
    offset?: number
    /** wether the element this tooltip surrounds is itself interactive (eg. a button). Otherwise set this property to false. Defaults to true */
    innerElementFocusable?: boolean
    /** the preferred opening direction of this tooltip, defaults to top */
    preferredDirection?: 'top' | 'bottom'
    /** if the inner element is interactive an ariaUis should be provided to make screen-reader recognize the tooltip and read it out */
    ariaUid?: string
    className?: string
}

type TooltipPosition = {
    top: string
    bottom: string
    marginBottom: string
    marginTop: string
}

export const TooltipWrapper: FC<TooltipWrapperProps> = ({
    children,
    tooltip,
    offset = 8,
    innerElementFocusable = true,
    preferredDirection = 'top',
    ariaUid,
    className
}) => {
    const containerRef = useRef<HTMLDivElement>(null)
    const tooltipRef = useRef<HTMLDivElement>(null)
    const [tooltipPosition, setTooltipPosition] = useState<TooltipPosition>({
        bottom: 'unset',
        top: 'unset',
        marginBottom: 'unset',
        marginTop: 'unset'
    })
    const [tooltipShown, setTooltipShown] = useState<boolean>(false)

    /** position the tooltip on top of the reference control */
    const _positionAtTop = (offset: number, position: number) => {
        setTooltipPosition({
            marginBottom: `${offset}px`,
            bottom: `${position}px`,
            top: 'unset',
            marginTop: 'unset'
        })
    }

    /** position the tooltip below of the reference control */
    const _positionAtBottom = (offset: number) => {
        setTooltipPosition({
            marginTop: `${offset}px`,
            top: '100%',
            bottom: 'unset',
            marginBottom: 'unset'
        })
    }

    /** position the tooltip */
    const positionTooltip = () => {
        if (!tooltipRef.current || !containerRef.current) {
            return
        }
        // tooltipRef.current.style.display = 'block'
        if (preferredDirection === 'top') {
            _positionAtTop(offset, containerRef.current.clientHeight)
        } else {
            _positionAtBottom(offset)
        }
        // tooltipRef.current.style.display = ''
    }

    const hideTooltip = () => {
        setTooltipShown(false)
    }

    useEffect(() => {
        if (!tooltipRef.current || !containerRef.current) {
            return
        }
        const rect = tooltipRef.current.getBoundingClientRect()
        if (preferredDirection === 'top') {
            if (rect.top < 0) {
                _positionAtBottom(offset)
            }
        } else {
            if (rect.bottom > window.innerHeight) {
                _positionAtTop(offset, containerRef.current.clientHeight)
            }
        }
        setTooltipShown(true)
    }, [preferredDirection, tooltipPosition, offset])

    return (
        <div
            // the following element has a role which is recognized by screen readers
            // eslint-disable-next-line jsx-a11y/no-noninteractive-tabindex
            tabIndex={innerElementFocusable ? -1 : 0}
            role="tooltip"
            aria-labelledby={ariaUid}
            ref={containerRef}
            className={cn(['relative inline-block', s['container']], className)}
            onMouseEnter={positionTooltip}
            onFocus={positionTooltip}
            onTouchStart={positionTooltip}
            onMouseLeave={hideTooltip}
            onBlur={hideTooltip}
            onTouchEnd={hideTooltip}
        >
            {children}
            <div
                id={ariaUid}
                ref={tooltipRef}
                style={tooltipPosition}
                className={cn([
                    'absolute hidden left-0',
                    s['tooltip'],
                    tooltipShown ? s['tooltip--show'] : '',
                    s['tooltip-default']
                ])}
            >
                {tooltip}
            </div>
        </div>
    )
}
