import React, { FC } from 'react'
import s from './BundleConfiguratorFeaturesItem.module.scss'
import cn from 'classnames'
import { FeatureProps } from '@components/templates/FeatureList/FeatureList'
import { FeatureIconTextProp } from '@components/molecules/FeatureIconText/FeatureIconText'
import Image from '@corsairitshopify/corsair-image'
import { useBundleConfiguratorContext } from '@components/molecules/BundleConfigurator/context/BundleConfiguratorContext'

export interface BundleConfiguratorFeaturesListProps {
    feature?: FeatureProps
}

export const BundleConfiguratorFeaturesItem: FC<BundleConfiguratorFeaturesListProps> = (
    props
) => {
    const { feature } = props

    const { updateConfigurator } = useBundleConfiguratorContext()

    const setConfigurator = (configurator: string) => {
        if (configurator) {
            updateConfigurator(configurator)
        }
    }

    return (
        // eslint-disable-next-line jsx-a11y/click-events-have-key-events
        <div
            className={cn(
                'flex flex-col gap-4 text-center',
                s['bundle-configurator-feature-item']
            )}
            role="button"
            tabIndex={0}
            onClick={() => setConfigurator(feature?.children[0]?.variant)}
        >
            {feature?.image && (
                <Image
                    src={feature?.image?.file?.url}
                    alt={feature?.image?.description || ''}
                    width={feature.image?.file.details.image.width}
                    height={feature.image?.file.details.image.height}
                    objectFit="cover"
                    className={cn(
                        s['bundle-configurator-feature-item__image'],
                        'h-full w-full'
                    )}
                />
            )}
            {feature?.text && (
                <p
                    className={cn(
                        s['bundle-configurator-feature-item__text'],
                        'break-all'
                    )}
                >
                    {feature.text}
                </p>
            )}
        </div>
    )
}
