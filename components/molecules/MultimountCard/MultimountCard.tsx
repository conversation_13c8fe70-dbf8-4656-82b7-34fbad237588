import { CloudinaryMedia } from '@components/common/CloudinaryMedia/Cloudinary'
import ElgatoImage from '@components/common/ElgatoImage'
import { LinkResponse } from '@components/molecules/Link/Link'
import { ImageType } from '@pylot-data/hooks/contentful/use-content-json'
import cn from 'classnames'
import { useTranslation } from 'next-i18next'
import NextLink from 'next/link'
import { FC } from 'react'
import s from './MultimountCard.module.scss'

export type MultimountCardProps = {
    cloudinaryImage: CloudinaryMedia[]
    label?: string
    image?: ImageType
    link?: LinkResponse
    onClick?: () => void
    className?: string
    variant?: 'default' | 'small' | 'overlay'
    bgClass?: 'bg-white-smoke' | 'bg-white'
}

export const MultimountCard: FC<MultimountCardProps> = (props) => {
    const {
        label,
        className,
        variant = 'default',
        link,
        onClick,
        bgClass = 'bg-white',
        cloudinaryImage
    } = props
    const { t } = useTranslation(['common'])
    const ButtonComponent = link && link.linkUrl ? 'a' : 'div'
    let textClass = 'body-copy'
    if (variant === 'small') {
        textClass = 'small-copy'
    } else if (variant === 'overlay') {
        textClass = 'xs-copy'
    }
    const rel: string | undefined =
        link && link.newTab ? 'noreferrer' : undefined
    const isExternal = link?.newTab

    const cardInner = (
        <>
            {cloudinaryImage && cloudinaryImage?.[0]?.secure_url && (
                <div className={cn(s['multimount-card__image'], bgClass)}>
                    <ElgatoImage
                        src={cloudinaryImage?.[0]?.secure_url}
                        alt={cloudinaryImage?.[0]?.context?.custom?.alt}
                        layout="fill"
                        objectFit="cover"
                        className="absolute"
                    />
                </div>
            )}
            {label && (
                <div className={cn(s['multimount-card__text'], textClass)}>
                    {label}
                </div>
            )}
        </>
    )
    if (onClick || isExternal || !link) {
        return (
            <ButtonComponent
                className={cn(
                    s['multimount-card'],
                    s[`multimount-card--${variant}`],
                    className
                )}
                href={link?.linkUrl}
                title={link?.linkTitle}
                target={link ? (link?.newTab ? '_blank' : '_self') : undefined}
                rel={rel}
                role={onClick ? 'button' : undefined}
                onClick={onClick}
                aria-label={link?.linkTitle}
            >
                {cardInner}
            </ButtonComponent>
        )
    }

    return (
        <NextLink href={link.linkUrl} passHref>
            <a
                className={cn(
                    s['multimount-card'],
                    s[`multimount-card--${variant}`],
                    className
                )}
                href={link.linkUrl}
                title={link.linkTitle}
                aria-label={`${label} - ${t('ada|Opens in the current Tab')}`}
            >
                {cardInner}
            </a>
        </NextLink>
    )
}
