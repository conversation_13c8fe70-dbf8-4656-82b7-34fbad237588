import React, { FC, ReactNode, useMemo } from 'react'
import NextLink from 'next/link'
import { useLayoutContext } from '@components/layouts/MainLayout/LayoutContext'
import { useLinkContext } from '@components/layouts/MainLayout/LinkContext'
import { useTranslation } from 'next-i18next'
import { ContentOverlayProps } from '@components/templates/ContentOverlay/ContentOverlay'
import { DownloadDataContent } from '@components/templates/ProductSelector/Downloads'

type DownloadEntry = {
    downloadURL: string
    enabled: boolean
    fileURL: string
    minimumOS: string
    version: string
}
export interface LinkResponse {
    id?: string
    linkTitle: string
    linkUrl: string
    newTab?: boolean
    icon?: string
    iconAlignment?: 'left' | 'right'
    style?:
        | 'link'
        | 'primary'
        | 'secondary'
        | 'tertiary'
        | 'tertiary-underlined'
        | 'apple-app-store'
        | 'google-play-store'
        | 'mac-os-store'
        | 'windows-store'
        | 'invisible'
        | 'button-with-label'
    styleColor?: 'blue' | 'light' | 'dark'
    logo?: string
    download?: boolean
    copyCode?: boolean
    meta?: {
        contentType: 'moleculeLink'
    }
    ariaLabel?: string
    eventTracking?: []
    regions?: [
        {
            identifier: string
            name: string
        }
    ]
    textAlignment?: 'left' | 'center' | 'right'
    overlay?: ContentOverlayProps
    downloadableContent?: Record<string, DownloadEntry>
    downloadDataContent?: DownloadDataContent
}

export type LinkProps = {
    link: LinkResponse
    children?: ReactNode
    className?: string
    anchorOffsetTop?: number
    onClick?: () => void
    role?: string
}

export const Link: FC<LinkProps> = (props) => {
    const {
        link,
        children,
        className,
        anchorOffsetTop,
        onClick,
        ...rest
    } = props
    const rel: string = link.newTab ? 'noreferrer' : ''!
    const { navStickyNav } = useLayoutContext()
    const { setIdFromLastClickedAnchorLink } = useLinkContext()
    const { t } = useTranslation(['common'])
    const isExternal = link.newTab

    const isAnchorLink = useMemo(() => {
        const hash = link.linkUrl.split('#')
        if (hash.length && hash[1]) {
            return true
        }
        return false
    }, [link.linkUrl])

    if (isExternal) {
        return (
            // eslint-disable-next-line react/jsx-no-target-blank
            <a
                className={`${className} elgato-links`}
                href={link.linkUrl}
                title={link.linkTitle}
                target={link.newTab ? '_blank' : '_self'}
                rel={rel}
                {...rest}
            >
                {children ? children : link.linkTitle}
            </a>
        )
    }

    if (isAnchorLink) {
        return (
            <NextLink href={link.linkUrl} {...rest}>
                {/* eslint-disable-next-line jsx-a11y/anchor-is-valid,jsx-a11y/click-events-have-key-events,jsx-a11y/no-static-element-interactions */}
                <a
                    className={`${className} elgato-links`}
                    title={link.linkTitle}
                    rel={rel}
                    aria-label={`${link.linkTitle} - ${t(
                        'ada|Scroll to content'
                    )}`}
                    onClick={(e) => {
                        const hash = link.linkUrl.split('#')
                        const idFromAnchorLink = hash[1]
                        const foundAnchorLink = document.getElementById(
                            idFromAnchorLink
                        )
                        if (foundAnchorLink) {
                            e.preventDefault()
                            e.stopPropagation()

                            setIdFromLastClickedAnchorLink(idFromAnchorLink)

                            let offsetTop =
                                foundAnchorLink.getBoundingClientRect().top +
                                window.pageYOffset
                            if (navStickyNav) {
                                offsetTop = offsetTop - 88
                            }
                            const resizeObserver = new ResizeObserver(() => {
                                let newOffsetTop =
                                    foundAnchorLink.getBoundingClientRect()
                                        .top + window.pageYOffset
                                if (navStickyNav && !anchorOffsetTop) {
                                    newOffsetTop = newOffsetTop - 88
                                }
                                if (anchorOffsetTop) {
                                    newOffsetTop =
                                        newOffsetTop - anchorOffsetTop
                                }
                                if (
                                    newOffsetTop.toFixed(0) !==
                                    offsetTop.toFixed(0)
                                ) {
                                    window.scrollTo({
                                        top: newOffsetTop,
                                        behavior: 'auto'
                                    })
                                }
                            })
                            onClick?.()
                            resizeObserver.observe(document.body)
                            window.onscroll = () => {
                                const rect = foundAnchorLink.getBoundingClientRect()
                                if (
                                    rect.top >= 0 &&
                                    rect.bottom <= window.innerHeight
                                ) {
                                    resizeObserver.disconnect()
                                }
                            }
                            window.scrollTo({
                                top: offsetTop,
                                behavior: 'auto'
                            })
                            // history.pushState(null, '', link.linkUrl)
                            return false
                        }
                    }}
                >
                    {children ? children : link.linkTitle}
                </a>
            </NextLink>
        )
    }
    return (
        <NextLink href={link.linkUrl} {...rest}>
            <a
                className={`${className} elgato-links`}
                title={link.linkTitle}
                rel={rel}
                {...rest}
            >
                {children ? children : link.linkTitle}
            </a>
        </NextLink>
    )
}
