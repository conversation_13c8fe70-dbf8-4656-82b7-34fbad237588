import { LinkResponse } from '@components/molecules/Link/Link'

export const convertToStoreLink = (link: LinkResponse): LinkResponse => {
    if (!link.icon && link.style) {
        switch (link.style) {
            case 'google-play-store':
                link.icon = 'playStore'
                break
            case 'apple-app-store':
                link.icon = 'appStore'
                break
            case 'mac-os-store':
                link.icon = 'macOS'
                break
            case 'windows-store':
                link.icon = 'windows'
                break
        }
    }
    return link
}
