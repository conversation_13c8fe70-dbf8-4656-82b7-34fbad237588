import s from './StoreShowroom.module.scss'
import React, { FC } from 'react'
import {
    Container,
    ContainerSize
} from '@components/organisms/Container/Container'
import { LinkListProps } from '@components/organisms/LinkList/LinkList'
import { MenuItemProps } from '@components/organisms/Header/Header'
import PrimaryText, {
    HeadlineStyleEnum,
    HorizontalAlignmentEnum
} from '@components/molecules/PrimaryText/PrimaryText'
import { StoreShowroomSlider } from '@components/templates/StoreShowroom/StoreShowroomSlider'

type StoreShowroomProps = Pick<LinkListProps, 'headline' | 'textPanel'> & {
    linkListItems: MenuItemProps[]
    subheader?: string
}

export const StoreShowroom: FC<StoreShowroomProps> = ({
    headline,
    subheader,
    linkListItems = []
}) => {
    return (
        <div className={s['store-showroom']}>
            <Container size={ContainerSize.LARGE}>
                <div className="flex flex-col gap-16px md:gap-24px body-copy text-charcoal">
                    {(headline || subheader) && (
                        <PrimaryText
                            headline={headline}
                            headlineTag={HeadlineStyleEnum.H1}
                            headlineStyle={HeadlineStyleEnum.H1}
                            bodyCopy={subheader}
                            textAlignment={HorizontalAlignmentEnum.LEFT}
                        />
                    )}
                </div>
            </Container>
            <StoreShowroomSlider items={linkListItems} />
        </div>
    )
}
