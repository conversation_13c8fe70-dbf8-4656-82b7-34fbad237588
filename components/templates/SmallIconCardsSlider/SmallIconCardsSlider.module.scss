.small-icon-cards {
    @apply mx-auto py-16;
        overflow: hidden;
    @screen md {
        @apply py-32;
    }
    &__slider {
        max-width: none;
        width: 100%;

        &--item {
            height: 100%;
            min-height: 235px;

            &__lottie {
                width: 72px;
                height: 72px;
            }

            &__pagination  {
            }
        }
    }

    &__text-wrapper {
        @apply mb-16;

        @screen md {
            @apply mb-32;
        }
    }

    &__popup {
        position: absolute;
        width: 100%;
        left: 150px;
        right: 0px;
        top: 40px;
        overflow: visible;
        height: -moz-fit-content;
        height: fit-content;
        @screen md {
            left: 285px;
        }
        @screen md-max {
            right: auto;
            left: 50%;
            transform: translate(-50%, -50%);
            top: 50%;
        }

        :global {
            .swiper {
                overflow: visible !important;

                .swiper-slide-active {
                    overflow: visible !important;
                }
            }
        }
        &-inner {
            &__headline {
            }

            &__external-link {
                margin-top: 24px;
                max-width: 180px;
            }
        }
    }

    :global {
        .swiper {
            overflow: visible;
            .swiper-wrapper {
                @apply w-auto;
                transition-timing-function: linear !important;

                .swiper-slide {
                    width: 230px;
                    border-radius: 12px;
                    transform: translateZ(1px);
                    /* fix for safari */
                    video {
                        transform: translateZ(1px);
                        border-radius: 12px;
                    }

                    @screen md {
                        width: 25%;
                    }
                }
            }
        }

        .swiper-button-disabled {
            @apply opacity-0 pointer-events-none;
        }

        .swiper-pagination {
            @screen md-max {
                display: none;
            }
        }
    }

    &__slide {
        &--with-popup {
            overflow: visible !important;
        }
    }
}
