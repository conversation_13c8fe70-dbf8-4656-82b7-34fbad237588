import React, { FC } from 'react'
import cn from 'classnames'
import s from '@components/templates/StreamDeckInteractionPanel/StreamDeckInteractionPanel.module.scss'
import Image from '@corsairitshopify/corsair-image/index'
import Video from '@components/molecules/Video/Video'
import Lottie from 'react-lottie-player'
import { StreamDeckInteractionPanelProps } from '@components/templates/StreamDeckInteractionPanel/StreamDeckInteractionPanel'
import { useTranslation } from 'next-i18next'
import { useAnimationAndVideosToggle } from '@components/common/AnimationAndVideosToggle/AnimationAndVideoContext'

export type StreamDeckInteractionPanelStreamDeckMk2Props = StreamDeckInteractionPanelProps & {
    activeInteractionImage: null | number
    audioRef?: any
    playLottieAnimation: boolean
    lottieAnimationData: any
    setPlayLottieAnimation: (play: boolean) => void
}
export const StreamDeckInteractionPanelStreamDeckMk2: FC<StreamDeckInteractionPanelStreamDeckMk2Props> = (
    props
) => {
    const loadingLazy = 'lazy'
    const {
        imageKey2,
        activeInteractionImage,
        videoKey3,
        videoKey4,
        audio,
        audioRef,
        playLottieAnimation,
        lottieAnimationData,
        setPlayLottieAnimation
    } = props
    const { t } = useTranslation()
    const { isAnimationStopped } = useAnimationAndVideosToggle()
    return (
        <>
            {imageKey2 && (
                <div
                    className={cn(
                        s['stream-deck-interaction-panel__key-media'],
                        s['stream-deck-interaction-panel__key-image--2'],
                        {
                            [s[
                                'stream-deck-interaction-panel__key-media--active'
                            ]]: activeInteractionImage === 1
                        }
                    )}
                >
                    <Image
                        src={imageKey2?.file?.url}
                        alt=""
                        width={179}
                        height={179}
                        loading={loadingLazy}
                    />
                </div>
            )}
            {videoKey3 && (
                <div
                    className={cn(
                        s['stream-deck-interaction-panel__key-media'],
                        s['stream-deck-interaction-panel__key-video--3'],
                        {
                            [s[
                                'stream-deck-interaction-panel__key-media--active'
                            ]]: activeInteractionImage === 2
                        }
                    )}
                >
                    <Video
                        video={videoKey3}
                        options={{
                            autoPlay: true,
                            preload: 'none',
                            muted: true,
                            loop: true
                        }}
                        hidden
                        play={activeInteractionImage === 2}
                    />
                </div>
            )}
            {videoKey4 && (
                <div
                    className={cn(
                        s['stream-deck-interaction-panel__key-media'],
                        s['stream-deck-interaction-panel__key-video--4'],
                        {
                            [s[
                                'stream-deck-interaction-panel__key-media--active'
                            ]]: activeInteractionImage === 3
                        }
                    )}
                >
                    <Video
                        video={videoKey4}
                        options={{
                            autoPlay: true,
                            preload: 'none',
                            muted: true,
                            loop: true
                        }}
                        hidden
                        play={activeInteractionImage === 3}
                    />
                </div>
            )}
            <Lottie
                play={isAnimationStopped ? false : playLottieAnimation}
                loop
                animationData={lottieAnimationData}
                className={s['stream-deck-interaction-panel__lottie-animation']}
                onLoopComplete={() => setPlayLottieAnimation(false)}
            />
            {audio && (
                <audio
                    className={
                        s['stream-deck-interaction-panel__lottie-animation']
                    }
                    controls={false}
                    src={audio}
                    ref={audioRef}
                    preload="none"
                >
                    <track kind="captions" />
                    {t('Your browser does not support the audio element.')}
                </audio>
            )}
        </>
    )
}
