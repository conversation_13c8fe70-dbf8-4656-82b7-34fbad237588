import TextPanel from '@components/organisms/TextPanel/TextPanel'
import { CardProps } from '@components/templates/CardList/CardList'
import { SpecialAnimationContent } from '@components/templates/SpecialAnimation/SpecialAnimation'
import { gsap } from 'gsap'
import { FC, useEffect, useRef } from 'react'
import s from './MediaSequenceZoomIn.module.scss'

import ElgatoMedia from '@components/common/ElgatoMedia/ElgatoMedia'
import { parseNoBreakLines } from '@config/hooks/useParseNoBreakLines'
import cn from 'classnames'
import unescape from 'lodash.unescape'

export const MediaSequenceZoomIn: FC<SpecialAnimationContent> = ({
    content
}) => {
    const textPanel = content.textPanels?.[0]
    const cards = content.children?.filter(
        (child) => child.meta.contentType === 'organismCard'
    ) as CardProps[] | undefined
    const background = content.additionalOptions?.background as
        | string
        | undefined
    const containerRef = useRef<HTMLDivElement>(null)
    const cardsRef = useRef<(HTMLDivElement | null)[]>([])
    const bigTextsRef = useRef<(HTMLDivElement | null)[]>([])

    useEffect(() => {
        const containerElement = containerRef.current
        const cardElements = cardsRef.current
        const bigTexts = bigTextsRef.current

        const timelines: gsap.core.Timeline[] = []

        cardElements?.forEach((card, index) => {
            if (index === 0) {
                const timeline = gsap
                    .timeline({
                        scrollTrigger: {
                            trigger: containerElement,
                            markers: false,
                            scrub: true,
                            start: () => `top top+=${innerHeight / 2}`,
                            end: () => `top+=${innerHeight} bottom`,
                            id: `${index} first`
                        }
                    })
                    .fromTo(
                        cardElements[index],
                        {
                            scale: (1980 - 64 * 2) / 1980,
                            borderTopLeftRadius: 48,
                            borderTopRightRadius: 48
                        },
                        {
                            scale: 1,
                            borderTopLeftRadius: 0,
                            borderTopRightRadius: 0
                        }
                    )
                timelines.push(timeline)
            }
            const timeline = gsap
                .timeline({
                    scrollTrigger: {
                        trigger: cardElements[index],
                        markers: false,
                        scrub: true,
                        start: () => `top top+=${innerHeight / 2 + 110}`,
                        end: () => `top top+=${innerHeight / 2 - 110}`,
                        id: `${index}`
                    }
                })
                .to(bigTexts[index - 1], { opacity: 0 })
                .to(bigTexts[index], { opacity: 1 })

            timelines.push(timeline)
        })

        return () => {
            timelines.forEach((timeline) => {
                timeline.kill()
            })
        }
    }, [])

    return (
        <div className={s['media-sequence-zoom-in']}>
            {textPanel && <TextPanel content={textPanel} />}
            <div
                className={cn(
                    s['media-sequence-zoom-in__cards-container'],
                    'w-full relative z-10'
                )}
                style={background ? { background: background } : undefined}
                ref={containerRef}
            >
                <div className="sticky top-0 w-full h-screen z-30 pointer-events-none">
                    {cards?.map((card, index) => {
                        const bigText = card.textPanel?.headline ?? ''
                        return (
                            <p
                                key={`big-text-${card.title}`}
                                className={cn(
                                    s['media-sequence-zoom-in__big-text'],
                                    'px-10'
                                )}
                                ref={(el) => (bigTextsRef.current[index] = el)}
                                dangerouslySetInnerHTML={{
                                    __html: unescape(parseNoBreakLines(bigText))
                                }}
                            />
                        )
                    })}
                </div>
                {cards?.map((card, index) => {
                    return (
                        <div
                            key={card.title}
                            ref={(el) => (cardsRef.current[index] = el)}
                            className={cn('overflow-hidden w-full h-screen', {
                                'absolute top-0 insets-0 z-20': index === 0
                            })}
                        >
                            {card.cloudinaryMedia && (
                                <div
                                    className={cn(
                                        s['media-sequence-zoom-in__image'],
                                        'relative inset-0 h-full w-full'
                                    )}
                                >
                                    <ElgatoMedia
                                        cloudinaryMedia={card.cloudinaryMedia}
                                        cloudinaryMobileMedia={
                                            card.cloudinaryMobileMedia
                                        }
                                        cloudinaryPosterImage={
                                            card.cloudinaryPosterImage
                                        }
                                        cloudinaryPosterImageMobile={
                                            card.cloudinaryPosterImageMobile
                                        }
                                        sizing="constrain-ratio"
                                        objectFit="cover"
                                        showPauseButton
                                        controlsPosition="bottom-left"
                                        className="h-full"
                                        loop
                                        autoPlay
                                        controlsBackground="white"
                                        intersectionDisabled
                                    />
                                </div>
                            )}
                        </div>
                    )
                })}
            </div>
        </div>
    )
}

export default MediaSequenceZoomIn
