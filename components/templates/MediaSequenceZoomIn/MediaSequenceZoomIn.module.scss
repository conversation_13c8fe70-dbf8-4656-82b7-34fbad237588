.media-sequence-zoom-in {
    &__image {
        img {
            object-fit: cover;
        }
    }

    &__big-text {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        opacity: 0;
        width: 100%;
        color: var(--white);
        text-align: center;
        text-shadow: 0 0 60px rgba(0, 0, 0, 0.8);

        @apply font-univers67BoldCondensed;

        @screen md-max {
            @apply uppercase;
            @apply text-h1-md-max;
            @apply leading-tight-md-max;
            padding: 0 60px;
        }

        @screen md {
            @apply uppercase;
            @apply text-gigantic;
            @apply leading-tight-md-max;
        }
    }

    &__big-text:lang(ru),
    &__big-text:lang(ja),
    &__big-text:lang(ko),
    &__big-text:lang(zh) {
        @screen md-max {
            @apply uppercase;
            @apply text-h2-md-max;
            @apply leading-tight-md-max;
        }
    }
}
