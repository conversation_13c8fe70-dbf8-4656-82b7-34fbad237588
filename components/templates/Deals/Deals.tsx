import React, { FC, useCallback, useEffect, useRef, useState } from 'react'
import { SpecialPageProps } from '@components/templates/SpecialPage/SpecialPage'
import { LinkListProps } from '@components/organisms/LinkList/LinkList'
import { CardListProps } from '@components/templates/CardList/CardList'

import s from './Deals.module.scss'
import cn from 'classnames'
import { DealsHero } from '@components/templates/Deals/DealsHero'
import { useRouter } from 'next/router'
import { DealsHeroNoStream } from '@components/templates/Deals/DealsHeroNoStream'
import { DealsRegionSelect } from '@components/templates/Deals/DealsRegionSelect'
import { DealsDropdown } from '@components/templates/Deals/DealsDropdown'
import { DealsSidebar } from '@components/templates/Deals/DealsSidebar'
import { DealsCardsList } from '@components/templates/Deals/DealsCardsList'
import { useMobile } from '@pylot-data/hooks/use-mobile'
import ChevronDownIcon from '@components/atoms/Icon/general/ChevronDownIcon'
import {
    CONTENT_ID,
    getContentfulRegion,
    REGION_REMAP
} from '@components/templates/Deals/utils'
import { useMedia } from '@lib/hooks/useMedia'
import { CloudinaryMedia } from '@components/common/CloudinaryMedia/Cloudinary'
import { PrimaryTextProps } from '@components/molecules/PrimaryText/PrimaryText'
import TextPanel from '@components/organisms/TextPanel/TextPanel'

type DealsProps = {
    content: SpecialPageProps
}

const Deals: FC<DealsProps> = ({ content }) => {
    const { locale, asPath, push } = useRouter()
    const { isTablet } = useMobile()
    const cardsContainerRef = useRef<HTMLDivElement | null>(null)
    const contentRef = useRef<HTMLDivElement | null>(null)
    const [sidebarTop, setSidebarTop] = useState('0px')
    const contentfulRegion = getContentfulRegion(locale)

    function onSelectRegion(newRegionCode: string) {
        const currentLangCode = locale?.substring(0, 2)
        const id = `#${CONTENT_ID}`
        const remappedLocal = REGION_REMAP[newRegionCode] || newRegionCode
        const nextLocale = `${remappedLocal}/${currentLangCode}`

        let nextPath = ''
        const nextPathWithoutId = asPath.split('#')?.[0]

        if (REGION_REMAP[newRegionCode] === 'eu') {
            const url = new URL(nextPathWithoutId, window.location.href)
            url.searchParams.delete('subRegion')
            url.searchParams.set('subRegion', newRegionCode)

            nextPath = `${url.href}${id}`
        } else {
            const url = new URL(nextPathWithoutId, window.location.href)
            url.searchParams.delete('subRegion')
            nextPath = `${url.href}${id}`
        }
        push(nextPath, undefined, { locale: nextLocale })
    }

    if (contentfulRegion !== '' && cardsContainerRef.current) {
        cardsContainerRef.current.scrollIntoView()
    }

    const { heroSlider, children = [] } = content
    const backgroundCSS = heroSlider?.backgroundCss
    const streamEnabledRegions = heroSlider?.primeDealsStreamEnabledRegions
    const streamEnabled = !!streamEnabledRegions?.find(
        (region) =>
            region.identifier.toLowerCase() === contentfulRegion?.toLowerCase()
    )

    const cardLists = children.filter(
        (child) => child.meta?.contentType === 'templateCardList'
    ) as CardListProps[] | undefined
    const textPanels = children.filter(
        (child) => child.meta?.contentType === 'organismHeadlineMedia'
    ) as PrimaryTextProps[] | undefined
    const selectedCardList = cardLists?.find((cardList) =>
        cardList.regions?.some(
            (region) =>
                region.identifier.toLowerCase() ===
                contentfulRegion?.toLowerCase()
        )
    )

    const cardLinks = selectedCardList?.children
        ?.map((card) => {
            if (card.meta.contentType === 'organismHeadlineMedia') {
                return card.id
            }
            return null
        })
        .filter(Boolean)

    const linkLists = children.filter(
        (child) => child.meta?.contentType === 'organismLinkList'
    ) as LinkListProps[] | undefined

    const linkList = linkLists?.[0].linkListItems
        ?.filter((item) => {
            return cardLinks?.includes(item.linkUrl.slice(1))
        })
        .sort((a, b) => {
            return (
                (cardLinks?.indexOf(a.linkUrl.slice(1)) || 0) -
                (cardLinks?.indexOf(b.linkUrl.slice(1)) || 0)
            )
        })
    const scrollToRef = useRef<HTMLDivElement>(null)

    const scrollDown = useCallback(() => {
        if (contentRef.current) {
            contentRef.current.scrollIntoView({
                behavior: 'smooth'
            })
        }
    }, [contentRef])

    const dealsStartDate =
        selectedCardList?.blackFridayStartDate ?? content.blackFridayStartDate
    const dealsEndDate =
        selectedCardList?.blackFridayEndDate ?? content.blackFridayEndDate

    const { src: backgroundVideo, alt: backgroundVideoAlt } = useMedia({
        cloudinaryMedia: [heroSlider?.cloudinaryMedia?.[1]] as CloudinaryMedia[]
    })

    useEffect(() => {
        const path = window.location.hash
        if (path && path.includes('#')) {
            const id = path.replace('#', '')
            if (id) {
                document.querySelector(`#${id}`)?.scrollIntoView({
                    behavior: 'smooth'
                })
            }
        }
    }, [])

    useEffect(() => {
        const event = new CustomEvent('updateHeaderDates', {
            detail: {
                dealsStartDate,
                dealsEndDate
            }
        })
        window.dispatchEvent(event)
    }, [dealsStartDate, dealsEndDate])

    return (
        <div className={cn(s['deals'])}>
            <div
                className={cn(
                    s['deals__hero'],
                    'w-full flex flex-col relative min-h-0'
                )}
            >
                {backgroundVideo && (
                    <video
                        src={backgroundVideo}
                        autoPlay
                        muted
                        loop
                        className="absolute inset-0 w-full h-full object-cover"
                        playsInline
                        preload="true"
                    />
                )}
                <div className="flex-1 relative w-full flex justify-center items-center">
                    {streamEnabled ? (
                        <DealsHero
                            content={content}
                            dealsEndDate={dealsEndDate}
                            dealsStartDate={dealsStartDate}
                        />
                    ) : (
                        <DealsHeroNoStream
                            content={content}
                            dealsEndDate={dealsEndDate}
                            dealsStartDate={dealsStartDate}
                            contentRef={contentRef}
                        />
                    )}
                    <div
                        className={cn(
                            s['deals__scroll-down'],
                            'absolute left-1/2 bottom-0 flex-col gap-4px justify-center items-center mt-2 mb-3 text-primitive-gray-130 hidden lg:flex'
                        )}
                    >
                        <button
                            onClick={scrollDown}
                            onKeyPress={scrollDown}
                            className={cn(s['deals__scroll-down__icon'])}
                        >
                            <ChevronDownIcon className="w-32px h-32px text-black" />
                        </button>
                    </div>
                </div>
                <DealsRegionSelect
                    initialValue={contentfulRegion}
                    content={content}
                    onChange={onSelectRegion}
                />
            </div>
            <div
                id={CONTENT_ID}
                className={cn(s['deals-content'], 'relative')}
                ref={contentRef}
            >
                {isTablet && (
                    <div
                        className="sticky top-16px bg-primitive-gray-130 h-0 ml-16px"
                        style={{ zIndex: 9999 }}
                    >
                        <DealsDropdown links={linkList} />
                    </div>
                )}
                <div className="flex lg-max:flex-col bg-primitive-gray-10 relative">
                    {!isTablet ? (
                        <div className="bg-primitive-gray-10 border-primitive-gray-30 border-r relative">
                            <DealsSidebar
                                headline={linkLists?.[0].headline}
                                links={linkList}
                                dealsStartDate={dealsStartDate}
                                dealsEndDate={dealsEndDate}
                            />
                        </div>
                    ) : null}
                    <div
                        className="flex flex-col bg-primitive-gray-10 w-full"
                        ref={scrollToRef}
                    >
                        {selectedCardList && (
                            <DealsCardsList cardList={selectedCardList} />
                        )}
                    </div>
                </div>
            </div>
            {textPanels?.map((textPanel) => (
                <TextPanel key={textPanel.id} content={textPanel} />
            ))}
        </div>
    )
}

export default Deals
