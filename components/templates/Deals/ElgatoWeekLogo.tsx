import { FC, SVGProps } from 'react'

export const ElgatoWeekLogo: FC<SVGProps<SVGSVGElement>> = (props) => {
    return (
        <svg
            width="399"
            height="61"
            viewBox="0 0 399 61"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <path
                d="M206.879 0H219.874L227.757 42.5056H227.925L237.482 0H252.409L261.462 42.5056H261.63L269.171 0H281.577L268.162 60.5342H254.159L244.687 16.2643H244.519L234.037 60.5342H220.287L206.879 0Z"
                fill="currentColor"
            />
            <path
                d="M284.595 60.5342V0H317.628V9.30488H297.254V24.2319H316.032V33.5368H297.254V51.2294H318.3V60.5342H284.595Z"
                fill="currentColor"
            />
            <path
                d="M321.002 60.5342V0H354.035V9.30488H333.661V24.2319H352.438V33.5368H333.661V51.2294H354.707V60.5342H321.002Z"
                fill="currentColor"
            />
            <path
                d="M385.078 0H398.997L382.061 27.1655L398.997 60.5342H384.742L370.235 29.511H370.067V60.5342H357.409V0H370.067V25.9052H370.235L385.078 0Z"
                fill="currentColor"
            />
            <path
                d="M26.517 1.92947V4.7193C26.517 5.15945 26.1581 5.51156 25.7247 5.51156H5.97919C5.53905 5.51156 5.18693 5.87045 5.18693 6.30382V26.6114C5.18693 27.0515 5.54582 27.4037 5.97919 27.4037H24.7429C25.183 27.4037 25.5351 27.7626 25.5351 28.1959V30.9858C25.5351 31.4259 25.1763 31.778 24.7429 31.778H5.97919C5.53905 31.778 5.18693 32.1369 5.18693 32.5703V54.4962C5.18693 54.9364 5.54582 55.2885 5.97919 55.2885H26.2868C26.7269 55.2885 27.079 55.6474 27.079 56.0808V58.8706C27.079 59.3107 26.7201 59.6629 26.2868 59.6629H0.79226C0.352115 59.6629 0 59.304 0 58.8706V1.92947C0 1.48932 0.352115 1.13721 0.79226 1.13721H25.718C26.1581 1.13721 26.5102 1.49609 26.5102 1.92947H26.517Z"
                fill="currentColor"
            />
            <path
                d="M31.2165 58.8841V1.92947C31.2165 1.48932 31.5754 1.13721 32.0087 1.13721H35.6112C36.0513 1.13721 36.4034 1.49609 36.4034 1.92947V54.5098C36.4034 54.9499 36.7623 55.302 37.1957 55.302H54.9843C55.4244 55.302 55.7765 55.6609 55.7765 56.0943V58.8841C55.7765 59.3243 55.4177 59.6764 54.9843 59.6764H32.002C31.5618 59.6764 31.2097 59.3175 31.2097 58.8841H31.2165Z"
                fill="currentColor"
            />
            <path
                d="M85.8821 33.4916H75.6301C75.19 33.4916 74.8379 33.1327 74.8379 32.6993V29.9095C74.8379 29.4694 75.1968 29.1172 75.6301 29.1172H91.069C91.5092 29.1172 91.8613 29.4761 91.8613 29.9095V57.7943C91.8613 58.1465 91.6311 58.4579 91.2925 58.5595C86.2816 60.0628 81.0879 60.8212 75.7249 60.8212C72.5356 60.8212 69.8879 60.3336 67.7753 59.3585C65.6626 58.3835 63.9629 57.0901 62.6696 55.465C61.3762 53.8398 60.4011 51.9777 59.7511 49.8717C59.101 47.7658 58.6541 45.5854 58.4103 43.3441C58.1666 41.1027 58.0447 38.8546 58.0447 36.6132V24.2079C58.0447 21.9666 58.1666 19.7185 58.4103 17.4771C58.6541 15.2358 59.101 13.0554 59.7511 10.9494C60.4011 8.84352 61.3695 6.97459 62.6696 5.35622C63.9697 3.73784 65.6693 2.43772 67.7753 1.46263C69.8812 0.487545 72.5288 0 75.7249 0C81.2911 0 85.4961 1.27303 88.3334 3.81233C91.0352 6.23651 92.403 10.0759 92.4369 15.3441C92.4369 15.7843 92.078 16.1431 91.6379 16.1431H88.0219C87.5885 16.1431 87.2432 15.7978 87.2296 15.3644C87.1145 11.8568 86.2275 9.18886 84.5685 7.34702C82.7876 5.37653 79.8352 4.3879 75.7317 4.3879C72.5424 4.3879 70.1114 5.0515 68.4321 6.37194C66.7528 7.69914 65.5271 9.52066 64.7416 11.8433C63.9562 14.1659 63.5092 16.9219 63.4009 20.1112C63.2926 23.3006 63.2384 26.7337 63.2384 30.4106C63.2384 34.0875 63.2926 37.5206 63.4009 40.71C63.5092 43.8993 63.9562 46.6553 64.7416 48.9779C65.5271 51.3005 66.7528 53.1288 68.4321 54.4492C70.1046 55.7764 72.5424 56.4333 75.7317 56.4333C78.0001 56.4333 80.0451 56.2843 81.8531 55.9864C83.4444 55.729 84.8799 55.3702 86.1597 54.9232C86.478 54.8149 86.6811 54.5102 86.6811 54.1716V34.2771C86.6811 33.8369 86.3223 33.4848 85.8889 33.4848L85.8821 33.4916Z"
                fill="currentColor"
            />
            <path
                d="M120.017 1.69247L137.765 58.6471C137.927 59.1618 137.541 59.6764 137.007 59.6764H133.242C132.889 59.6764 132.585 59.4462 132.483 59.1144L128.122 44.8334C128.021 44.5016 127.709 44.2713 127.364 44.2713H105.594C105.242 44.2713 104.93 44.5016 104.835 44.8401L100.556 59.1144C100.454 59.4529 100.143 59.6832 99.7974 59.6832H96.283C95.7481 59.6832 95.3621 59.1618 95.5246 58.6471L113.503 1.69247C113.604 1.36066 113.916 1.13721 114.261 1.13721H119.259C119.604 1.13721 119.915 1.36066 120.017 1.69247ZM125.502 39.897C126.037 39.897 126.416 39.3824 126.26 38.8745L116.597 6.81168H116.435L106.846 38.8745C106.691 39.3824 107.077 39.897 107.605 39.897H125.495H125.502Z"
                fill="currentColor"
            />
            <path
                d="M144.029 5.51156H131.346C130.906 5.51156 130.554 5.15268 130.554 4.7193V1.92947C130.554 1.48932 130.913 1.13721 131.346 1.13721H163.49C163.93 1.13721 164.282 1.49609 164.282 1.92947V4.7193C164.282 5.15945 163.923 5.51156 163.49 5.51156H150.807C150.367 5.51156 150.015 5.87045 150.015 6.30382V58.8841C150.015 59.3243 149.656 59.6764 149.223 59.6764H145.62C145.18 59.6764 144.828 59.3175 144.828 58.8841V6.3106C144.828 5.87045 144.469 5.51834 144.036 5.51834L144.029 5.51156Z"
                fill="currentColor"
            />
            <path
                d="M166.145 30.4106C166.145 27.7629 166.172 25.183 166.226 22.664C166.28 20.1518 166.484 17.7547 166.836 15.4863C167.188 13.2179 167.716 11.1323 168.413 9.24303C169.118 7.3538 170.127 5.7151 171.454 4.3405C172.781 2.95912 174.44 1.89601 176.444 1.1376C178.442 0.379201 180.907 0 183.825 0C186.744 0 189.202 0.379201 191.206 1.1376C193.204 1.89601 194.87 2.95912 196.197 4.3405C197.524 5.72188 198.533 7.3538 199.237 9.24303C199.941 11.139 200.463 13.2179 200.815 15.4863C201.167 17.7547 201.37 20.1518 201.424 22.664C201.478 25.1763 201.506 27.7629 201.506 30.4106C201.506 33.0582 201.478 35.6381 201.424 38.1571C201.37 40.6693 201.167 43.0597 200.815 45.3349C200.463 47.6033 199.935 49.6889 199.237 51.5781C198.533 53.4741 197.517 55.1061 196.197 56.4874C194.87 57.8688 193.211 58.9319 191.206 59.6903C189.209 60.4487 186.744 60.8279 183.825 60.8279C180.907 60.8279 178.449 60.4487 176.444 59.6903C174.44 58.9319 172.781 57.8688 171.454 56.4874C170.127 55.1061 169.118 53.4741 168.413 51.5781C167.709 49.6889 167.181 47.6033 166.836 45.3349C166.484 43.0664 166.28 40.6693 166.226 38.1571C166.172 35.6449 166.145 33.065 166.145 30.4106ZM171.339 30.4106C171.339 34.3042 171.406 37.8456 171.542 41.035C171.677 44.2243 172.151 46.9668 172.964 49.2623C173.776 51.5578 175.016 53.3319 176.695 54.5711C178.367 55.8171 180.751 56.4333 183.832 56.4333C186.913 56.4333 189.29 55.8103 190.969 54.5711C192.642 53.3252 193.888 51.5578 194.7 49.2623C195.513 46.9668 195.987 44.2243 196.122 41.035C196.258 37.8456 196.325 34.3042 196.325 30.4106C196.325 26.517 196.258 22.9755 196.122 19.7862C195.987 16.5968 195.513 13.8544 194.7 11.5589C193.888 9.26334 192.642 7.48922 190.969 6.25005C189.29 5.0041 186.913 4.3879 183.832 4.3879C180.751 4.3879 178.374 5.01087 176.695 6.25005C175.016 7.496 173.776 9.26334 172.964 11.5589C172.151 13.8544 171.677 16.5968 171.542 19.7862C171.406 22.9755 171.339 26.517 171.339 30.4106Z"
                fill="currentColor"
            />
        </svg>
    )
}
