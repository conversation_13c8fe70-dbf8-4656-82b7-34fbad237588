import TextPanel from '@components/organisms/TextPanel/TextPanel'
import { CardListProps } from '@components/templates/CardList/CardList'
import { SpecialDaySalesCard } from '@components/templates/SpecialDay/SpecialDaySalesCard'
import { FC } from 'react'

type DealsCardsListProps = {
    cardList: CardListProps
}
export const DealsCardsList: FC<DealsCardsListProps> = ({ cardList }) => {
    // splits cardLists children into sections of objects and arrays. object when there is a textpanel and array when there are cards
    // in addition, array when there is also a banner
    const sections = cardList?.children?.reduce((acc, curr) => {
        if (curr.meta?.contentType === 'organismHeadlineMedia') {
            acc.push({ textPanel: curr })
        } else if (curr.meta?.contentType === 'organismCard') {
            if (acc.length === 0 || !Array.isArray(acc[acc.length - 1])) {
                acc.push([curr])
            } else {
                acc[acc.length - 1].push(curr)
            }
        }
        // console.log('acc', acc)
        return acc
    }, [])
    return sections.map((section: any[] | any, i: number) => {
        if (Array.isArray(section)) {
            return (
                <div
                    key={i}
                    className="py-16 lg:py-32 px-16px lg:px-32px flex flex-wrap gap-16 md:gap-16px bg-primitive-gray-10"
                >
                    {section.map((card, index) => (
                        <SpecialDaySalesCard card={card} key={index} />
                    ))}
                </div>
            )
        } else {
            const { textPanel } = section
            const textColor = textPanel.textColor?.[0]
            return (
                <TextPanel
                    content={{
                        ...section.textPanel,
                        textColor:
                            textColor && textColor === 'white'
                                ? 'light'
                                : 'dark'
                    }}
                    key={i}
                />
            )
        }
    })
}
