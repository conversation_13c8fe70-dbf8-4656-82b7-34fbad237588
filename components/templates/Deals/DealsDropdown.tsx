import React, { FC, useEffect, useState, useRef, useCallback } from 'react'
import cn from 'classnames'
import s from './DealsDropdown.module.scss'
import { Icon } from '@components/atoms/Icon/Icon'
import { Link, LinkResponse } from '@components/molecules/Link/Link'
import dynamic from 'next/dynamic'
import { useOutsideClick } from '@lib/hooks/useOutsideClick'
import { debounce } from 'lodash'
import { useTranslation } from 'next-i18next'
import gsap from 'gsap'

const AnimateHeight = dynamic(() => import('react-animate-height'), {
    ssr: false
})

type DealsDropdownProps = {
    links?: LinkResponse[]
    onToggle?: (prevIsOpen: boolean) => void
}

export const DealsDropdown: FC<DealsDropdownProps> = ({ links }) => {
    const { t } = useTranslation(['common'])
    const [isMobile, setIsMobile] = useState(false)

    const [isOpen, setIsOpen] = useState(false)
    const ref = useOutsideClick(() => setIsOpen(false))
    const [activeIndex, setActiveIndex] = useState<number | null>(null)
    const [isAutoScrolling, setIsAutoScrolling] = useState(false)
    const scrollTimeoutRef = useRef<NodeJS.Timeout>()

    useEffect(() => {
        const checkMobile = () => {
            setIsMobile(window.innerWidth <= 768)
        }
        checkMobile()
        window.addEventListener('resize', checkMobile)
        return () => window.removeEventListener('resize', checkMobile)
    }, [])

    useEffect(() => {
        const timeline = gsap.timeline({
            scrollTrigger: {
                trigger: ref.current,
                start: 'top+=80 top',
                end: 'bottom 20%'
            }
        })

        timeline.fromTo(
            ref.current,
            {
                opacity: 0,
                y: 20,
                visibility: 'hidden'
            },
            {
                opacity: 1,
                y: 0,
                visibility: 'visible',
                duration: 0.5,
                ease: 'power2.out'
            }
        )
    }, [])

    useEffect(() => {
        function handleScroll() {
            setIsAutoScrolling(false)
            const pageScrollTop = window.scrollY
            const linkElements =
                links?.map((link) => {
                    const id = link.linkUrl.slice(1)
                    const element = document.getElementById(id)
                    return element
                }) || []

            let found = false
            for (let i = 0; i < linkElements?.length; i++) {
                const element = linkElements[i]
                const scrollTop = element?.offsetTop || 0

                if (pageScrollTop + 100 < scrollTop) {
                    if (i > 0) {
                        setActiveIndex(i - 1)
                    } else {
                        setActiveIndex(0)
                    }
                    found = true
                    break
                }
            }

            if (!found && linkElements.length > 0) {
                setActiveIndex(linkElements.length - 1)
            }
        }

        const scroller = debounce(handleScroll, isAutoScrolling ? 100 : 0)

        window.addEventListener('scroll', scroller)

        return () => {
            window.removeEventListener('scroll', scroller)
        }
    }, [links, isAutoScrolling])

    const scrollToSection = useCallback(
        (index: number) => {
            if (scrollTimeoutRef.current) {
                clearTimeout(scrollTimeoutRef.current)
            }

            setIsAutoScrolling(true)
            setActiveIndex(index)

            const id = links?.[index].linkUrl.slice(1)
            const element = document.getElementById(id!)

            if (element) {
                element.scrollIntoView({ behavior: 'smooth' })

                scrollTimeoutRef.current = setTimeout(() => {
                    setIsAutoScrolling(false)
                }, 1000)
            }
        },
        [links]
    )

    return (
        <div
            ref={ref}
            className={cn(
                s['deals-dropdown-content__button'],
                'relative p-8px bg-white',
                'fixed md:relative'
            )}
            style={{ visibility: 'hidden' }}
        >
            <button
                className="flex flex-row outline-none"
                onClick={() => setIsOpen(!isOpen)}
            >
                {/* eslint-disable-next-line i18next/no-literal-string */}
                <Icon className="relative" name="menu" />
                <div
                    className={cn(
                        s['deals-dropdown-content__status-icon'],
                        'rounded-full w-4 h-4'
                    )}
                />
                <span className="ml-5px">{t('special-day-nav-products')}</span>
            </button>
            <div
                className={cn(
                    s['deals-dropdown-content__wrapper'],
                    isOpen ? s['open'] : '',
                    'absolute top-full bg-white z-100 rounded-3xl p-8px'
                )}
            >
                <AnimateHeight height={isOpen ? 'auto' : 0}>
                    <div
                        className={cn(
                            s['deals-dropdown-content__wrapper__filter']
                        )}
                    >
                        {links?.map((link, index) => (
                            <div
                                key={index}
                                className={cn(
                                    'font-univers55Roman pt-2.5 pb-3.5 pr-4 pl-16px mb-2.5 text-black',
                                    {
                                        [s[
                                            'deals-dropdown-content__links'
                                        ]]: true,
                                        [s[
                                            'deals-dropdown-content__links--active'
                                        ]]: activeIndex === index,
                                        [s[
                                            `deals-dropdown-content__links--active-${index}`
                                        ]]: activeIndex === index
                                    }
                                )}
                            >
                                {/* eslint-disable-next-line jsx-a11y/anchor-is-valid */}
                                <Link
                                    onClick={() => {
                                        scrollToSection(index)
                                        setIsOpen(false) // Close dropdown after selection
                                    }}
                                    link={link}
                                    key={index}
                                    className={cn(
                                        s['deals-dropdown-content__anchor'],
                                        'flex w-full h-full'
                                    )}
                                    aria-label={`${
                                        link?.linkTitle &&
                                        `${link.linkTitle} - `
                                    }${t('ada|Opens in the current Tab')}`}
                                >
                                    {link?.linkTitle}
                                </Link>
                            </div>
                        ))}
                    </div>
                </AnimateHeight>
            </div>
        </div>
    )
}
