import {
    CardListProps,
    CardProps
} from '@components/templates/CardList/CardList'
import { SetupOverlayCardSectionItem } from '@components/templates/SetupCards/SetupOverlayCard/SetupOverlayCardSectionItem'
import { ConfigurableProduct, SimpleProduct } from '@pylot-data/fwrdschema'
import { FC } from 'react'

type SetupOverlayCardSectionProps = {
    cardList: CardListProps
    productsData: (ConfigurableProduct | SimpleProduct)[]
    hoverItem: CardProps | undefined
}

export const SetupOverlayCardSection: FC<SetupOverlayCardSectionProps> = ({
    cardList,
    productsData,
    hoverItem
}) => {
    const items = cardList.children?.filter(
        (child) => child.meta?.contentType === 'organismCard'
    ) as CardProps[] | undefined
    return (
        <div className="flex flex-col gap-8px">
            {cardList.headline && <h5>{cardList.headline}</h5>}
            <div className="flex flex-col p-12px gap-8px bg-primitive-gray-10 rounded-xl">
                {items?.map((card) => (
                    <SetupOverlayCardSectionItem
                        card={card}
                        key={card.title}
                        productsData={productsData}
                        hoverItem={hoverItem}
                    />
                ))}
            </div>
        </div>
    )
}
