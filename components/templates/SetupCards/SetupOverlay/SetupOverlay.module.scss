.setup-overlay {
  &--open {
    background: rgba(0, 0, 0, 0.5);
  }
  &_container{
    @media only screen and (max-width: 767px){
      padding: 0;
    }
  }
  &__btn{
    background: rgba(255, 355,355,0.4);
    width: 50px;
    height: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    svg{
      margin-top: 1px;
      pointer-events: none;
    }
    @media only screen and (max-width: 767px){
      border-radius: 100%;
      top: 30px;
      right: 20px;
      left: auto;
      &:first-of-type{
        right: 80px;
      }
    }
  }
  &__overlay-card {
    &__close-btn {
    
      @media only screen and (max-width: 767px) {
        display: none;
      }
    }
  }
  &__card-wrapper {
    display: flex;
    height: calc(100vh - 280px );
    max-width: 85vw;
    margin: 0 auto;

    @media only screen and (max-width: 767px){
      max-width: unset;
      height: auto;
      padding-top: 0;
      padding-bottom: 0;
      margin-left: 0;
      margin-right: 0;
      position: absolute;
      overflow: hidden;
      top: 50%;
      width: 100%;
    }
  }
  &__close{
    button{
      padding: 0;
      position: absolute;
      top: 20px;
      right: 20px;
      background-color: #fff;
      outline: none;
      width: 50px;
      height: 50px;
      border-radius: 100%;
      display: flex;
      justify-content: center;
      @media only screen and (max-width: 767px){
        top: 10px;
        right: 10px;

      }
    }
  }
}

@keyframes fadeIn {
  0% {
    opacity: 0.3;
  }
  100% {
    opacity: 1;
  }
}

