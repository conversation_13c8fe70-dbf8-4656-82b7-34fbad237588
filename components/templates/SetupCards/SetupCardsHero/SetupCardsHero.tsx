import { useAnimationAndVideosToggle } from '@components/common/AnimationAndVideosToggle/AnimationAndVideoContext'
import { SetupCardHeroMedia } from '@components/common/CloudinaryMedia/Cloudinary'
import ElgatoImage from '@components/common/ElgatoImage'
import ArrowDownThick from '@components/icons/ArrowDownThick'
import cn from 'classnames'
import { useTranslation } from 'next-i18next'
import { FC, useEffect, useRef, useState } from 'react'
import s from './SetupCardsHero.module.scss'

export interface SetupCardsHeroProps {
    views?: Array<{
        title: string
        cloudinaryImages: {
            cloudinaryMedia1: SetupCardHeroMedia
            cloudinaryMedia2: SetupCardHeroMedia
            cloudinaryMedia3: SetupCardHeroMedia
            cloudinaryMedia4: SetupCardHeroMedia
            cloudinaryMedia5: SetupCardHeroMedia
            cloudinaryMedia6: SetupCardHeroMedia
            cloudinaryMedia7: SetupCardHeroMedia
        }
        textPanels: any[]
        animation: string
        meta: {
            contentType: string
        }
    }>
}

interface SetupCardsHeroContent {
    content: SetupCardsHeroProps
}

const SetupCardsHero: FC<SetupCardsHeroContent> = ({ content }) => {
    const { views } = content
    const [currentViewIndex, setCurrentViewIndex] = useState(0)
    const [isFaded, setIsFaded] = useState(true)
    const [isFadedImage, setIsFadedImage] = useState(true)
    const [isFadedHero, setIsFadedHero] = useState(false)
    const heroRef = useRef(null)
    const { isAnimationStopped } = useAnimationAndVideosToggle()
    const { t } = useTranslation(['common'])

    useEffect(() => {
        const func1 = () => {
            if (!views || views.length === 0) {
                return
            }
            setCurrentViewIndex((prevIndex) => (prevIndex + 1) % views.length)
            setTimeout(func3, 100)
        }
        const func2 = () => {
            setIsFadedImage(false)
            setTimeout(func4, 800)
        }
        const func4 = () => {
            setIsFaded(false)
            setTimeout(func1, 800)
        }
        const func3 = () => {
            setIsFaded(true)
            setIsFadedImage(true)
            setTimeout(func2, 4000)
        }
        setTimeout(func2, 5000)
    }, [views])

    useEffect(() => {
        const handleScroll = () => {
            const scrollPosition = window.scrollY
            const heroElement = heroRef.current as HTMLDivElement | null

            if (heroElement) {
                const heroPosition = heroElement.offsetTop

                if (scrollPosition >= heroPosition + window.innerHeight) {
                    setIsFadedHero(true)
                } else {
                    setIsFadedHero(false)
                }
            }
        }

        window.addEventListener('scroll', handleScroll)

        return () => {
            window.removeEventListener('scroll', handleScroll)
        }
    }, [])

    if (!views || views.length === 0) {
        return null
    }

    const currentView = views[currentViewIndex]

    function smoothScroll() {
        const viewportHeight = window.innerHeight
        const offset = 53
        const targetPosition = viewportHeight + offset

        window.scrollTo({
            top: targetPosition,
            behavior: 'smooth'
        })
    }

    const focusOnFilter = () => {
        const filterElement = document.querySelector(
            '.setup-cards-filters-btn > button'
        ) as HTMLElement
        filterElement?.focus()
    }

    return (
        <div
            className={cn(
                s['setup-cards-hero'],
                'setup-cards-hero text-center relative duration-500 flex flex-wrap justify-center items-end',
                {
                    'opacity-0 translate-y-[-100vh]': isFadedHero,
                    'opacity-100 translate-y-0': !isFadedHero
                }
            )}
            ref={heroRef}
        >
            <div className="fixed h-full flex flex-wrap justify-center items-end">
                {currentView.textPanels?.map((text, index) => (
                    <div
                        key={index}
                        className={cn(
                            s['setup-cards-hero__header'],
                            'duration-500 mt-12'
                        )}
                    >
                        <h1 className="text-5xl md:text-6xl">
                            {text.headline}
                        </h1>
                        <h1
                            className={cn(
                                `text${text.headlineClasses} duration-500`,
                                {
                                    [s['fade-in']]:
                                        !isAnimationStopped && isFaded,
                                    [s['fade-out']]:
                                        !isAnimationStopped && !isFaded
                                }
                            )}
                        >
                            {text.subheader}
                        </h1>
                        <div
                            className={cn(
                                {
                                    [s[
                                        'setup-cards-hero__header__button'
                                    ]]: !isAnimationStopped
                                },
                                `bg${text.headlineClasses} w-16 h-16 rounded-full mt-24 md:mt-16 m-auto flex justify-center items-center p-px cursor-pointer`,
                                {
                                    [s['fade-in']]:
                                        !isAnimationStopped && isFaded,
                                    [s['fade-out']]:
                                        !isAnimationStopped && !isFaded
                                }
                            )}
                            onKeyPress={(e) => {
                                if (e.key === 'Enter' || e.key === ' ') {
                                    smoothScroll()
                                    focusOnFilter()
                                }
                            }}
                            onClick={() => smoothScroll()}
                            role="button"
                            tabIndex={0}
                            aria-label={t('ada|Scroll to content')}
                        >
                            <ArrowDownThick />
                        </div>
                    </div>
                ))}
                {/* placeholder for holding the color classes */}
                <div
                    className="text-primitive-purple-80 text-primitive-purple-90 text-primitive-purple-100 text-neon-carrot-1 text-neon-carrot-2 text-neon-carrot-3 text-persian-blue-1 text-persian-blue-2 text-persian-blue-3 text-wild-berry-1 text-wild-berry-2 text-wild-berry-3
        bg-primitive-purple-80 bg-primitive-purple-90 bg-primitive-purple-100 bg-neon-carrot-1 bg-neon-carrot-2 bg-neon-carrot-3 bg-persian-blue-1 bg-persian-blue-2 bg-persian-blue-3 bg-wild-berry-1 bg-wild-berry-2 bg-wild-berry-3"
                />

                <div
                    className={cn(
                        s['setup-cards-hero__wrapper'],
                        'flex justify-center px-24 relative overflow-visible left-1/2 overflow-hidden'
                    )}
                >
                    <div
                        className={cn(
                            s['setup-cards-hero__wrapper__image'],
                            s['delay-2'],
                            {
                                [s['fade-in']]:
                                    !isAnimationStopped && isFadedImage,
                                [s['fade-out']]:
                                    !isAnimationStopped && !isFadedImage
                            }
                        )}
                    >
                        <ElgatoImage
                            className="rounded-3xl opacity-100 duration-500 ease-in mb-5"
                            src={
                                currentView.cloudinaryImages?.cloudinaryMedia1
                                    ?.cloudinaryMedia?.[0]?.secure_url
                            }
                            alt={
                                currentView.cloudinaryImages?.cloudinaryMedia1
                                    ?.description
                            }
                        />
                        <ElgatoImage
                            className="rounded-3xl opacity-100 duration-500 ease-in md:block hidden"
                            src={
                                currentView.cloudinaryImages?.cloudinaryMedia1
                                    ?.cloudinaryMedia?.[0]?.secure_url
                            }
                            alt={
                                currentView.cloudinaryImages?.cloudinaryMedia1
                                    ?.description
                            }
                        />
                    </div>
                    <div
                        className={cn(
                            s['setup-cards-hero__wrapper__image'],
                            s['delay-3'],
                            {
                                [s['fade-in']]:
                                    !isAnimationStopped && isFadedImage,
                                [s['fade-out']]:
                                    !isAnimationStopped && !isFadedImage
                            }
                        )}
                    >
                        <ElgatoImage
                            className="rounded-3xl opacity-100 duration-500 ease-in mb-5"
                            src={
                                currentView.cloudinaryImages?.cloudinaryMedia2
                                    ?.cloudinaryMedia?.[0]?.secure_url
                            }
                            alt={
                                currentView.cloudinaryImages?.cloudinaryMedia2
                                    ?.description
                            }
                            style={{
                                width: 'auto',
                                height: 'auto',
                                objectFit: 'contain'
                            }}
                        />
                        <ElgatoImage
                            className="rounded-3xl opacity-100 duration-500 ease-in md:block hidden"
                            src={
                                currentView.cloudinaryImages?.cloudinaryMedia3
                                    ?.cloudinaryMedia?.[0]?.secure_url
                            }
                            alt={
                                currentView.cloudinaryImages?.cloudinaryMedia3
                                    ?.description
                            }
                            style={{
                                width: 'auto',
                                height: 'auto',
                                objectFit: 'contain'
                            }}
                        />
                    </div>
                    <div
                        className={cn(
                            s['setup-cards-hero__wrapper__image'],
                            s['delay-4'],
                            {
                                [s['fade-in']]:
                                    !isAnimationStopped && isFadedImage,
                                [s['fade-out']]:
                                    !isAnimationStopped && !isFadedImage
                            }
                        )}
                    >
                        <ElgatoImage
                            className="rounded-3xl opacity-100 duration-500 ease-in"
                            src={
                                currentView.cloudinaryImages?.cloudinaryMedia4
                                    ?.cloudinaryMedia?.[0]?.secure_url
                            }
                            alt={
                                currentView.cloudinaryImages?.cloudinaryMedia4
                                    ?.description
                            }
                            style={{
                                width: 'auto',
                                height: 'auto',
                                objectFit: 'contain'
                            }}
                        />
                    </div>
                    <div
                        className={cn(
                            s['setup-cards-hero__wrapper__image'],
                            s['delay-5'],
                            {
                                [s['fade-in']]:
                                    !isAnimationStopped && isFadedImage,
                                [s['fade-out']]:
                                    !isAnimationStopped && !isFadedImage
                            }
                        )}
                    >
                        <ElgatoImage
                            className="rounded-3xl opacity-100 duration-500 ease-in mb-5"
                            src={
                                currentView.cloudinaryImages?.cloudinaryMedia5
                                    ?.cloudinaryMedia?.[0]?.secure_url
                            }
                            alt={
                                currentView.cloudinaryImages?.cloudinaryMedia5
                                    ?.description
                            }
                            style={{
                                width: 'auto',
                                height: 'auto',
                                objectFit: 'contain'
                            }}
                        />
                        <ElgatoImage
                            className="rounded-3xl opacity-100 duration-500 ease-in"
                            src={
                                currentView.cloudinaryImages?.cloudinaryMedia6
                                    ?.cloudinaryMedia?.[0]?.secure_url
                            }
                            alt={
                                currentView.cloudinaryImages?.cloudinaryMedia6
                                    ?.description
                            }
                            style={{
                                width: 'auto',
                                height: 'auto',
                                objectFit: 'contain'
                            }}
                        />
                    </div>
                </div>
            </div>
        </div>
    )
}

export default SetupCardsHero
