import React, { FC, useEffect, useRef, useState } from 'react'
import { ScrollTrigger } from 'gsap/dist/ScrollTrigger'
import cn from 'classnames'
import s from './SideScrollingAnimationDesktopGrid.module.scss'
import { useNeoAction } from '@lib/gtm/neoActions'
import { Button } from '@components/molecules/Button/Button'
import { focusController } from 'helpers/AdaHelpers'
import { Icon } from '@components/atoms/Icon/Icon'
import PlayIcon from '@components/atoms/Icon/general/PlayIcon'
import { VideoOverlay } from '@components/organisms/VideoOverlay/VideoOverlay'
import { decode } from 'he'
import { ArrowRight } from '@components/icons'
import ImageHoverSlider from '@components/templates/TextImageSlider/ImageHoverSlider'
import ElgatoMedia from '@components/common/ElgatoMedia/ElgatoMedia'

interface SideScrollingAnimationDesktopProps {
    content: any
}

const SideScrollingAnimationDesktopGrid: FC<SideScrollingAnimationDesktopProps> = ({
    content
}) => {
    const mediaWithContent = content?.mediaWithContent
    const wrapperRef = useRef<HTMLDivElement>(null)
    const containerRef = useRef<HTMLDivElement>(null)
    const { pushNeoAction } = useNeoAction()
    const [videoOverlayStates, setVideoOverlayStates] = useState<boolean[]>(
        mediaWithContent?.map(() => false) || []
    )

    const handlePushNeoAction = ({
        videoUrl,
        videoTitle
    }: {
        videoUrl: string
        videoTitle: string
    }) => {
        pushNeoAction({
            cta: 'VideoCTA',
            component: document.title,
            action: 'click',
            clickText: videoTitle,
            clickUrl: videoUrl
        })
    }

    const openVideoOverlay = (index: number) => {
        const newStates = [...videoOverlayStates]
        newStates[index] = true
        setVideoOverlayStates(newStates)
        handlePushNeoAction({
            videoTitle: mediaWithContent[index]?.textPanel?.headline || '',
            videoUrl: mediaWithContent[index]?.overlay?.file?.url || ''
        })
    }

    const closeVideoOverlay = (index: number) => {
        const newStates = [...videoOverlayStates]
        newStates[index] = false
        setVideoOverlayStates(newStates)
    }

    const linkRef = useRef<HTMLDivElement>(null)
    const imageRef = useRef<HTMLDivElement>(null)

    useEffect(() => {
        if (containerRef && containerRef.current) {
            const sections = Array.from(containerRef.current.children)
            // const triggers: ScrollTrigger[] = []

            sections.forEach((section, i) => {
                const textElement = section.querySelector(
                    `.${s['side-scrolling-view-wrapper']}`
                )
                const imageElement = section.querySelector(
                    `.${s['side-scrolling-view-wrapper__image__container']}`
                )

                if (textElement && imageElement) {
                    ScrollTrigger.create({
                        trigger: imageElement,
                        // markers: true,
                        start: () => `top 40%`,
                        end: () => `top -20%`,
                        onEnter() {
                            textElement.classList.add(s['show'])
                        },
                        onLeave() {
                            textElement.classList.remove(s['show'])
                        },
                        onEnterBack() {
                            textElement.classList.add(s['show'])
                        },
                        onLeaveBack() {
                            textElement.classList.remove(s['show'])
                        }
                    })
                }
            })
        }
    }, [])

    return (
        <div
            className={cn(
                s['side-scrolling-container'],
                'py-80px px-64px mx-auto'
            )}
        >
            <div
                ref={wrapperRef}
                className={cn(
                    s['side-scrolling'],
                    'flex flex-start flex-wrap relative'
                )}
            >
                <div ref={containerRef} className="grid gap-y-16px">
                    {mediaWithContent &&
                        mediaWithContent.map((media: any, i: number) => {
                            const quote = media?.textPanel?.richText
                            const adjusted_quote = quote
                                ? quote
                                      .replace('&quot;', '"')
                                      .replace('&quot;', '"')
                                : ''
                            return (
                                <div className="grid grid-cols-12" key={i}>
                                    <div
                                        className={cn(
                                            s['side-scrolling-view-wrapper'],
                                            'col-start-2 col-span-4 pt-64px'
                                        )}
                                    >
                                        <div>
                                            <h2
                                                className={
                                                    s[
                                                        'side-scrolling-view-wrapper__content__title'
                                                    ]
                                                }
                                            >
                                                {media?.textPanel?.headline}
                                            </h2>
                                            <p className="text-body-copy mb-24px mt-16px bold">
                                                {decode(
                                                    media?.textPanel?.subheader
                                                )}
                                            </p>
                                            <div
                                                className={cn(
                                                    s[
                                                        'side-scrolling-view-wrapper__content__text'
                                                    ]
                                                )}
                                            >
                                                {decode(
                                                    media?.textPanel?.bodyCopy
                                                )}
                                            </div>
                                            {media?.textPanel?.link &&
                                                !media?.textPanel?.link
                                                    .logo && (
                                                    <Button
                                                        id={`side-scrolling-view-${i}-button`}
                                                        variant="primary"
                                                        onClick={() =>
                                                            openVideoOverlay(i)
                                                        }
                                                        onKeyPress={(
                                                            e: React.KeyboardEvent
                                                        ) => {
                                                            if (
                                                                e.key ===
                                                                    'Enter' ||
                                                                e.key === ' '
                                                            ) {
                                                                openVideoOverlay(
                                                                    i
                                                                )
                                                                focusController(
                                                                    `#side-scrolling-animation-${i}-button`,
                                                                    '.content__open',
                                                                    '.content__open .overlay-close'
                                                                )
                                                            }
                                                        }}
                                                        href={
                                                            !media?.overlay
                                                                ? media
                                                                      ?.textPanel
                                                                      ?.link
                                                                      ?.linkUrl
                                                                : undefined
                                                        }
                                                        newTab={
                                                            !media?.overlay
                                                                ? media
                                                                      ?.textPanel
                                                                      ?.link
                                                                      .linkUrl
                                                                : media
                                                                      ?.textPanel
                                                                      ?.link
                                                                      .newTab
                                                        }
                                                        iconAlignment={
                                                            media?.textPanel
                                                                ?.link
                                                                .iconAlignment
                                                        }
                                                        className={cn(
                                                            s[
                                                                'side-scrolling-view-wrapper__content__button'
                                                            ],
                                                            'mt-8'
                                                        )}
                                                        label={
                                                            media?.textPanel
                                                                ?.link.linkTitle
                                                        }
                                                    >
                                                        {media?.textPanel?.link
                                                            ?.icon && (
                                                            <Icon
                                                                name={
                                                                    media
                                                                        ?.textPanel
                                                                        ?.link
                                                                        ?.icon
                                                                }
                                                            />
                                                        )}
                                                        {
                                                            media?.textPanel
                                                                ?.link.linkTitle
                                                        }
                                                        {media?.overlay && (
                                                            <PlayIcon />
                                                        )}
                                                    </Button>
                                                )}
                                            <div className="flex flex-col">
                                                {adjusted_quote && (
                                                    <div
                                                        className={cn(
                                                            s[
                                                                'side-scrolling-view-wrapper__content__notice'
                                                            ]
                                                        )}
                                                    >
                                                        {adjusted_quote}
                                                    </div>
                                                )}
                                                {media?.textPanel?.author && (
                                                    <div
                                                        className={cn(
                                                            s[
                                                                'side-scrolling-view-wrapper__content__autor'
                                                            ]
                                                        )}
                                                    >
                                                        {
                                                            media?.textPanel
                                                                ?.author
                                                        }
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                        {media?.overlay && (
                                            <VideoOverlay
                                                video={media?.overlay}
                                                isOpen={videoOverlayStates[i]}
                                                setIsOpen={(value) => {
                                                    const newStates = [
                                                        ...videoOverlayStates
                                                    ]
                                                    newStates[i] =
                                                        typeof value ===
                                                        'function'
                                                            ? value(
                                                                  videoOverlayStates[
                                                                      i
                                                                  ]
                                                              )
                                                            : value
                                                    setVideoOverlayStates(
                                                        newStates
                                                    )
                                                }}
                                                onClose={() =>
                                                    closeVideoOverlay(i)
                                                }
                                                posterImage={
                                                    media?.overlay.posterImage
                                                }
                                                videoCaptionFile={
                                                    media?.overlay
                                                        ?.videoCaptionFile
                                                }
                                                videoDescriptionFile={
                                                    media?.overlay
                                                        ?.videoDescriptionFile
                                                }
                                            />
                                        )}
                                    </div>
                                    <div
                                        className={cn(
                                            s[
                                                'side-scrolling-view-wrapper__image__container'
                                            ],
                                            'relative image-container col-start-7 col-span-5'
                                        )}
                                    >
                                        <div
                                            className={cn(
                                                s[
                                                    'side-scrolling-view-wrapper__image__container__inner'
                                                ],
                                                'rounded-xxl overflow-hidden'
                                            )}
                                        >
                                            {media?.textPanel?.iconText && (
                                                <div
                                                    className={cn(
                                                        'flex flex-row',
                                                        s[
                                                            'side-scrolling-view-wrapper__image__badge'
                                                        ]
                                                    )}
                                                >
                                                    <span
                                                        className={cn(
                                                            s[
                                                                'side-scrolling-view-wrapper__image__icon'
                                                            ]
                                                        )}
                                                    >
                                                        {media?.textPanel
                                                            ?.icon && (
                                                            <Icon
                                                                name={
                                                                    media
                                                                        ?.textPanel
                                                                        ?.icon
                                                                }
                                                            />
                                                        )}
                                                    </span>
                                                    <span
                                                        className={cn(
                                                            s[
                                                                'side-scrolling-view-wrapper__image__icon-text'
                                                            ]
                                                        )}
                                                    >
                                                        {
                                                            media?.textPanel
                                                                ?.iconText
                                                        }
                                                    </span>
                                                </div>
                                            )}
                                            <div className="relative">
                                                {media?.textPanel?.link && (
                                                    <div
                                                        className={cn(
                                                            'absolute top-0 right-0 bg-white m-40px p-24px',
                                                            s[
                                                                'side-scrolling__logo'
                                                            ],
                                                            'side-scrolling-logo'
                                                        )}
                                                        ref={linkRef}
                                                    >
                                                        <div
                                                            className={cn(
                                                                s[
                                                                    'side-scrolling__link'
                                                                ],
                                                                'mt-24px'
                                                            )}
                                                            ref={linkRef}
                                                        >
                                                            <Button
                                                                isNeo
                                                                variant="secondary"
                                                                className="btn-neo-secondary"
                                                                href={
                                                                    media
                                                                        ?.textPanel
                                                                        ?.link
                                                                        .linkUrl
                                                                }
                                                                size="sm"
                                                                newTab
                                                                label={
                                                                    media
                                                                        ?.textPanel
                                                                        ?.link
                                                                        .linkTitle
                                                                }
                                                            >
                                                                <div
                                                                    className={cn(
                                                                        s[
                                                                            'side-scrolling__link-title'
                                                                        ]
                                                                    )}
                                                                >
                                                                    {
                                                                        media
                                                                            ?.textPanel
                                                                            ?.link
                                                                            .linkTitle
                                                                    }
                                                                </div>
                                                                <ArrowRight
                                                                    width="24"
                                                                    height="24"
                                                                />
                                                            </Button>
                                                        </div>
                                                    </div>
                                                )}
                                                {!media.media
                                                    ?.cloudinaryMedia && (
                                                    <ImageHoverSlider
                                                        view={media}
                                                    />
                                                )}
                                            </div>
                                            {media?.media?.cloudinaryMedia && (
                                                <ElgatoMedia
                                                    {...media?.media}
                                                    sizing="constrain-ratio"
                                                    className={cn(
                                                        s[
                                                            'side-scrolling-view-wrapper__image__container__inner--media'
                                                        ]
                                                    )}
                                                />
                                            )}
                                        </div>
                                    </div>
                                </div>
                            )
                        })}
                </div>
            </div>
        </div>
    )
}

export default SideScrollingAnimationDesktopGrid
