.text-with-media-sound {
    &__text-wrapper {
        @apply text-center px-16px;
        padding-top: var(--spacing--sm);

        @screen md {
            @apply px-32px;
            padding-top: var(--spacing--lg);
        }

        @screen xl {
            @apply px-0;
        }
    }

    &__text {
        @screen md {
            margin-bottom: 48px;
        }
    }

    &__media-wrapper {
        height: 100%;
        width: 100%;
        min-height: 35vh;

        @screen md {
            padding-bottom: 0;
            min-height: 40vh;
        }

        &--without-toggle {
            padding-bottom: 0;
        }
    }

    &__media {
        position: absolute !important;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        opacity: 0;
        visibility: hidden;
        z-index: -1;

        @screen md {
            width: 100%;
        }

        &--active {
            z-index: 1;
            opacity: 1;
            visibility: visible;
        }
    }

    &__video {
        width: 100%;
        height: 100%;
        object-fit: contain;

        @screen md {
            object-fit: cover;
        }
    }
}
