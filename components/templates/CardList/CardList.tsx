import { CloudinaryMedia } from '@components/common/CloudinaryMedia/Cloudinary'
import { BannerProps } from '@components/molecules/Banner/Banner'
import { LinkResponse } from '@components/molecules/Link/Link'
import { convertToStoreLink } from '@components/molecules/Link/linkUtils'
import {
    HorizontalAlignmentEnum,
    PrimaryTextProps,
    VerticalAlignmentEnum
} from '@components/molecules/PrimaryText/PrimaryText'
import CardComparisonWithToggle from '@components/organisms/CardComparisonWithToggle/CardComparisonWithToggle'
import { FeatureCardsTrio } from '@components/organisms/FeatureCardsTrio/FeatureCardsTrio'
import ImageCardsWithOverlay from '@components/organisms/ImageCardsWithOverlay/ImageCardsWithOverlay'
import { InfoCardProps } from '@components/organisms/InfoCard/InfoCard'
import MediaWithAnimatedTextOnhover from '@components/organisms/MediaWithAnimatedTextOnhover/MediaWithAnimatedTextOnhover'
import NeoCardWithToggle from '@components/organisms/NeoCardWithToggle/NeoCardWithToggle'
import NeoProductCardsGrid from '@components/organisms/NeoProductLineup/NeoProductCardsGrid'
import AnimatedPaddedTextCardList from '@components/organisms/PaddedTextCardList/AnimatedPaddedCardList'
import { SalesCardToggleList } from '@components/organisms/SalesCardToggleList/SalesCardToggleList'
import { TrioCardProps } from '@components/organisms/TrioCard/TrioCard'
import { WaveInTheBox } from '@components/organisms/WaveInTheBox/WaveInTheBox'
import { SectionBgColor } from '@components/templates/Section/Section'
import { TextWithOverlayPages } from '@components/templates/TextWithOverlayPages/TextWithOverlayPages'
import {
    ImageType,
    VideoType
} from '@pylot-data/hooks/contentful/use-content-json'
import dynamic from 'next/dynamic'
import { FC, useMemo } from 'react'
import { FeatureCardIconProps } from '../../molecules/FeatureCardIcon/FeatureCardIcon'
import { FeatureCardProps } from '../../organisms/FeatureCard/FeatureCard'
import { CaptureCardSelector } from '../CaptureCardSelector/CaptureCardSelector'
import { FeatureProps } from '../FeatureList/FeatureList'

const InfoCardList = dynamic(
    () => import('@components/templates/InfoCardList/InfoCardList')
)
const FeatureCardList = dynamic(
    () => import('@components/organisms/FeatureCardList/FeatureCardList')
)
const ComparisonCards = dynamic(
    () => import('@components/organisms/ComparisonCard/ComparisonCards')
)
const PartnerHeader = dynamic(
    () => import('@components/templates/PartnerHeader/PartnerHeader')
)
const ProductTileLayout = dynamic(
    () => import('@components/organisms/ProductTileLayout/ProductTileLayout')
)
const PaddedTextCardList = dynamic(
    () => import('@components/organisms/PaddedTextCardList/PaddedTextCardList')
)
const CardGallery = dynamic(
    () => import('@components/organisms/CardGallery/CardGallery')
)
const SalesCardList = dynamic(
    () => import('@components/organisms/SalesCardList/SalesCardList')
)
const SalesEventCardList = dynamic(
    () => import('@components/organisms/SalesEventCardList/SalesEventCardList')
)
const BundleCardList = dynamic(
    () => import('@components/organisms/BundleCardList/BundleCardList')
)
const BundleSliderSimpleCard = dynamic(
    () =>
        import(
            '@components/organisms/BundleSliderSimpleCard/BundleSliderSimpleCard'
        )
)
const BundleSliderSimpleOverlay = dynamic(
    () =>
        import(
            '@components/organisms/BundleSliderSimpleOverlay/BundleSliderSimpleOverlay'
        )
)
const NeoCardSliderList = dynamic(
    () => import('@components/organisms/NeoCardSlider/NeoCardSliderList')
)
const BundleSliderSimple = dynamic(
    () => import('@components/organisms/BundleSliderSimple/BundleSliderSimple')
)
const CustomizableCardList = dynamic(
    () =>
        import(
            '@components/organisms/CustomizableCardList/CustomizableCardList'
        )
)
const FlippableCardSliderNeo = dynamic(
    () =>
        import(
            '@components/organisms/FlippableCardSlider/FlippableCardSliderNeo'
        )
)
const FlippableCardSlider = dynamic(
    () =>
        import('@components/organisms/FlippableCardSlider/FlippableCardSlider')
)
const ProductCardLarge = dynamic(
    () => import('@components/organisms/ProductCardLarge/ProductCardLarge')
)
const DownloadCards = dynamic(
    () => import('@components/organisms/DownloadCard/DownloadCards')
)
const AutoScrollCards = dynamic(
    () => import('@components/organisms/AutoScrollCards/AutoScrollCards')
)
const ExpandableCardList = dynamic(
    () => import('@components/organisms/ExpandableCardList/ExpandableCardList')
)
const ChallengesCardList = dynamic(
    () => import('@components/templates/ChallengesCard/ChallengesCardList')
)
const DropdownCardList = dynamic(
    () => import('@components/organisms/DropdownCardList/DropdownCardList')
)

const SalesCardsExclusiveList = dynamic(
    () =>
        import(
            '@components/organisms/SalesCardsExclusiveList/SalesCardsExclusiveList'
        )
)

const ExpandingCardsOverlay = dynamic(
    () =>
        import(
            '@components/organisms/ExpandingCardsOverlay/ExpandingCardsOverlay'
        )
)

const NeoProductCards = dynamic(
    () => import('@components/organisms/NeoProductLineup/NeoProductCards')
)

const StorePrototypeCardList = dynamic(
    () =>
        import(
            '@components/organisms/StorePrototypeCardList/StorePrototypeCardList'
        )
)

const CodCardList = dynamic(
    () => import('@components/organisms/CodCardList/CodCardList')
)

const DefaultProductCards = dynamic(
    () =>
        import('@components/organisms/DefaultProductCards/DefaultProductCards')
)

const SalesCardSliderList = dynamic(
    () =>
        import('@components/organisms/SalesCardSliderList/SalesCardSliderList')
)

const AwardsShowcase = dynamic(
    () => import('@components/organisms/AwardsShowcase/AwardsShowcase')
)

export type CustomOptionsProps = {
    cardBackgroundColor?: string
}

export type CardProps = {
    sizeCard?: string
    eventTracking?: any
    title?: string
    textHorizontalAlignment?: HorizontalAlignmentEnum
    media?: ImageType | VideoType
    cloudinaryMedia?: CloudinaryMedia[]
    neoMedia?: VideoType
    neoMobileMedia?: VideoType
    mobileMedia?: ImageType | VideoType
    cloudinaryMobileMedia?: CloudinaryMedia[]
    cloudinaryImage?: string
    icon?: string
    iconText?: string
    flippedCardPanel?: PrimaryTextProps
    children?: any[]
    contnet?: any
    textPanel?: PrimaryTextProps
    audioSrc1?: VideoType
    audioSrc2?: VideoType
    audioLabel?: string
    link?: LinkResponse
    meta?: { contentType: string }
    textColor?: string
    backgroundColor?: string
    mediaEmbedded?: string
    sku?: string
    posterImage?: ImageType
    cloudinaryPosterImage?: CloudinaryMedia[]
    posterImageMobile?: ImageType
    cloudinaryPosterImageMobile?: CloudinaryMedia[]
    id?: string
    imageSize?: string
    size?: string
    product?: any
    sizeColumn?: string
    extraLinks?: LinkResponse[]
    textVerticalAlignment?: VerticalAlignmentEnum
    hideDiscover?: boolean
    customOptions?: any
    videoOptions?: Record<string, unknown>
    cmsATCLocation?: number
    animationImage?: ImageType | VideoType
    tabletMedia?: ImageType | VideoType
    tabletMediaPoster?: ImageType
    bottomText?: boolean
    dealsProductDiscount?: number
    customConfiguration?: any
    cloudinaryNeoMedia?: CloudinaryMedia[]
    cloudinaryNeoMobileMedia?: CloudinaryMedia[]
}

export interface CardListProps {
    title?: string
    variant:
        | 'info-cards-trio'
        | 'feature-cards'
        | 'feature-cards-steps'
        | 'feature-cards-trio'
        | 'comparison-card'
        | 'comparison-card-on-image'
        | 'product-card-on-image'
        | 'product-tiles'
        | 'partner-header'
        | 'product-cards'
        | 'capture-card-selector'
        | 'padded-text-cards'
        | 'animated-padded-text-cards'
        | 'card-gallery'
        | 'sales-cards'
        | 'sales-cards-small'
        | 'sales-event-cards'
        | 'bundle-products'
        | 'bundle-slider'
        | 'product-cards-large'
        | 'download-cards'
        | 'autoscroll-cards'
        | 'expandable-cards'
        | 'challenges-cards'
        | 'dropdown-cards'
        | 'sales-cards-exclusive'
        | 'flippable-card-slider'
        | 'neo-expanding-cards-overlay'
        | 'wave-neo-cards'
        | 'wave-in-the-box'
        | 'neo-card-with-toggle'
        | 'neo-product-cards'
        | 'neo-product-cards-grid'
        | 'store-prototype-card'
        | 'text-with-overlay-pages'
        | 'sales-card-toggle-list'
        | 'cod-card'
        | 'neo-card-slider'
        | 'special-page-sales-card-list'
        | 'media-with-animated-text-on-hover'
        | 'card-comparison-with-toggle'
        | 'sales-cards-slider'
        | 'awards-showcase'
    children?: any[]
    textPanel?: PrimaryTextProps
    additionalTextPanels?: PrimaryTextProps[]
    backgroundColor?: SectionBgColor
    id?: string
    eventTracking?: any
    sizeCardWrapper?: boolean
    /** custom json object */
    customConfiguration?: any
    regions?: [
        {
            identifier: string
            name: string
        }
    ]
    headline?: string
    banners?: BannerProps[]
    meta?: {
        contentType: string
    }
    contentEntries?: any[]
    cmsATCLocation?: number
    isAnimated?: boolean
    removePadding?: boolean
    hasSlider?: boolean
    blackFridayStartDate?: string // iso date
    blackFridayEndDate?: string // iso date
    primeDealsAmazonActive?: boolean
    customClassNames?: string
    cloudinaryMedia?: CloudinaryMedia[]
    cloudinaryMobileMedia?: CloudinaryMedia[]
    cloudinaryNeoMedia?: CloudinaryMedia[]
}

export interface CardListContent {
    content: CardListProps
}

export const CardList: FC<CardListContent> = ({ content }) => {
    const {
        title,
        variant,
        sizeCardWrapper,
        children = [],
        textPanel,
        additionalTextPanels,
        backgroundColor = SectionBgColor.TRANSPARENT,
        id,
        customConfiguration,
        headline = '',
        isAnimated,
        removePadding,
        hasSlider,
        customClassNames,
        cloudinaryMedia
    } = content

    const convertToFeatureCard = (cards: CardProps[]) => {
        const featuresList: FeatureCardProps[] = []

        cards.forEach((card) => {
            const cardText = card.textPanel
            const cardChildren = card.children
            const links: LinkResponse[] = []
            const icons: FeatureCardIconProps[] = []
            cardChildren?.forEach((cardFeature) => {
                if (cardFeature.meta?.contentType === 'moleculeLink') {
                    const feature = cardFeature as LinkResponse
                    links.push(convertToStoreLink(feature))
                } else {
                    const feature = cardFeature as FeatureProps
                    if (feature.link) {
                        links.push(convertToStoreLink(feature.link))
                    }
                    if (
                        feature.icon ||
                        feature.cloudinaryImage ||
                        feature.text
                    ) {
                        icons.push(feature)
                    }
                }
            })
            featuresList.push({
                headline: cardText?.headline ?? '',
                description: cardText?.bodyCopy,
                icons: icons,
                links: links
            })
        })

        return featuresList
    }

    const getCardList = (selectedVariant: string | undefined) => {
        switch (selectedVariant) {
            case 'info-cards-trio': {
                const infoCards: InfoCardProps[] = []
                children.forEach((child: CardProps) => {
                    if (child.media || child.cloudinaryMedia) {
                        infoCards.push({
                            cloudinaryMedia: child.cloudinaryMedia,
                            icon: child.icon,
                            infoBox: {
                                calloutTitle: child.textPanel?.calloutTitle,
                                headline: child.textPanel?.headline,
                                subHeader: child.textPanel?.subheader,
                                text: child.textPanel?.bodyCopy,
                                link: child.textPanel?.link,
                                disclaimerText: child.textPanel?.disclaimerText
                            },
                            textColor: child.textColor,
                            link: child.link,
                            videoOptions: child.videoOptions,
                            cloudinaryPosterImage: child.cloudinaryPosterImage,
                            customOptions: child?.customOptions
                        })
                    }
                })
                const infoCardList = {
                    textPanel: textPanel,
                    cards: infoCards,
                    bgColor: backgroundColor,
                    id: id,
                    customConfiguration: customConfiguration
                }
                const backgroundStyle =
                    typeof customConfiguration?.background === 'string'
                        ? { background: customConfiguration.background }
                        : undefined
                return (
                    <InfoCardList
                        content={infoCardList}
                        style={backgroundStyle}
                    />
                )
            }
            case 'challenges-cards': {
                const cards = children as CardProps[]
                return (
                    <ChallengesCardList
                        cards={cards}
                        backgroundColor={backgroundColor}
                        id={id}
                    />
                )
            }
            case 'feature-cards': {
                const cards = children as CardProps[]
                const featuresList = convertToFeatureCard(cards)
                return (
                    <FeatureCardList
                        features={featuresList}
                        textProps={textPanel}
                        id={id}
                    />
                )
            }
            case 'feature-cards-steps': {
                const cards = children as CardProps[]
                const featuresList = convertToFeatureCard(cards)
                return (
                    <FeatureCardList
                        stepsList
                        features={featuresList}
                        textProps={textPanel}
                        id={id}
                    />
                )
            }
            case 'feature-cards-trio': {
                const cards = children as TrioCardProps[]
                return (
                    <FeatureCardsTrio
                        headline={headline}
                        textProps={textPanel}
                        cards={cards}
                        id={id}
                    />
                )
            }
            case 'comparison-card-on-image':
            case 'comparison-card': {
                const cards = children as CardProps[]
                return (
                    <ComparisonCards
                        cards={cards}
                        variant={
                            selectedVariant === 'comparison-card'
                                ? 'default'
                                : 'onImage'
                        }
                        textPanel={textPanel}
                        backgroundColor={backgroundColor}
                        id={id}
                        removePadding={removePadding}
                    />
                )
            }
            case 'product-card-on-image': {
                const cards = children as CardProps[]
                return (
                    <ComparisonCards
                        cards={cards}
                        variant={
                            selectedVariant.includes('product-card')
                                ? 'onImage'
                                : 'default'
                        }
                        textPanel={textPanel}
                        backgroundColor={backgroundColor}
                        id={id}
                    />
                )
            }
            case 'partner-header': {
                const cards = children as CardProps[]
                return (
                    <PartnerHeader
                        cards={cards.map((card) => {
                            const {
                                children,
                                media,
                                mobileMedia,
                                ...other
                            } = card
                            return {
                                media: media as ImageType,
                                mobileMedia: mobileMedia as ImageType,
                                featureList: children?.find(
                                    (el) =>
                                        el.meta.contentType ===
                                        'organismFeatureList'
                                ),
                                linkList: children?.find(
                                    (el) =>
                                        el.meta.contentType ===
                                        'organismLinkList'
                                ),
                                ...other
                            }
                        })}
                    />
                )
            }
            case 'product-tiles': {
                const cards = children as CardProps[]
                const getStoreButton = (links: LinkResponse[]) => {
                    const link = links.find(
                        (link) =>
                            link.style === 'apple-app-store' ||
                            link.style === 'google-play-store' ||
                            link.style === 'mac-os-store' ||
                            link.style === 'windows-store'
                    )
                    if (!link) {
                        return
                    }
                    return convertToStoreLink(link)
                }
                return (
                    <ProductTileLayout
                        tiles={cards.map((card) => {
                            const links =
                                (card?.children as LinkResponse[]) ?? []
                            return {
                                title: card.textPanel?.headline,
                                text: card.textPanel?.bodyCopy,
                                logo: card.textPanel?.logos?.[0],
                                image: card.cloudinaryMedia,
                                link: links.find(
                                    (link) => link?.style === 'primary'
                                ),
                                additionalLink: links.find(
                                    (link) => link?.style === 'tertiary'
                                ),
                                appStoreLink: getStoreButton(links)
                            }
                        })}
                        textPanel={textPanel}
                    />
                )
            }
            case 'capture-card-selector': {
                return (
                    <CaptureCardSelector
                        cards={children}
                        textPanel={textPanel}
                        questions={customConfiguration}
                    />
                )
            }
            case 'padded-text-cards': {
                return (
                    <PaddedTextCardList
                        cards={children}
                        headline={headline}
                        id={id}
                        isAnimated={isAnimated}
                    />
                )
            }
            case 'animated-padded-text-cards': {
                return (
                    <AnimatedPaddedTextCardList
                        cards={children}
                        headline={textPanel?.headline}
                        bodyCopy={textPanel?.bodyCopy}
                        disclaimerText={textPanel?.disclaimerText}
                        classes={textPanel?.classes}
                        id={id}
                    />
                )
            }
            case 'card-gallery': {
                return (
                    <CardGallery
                        cards={children}
                        textPanel={textPanel}
                        customConfiguration={customConfiguration}
                    />
                )
            }
            case 'sales-cards': {
                return (
                    <SalesCardList
                        textPanel={textPanel}
                        cards={children}
                        id={id}
                        backgroundColor={backgroundColor}
                        mobileSlider={customConfiguration?.mobileSlider}
                    />
                )
            }

            case 'bundle-product': {
                const cards = (content as unknown) as CardProps[]
                let cardsArray: CardProps[] | undefined

                if (Array.isArray(cards)) {
                    cardsArray = cards
                } else {
                    cardsArray = [cards]
                }

                return (
                    <BundleCardList
                        textPanel={textPanel}
                        cards={cardsArray}
                        id={id}
                        backgroundColor={backgroundColor}
                        mobileSlider={customConfiguration?.mobileSlider}
                        variant="bundle"
                    />
                )
            }
            case 'bundle-slider': {
                return <BundleSliderSimple cards={children} />
            }
            case 'bundle-products': {
                return (
                    <BundleCardList
                        textPanel={textPanel}
                        cards={children}
                        id={id}
                        backgroundColor={backgroundColor}
                        mobileSlider={customConfiguration?.mobileSlider}
                    />
                )
            }
            case 'customizable-card': {
                return (
                    <CustomizableCardList
                        textPanel={textPanel}
                        cards={children}
                        id={id}
                        backgroundColor={backgroundColor}
                        variant="small"
                        customConfiguration={customConfiguration}
                    />
                )
            }
            case 'store-prototype-card': {
                return (
                    <StorePrototypeCardList
                        textPanel={textPanel}
                        cards={children}
                        id={id}
                        backgroundColor={backgroundColor}
                        variant="small"
                        customConfiguration={customConfiguration}
                    />
                )
            }
            case 'cod-card': {
                const cards = children as CardProps[]
                return (
                    <CodCardList
                        cards={cards}
                        variant={
                            selectedVariant === 'cod-card'
                                ? 'default'
                                : 'onImage'
                        }
                        textPanel={textPanel}
                        backgroundColor={backgroundColor}
                        id={id}
                        removePadding={removePadding}
                    />
                )
            }
            case 'flippable-card-slider': {
                return (
                    <FlippableCardSliderNeo
                        textPanel={textPanel}
                        cards={children}
                        id={id}
                        backgroundColor={backgroundColor}
                    />
                )
            }
            case 'flippable-card-slider-regular': {
                return (
                    <FlippableCardSlider
                        textPanel={textPanel}
                        cards={children}
                        id={id}
                    />
                )
            }
            case 'wave-in-the-box': {
                return (
                    <WaveInTheBox
                        textPanel={textPanel}
                        cards={children}
                        id={id}
                        backgroundColor={backgroundColor}
                    />
                )
            }
            case 'neo-card-with-toggle': {
                return (
                    <NeoCardWithToggle
                        textPanel={textPanel}
                        cards={children}
                        id={id}
                        backgroundColor={backgroundColor}
                        key={id}
                    />
                )
            }
            case 'sales-cards-small': {
                return (
                    <SalesCardList
                        textPanel={textPanel}
                        additionalTextPanels={additionalTextPanels}
                        cards={children}
                        id={id}
                        backgroundColor={backgroundColor}
                        variant="small"
                    />
                )
            }
            case 'sales-event-cards': {
                return (
                    <SalesEventCardList
                        textPanel={textPanel}
                        cards={children}
                        id={id}
                        backgroundColor={backgroundColor}
                    />
                )
            }
            case 'neo-card-slider': {
                return (
                    <NeoCardSliderList
                        textPanel={textPanel}
                        cards={children}
                        id={id}
                        backgroundColor={backgroundColor}
                    />
                )
            }
            case 'autoscroll-cards': {
                return (
                    <AutoScrollCards
                        textPanel={textPanel}
                        cards={children}
                        id={id}
                        backgroundColor={backgroundColor}
                    />
                )
            }
            case 'product-cards-large': {
                const productCard = children[0]
                return (
                    <ProductCardLarge
                        backgroundColor={productCard.backgroundColor}
                        textPanel={productCard.textPanel}
                        link={productCard.link}
                        media={productCard.media}
                        cloudinaryMedia={productCard.cloudinaryMedia}
                        audioLabel={productCard.audioLabel}
                        customOptions={productCard?.customOptions}
                    >
                        {productCard.children}
                    </ProductCardLarge>
                )
            }
            case 'download-cards': {
                const cards = children as CardProps[]
                return (
                    <DownloadCards
                        cards={cards}
                        textPanel={textPanel}
                        backgroundColor={backgroundColor}
                        id={id}
                    />
                )
            }
            case 'expandable-cards': {
                const cards = children as CardProps[]
                return (
                    <ExpandableCardList
                        cards={cards}
                        backgroundColor={backgroundColor}
                    />
                )
            }
            case 'dropdown-cards': {
                const cards = children as CardProps[]
                return (
                    <DropdownCardList
                        cards={cards}
                        backgroundColor={backgroundColor}
                        id={id}
                    />
                )
            }
            case 'sales-cards-exclusive': {
                return (
                    <SalesCardsExclusiveList
                        textPanel={textPanel}
                        additionalTextPanels={additionalTextPanels}
                        cards={children}
                        id={id}
                        backgroundColor={backgroundColor}
                        variant="small"
                    />
                )
            }
            case 'neo-expanding-cards-overlay': {
                return (
                    <ExpandingCardsOverlay
                        textPanel={textPanel}
                        cards={children}
                        id={backgroundColor}
                    />
                )
            }
            case 'special-page-sales-card-list': {
                return (
                    <SalesCardToggleList
                        cards={children}
                        textPanel={textPanel}
                        id={id}
                        backgroundColor={backgroundColor}
                        variant="small"
                        additionalTextPanels={additionalTextPanels}
                    />
                )
            }
            case 'wave-neo-cards': {
                return (
                    <ImageCardsWithOverlay
                        textPanel={textPanel}
                        sizeCardWrapper={sizeCardWrapper}
                        cards={children}
                        backgroundColor={backgroundColor}
                        hasSlider={hasSlider}
                        customConfiguration={customConfiguration}
                        id={id}
                    />
                )
            }
            case 'neo-product-cards': {
                return (
                    <NeoProductCards
                        cards={children as CardProps[]}
                        classNames={customClassNames}
                        arrowStyle={customConfiguration?.arrowStyle}
                        capitalizeHeadlines={
                            customConfiguration?.capitalizeHeadlines
                        }
                    />
                )
            }
            case 'neo-product-cards-grid': {
                return (
                    <NeoProductCardsGrid
                        cards={children as CardProps[]}
                        classNames={customClassNames}
                        arrowStyle={customConfiguration?.arrowStyle}
                        capitalizeHeadlines={
                            customConfiguration?.capitalizeHeadlines
                        }
                    />
                )
            }
            case 'text-with-overlay-pages': {
                return <TextWithOverlayPages {...content} pages={children} />
            }
            case 'sales-card-toggle-list': {
                return (
                    <SalesCardToggleList
                        cards={children}
                        textPanel={textPanel}
                        id={id}
                        backgroundColor={backgroundColor}
                        variant="small"
                        additionalTextPanels={additionalTextPanels}
                    />
                )
            }
            case 'default-product-cards': {
                return <DefaultProductCards cards={children} />
            }
            case 'sales-cards-slider': {
                return (
                    <SalesCardSliderList
                        id={id}
                        cards={children}
                        textPanel={textPanel}
                        backgroundColor={backgroundColor}
                    />
                )
            }
            case 'awards-showcase': {
                return (
                    <AwardsShowcase textPanel={textPanel}>
                        {children}
                    </AwardsShowcase>
                )
            }

            default: {
                return null
            }
            case 'media-with-animated-text-on-hover': {
                return (
                    <MediaWithAnimatedTextOnhover
                        id={id}
                        textPanel={textPanel}
                        cards={children}
                        cloudinaryMedia={cloudinaryMedia}
                    />
                )
            }
            case 'card-comparison-with-toggle': {
                return (
                    <CardComparisonWithToggle
                        id={id}
                        textPanel={textPanel}
                        cards={children}
                    />
                )
            }
        }
    }
    return useMemo(() => getCardList(variant), [variant, title])
}

export default CardList
