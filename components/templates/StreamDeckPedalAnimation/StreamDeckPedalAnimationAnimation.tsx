import { FC, RefObject, useEffect, useRef } from 'react'
import { gsap } from 'gsap'
import { useOnScreen } from '@lib/hooks/useOnScreen'

type StreamDeckPedalAnimationAnimationProps = {
    containerRef: RefObject<HTMLElement>
    imagesContainerRef: RefObject<HTMLElement>
    deviceImageRef: RefObject<HTMLElement>
    footImageRef: RefObject<HTMLElement>
}

const animationIds = {
    zoom: 'stream-deck-pedal-animation-zoom',
    translate: 'stream-deck-pedal-animation-translate',
    h4Fade: 'stream-deck-pedal-animation-h4-fade',
    textFade: 'stream-deck-pedal-animation-text-fade'
}

const StreamDeckPedalAnimationAnimation: FC<StreamDeckPedalAnimationAnimationProps> = ({
    containerRef,
    imagesContainerRef,
    deviceImageRef,
    footImageRef
}) => {
    const zoomAnimationRef = useRef<gsap.core.Timeline | null>(null)
    const translateAnimationRef = useRef<gsap.core.Timeline | null>(null)
    const h4FadeInAnimationRef = useRef<gsap.core.Timeline | null>(null)
    const textFadeInAnimationRef = useRef<gsap.core.Timeline | null>(null)
    const footImageAnimationRef = useRef<gsap.core.Timeline | null>(null)

    const { isOnScreen } = useOnScreen(containerRef)

    useEffect(() => {
        const { current: containerRefCurrent } = containerRef
        if (
            !containerRefCurrent ||
            !imagesContainerRef.current ||
            !deviceImageRef.current ||
            !footImageRef.current
        ) {
            return
        }

        if (!zoomAnimationRef.current) {
            zoomAnimationRef.current = gsap
                .timeline({
                    scrollTrigger: {
                        trigger: containerRefCurrent,
                        invalidateOnRefresh: true,
                        scrub: 0.3,
                        start: 'top top',
                        end: () => '+=30%',
                        // markers: true,
                        id: animationIds.zoom
                    }
                })
                .to(imagesContainerRef.current, {
                    y: -200,
                    scale: 1
                })
        }

        if (!translateAnimationRef.current) {
            translateAnimationRef.current = gsap
                .timeline({
                    scrollTrigger: {
                        trigger: containerRefCurrent,
                        invalidateOnRefresh: true,
                        scrub: 0.3,
                        start: '30% top',
                        end: () => '+=50%',
                        // markers: true,
                        id: animationIds.translate
                    }
                })
                .to(imagesContainerRef.current, {
                    y: 10
                })
        }

        const calloutTitle = containerRefCurrent.querySelector('h4')
        if (!h4FadeInAnimationRef.current) {
            h4FadeInAnimationRef.current = gsap
                .timeline({
                    scrollTrigger: {
                        trigger: containerRefCurrent,
                        invalidateOnRefresh: true,
                        scrub: 0.3,
                        start: '10% top',
                        end: () => '+=50%',
                        // markers: true,
                        id: animationIds.h4Fade
                    }
                })
                .to(calloutTitle, {
                    opacity: 1
                })
        }

        const bodyCopy = containerRefCurrent.querySelector('.body-copy')
        const h2 = containerRefCurrent.querySelector('h2')
        if (bodyCopy && h2 && !textFadeInAnimationRef.current) {
            textFadeInAnimationRef.current = gsap
                .timeline({
                    scrollTrigger: {
                        trigger: containerRefCurrent,
                        invalidateOnRefresh: true,
                        scrub: 0.3,
                        start: '30% top',
                        end: () => '+=50%',
                        // markers: true,
                        id: animationIds.textFade
                    }
                })
                .to([bodyCopy, h2], {
                    opacity: 1
                })
        }

        if (!footImageAnimationRef.current) {
            footImageAnimationRef.current = gsap
                .timeline({
                    scrollTrigger: {
                        trigger: containerRefCurrent,
                        invalidateOnRefresh: true,
                        scrub: 0.3,
                        start: '40% top',
                        end: () => '+=60%'
                    }
                })
                .to(footImageRef.current, {
                    y: 0,
                    x: 0
                })
        }
    }, [containerRef, deviceImageRef, footImageRef, imagesContainerRef])

    useEffect(() => {
        if (isOnScreen) {
            zoomAnimationRef.current?.scrollTrigger?.refresh()
            translateAnimationRef.current?.scrollTrigger?.refresh()
            h4FadeInAnimationRef.current?.scrollTrigger?.refresh()
            textFadeInAnimationRef.current?.scrollTrigger?.refresh()
            footImageAnimationRef.current?.scrollTrigger?.refresh()
        }
    }, [isOnScreen])

    useEffect(() => {
        return () => {
            zoomAnimationRef.current?.kill()
            translateAnimationRef.current?.kill()
            h4FadeInAnimationRef.current?.kill()
            textFadeInAnimationRef.current?.kill()
            footImageAnimationRef.current?.kill()

            zoomAnimationRef.current = null
            translateAnimationRef.current = null
            h4FadeInAnimationRef.current = null
            textFadeInAnimationRef.current = null
            footImageAnimationRef.current = null
        }
    }, [])

    return null
}

export default StreamDeckPedalAnimationAnimation
