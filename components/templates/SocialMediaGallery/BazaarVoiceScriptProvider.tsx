import { SUPPORTED_BAZAARVOICE_LANGS } from '@components/molecules/BazaarComponents/BazaarLoader'
import { defaultLocale } from '@config/hooks/useStoreConfig'
import { useRouter } from 'next/router'
import { createContext, FC, useContext, useEffect, useState } from 'react'

const initialState = {
    isReady: false,
    hasLoaded: false
}

declare global {
    interface Window {
        crl8: any
    }
}

type StateType = typeof initialState
// Context.
export const BazaarVoiceScriptContext = createContext<StateType>(initialState)

// Create a custom hook to use the context.
export const useBazaarVoiceScriptContext = () =>
    useContext(BazaarVoiceScriptContext)

// Provider of context.
// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
const BazaarVoiceScriptProvider: FC = ({ children }) => {
    const { events } = useRouter()
    const [hasLoaded, setHasLoaded] = useState(false)
    const [isReady, setIsReady] = useState(false)
    const router = useRouter()
    const { locale = defaultLocale } = router
    const language =
        locale && locale.includes('-') ? locale.split('-')[0] : 'en'
    let lang = 'en_US'
    if (Object.keys(SUPPORTED_BAZAARVOICE_LANGS).includes(language)) {
        // @ts-ignore
        lang = SUPPORTED_BAZAARVOICE_LANGS[language]
    }
    const zone = 'main_site'
    /**
     * Extra security measure to check if the script has
     * already been included in the DOM
     */
    const scriptAlreadyExists = () => {
        return document.querySelector('script#bazaarvoice') !== null
    }
    const crl8ScriptAlreadyExists = () => {
        return document.querySelector('script#crl8Sitename') !== null
    }

    /**
     * Append the script to the document.
     * Whenever the script has been loaded it will
     * set the isLoaded state to true.
     */
    const appendBazaarVoiceScript = () => {
        // TODO this is the same as in BazaarLoader.tsx
        let env = 'production'
        if (
            document.location.host.includes('staging') ||
            document.location.host.includes('dev') ||
            document.location.host.includes('localhost')
        ) {
            env = 'staging'
        }
        const head =
            document.getElementsByTagName('head')[0] || document.documentElement
        const script = document.createElement('script')
        script.id = 'bazaarvoice'
        script.src = `https://apps.bazaarvoice.com/deployments/elgato/${zone}/${env}/${lang}/bv.js`
        script.type = 'text/javascript'
        script.charset = 'utf-8'
        script.setAttribute('async', 'async')
        script.onload = () => {
            setHasLoaded(true)
        }
        setTimeout(function () {
            head.insertBefore(script, head.firstChild)
        }, 2000)
    }
    const appendCrl8SitenameScript = () => {
        const head =
            document.getElementsByTagName('head')[0] || document.documentElement
        const crl8SitenameScript = document.createElement('script')
        crl8SitenameScript.type = 'text/javascript'
        //crl8SitenameScript.innerHTML = `var CRL8_SITENAME = 'elgato-zi4hmw'; !function(){var e=window.crl8=window.crl8||{},n=!1,i=[];e.ready=function(e){n?e():i.push(e)},e.pixel=e.pixel||function(){e.pixel.q.push(arguments)},e.pixel.q=e.pixel.q||[];var t=window.document,o=t.createElement("script"),c=e.debug||-1!==t.location.search.indexOf("crl8-debug=true")?"js":"min.js";o.async=!0,o.id="curalate",o.src=t.location.protocol+"//edge.curalate.com/sites/"+CRL8_SITENAME+"/site/latest/site."+c,o.onload=function(){n=!0,i.forEach(function(e){e()})};var r=t.getElementsByTagName("script")[0];r.parentNode.insertBefore(o,r.nextSibling)}();`
        crl8SitenameScript.innerHTML = `var CRL8_SITENAME = 'elgato-zi4hmw'; !function(){var e=window.crl8=window.crl8||{},n=!1,i=[];e.ready=function(e){n?e():i.push(e)},e.pixel=e.pixel||function(){e.pixel.q.push(arguments)},e.pixel.q=e.pixel.q||[];var t=window.document,o=t.createElement("script"),c=e.debug||-1!==t.location.search.indexOf("crl8-debug=true")?"js":"min.js";o.async=!0,o.id="curalate",o.src="/scripts/curalate-site.js",o.onload=function(){n=!0,i.forEach(function(e){e()})};var r=t.getElementsByTagName("script")[0];r.parentNode.insertBefore(o,r.nextSibling)}();`
        crl8SitenameScript.id = 'crl8Sitename'
        head.insertBefore(crl8SitenameScript, head.firstChild)
    }
    /**
     * Runs first time when component is mounted
     * and adds the script to the document.
     */
    useEffect(() => {
        const appendScripts = () => {
            if (!crl8ScriptAlreadyExists()) {
                appendCrl8SitenameScript()
            } else {
                if (typeof window !== undefined && window.crl8) {
                    window.crl8 = window.crl8 || {}
                    if (window.crl8.destroyExperience) {
                        // we need to destroy the current community slider and recreate it
                        try {
                            window.crl8
                                .destroyExperience('product')
                                .then(() => {
                                    window.crl8.createExperience('product')
                                })
                        } catch (error) {
                            console.error(
                                'BazaarVoiceScriptProvider Error creating experience'
                            )
                        }
                    }
                }
            }
            if (!scriptAlreadyExists()) {
                appendBazaarVoiceScript()
            }
        }
        if (typeof window !== undefined) {
            appendScripts()
            const handleRouteChangeComplete = () => {
                appendScripts()
            }
            events.on('routeChangeComplete', handleRouteChangeComplete)
            return () => {
                events.off('routeChangeComplete', handleRouteChangeComplete)
            }
        }
    }, [events])

    /**
     * Whenever the script has loaded initialize the
     * script with the init method. This will then set
     * the isReady state to true and passes that
     * through the context to the consumers.
     */
    useEffect(() => {
        if (hasLoaded) {
            setIsReady(true)
        }
    }, [hasLoaded])

    return (
        <BazaarVoiceScriptContext.Provider value={{ isReady, hasLoaded }}>
            {children}
        </BazaarVoiceScriptContext.Provider>
    )
}

export default BazaarVoiceScriptProvider
