.dropdown-compare-product-list {
    width: 100%;
}

.feature-grid-wrapper {
    display: flex;
    flex-direction: column;
}

.feature-category {
    &__title {
        &:hover {
            color: var(--black);
        }
    }
}

.feature-category__header {
    padding: 16px 20px;
    border-radius: 16px;
    background-color: var(--primitive-gray-10);
    transition: background-color 0.2s ease;

    &:hover {
        background-color: var(--primitive-gray-20);
    }

    &:focus {
        outline: 2px solid var(--primitive-blue-500);
        outline-offset: -2px;
    }
}

.feature-category__chevron {
    transition: transform 0.2s ease;
    color: var(--primitive-gray-600);
    flex-shrink: 0;
}

.feature-category__chevron--expanded {
    transform: rotate(90deg);
}

.feature-rows-container {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.feature-rows-container--expanded {
    max-height: 2000px; // Large enough to accommodate content
}

.feature-row {
    display: grid;
    align-items: center;
    border-bottom: 1px solid var(--primitive-gray-30);
    padding: 24px;

    &:last-child {
        border-bottom: none;
    }
}

.feature-cell {
    display: flex;
    align-items: center;
    background-color: var(--primitive-white);

    &:first-child {
        justify-content: flex-start;
    }
    &:not(:first-child) {
        justify-content: center;
    }
}

// Dark theme
.dark {
    .feature-category {
        border-color: var(--primitive-gray-700);
    }

    .feature-category__header {
        background-color: var(--primitive-gray-800);

        &:hover {
            background-color: var(--primitive-gray-700);
        }
    }

    .feature-category__chevron {
        color: var(--primitive-gray-500);
    }

    .feature-row {
        border-bottom-color: var(--primitive-gray-700);
    }
    .feature-cell {
        background-color: transparent;
    }
    .dropdown-compare-product-list__add-slot {
        background: var(--primitive-gray-800);
        border-color: var(--primitive-gray-600);

        &:hover {
            border-color: var(--primitive-blue-400);
            background: var(--primitive-gray-700);
        }
    }
    .dropdown-compare-product-list__slot__remove-btn {
        border-color: var(--primitive-gray-400);
        &:hover {
            background: var(--primitive-gray-800);
        }
    }
}
