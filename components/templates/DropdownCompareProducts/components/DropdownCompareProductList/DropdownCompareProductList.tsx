import ChevronRightIcon from '@components/atoms/Icon/general/ChevronRightIcon'
import { useLayoutContext } from '@components/layouts/MainLayout/LayoutContext'
import { Button } from '@components/molecules/Button/Button'
import { ProductProps } from '@components/templates/CompareProducts/components/CompareProductList/CompareProductList'
import { defaultLocale } from '@config/index'
import { getProducts, Products } from '@pylot-data/api/operations/get-products'
import cn from 'classnames'
import { useTranslation } from 'next-i18next'
import { useRouter } from 'next/router'
import React, { FC, useCallback, useEffect, useMemo, useState } from 'react'
import DropdownCompareProduct from '../DropdownCompareProduct/DropdownCompareProduct'
import DropdownCompareProductCategorySectionFeature from '../DropdownCompareProductCategorySectionFeature/DropdownCompareProductCategorySectionFeature'
import ProductSelector from '../ProductSelector/ProductSelector'
import s from './DropdownCompareProductList.module.scss'

export interface DropdownCompareProductListProps {
    content: ProductProps[]
    maxComparisons: number
    initialProductCount: number
}

export interface AddProductSlotProps {
    onAddProduct: () => void
    canAddMore: boolean
    disabled?: boolean
}

export interface SelectedProduct {
    slot: number
    product: ProductProps | null
}

// Helper to normalize feature keys
const normalize = (str: string) => (str || '').trim().toLowerCase()

const getFeatureForProduct = (
    product: any,
    categoryTitle: string,
    featureHeadline: string
) => {
    if (!product || !product.productCategory) return null

    const category = product.productCategory.find(
        (c: any) => c.categoryTitle === categoryTitle
    )
    if (!category) return null

    const features = category.feature || category.featureList || []
    const isNested = features.length > 0 && Array.isArray(features[0]?.features)
    const flatFeatures = isNested
        ? features.flatMap((f: any) => f.features)
        : features

    const key = normalize(featureHeadline)
    return flatFeatures.find((f: any) => normalize(f.headline) === key) || null
}

const DropdownCompareProductList: FC<DropdownCompareProductListProps> = ({
    content,
    maxComparisons,
    initialProductCount
}) => {
    const { t } = useTranslation(['common'])
    const { pageTheme } = useLayoutContext()
    const { locale = 'en' } = useRouter()
    const [productsData, setProductsData] = useState<Products[]>([])

    const [selectedProducts, setSelectedProducts] = useState<SelectedProduct[]>(
        () => {
            const slots: SelectedProduct[] = []
            for (
                let i = 0;
                i <
                Math.min(initialProductCount, content.length, maxComparisons);
                i++
            ) {
                slots.push({
                    slot: i,
                    product: content[i] || null
                })
            }
            return slots
        }
    )

    const [activeSlots, setActiveSlots] = useState<number>(initialProductCount)

    useEffect(() => {
        if (content && content.length > 0) {
            getProducts(
                content.map((product: any) => product?.sku),
                null,
                locale || defaultLocale || 'en-US'
            ).then((products) => {
                if (products && products.length) {
                    setProductsData(products)
                }
            })
        }
    }, [locale, content])

    const handleProductSelect = useCallback(
        (slotIndex: number, product: any) => {
            setSelectedProducts((prev) => {
                const updated = [...prev]
                const existingSlot = updated.find((sp) => sp.slot === slotIndex)
                if (existingSlot) {
                    existingSlot.product = product
                } else {
                    updated.push({ slot: slotIndex, product })
                }
                return updated
            })
        },
        []
    )

    const handleAddProductSlot = useCallback(() => {
        if (activeSlots < maxComparisons && activeSlots < content.length) {
            const newSlotIndex = activeSlots
            setActiveSlots((prev) => prev + 1)
            setSelectedProducts((prev) => {
                const updated = [...prev]
                if (!updated.find((sp) => sp.slot === newSlotIndex)) {
                    updated.push({ slot: newSlotIndex, product: null })
                }
                return updated
            })
        }
    }, [activeSlots, maxComparisons, content.length])

    const handleRemoveProductSlot = useCallback(
        (slotIndex: number) => {
            if (activeSlots > 2) {
                setSelectedProducts((prev) =>
                    prev.filter((sp) => sp.slot !== slotIndex)
                )
                setActiveSlots((prev) => prev - 1)
            }
        },
        [activeSlots]
    )

    const canAddMore =
        activeSlots < maxComparisons && activeSlots < content.length
    const canRemove = activeSlots > 2

    const productsToRender = useMemo(() => {
        return Array.from({ length: activeSlots }, (_, index) => {
            return (
                selectedProducts.find((sp) => sp.slot === index)?.product ||
                null
            )
        })
    }, [selectedProducts, activeSlots])

    // Create a unified list of all features from all products
    const unifiedFeatureData = useMemo(() => {
        const categoriesMap = new Map()

        for (const product of content) {
            if (!product.productCategory) continue

            for (const category of product.productCategory) {
                const categoryTitle = category.categoryTitle
                if (!categoryTitle) continue

                if (!categoriesMap.has(categoryTitle)) {
                    categoriesMap.set(categoryTitle, {
                        categoryTitle,
                        id: category.id,
                        features: new Map()
                    })
                }

                const featuresMap = categoriesMap.get(categoryTitle).features
                const featureList =
                    category.feature || category.featureList || []
                const isNested =
                    featureList.length > 0 &&
                    Array.isArray(featureList[0]?.features)
                const allFeatures = isNested
                    ? featureList.flatMap((fl: any) => fl.features)
                    : featureList

                for (const feature of allFeatures) {
                    const headline = feature.headline || feature.headLine
                    const key = normalize(headline)
                    if (key && !featuresMap.has(key)) {
                        featuresMap.set(key, feature)
                    }
                }
            }
        }

        return Array.from(categoriesMap.values()).map((categoryData: any) => ({
            ...categoryData,
            features: Array.from(categoryData.features.values())
        }))
    }, [content])

    const initialExpandedCategories = useMemo(() => {
        return new Set(unifiedFeatureData.map((category: any) => category.id))
    }, [unifiedFeatureData])

    const [expandedCategories, setExpandedCategories] = useState<Set<string>>(
        initialExpandedCategories
    )

    const toggleCategory = useCallback((categoryId: string) => {
        setExpandedCategories((prev) => {
            const newSet = new Set(prev)
            if (newSet.has(categoryId)) {
                newSet.delete(categoryId)
            } else {
                newSet.add(categoryId)
            }
            return newSet
        })
    }, [])

    return (
        <section className={cn(s['dropdown-compare-product-list'])}>
            {/* Product Header Selection */}
            <div
                className={cn(
                    s['dropdown-compare-product-list__grid'],
                    s['dropdown-compare-product-list__grid-header'],
                    'grid'
                )}
                style={{
                    gridTemplateColumns: `repeat(${activeSlots}, 2fr)${
                        canAddMore ? ' 1fr' : ''
                    }`
                }}
            >
                {Array.from({ length: activeSlots }).map((_, index) => (
                    <div
                        key={index}
                        className={s['dropdown-compare-product-list__slot']}
                    >
                        <div
                            className={
                                s['dropdown-compare-product-list__slot__header']
                            }
                        >
                            <ProductSelector
                                availableProducts={content}
                                selectedProducts={selectedProducts}
                                onProductSelect={handleProductSelect}
                                slotIndex={index}
                                placeholder={t('Select a product to compare')}
                            />
                            {canRemove && (
                                <Button
                                    variant="tertiary"
                                    color={
                                        pageTheme === 'dark' ? 'light' : 'dark'
                                    }
                                    onClick={() =>
                                        handleRemoveProductSlot(index)
                                    }
                                    label={t('Remove')}
                                    className={
                                        s[
                                            'dropdown-compare-product-list__slot__remove-btn'
                                        ]
                                    }
                                >
                                    ×
                                </Button>
                            )}
                        </div>

                        {productsToRender[index] && (
                            <DropdownCompareProduct
                                product={productsToRender[index] as any}
                                productsData={productsData}
                            />
                        )}
                    </div>
                ))}
                {canAddMore && (
                    <div
                        className={cn(
                            s['dropdown-compare-product-list__add-slot']
                        )}
                    >
                        <Button
                            variant="secondary"
                            color={pageTheme === 'dark' ? 'light' : 'dark'}
                            onClick={handleAddProductSlot}
                            label={t('Add Product')}
                        >
                            + {t('Add Product')}
                        </Button>
                    </div>
                )}
            </div>

            {/* Feature Comparison Grid */}
            <div className={s['feature-grid-wrapper']}>
                {unifiedFeatureData.map((category: any) => {
                    const isExpanded = expandedCategories.has(category.id)
                    return (
                        <div
                            key={category.id}
                            className={cn(s['feature-category'], 'mb-8')}
                        >
                            <div
                                className={cn(
                                    s['feature-category__header'],
                                    'flex items-center gap-6px bg-primitive-gray-10 cursor-pointer user-select-none'
                                )}
                                onClick={() => toggleCategory(category.id)}
                                role="button"
                                tabIndex={0}
                                onKeyDown={(e) => {
                                    if (e.key === 'Enter' || e.key === ' ') {
                                        e.preventDefault()
                                        toggleCategory(category.id)
                                    }
                                }}
                            >
                                <ChevronRightIcon
                                    className={cn(
                                        s['feature-category__chevron'],
                                        {
                                            [s[
                                                'feature-category__chevron--expanded'
                                            ]]: isExpanded
                                        }
                                    )}
                                />
                                <h5
                                    className={cn(
                                        s['feature-category__title'],
                                        'text-primitive-gray-90'
                                    )}
                                >
                                    {category.categoryTitle}
                                </h5>
                            </div>
                            <div
                                className={cn(s['feature-rows-container'], {
                                    [s[
                                        'feature-rows-container--expanded'
                                    ]]: isExpanded
                                })}
                            >
                                {category.features.map((feature: any) => (
                                    <div
                                        key={
                                            feature.headline || feature.headLine
                                        }
                                        className={cn(s['feature-row'])}
                                        style={{
                                            gridTemplateColumns: `2fr repeat(${activeSlots}, 1fr)`
                                        }}
                                    >
                                        {/* Headline Column */}
                                        <div className={s['feature-cell']}>
                                            <DropdownCompareProductCategorySectionFeature
                                                feature={feature}
                                                displayMode="headline"
                                            />
                                        </div>

                                        {/* Product Value Columns */}
                                        {Array.from({
                                            length: activeSlots
                                        }).map((_, pIndex) => {
                                            const product =
                                                productsToRender[pIndex]
                                            const productFeature = getFeatureForProduct(
                                                product,
                                                category.categoryTitle,
                                                feature.headline
                                            )
                                            return (
                                                <div
                                                    key={pIndex}
                                                    className={
                                                        s['feature-cell']
                                                    }
                                                >
                                                    {product && (
                                                        <DropdownCompareProductCategorySectionFeature
                                                            feature={
                                                                productFeature
                                                            }
                                                            displayMode="value"
                                                        />
                                                    )}
                                                </div>
                                            )
                                        })}
                                    </div>
                                ))}
                            </div>
                        </div>
                    )
                })}
            </div>
        </section>
    )
}

export default React.memo(DropdownCompareProductList)
