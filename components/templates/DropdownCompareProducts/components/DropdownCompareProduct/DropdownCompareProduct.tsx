import ExternalLinkIcon from '@components/atoms/Icon/general/ExternalLinkIcon'
import ElgatoImage from '@components/common/ElgatoImage'
import { useLayoutContext } from '@components/layouts/MainLayout/LayoutContext'
import { ProductAddToCart } from '@components/molecules/ProductAddToCart/ProductAddToCart'
import { ProductPrice } from '@components/molecules/ProductPrice/ProductPrice'
import { ProductProps } from '@components/templates/CompareProducts/components/CompareProductList/CompareProductList'
import { usePrice } from '@corsairitshopify/pylot-price'
import { useMedia } from '@lib/hooks/useMedia'
import { Products } from '@pylot-data/api/operations/get-products'
import cn from 'classnames'
import { useTranslation } from 'next-i18next'
import React, { FC } from 'react'
import s from './DropdownCompareProduct.module.scss'

export interface DropdownCompareProductProps {
    product: ProductProps
    productsData: Products[]
}

const DropdownCompareProduct: FC<DropdownCompareProductProps> = ({
    product,
    productsData
}) => {
    const { t } = useTranslation(['common'])
    const { pageTheme } = useLayoutContext()

    console.log('productsData', productsData)
    const selectedProductData = productsData.find(
        (p) => p.productSku === product.sku
    )

    const { subtotal, total } = usePrice(
        selectedProductData?.productData?.[0]?.price_range
    )
    const productUrl = selectedProductData?.productData?.[0]?.url_key

    const { src, alt, width, height } = useMedia({
        cloudinaryMedia: product?.cloudinaryMedia
    })

    if (!product) {
        return (
            <div
                className={cn(
                    s['dropdown-compare-product'],
                    s['dropdown-compare-product--empty']
                )}
            />
        )
    }

    // Handle loading state while product data is being fetched
    if (!selectedProductData) {
        return (
            <div className={s['dropdown-compare-product__loading']}>
                <div className={s['spinner']} />
            </div>
        )
    }

    const link = product?.link
    const linkTitle = link?.linkTitle
    const linkUrl = !link?.linkUrl ? `/p/${productUrl}` : link?.linkUrl
    const hideBuyButton = product?.hideBuyButton
    const cloudinaryMedia = product?.cloudinaryMedia

    const productImage = selectedProductData?.productData?.[0]?.image?.url
    const displayName = selectedProductData?.productData?.[0]?.name || ''
    const mediaFallback = src ? src : productImage

    const renderPriceAndAddToCart = () => {
        if (
            selectedProductData &&
            selectedProductData?.productData?.[0] &&
            subtotal &&
            parseFloat(subtotal.replace(/[^\d.-]/g, '')) !== 0 &&
            total &&
            parseFloat(total.replace(/[^\d.-]/g, ''))
        ) {
            return (
                <ProductPrice
                    className={
                        s['dropdown-compare-product__product-price-wrapper']
                    }
                    product={selectedProductData?.productData?.[0]}
                    theme={pageTheme === 'dark' ? 'light' : 'dark'}
                    inNav={false}
                />
            )
        } else {
            return (
                <div
                    className={
                        s['dropdown-compare-product__empty-price-wrapper']
                    }
                />
            )
        }
    }

    const renderLink = () => {
        if (linkUrl) {
            return (
                <div
                    className={cn(
                        s['dropdown-compare-product__link__wrapper'],
                        'text-center'
                    )}
                >
                    <a
                        href={linkUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className={cn(
                            s['dropdown-compare-product__link'],
                            'absolute top-8px right-8px'
                        )}
                    >
                        <ExternalLinkIcon />
                    </a>
                </div>
            )
        }
        return null
    }

    return (
        <div className={cn(s['dropdown-compare-product'])}>
            <div
                className={cn(
                    s['dropdown-compare-product__media'],
                    'bg-primitive-gray-10 rounded-xxxl',
                    {
                        [s['dropdown-compare-product__media--dark']]:
                            pageTheme === 'dark'
                    }
                )}
            >
                <ElgatoImage
                    src={mediaFallback || ''}
                    alt={alt || ''}
                    width={width}
                    height={height}
                    objectFit="cover"
                />
                {renderLink()}
            </div>

            <div
                className={cn(
                    'flex justify-center items-center gap-4 flex-col lg:flex-row lg:gap-10',
                    s['dropdown-compare-product__price-and-buy'],
                    {
                        'my-8px': hideBuyButton
                    },
                    {
                        'my-16px': !hideBuyButton
                    }
                )}
            >
                {renderPriceAndAddToCart()}
            </div>

            {!hideBuyButton && (
                <div
                    className={cn(
                        s['dropdown-compare-product__price-and-buy__wrapper'],
                        'flex items-center md-max:flex-col justify-center gap-12px md:gap-24px'
                    )}
                >
                    <ProductAddToCart
                        id={`dropdown-compare-product-atc-btn-${selectedProductData?.productData?.[0]?.uid}`}
                        product={selectedProductData?.productData?.[0]}
                        buttonLabel={t('Buy')}
                    />
                </div>
            )}
        </div>
    )
}

export default React.memo(DropdownCompareProduct)
