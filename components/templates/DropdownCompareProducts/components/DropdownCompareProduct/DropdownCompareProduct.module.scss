.dropdown-compare-product {
    display: flex;
    flex-direction: column;
    height: 100%;

    &--empty {
        background-color: var(--primitive-gray-10);
        border-radius: 8px;
        min-height: 300px;
    }

    &__loading {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 300px;
        width: 100%;
    }

    .spinner {
        border: 4px solid rgba(0, 0, 0, 0.1);
        border-left-color: var(--primitive-blue-500);
        border-radius: 50%;
        width: 40px;
        height: 40px;
        animation: spin 1s linear infinite;
    }

    &__media {
        width: 100%;
        height: 120px;
        margin-bottom: 16px;
        position: relative;

        @screen md {
            height: 160px;
        }

        &--dark {
            background: var(--primitive-gray-900);
        }

        img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
    }

    &__name {
        margin-bottom: 8px;

        &--light {
            color: var(--primitive-gray-900);
        }

        &--dark {
            color: var(--primitive-white);
        }
    }

    &__body-copy {
        margin-bottom: 16px;
        font-size: 14px;
        line-height: 1.4;
        color: var(--primitive-gray-600);
        min-height: 60px;
    }

    &__price-and-buy {
        margin-top: auto;
        padding-top: 16px;

        &__wrapper {
            margin-top: 16px;
        }
    }

    &__product-price-wrapper {
        display: flex;
        justify-content: center;
        align-items: center;
    }

    &__empty-price-wrapper {
        height: 24px; // Match the height of ProductPrice
    }
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.dark {
    .dropdown-compare-product {
        &--empty {
            background-color: var(--primitive-gray-800);
        }
        &__body-copy {
            color: var(--primitive-gray-400);
        }
    }
    .spinner {
        border-left-color: var(--primitive-blue-400);
    }
}
