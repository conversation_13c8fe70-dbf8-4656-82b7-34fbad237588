import { useLayoutContext } from '@components/layouts/MainLayout/LayoutContext'
import cn from 'classnames'
import { FC } from 'react'
import DropdownCompareProductCategorySectionFeature from '../DropdownCompareProductCategorySectionFeature/DropdownCompareProductCategorySectionFeature'

interface DropdownCompareProductCategoryContent {
    features: any
    slotIndex: number
    product: any
}

const DropdownCompareProductCategorySectionFeatures: FC<DropdownCompareProductCategoryContent> = ({
    features,
    slotIndex,
    product
}) => {
    const { pageTheme } = useLayoutContext()

    // Determine if we have nested lists (featureList w/ features) or flat list
    const isNested =
        Array.isArray(features) &&
        features.length > 0 &&
        Array.isArray(features[0]?.features)

    const renderFlat = () => (
        <div className="space-y-8px">
            {features?.map((feature: any, idx: number) => (
                <DropdownCompareProductCategorySectionFeature
                    key={idx}
                    feature={feature}
                    displayMode={slotIndex === 0 ? 'headline' : 'value'}
                />
            ))}
        </div>
    )

    const renderNested = () => (
        <div>
            {features?.map((featureList: any, index: number) => (
                <div key={index} className="space-y-8px">
                    {featureList.features?.map(
                        (feature: any, featureIndex: number) => (
                            <DropdownCompareProductCategorySectionFeature
                                key={featureIndex}
                                feature={feature}
                                displayMode={
                                    slotIndex === 0 ? 'headline' : 'value'
                                }
                            />
                        )
                    )}
                </div>
            ))}
        </div>
    )

    return (
        <section className={cn('flex flex-col gap-12px')}>
            {isNested ? renderNested() : renderFlat()}
        </section>
    )
}

export default DropdownCompareProductCategorySectionFeatures
