.dropdown-compare-product-category-section-feature {
    min-height: 40px;
    transition: all 0.2s ease;

    &--empty,
    .feature-placeholder {
        color: var(--primitive-gray-500);
        font-size: 14px;
        justify-content: center;
        width: 100%;
        display: flex;
    }

    &--highlighted {
        align-items: flex-start;
        min-height: 46px;
        border-radius: 6px;
        background: var(--primitive-white);
        box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.1),
            0px 4px 8px 0px rgba(0, 0, 0, 0.1),
            0px 8px 16px 0px rgba(0, 0, 0, 0.05);
    }

    &--dark {
        color: var(--primitive-white);
        &.dropdown-compare-product-category-section-feature--highlighted {
            background: var(--primitive-blue-900);
        }
    }

    &__main {
        display: flex;
        align-items: center;
        gap: 8px;
        width: 100%;
        line-height: 1.4;
    }

    &__icon {
        svg {
            height: 30px;
            width: fit-content;
        }
    }
}

.compatibility-icon {
    flex-shrink: 0;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    font-size: 14px;
    font-weight: bold;
    margin-left: auto;

    &--yes {
        background: var(--primitive-green-100);
        color: var(--primitive-white);
    }

    &--no {
        background: var(--primitive-red-100);
        color: var(--primitive-white);
    }

    .dark & {
        &--yes {
            background: var(--primitive-green-400);
        }
        &--no {
            background: var(--primitive-red-400);
        }
    }
}
