import CheckmarkIcon from '@components/atoms/Icon/pictograms/CheckmarkIcon'
import { useLayoutContext } from '@components/layouts/MainLayout/LayoutContext'
import { FeatureIconText } from '@components/molecules/FeatureIconText/FeatureIconText'
import cn from 'classnames'
import { decode } from 'he'
import { FC } from 'react'
import s from './DropdownCompareProductCategorySectionFeature.module.scss'

interface DropdownCompareProductCategoryContent {
    feature: any
    displayMode: 'headline' | 'value'
}

const DropdownCompareProductCategorySectionFeature: FC<DropdownCompareProductCategoryContent> = ({
    feature,
    displayMode
}) => {
    const { pageTheme } = useLayoutContext()

    if (!feature) {
        return (
            <div
                className={cn(
                    s['dropdown-compare-product-category-section-feature'],
                    s[
                        'dropdown-compare-product-category-section-feature--empty'
                    ]
                )}
            >
                —
            </div>
        )
    }

    const featureInfo = feature?.featureInfo
    const isCompatible = feature?.isCompatible
    const highlight = feature?.highlight
    const headline = feature?.headline
    const bodyCopy = feature?.bodyCopy
    const icon = feature?.icon

    const renderHeadline = () => (
        <div
            className={cn(
                s['dropdown-compare-product-category-section-feature__main']
            )}
        >
            <div
                className={cn(
                    s[
                        'dropdown-compare-product-category-section-feature__content'
                    ]
                )}
            >
                {headline && (
                    <div className="text-body-copy-md-max md:text-body-copy font-bold">
                        {decode(headline)}
                    </div>
                )}
                {bodyCopy && (
                    <div className="text-small-copy-md-max md:text-small-copy">
                        {decode(bodyCopy)}
                    </div>
                )}
            </div>
        </div>
    )

    const renderValue = () => {
        if (featureInfo && typeof featureInfo === 'string') {
            return (
                <div className="text-center flex flex-col w-full text-body-copy-md-max md:text-body-copy">
                    {icon && (
                        <FeatureIconText
                            icon={icon}
                            className={
                                s[
                                    'dropdown-compare-product-category-section-feature__icon'
                                ]
                            }
                        />
                    )}
                    {decode(featureInfo)}
                </div>
            )
        }

        if (isCompatible === true) {
            return <CheckmarkIcon />
        } else {
            return <div className={s['feature-placeholder']}>—</div>
        }
    }

    return (
        <div
            className={cn(
                s['dropdown-compare-product-category-section-feature'],
                'p-8px w-full flex items-center justify-center',
                {
                    [s[
                        'dropdown-compare-product-category-section-feature--highlighted'
                    ]]: highlight,
                    [s[
                        'dropdown-compare-product-category-section-feature--dark'
                    ]]: pageTheme === 'dark'
                }
            )}
        >
            {displayMode === 'headline' ? renderHeadline() : renderValue()}
            {highlight && (
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="14"
                    height="12"
                    viewBox="0 0 14 12"
                    fill="none"
                >
                    <path
                        d="M6.39173 0.365029C6.64055 -0.121676 7.35945 -0.121677 7.60827 0.365028L9.24922 3.57487L12.9185 4.08959C13.4749 4.16764 13.697 4.8277 13.2944 5.20654L10.6393 7.70506L11.2661 11.233C11.3611 11.768 10.7795 12.1759 10.2819 11.9233L7 10.2577L3.71811 11.9233C3.22048 12.1759 2.63887 11.768 2.73391 11.233L3.36069 7.70506L0.705585 5.20654C0.302993 4.8277 0.525148 4.16764 1.08152 4.08959L4.75078 3.57487L6.39173 0.365029Z"
                        fill="#204CFE"
                    />
                </svg>
            )}
        </div>
    )
}

export default DropdownCompareProductCategorySectionFeature
