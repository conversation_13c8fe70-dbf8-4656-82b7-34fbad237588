import { useLayoutContext } from '@components/layouts/MainLayout/LayoutContext'
import {
    Dropdown,
    DropdownLabelValue
} from '@components/molecules/Dropdown/Dropdown'
import { ProductProps } from '@components/templates/CompareProducts/components/CompareProductList/CompareProductList'
import { defaultLocale } from '@config/index'
import { Products, getProducts } from '@pylot-data/api/operations/get-products'
import cn from 'classnames'
import { useTranslation } from 'next-i18next'
import { useRouter } from 'next/router'
import React, { FC, useEffect, useMemo, useState } from 'react'
import s from './ProductSelector.module.scss'

export interface SelectedProduct {
    slot: number
    product: ProductProps | null
}
export interface ProductSelectorProps {
    availableProducts: ProductProps[]
    selectedProducts: SelectedProduct[]
    onProductSelect: (slotIndex: number, product: ProductProps | null) => void
    slotIndex: number
    placeholder?: string
    disabled?: boolean
}
const ProductSelector: FC<ProductSelectorProps> = ({
    availableProducts,
    selectedProducts,
    onProductSelect,
    slotIndex,
    placeholder = 'Select a product',
    disabled = false
}) => {
    const { pageTheme } = useLayoutContext()
    const { locale = 'en' } = useRouter()
    const { t } = useTranslation(['common'])
    const [productsData, setProductsData] = useState<Products[]>([])

    // Fetch product data for all available products
    useEffect(() => {
        if (availableProducts && availableProducts.length > 0) {
            getProducts(
                availableProducts.map((product) => product.sku),
                null,
                locale || defaultLocale || 'en-US'
            ).then((products) => {
                if (products && products.length) {
                    setProductsData(products)
                }
            })
        }
    }, [locale, availableProducts])

    // Create dropdown options from available products
    const dropdownOptions: DropdownLabelValue[] = useMemo(() => {
        return availableProducts
            .map((product) => {
                const productData = productsData.find(
                    (item) => item.productSku === product.sku
                )
                return {
                    label: productData?.productData?.[0]?.name || product.sku,
                    value: product.sku
                }
            })
            .filter((option) => {
                // Filter out products that are already selected in other slots
                const selectedSkus = selectedProducts
                    .filter((sp) => sp.slot !== slotIndex && sp.product)
                    .map((sp) => sp.product!.sku)
                return !selectedSkus.includes(option.value)
            })
    }, [availableProducts, productsData, selectedProducts, slotIndex])

    // Get current selection
    const currentSelectedProduct = selectedProducts.find(
        (sp) => sp.slot === slotIndex
    )?.product
    const currentOption = currentSelectedProduct
        ? dropdownOptions.find(
              (option) => option.value === currentSelectedProduct.sku
          ) || null
        : null

    const handleProductChange = (sku: string) => {
        if (sku === '') {
            onProductSelect(slotIndex, null)
        } else {
            const selectedProduct = availableProducts.find(
                (product) => product.sku === sku
            )
            if (selectedProduct) {
                onProductSelect(slotIndex, selectedProduct)
            }
        }
    }

    const displayTitle = currentOption ? currentOption.label : placeholder

    return (
        <div
            className={cn(s['product-selector'], {
                [s['product-selector--disabled']]: disabled
            })}
        >
            <Dropdown
                title={displayTitle}
                currentOption={
                    currentOption || { label: placeholder, value: '' }
                }
                variant="primary"
                buttonColor={pageTheme === 'dark' ? 'dark-grey' : 'gray-10'}
                className={cn(s['product-selector__dropdown'], {
                    [s['product-selector__dropdown--dark']]:
                        pageTheme === 'dark',
                    [s['product-selector__dropdown--light']]:
                        pageTheme !== 'dark'
                })}
                options={[
                    {
                        title: '',
                        options: [
                            { label: placeholder, value: '' },
                            ...dropdownOptions
                        ]
                    }
                ]}
                onChange={handleProductChange}
                disabled={disabled}
            />
        </div>
    )
}

export default React.memo(ProductSelector)
