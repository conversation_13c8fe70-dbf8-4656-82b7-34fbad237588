import cn from 'classnames'
import { FC } from 'react'
import DropdownCompareProductCategorySectionFeatures from '../DropdownCompareProductCategorySectionFeatures/DropdownCompareProductCategorySectionFeatures'

interface DropdownCompareProductCategoryContent {
    productCategory: any
    slotIndex: number
    product: any
}

const DropdownCompareProductCategorySection: FC<DropdownCompareProductCategoryContent> = ({
    productCategory,
    slotIndex,
    product
}) => {
    return (
        <section className={cn('flex flex-col mt-24px')}>
            {productCategory.categoryTitle && (
                <h4
                    className={cn(
                        'mb-16px md-max:mb-0 h-auto text-primitive-gray-100 text-sm font-semibold'
                    )}
                >
                    {productCategory.categoryTitle}
                </h4>
            )}
            <DropdownCompareProductCategorySectionFeatures
                features={
                    productCategory.featureList || productCategory.feature
                }
                slotIndex={slotIndex}
                product={product}
            />
        </section>
    )
}

export default DropdownCompareProductCategorySection
