import { useLayoutContext } from '@components/layouts/MainLayout/LayoutContext'
import { Button } from '@components/molecules/Button/Button'
import PrimaryText, {
    HorizontalAlignmentEnum,
    PrimaryTextProps
} from '@components/molecules/PrimaryText/PrimaryText'
import {
    Container,
    ContainerSize
} from '@components/organisms/Container/Container'
import TextPanel from '@components/organisms/TextPanel/TextPanel'
import { ProductProps } from '@components/templates/CompareProducts/components/CompareProductList/CompareProductList'
import cn from 'classnames'
import { useTranslation } from 'next-i18next'
import React, { FC, useState } from 'react'
import DropdownCompareProductList from './components/DropdownCompareProductList/DropdownCompareProductList'
import s from './DropdownCompareProducts.module.scss'

export interface DropdownCompareProductsProps {
    title: string
    textPanel: PrimaryTextProps
    product: ProductProps[]
    id?: string
    hideBuyButton?: boolean
    maxComparisons?: number
    initialProductCount?: number
}

interface DropdownCompareProductsContent {
    content: DropdownCompareProductsProps
}

/**
 * Displays a dropdown-based product comparison where users can select 2 products initially
 * and add more products from available contentful entries up to a maximum limit.
 * Each product slot has a dropdown to select from all available products.
 */
const DropdownCompareProducts: FC<DropdownCompareProductsContent> = ({
    content
}) => {
    const { textPanel: textPanelContent, product: productContent } = content
    const { pageTheme } = useLayoutContext()
    const { t } = useTranslation(['common'])
    const [expanded, setExpanded] = useState(false)

    const maxComparisons = content.maxComparisons || 4
    const initialProductCount = content.initialProductCount || 2

    const showViewMoreButton =
        productContent &&
        content?.id !== 'overlay-comparison' &&
        productContent.some((product: any) => {
            const categories = product.productCategory || []
            return categories.length > 4
        })

    const borderColor =
        pageTheme === 'dark'
            ? 'border-primitive-gray-100'
            : 'border-primitive-gray-30'

    console.log(productContent, 'productContent')
    console.log(content, 'content')
    return (
        <section
            className={cn(
                pageTheme === 'dark' ? 'text-white' : 'text-black',
                'xxl:mt-36'
            )}
            id={content.id}
        >
            <Container size={ContainerSize.MEDIUM}>
                <TextPanel content={textPanelContent} />
                <div
                    className={cn(s['dropdown-compare-products-container'], {
                        [s['dropdown-compare-products-container--collapsed']]:
                            !expanded && showViewMoreButton
                    })}
                >
                    <DropdownCompareProductList
                        content={productContent}
                        maxComparisons={maxComparisons}
                        initialProductCount={initialProductCount}
                    />
                    <div
                        className={cn(
                            'pt-24px pb-32px md:py-32 border-t text-primitive-gray-50',
                            borderColor
                        )}
                    >
                        <PrimaryText
                            textAlignment={HorizontalAlignmentEnum.LEFT}
                            disclaimerText={textPanelContent.notice}
                        />
                    </div>
                </div>
                {showViewMoreButton && (
                    <div
                        className={cn(
                            s[
                                'dropdown-compare-products-container__view-more-button'
                            ],
                            'flex justify-center items-center py-16 relative'
                        )}
                    >
                        <div
                            className={cn(
                                'absolute inset-x-0 -top-32 h-32 pointer-events-none'
                            )}
                            style={{
                                background:
                                    pageTheme === 'dark'
                                        ? 'linear-gradient(to top, #000000, transparent)'
                                        : 'linear-gradient(to top, #f9f9f9, transparent)'
                            }}
                        />
                        <Button
                            variant="tertiary"
                            color={pageTheme === 'dark' ? 'light' : 'dark'}
                            onClick={() => setExpanded(!expanded)}
                            label={expanded ? t('View Less') : t('View More')}
                            className={
                                s[
                                    'dropdown-compare-products-container__view-more-button'
                                ]
                            }
                        >
                            {expanded ? t('View Less') : t('View More')}
                        </Button>
                    </div>
                )}
            </Container>
        </section>
    )
}

export default React.memo(DropdownCompareProducts)
