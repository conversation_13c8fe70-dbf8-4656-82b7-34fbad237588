.table-popup__table {
    display: flex;
    gap: 20px;
    flex-direction: row;
    flex-wrap: wrap;
    @screen lg-max {
        justify-content: center;
    }
}

.padded-text-trio-features {
    position: relative;
    display: flex;
    flex-direction: row;
    gap: 16px;
    justify-content: center;
    margin-top: 0;
    flex-wrap: nowrap;
    @screen md-max {
        flex-direction: column;
        gap: 80px;
    }
}

.padded-text-trio-feature {
    display: flex;
    flex-direction: column;
    gap: 24px;
    color: var(--white);
    overflow: hidden;
    padding: 20px 40px 40px 40px;
    @screen md {
        width: calc((1490px - (16px * 2)) / 3);
        padding: 40px;
    }

    &__icon-container {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 68px;

        @screen md {
            min-height: 135px;
        }
    }

    &__text {
        text-align: left;
        @screen md-max {
            max-width: 90%
        }
    }

    &__button {
        display: flex;
        padding: 10px 0 10px 0;
        justify-content: center;
        align-items: center;
        gap: 8px;
        border-radius: 6px;
        @screen md {
            max-width: 300px;
            width: 100%;
            height: 100%;
        }
    } 
    &__button--light-text {
        background-color: var(--black) !important;
        color: var(--white) !important;
        padding: 10px 20px;
        width: fit-content;
    }
}
.padded-text-trio-features__light-text {
    color: var(--black);
    @screen md-max {
        padding: 0;
    }
}