import AddCircleIcon from '@components/atoms/Icon/general/AddCircleIcon'
import { Icon } from '@components/atoms/Icon/Icon'
import { TablePopup } from '@components/molecules/TablePopup/TablePopup'
import { TextBlock } from '@components/molecules/TextBlock/TextBlock'
import { FeatureListProps } from '@components/templates/FeatureList/FeatureList'
import cn from 'classnames'
import { FC, useState } from 'react'
import s from './PaddedTextTrioFeatures.module.scss'

type PaddedTextTrioFeaturesProps = {
    className?: string
} & Pick<FeatureListProps, 'features' | 'backgroundColor'>

export const PaddedTextTrioFeatures: FC<PaddedTextTrioFeaturesProps> = ({
    className,
    features,
    backgroundColor
}) => {
    const [popupOpened, setPopupOpened] = useState(false)
    return (
        <div
            className={cn(
                s['padded-text-trio-features'],
                {
                    [s['padded-text-trio-features__light-theme']]:
                        backgroundColor === 'bg-white'
                },
                className,
                backgroundColor
            )}
        >
            {features?.map((feature, index) => {
                return (
                    <div
                        className={cn(s['padded-text-trio-feature'], {
                            [s['padded-text-trio-features__light-text']]:
                                backgroundColor === 'bg-white'
                        })}
                        key={`padded-text-trio-feature--${index}`}
                    >
                        <div
                            className={
                                s['padded-text-trio-feature__icon-container']
                            }
                        >
                            {feature.icon && <Icon name={feature.icon} />}
                        </div>
                        {feature.label && (
                            <TextBlock
                                className={s['padded-text-trio-feature__text']}
                                size="small"
                                headline={feature.label}
                                bodyCopy={feature.text || feature.richText}
                            />
                        )}
                        {feature.link && (
                            <button
                                className={cn(
                                    s['padded-text-trio-feature__button'],
                                    'bg-white text-charcoal',
                                    {
                                        [s[
                                            'padded-text-trio-feature__button--light-text'
                                        ]]: backgroundColor === 'bg-white'
                                    }
                                )}
                                onClick={() => {
                                    setPopupOpened(true)
                                }}
                            >
                                <AddCircleIcon />
                                {feature.link.linkTitle}
                            </button>
                        )}
                        {feature.tooltip && (
                            <TablePopup
                                open={popupOpened}
                                onClose={() => setPopupOpened(false)}
                                richText={feature?.richText}
                            >
                                <span
                                    className={cn(s['table-popup__table'])}
                                    dangerouslySetInnerHTML={{
                                        __html: feature.tooltip
                                    }}
                                />
                            </TablePopup>
                        )}
                    </div>
                )
            })}
        </div>
    )
}
