.media-w-text-overlay {
    &__logo-wrapper {
        width: 176px;
        height: 176px;

        @screen md-max {
            width: 80px;
            height: 80px;
            margin-bottom: 16px;
        }
    }

    &__overlay {
        position: absolute;
        top: 0;
        padding: 50px 0 0 0;
        height: 100vh;
        width: 100vw;
        background-color: var(--bg-grey);
        transform: translateX(100%);
        transition: transform 300ms ease-in-out;
        visibility: visible;
        
        &--open {
            transform: translateX(0);
        }

        &--hidden {
            visibility: hidden;
            transition: transform 300ms ease-in-out, visibility 300ms;
        }
    }

    @apply relative;

    &__text-overlay {
        @apply absolute mt-0 z-10 w-full top-0;
        padding-top: var(--spacing--sm);
        padding-bottom: var(--spacing--sm);
        flex-direction: column;

        @screen md {
            @apply mt-0;
            padding-top: var(--spacing--lg);
            padding-bottom: var(--spacing--lg);
        }
    }

    &__text-wrapper + &__content {
        padding-top: 24px;

        @screen md {
            padding-top: 48px;
        }
    }

    &__media {
        @apply relative w-full h-full min-h-screen;
    }

    &__video {
        position: initial !important;
    }

    &__video video {
        @apply absolute top-0 left-0 block w-full h-full object-cover;
    }

    /** vertical alignments */
    &--vertical {
        &-center {
            .media-w-text-overlay__text-overlay {
                @apply justify-center;
            }

            .media-w-text-overlay__text {
                @screen md-max {
                    @apply text-center;
                }
            }
        }

        &-center-mobile {
            .media-w-text-overlay__text-overlay {
                @screen md-max {
                    @apply justify-center;
                }
            }
        }

        &-bottom {
            .media-w-text-overlay__text-overlay {
                @apply justify-end;
            }
        }

        &-top {
            .media-w-text-overlay__text-overlay {
                @apply justify-start;
            }
        }

        &-center,
        &-bottom {
            .media-w-text-overlay__text-overlay {
                @apply mt-0 flex w-full h-full;
                padding-top: var(--spacing--sm);
                padding-bottom: var(--spacing--sm);

                @screen md {
                    padding-top: var(--spacing--lg);
                    padding-bottom: var(--spacing--lg);
                }
            }
        }
    }

    /** horizontal alignments **/
    &--horizontal {
        &-center {
            .media-w-text-overlay__text-overlay {
                @apply items-center;
            }
        }

        &-left {
            .media-w-text-overlay__text-overlay {
                @screen md-max {
                    @apply justify-start;
                }
                @screen md {
                    @apply items-start;

                    .media-w-text-overlay__text-wrapper {
                        padding-left: calc(var(--container-padding--md) + 4rem);
                    }
                }
                @screen xl {
                    .media-w-text-overlay__text-wrapper {
                        padding-left: calc(var(--container-padding--lg) + 8rem);
                    }
                }
                @screen xxl {
                    .media-w-text-overlay__text-wrapper {
                        padding-left: calc(
                            var(--container-padding--lg) + 15rem
                        );
                    }
                }
            }
        }

        &-right {
            .media-w-text-overlay__text-overlay {
                @screen md {
                    @apply items-end;
                }
            }
        }
    }

    &--horizontal-right.media-w-text-overlay--vertical-bottom {
        .media-w-text-overlay__text {
            @screen md-max {
                @apply text-left;
            }
        }
    }

    /* mobile media below or media top */
    &--mobile-below,
    &--mobile-top {
        @screen lg-max {
            @apply pb-16 px-16px;

            .media-w-text-overlay__media {
                @apply h-auto min-h-0 rounded-xl overflow-hidden;
                padding-bottom: 56.268%;
            }

            .media-w-text-overlay__text-overlay {
                @apply relative mt-0;
            }
        }
    }

    /* mobile media below */
    &--mobile-below {
        &.media-w-text-overlay--height-image-auto {
            .media-w-text-overlay__video > video {
                @screen lg {
                    padding-top: 17vh;
                    padding-bottom: 10vh;
                    max-width: 70%;
                    margin: 0 auto;
                }
            }
        }
    }

    /* mobile media top */
    &--mobile-top {
        @screen lg-max {
            @apply pt-16 flex flex-col;

            .media-w-text-overlay__text-overlay {
                @apply order-1;
            }

            .media-w-text-overlay__text {
                @apply text-charcoal;
            }
        }
    }

    /* height image auto */
    &--height-image-auto {
        /* the image or video below, should always has height auto */
        .media-w-text-overlay__media {
            padding-bottom: 0;
            min-height: 0;

            > div,
            > span,
            > span > img,
            > span > video {
                min-width: 0 !important;
                min-height: 0 !important;
                max-height: none !important;
                object-fit: contain;
                width: 100% !important;
                height: unset !important;
                position: unset !important;
                padding-top: 0 !important;
            }
        }

        .media-w-text-overlay__video > video {
            position: relative;
        }
    }

    /* image height as text */
    &--height-auto {
        position: relative;

        .media-w-text-overlay__media {
            width: 100%;
            height: 100%;
            position: absolute;
            top: 0;
            left: 0;
            min-height: 0;
        }

        .media-w-text-overlay__text-overlay {
            @apply relative;
            transform: none;
            left: 0;
            top: 0;
        }
    }

    &--padded {
        @apply mx-auto h-full flex justify-center items-center flex-col w-full flex-grow;

        .media-w-text-overlay__text-overlay {
            @apply max-w-1490px;
        }

        .media-w-text-overlay__media {
            @apply rounded-xxl overflow-hidden max-w-1490px;
            min-height: unset;
            flex-grow: 1;
        }
    }

    &__container {
        &--padded {
            @apply p-16px min-h-screen flex flex-col;

            @screen md {
                @apply p-32;
            }
        }
    }

    &__text-overlay--no-padding {
        padding: 0 !important;
    }
}

.bundle-list-wrapper {
    background-color: #F6F6F6;
    @media only screen and (max-width: 767px) {
        overflow: scroll;
    }
}

.media-w-text-overlay__content__product-configurator {
    position: absolute;
    top: 0;
    padding: 50px 0 0 0;
    height: 100vh;
    width: 100vw;
    background-color: var(--bg-grey);

    @screen md {
        padding: 0;
        top: 15px;
    }
}
