import ElgatoVideo from '@components/common/ElgatoVideo/ElgatoVideo'
import { Panel } from '@components/templates/TextWithToggleVideos/types'
import { Splide, SplideSlide } from '@splidejs/react-splide'
import { FC } from 'react'

interface TextWithToggleVideosSliderProps {
    muted: boolean
    leftPanel: Panel
    rightPanel: Panel
}

export const TextWithToggleVideosSlider: FC<TextWithToggleVideosSliderProps> = ({
    muted,
    leftPanel,
    rightPanel
}) => {
    const {
        video: leftPanelVideo,
        header: leftPanelHeader,
        body: leftPanelBody
    } = leftPanel
    const {
        video: rightPanelVideo,
        header: rightPanelHeader,
        body: rightPanelBody
    } = rightPanel

    return (
        <div className="pb-16">
            <Splide
                options={{
                    arrows: false,
                    gap: '16px',
                    fixedWidth: '90%',
                    start: 0,
                    padding: 16,
                    trimSpace: true
                }}
            >
                <SplideSlide className="w-4/5">
                    {leftPanelVideo && (
                        <ElgatoVideo
                            secure_url={leftPanelVideo}
                            options={{
                                autoPlay: true,
                                loop: true,
                                muted
                            }}
                            resetOnStartPlay
                            className="max-w-screen-sm rounded-xxl overflow-hidden"
                            videoClasses="w-full"
                        />
                    )}
                    <div className="flex flex-col gap-8px px-16px pt-24px">
                        <h4 className="text-h4-md-max">{leftPanelHeader}</h4>
                        <p className="text-xs-copy">{leftPanelBody}</p>
                    </div>
                </SplideSlide>
                <SplideSlide className="w-4/5">
                    {rightPanelVideo?.[0] && (
                        <ElgatoVideo
                            secure_url={rightPanelVideo}
                            className="max-w-screen-sm rounded-xxl overflow-hidden"
                            videoClasses="w-full"
                            options={{
                                autoPlay: true,
                                loop: true,
                                muted
                            }}
                            resetOnStartPlay
                        />
                    )}
                    <div className="flex flex-col gap-8px px-16px pt-24px">
                        <h4 className="text-h4-md-max">{rightPanelHeader}</h4>
                        <p className="text-xs-copy">{rightPanelBody}</p>
                    </div>
                </SplideSlide>
            </Splide>
        </div>
    )
}
