import { Icon } from '@components/atoms/Icon/Icon'
import { Toggle } from '@components/atoms/Toggle/Toggle'
import TextPanel from '@components/organisms/TextPanel/TextPanel'
import { CardProps } from '@components/templates/CardList/CardList'
import { SpecialAnimationProps } from '@components/templates/SpecialAnimation/SpecialAnimation'
import { TextWithToggleVideosSlider } from '@components/templates/TextWithToggleVideos/TextWithToggleVideosSlider'
import { TextWithToggleVideosSwitcher } from '@components/templates/TextWithToggleVideos/TextWithToggleVideosSwitcher'
import { useMedia } from '@lib/hooks/useMedia'
import { useMobile } from '@pylot-data/hooks/use-mobile'
import { useTranslation } from 'next-i18next'
import { FC, useState } from 'react'
import s from './TextWithToggleVideos.module.scss'

export const TextWithToggleVideos: FC<
    Omit<SpecialAnimationProps, 'children'> & {
        cards: CardProps[]
    }
> = ({ textPanels, cards }) => {
    const { t } = useTranslation('common')
    const [audioEnabled, setAudioEnabled] = useState(false)
    const { isMobile } = useMobile()
    const textPanel = textPanels?.[0]
    const video1Desktop = cards?.[0]?.cloudinaryMedia
    const video1Mobile = cards?.[0]?.cloudinaryMobileMedia
    const video2Desktop = cards?.[1]?.cloudinaryMedia
    const video2Mobile = cards?.[1]?.cloudinaryMobileMedia

    const video1 = useMedia({
        cloudinaryMedia: video1Desktop,
        cloudinaryMobileMedia: video1Mobile
    })
    const video2 = useMedia({
        cloudinaryMedia: video2Desktop,
        cloudinaryMobileMedia: video2Mobile
    })

    const header1 = cards?.[0]?.textPanel?.headline
    const header2 = cards?.[1]?.textPanel?.headline
    const body1 = cards?.[0]?.textPanel?.bodyCopy
    const body2 = cards?.[1]?.textPanel?.bodyCopy

    if (!video1.src || !video2.src) return null

    return (
        <div className={s['text-with-toggle-videos']}>
            {textPanel && (
                <TextPanel
                    content={{
                        ...textPanel,
                        noPaddingBottom: true
                    }}
                />
            )}
            <div className="flex gap-16px justify-center items-center w-full pt-24px pb-24px lg:pb-16">
                <div className="flex items-center gap-8px">
                    <Icon name={audioEnabled ? 'volume3' : 'volumeMuteCross'} />
                    <div>{t('Audio enabled')}</div>
                </div>
                <Toggle
                    checked={audioEnabled}
                    onChange={() => setAudioEnabled(!audioEnabled)}
                    color="primary"
                />
            </div>
            {isMobile ? (
                <TextWithToggleVideosSlider
                    muted={!audioEnabled}
                    leftPanel={{
                        video: video1.src,
                        header: header1,
                        body: body1
                    }}
                    rightPanel={{
                        video: video2.src,
                        header: header2,
                        body: body2
                    }}
                />
            ) : (
                <TextWithToggleVideosSwitcher
                    audioEnabled={audioEnabled}
                    leftPanel={{
                        video: video1.src,
                        header: header1,
                        body: body1
                    }}
                    rightPanel={{
                        video: video2.src,
                        header: header2,
                        body: body2
                    }}
                />
            )}
        </div>
    )
}
