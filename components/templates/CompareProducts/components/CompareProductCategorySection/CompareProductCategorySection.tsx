import { FC } from 'react'
import cn from 'classnames'
import CompareProductCategorySectionFeatures from '@components/templates/CompareProducts/components/CompareProductCategorySectionFeatures/CompareProductCategorySectionFeatures'

interface CompareProductCategoryContent {
    productCategory: any
    columnIndex: number
    keyCountDropdownValue?: string
    onKeyCountDropdownChange?: (keyCount: string, columnIndex: number) => void
    productSku?: string
}

const CompareProductCategorySection: FC<CompareProductCategoryContent> = ({
    productCategory,
    columnIndex,
    keyCountDropdownValue,
    onKeyCountDropdownChange,
    productSku
}) => {
    return (
        <section className={cn('flex flex-col')}>
            {productCategory.categoryTitle && (
                <h4
                    className={cn(
                        'mb-16px md-max:mb-0 h-auto text-primitive-gray-100'
                    )}
                >
                    {productCategory.categoryTitle}
                </h4>
            )}
            <CompareProductCategorySectionFeatures
                features={productCategory.featureList}
                columnIndex={columnIndex}
                keyCountDropdownValue={keyCountDropdownValue}
                onKeyCountDropdownChange={onKeyCountDropdownChange}
                productSku={productSku}
            />
        </section>
    )
}

export default CompareProductCategorySection
