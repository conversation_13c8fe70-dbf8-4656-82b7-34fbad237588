import ElgatoImage from '@components/common/ElgatoImage'
import ChevronRight from '@components/icons/ChevronRight'
import { useLayoutContext } from '@components/layouts/MainLayout/LayoutContext'
import { Button } from '@components/molecules/Button/Button'
import {
    Dropdown,
    DropdownLabelValue
} from '@components/molecules/Dropdown/Dropdown'
import { ProductAddToCart } from '@components/molecules/ProductAddToCart/ProductAddToCart'
import { ProductPrice } from '@components/molecules/ProductPrice/ProductPrice'
import CompareProductCategorySection from '@components/templates/CompareProducts/components/CompareProductCategorySection/CompareProductCategorySection'
import { ProductProps } from '@components/templates/CompareProducts/components/CompareProductList/CompareProductList'
import { usePrice } from '@corsairitshopify/pylot-price'
import { useMedia } from '@lib/hooks/useMedia'
import { Products } from '@pylot-data/api/operations/get-products'
import { useMobile } from '@pylot-data/hooks/use-mobile'
import cn from 'classnames'
import { useTranslation } from 'next-i18next'
import React, { FC, useMemo, useState } from 'react'
import s from './CompareProduct.module.scss'

// Helper to get the current key count for a column
const getCurrentKeyCount = (product: any) => {
    for (const category of product.productCategory || []) {
        for (const featureList of category.featureList || []) {
            for (const feature of featureList.features || []) {
                const keyCount = feature.customOptions?.keys
                if (
                    keyCount === '6 Keys' ||
                    keyCount === '15 Keys' ||
                    keyCount === '32 Keys'
                ) {
                    return keyCount
                }
            }
        }
    }
    return ''
}
interface ProductContent {
    initialSelectedProductIndex: number
    contentfulProducts: ProductProps[]
    productsData: Products[]
    dropdownOptions?: DropdownLabelValue[]
}

const CompareProduct: FC<ProductContent> = ({
    initialSelectedProductIndex,
    contentfulProducts,
    dropdownOptions,
    productsData
}) => {
    const { t } = useTranslation(['common'])
    const { pageTheme } = useLayoutContext()
    const { isMobile } = useMobile()
    const [selectedProductSku, setSelectedProductSku] = useState<string>(
        contentfulProducts[initialSelectedProductIndex].sku
    )
    const selectedProduct = contentfulProducts.find(
        (product) => product.sku === selectedProductSku
    )
    const productCategory = selectedProduct?.productCategory
    const link = selectedProduct?.link
    const linkTitle = link?.linkTitle
    const linkUrl = link?.linkUrl
    const bodyCopy = selectedProduct?.bodyCopy
    const hideBuyButton = selectedProduct?.hideBuyButton
    const hasDropdown = selectedProduct?.hasDropdown ?? true
    const media = selectedProduct?.media
    const cloudinaryMedia = selectedProduct?.cloudinaryMedia

    const selectedProductData = productsData.find(
        (product) => product.productSku === selectedProductSku
    )
    const { subtotal, total } = usePrice(
        selectedProductData?.productData?.[0]?.price_range
    )
    const { src, alt, width, height } = useMedia({
        media: media,
        cloudinaryMedia: cloudinaryMedia
    })
    const productImage = selectedProductData?.productData?.[0]?.image?.url
    const displayName = selectedProductData?.productData?.[0]?.name || ''
    const mediaFallback = src ? src : productImage

    const currentProductOption = dropdownOptions?.find(
        (option) => option.value === selectedProduct?.sku
    ) || {
        label: '',
        value: ''
    }

    const keyCountDropdownValue = getCurrentKeyCount(selectedProduct)

    const keyCountToProduct = useMemo(() => {
        const map: Record<string, ProductProps | undefined> = {}
        for (const product of contentfulProducts || []) {
            for (const category of (product as any).productCategory || []) {
                for (const featureList of category.featureList || []) {
                    for (const feature of featureList.features || []) {
                        const keyCount = feature.customOptions?.keys
                        if (keyCount === '6 Keys') map['6 Keys'] = product
                        if (keyCount === '15 Keys') map['15 Keys'] = product
                        if (keyCount === '32 Keys') map['32 Keys'] = product
                    }
                }
            }
        }
        return map
    }, [contentfulProducts])

    function onChangeProduct(sku: string) {
        setSelectedProductSku(sku)
    }

    function onKeyCountDropdownChange(keyCount: string) {
        const product = keyCountToProduct[keyCount]
        if (product) {
            setSelectedProductSku(product.sku)
        }
    }

    const renderDropdown = () => {
        if (hasDropdown) {
            console.log(
                'displayName',
                displayName,
                'currentProductOption',
                currentProductOption,
                selectedProductSku,
                selectedProduct
            )
            return (
                <div className={s['compare-product__dropdown__wrapper']}>
                    <Dropdown
                        title={displayName}
                        currentOption={
                            currentProductOption as DropdownLabelValue
                        }
                        variant="primary"
                        /* eslint-disable-next-line i18next/no-literal-string */
                        buttonColor={
                            pageTheme === 'dark' ? 'dark-grey' : 'gray-10'
                        }
                        className={cn(
                            s['compare-product__dropdown__wrapper__dropdown'],
                            {
                                [s[
                                    'compare-product__dropdown__wrapper__dropdown--dark'
                                ]]: pageTheme === 'dark'
                            },
                            {
                                [s[
                                    'compare-product__dropdown__wrapper__dropdown--light'
                                ]]: pageTheme !== 'dark'
                            }
                        )}
                        options={[
                            {
                                title: '',
                                options: dropdownOptions as DropdownLabelValue[]
                            }
                        ]}
                        onChange={onChangeProduct}
                    />
                </div>
            )
        } else {
            return (
                <>
                    {!isMobile && (
                        <div
                            className={cn(s['compare-product__name'], 'z-20', {
                                [s['compare-product__name--light']]:
                                    pageTheme !== 'dark',
                                [s['compare-product__name--dark']]:
                                    pageTheme === 'dark'
                            })}
                        >
                            <div className="text-center mt-16px text-sub-headline-md-max md-max:mt-4 overflow-hidden whitespace-nowrap overflow-ellipsis">
                                {displayName}
                            </div>
                        </div>
                    )}
                    {isMobile && (
                        <div
                            className={s['compare-product__dropdown__wrapper']}
                        >
                            <Dropdown
                                title={displayName}
                                currentOption={currentProductOption}
                                variant="primary"
                                /* eslint-disable-next-line i18next/no-literal-string */
                                buttonColor={
                                    pageTheme === 'dark'
                                        ? 'dark-grey'
                                        : 'gray-10'
                                }
                                className={cn(
                                    s[
                                        'compare-product__dropdown__wrapper__dropdown'
                                    ],
                                    {
                                        [s[
                                            'compare-product__dropdown__wrapper__dropdown--dark'
                                        ]]: pageTheme === 'dark'
                                    },
                                    {
                                        [s[
                                            'compare-product__dropdown__wrapper__dropdown--light'
                                        ]]: pageTheme !== 'dark'
                                    }
                                )}
                                options={[
                                    {
                                        title: '',
                                        options: dropdownOptions as DropdownLabelValue[]
                                    }
                                ]}
                                onChange={onChangeProduct}
                            />
                        </div>
                    )}
                </>
            )
        }
    }

    const renderPriceAndAddToCart = () => {
        if (
            selectedProductData &&
            selectedProductData?.productData?.[0] &&
            subtotal &&
            parseFloat(subtotal.replace(/[^\d.-]/g, '')) !== 0 &&
            total &&
            parseFloat(total.replace(/[^\d.-]/g, ''))
        ) {
            return (
                <ProductPrice
                    className={s['compare-product__product-price-wrapper']}
                    product={selectedProductData?.productData?.[0]}
                    /* eslint-disable-next-line i18next/no-literal-string */
                    theme={pageTheme === 'dark' ? 'light' : 'dark'}
                    inNav={false}
                />
            )
        } else {
            return <div className={s['compare-product__empty-price-wrapper']} />
        }
    }

    const renderLink = () => {
        if (linkTitle && linkUrl) {
            return (
                <div
                    className={cn(
                        s['compare-product__link__wrapper'],
                        'text-center'
                    )}
                >
                    <Button
                        variant="tertiary"
                        /* eslint-disable-next-line i18next/no-literal-string */
                        color={pageTheme === 'dark' ? 'light' : 'dark'}
                        href={linkUrl}
                        label={linkTitle}
                    >
                        {linkTitle}
                        <ChevronRight />
                    </Button>
                </div>
            )
        }
        return null
    }

    const renderProductContent = () => (
        <>
            {selectedProductData && (
                <div
                    className={cn(s['compare-product__media'], {
                        [s['compare-product__media--dark']]:
                            pageTheme === 'dark'
                    })}
                >
                    <ElgatoImage
                        src={mediaFallback || ''}
                        alt={alt || ''}
                        width={width}
                        height={height}
                        objectFit="cover"
                    />
                </div>
            )}
            {selectedProductData && renderDropdown()}
            {bodyCopy && (
                <div
                    className={cn(
                        s['compare-product__body-copy'],
                        'text-center text-h6-md-max text-primitive-gray-100 mt-4px'
                    )}
                >
                    {bodyCopy}
                </div>
            )}
            {/* {hideBuyButton && (
                <div
                    className={cn(
                        s['compare-product__learn-more-wrapper'],
                        'flex justify-center pt-16px'
                    )}
                >
                    <Button
                        variant="tertiary"
                        href={linkUrl}
                        color={pageTheme === 'dark' ? 'light' : 'dark'}
                        label={linkTitle}
                        className={cn(
                            s['compare-product__learn-more-wrapper__button']
                        )}
                    >
                        {linkTitle}
                        <ChevronRight />
                    </Button>
                </div>
            )} */}

            {selectedProductData && (
                <div
                    className={cn(
                        'flex justify-center items-center gap-4 flex-col lg:flex-row lg:gap-10',
                        s['compare-product__price-and-buy'],
                        {
                            'my-40px': hideBuyButton
                        },
                        {
                            'my-16px': !hideBuyButton
                        }
                    )}
                >
                    {renderPriceAndAddToCart()}
                </div>
            )}
            {selectedProductData && !hideBuyButton && (
                <div
                    className={cn(
                        s['compare-product__price-and-buy__wrapper'],
                        'flex items-center md-max:flex-col justify-center gap-12px md:gap-24px'
                    )}
                >
                    <ProductAddToCart
                        id={`compare-product-atc-btn-${selectedProductData.productData?.[0]?.uid}`}
                        product={selectedProductData?.productData?.[0]}
                        buttonLabel={t('Buy')}
                    />
                    {renderLink()}
                </div>
            )}
        </>
    )

    return (
        <div className={cn(s['compare-product'])}>
            {renderProductContent()}
            {productCategory?.map((category: any, index: number) => (
                <CompareProductCategorySection
                    productCategory={category}
                    key={index}
                    columnIndex={initialSelectedProductIndex}
                    keyCountDropdownValue={keyCountDropdownValue}
                    onKeyCountDropdownChange={onKeyCountDropdownChange}
                    productSku={selectedProductSku}
                />
            ))}
        </div>
    )
}

export default React.memo(CompareProduct)
