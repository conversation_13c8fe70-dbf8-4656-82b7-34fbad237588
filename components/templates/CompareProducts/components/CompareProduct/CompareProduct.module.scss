.compare-product {
    &__dropdown__wrapper {
        min-height: 64px;
        margin-top: 24px;

        &__dropdown {
            width: 100%;
            position: absolute;

            &:after {
                height: 16px;
                content: '';
                display: block;
                width: 100%;
                left: -1rem;
                top: 100%;
                position: absolute;
            }

            &:before {
                height: 20px;
                content: '';
                display: block;
                width: 100%;
                top: -20px;
                left: -1rem;
                position: absolute;
            }

            button {
                padding: 16px 24px;
                height: 64px;
                font-size: 1.4rem;
                font-weight: 700;

                &:last-child {
                    border-bottom: none !important;
                }
                @screen md {
                    font-size: 1.6rem;
                }
            }
            ul {
                button {
                    font-weight: 400 !important;
                }
            }
            &--dark {
                ul {
                    background: var(--primitive-gray-120) !important;
                    button {
                        border-bottom: 1px solid var(--primitive-gray-100);
                    }
                }
            }

            &--light {
                ul {
                    background: var(--primitive-gray-10) !important;
                    border-color: var(--primitive-gray-30) !important;
                    button {
                        border-bottom: 1px solid var(--primitive-gray-30) !important;
                    }
                }
            }
        }
    }

    &__name {
        position: sticky;
        top: 100px;

        &--light {
            background: white;
            color: black;

            &:before {
                @apply bg-white text-black;
            }
        }

        &--dark {
            background: black;
            color: white;

            &:before {
                @apply bg-black text-white;
            }

            &:after {
                background: linear-gradient(
                    0deg,
                    rgba(0, 0, 0, 0) 0%,
                    rgba(0, 0, 0, 1) 50%
                );
            }
        }

        &:after {
            height: 16px;
            content: '';
            display: block;
            width: 100%;
            top: 100%;
            position: absolute;
        }
    }

    &__price-and-buy {
        @screen md {
            // min-height: 4.3rem;
        }

        &__wrapper {
            padding-bottom: 40px;
            @screen md {
                padding-bottom: 80px;
            }
            button {
                min-width: 120px;
                text-align: center;
                display: flex;
                justify-content: center;
            }
        }
        :global {
            .price-discount-custom,
            .price-discount-custom-strike {
                font-size: 1.8rem !important;
            }
        }
    }

    &__empty-price-wrapper {
        min-height: 7.3rem;
        @screen md {
            min-height: 3.5rem;
        }
    }

    &__product-price-wrapper {
        @screen md-max {
            div {
                font-size: 2rem;
            }
        }
    }

    &__link__wrapper,
    &__learn-more-wrapper {
        svg {
            width: 1vw !important;
            height: 1.5vh !important;

            @screen md {
                width: 1.5vw !important;
                height: 1vh !important;
            }
        }
    }

    &__media,
    &__dropdown__wrapper__dropdown,
    &__body-copy,
    &__learn-more-wrapper {
        padding: 0 8px;
        img {
            @apply overflow-hidden rounded-xxxl;
        }
    }

    &__learn-more-wrapper {
        &__button {
            border-radius: 6px !important;
            border: 1px solid var(--primitive-gray-130) !important;
            padding: 12px 24px !important;
            overflow: hidden !important;
        }
    }
    &__media {
        aspect-ratio: 163 / 140;
        height: 100%;
        width: 100%;
        img {
            object-fit: contain;
        }
        @screen md {
            aspect-ratio: 343 / 200;
        }
        &--dark {
            @screen md {
                aspect-ratio: 436 / 360;
            }
        }
    }
}
