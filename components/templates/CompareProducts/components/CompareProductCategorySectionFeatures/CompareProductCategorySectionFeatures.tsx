import { useLayoutContext } from '@components/layouts/MainLayout/LayoutContext'
import { FC } from 'react'
import cn from 'classnames'
import CompareProductCategorySectionFeature from '@components/templates/CompareProducts/components/CompareProductCategorySectionFeature/CompareProductCategorySectionFeature'

interface CompareProductCategoryContent {
    features: any
    columnIndex: number
    keyCountDropdownValue?: string
    onKeyCountDropdownChange?: (keyCount: string, columnIndex: number) => void
    productSku?: string
}

const CompareProductCategorySectionFeatures: FC<CompareProductCategoryContent> = ({
    features,
    columnIndex,
    keyCountDropdownValue,
    onKeyCountDropdownChange,
    productSku
}) => {
    const { pageTheme } = useLayoutContext()
    const borderColor =
        pageTheme === 'dark'
            ? 'border-primitive-gray-100'
            : 'border-primitive-gray-30'
    return (
        <div
            className={cn(
                'flex flex-col border-t py-8 md:pt-40px md:pb-32',
                borderColor
            )}
        >
            {features?.map((feature: any, index: number) => (
                <CompareProductCategorySectionFeature
                    key={index}
                    feature={feature}
                    columnIndex={columnIndex}
                    keyCountDropdownValue={keyCountDropdownValue}
                    onKeyCountDropdownChange={onKeyCountDropdownChange}
                    productSku={productSku}
                />
            ))}
        </div>
    )
}

export default CompareProductCategorySectionFeatures
