import { useLayoutContext } from '@components/layouts/MainLayout/LayoutContext'
import { Button } from '@components/molecules/Button/Button'
import PrimaryText, {
    HorizontalAlignmentEnum,
    PrimaryTextProps
} from '@components/molecules/PrimaryText/PrimaryText'
import {
    Container,
    ContainerSize
} from '@components/organisms/Container/Container'
import TextPanel from '@components/organisms/TextPanel/TextPanel'
import CompareProductList, {
    ProductProps
} from '@components/templates/CompareProducts/components/CompareProductList/CompareProductList'
import cn from 'classnames'
import { useTranslation } from 'next-i18next'
import React, { FC, useState } from 'react'
import s from './CompareProducts.module.scss'

export interface CompareProductsProps {
    title: string
    textPanel: PrimaryTextProps
    product: ProductProps[]
    id?: string
    hideBuyButton?: boolean
}

interface CompareProductsContent {
    content: CompareProductsProps
}

/**
 * Displays a content with text panel and presents comparison for products by groups
 * Each product has image, dropdown etc..
 * Example of this component can be found in: url + /us/en/p/game-capture-4k-x.
 */
const CompareProducts: FC<CompareProductsContent> = ({ content }) => {
    const { textPanel: textPanelContent, product: productContent } = content
    const { pageTheme } = useLayoutContext()
    const { t } = useTranslation(['common'])
    const [expanded, setExpanded] = useState(false)

    const showViewMoreButton =
        productContent &&
        content?.id !== 'overlay-comparison' &&
        productContent.some((product: any) => {
            const categories = product.productCategory || []
            return categories.length > 4
        })

    const borderColor =
        pageTheme === 'dark'
            ? 'border-primitive-gray-100'
            : 'border-primitive-gray-30'
    return (
        <section
            className={cn(
                pageTheme === 'dark' ? 'text-white' : 'text-black',
                'xxl:mt-36'
            )}
            id={content.id}
        >
            <Container size={ContainerSize.MEDIUM}>
                <TextPanel content={textPanelContent} />
                <div
                    className={cn(s['compare-products-container'], {
                        [s['compare-products-container--collapsed']]:
                            !expanded && showViewMoreButton
                    })}
                >
                    <CompareProductList content={productContent} />
                    <div
                        className={cn(
                            'pt-24px pb-32px md:py-32 border-t text-primitive-gray-50',
                            borderColor
                        )}
                    >
                        <PrimaryText
                            textAlignment={HorizontalAlignmentEnum.LEFT}
                            disclaimerText={textPanelContent.notice}
                        />
                    </div>
                </div>
                {showViewMoreButton && (
                    <div
                        className={cn(
                            s['compare-products-container__view-more-button'],
                            'flex justify-center items-center py-16 relative'
                        )}
                    >
                        <div
                            className={cn(
                                'absolute inset-x-0 -top-32 h-32 pointer-events-none'
                            )}
                            style={{
                                background:
                                    pageTheme === 'dark'
                                        ? 'linear-gradient(to top, #000000, transparent)'
                                        : 'linear-gradient(to top, #f9f9f9, transparent)'
                            }}
                        />
                        <Button
                            variant="tertiary"
                            color={pageTheme === 'dark' ? 'light' : 'dark'}
                            onClick={() => setExpanded(!expanded)}
                            label={expanded ? t('View Less') : t('View More')}
                            className={
                                s[
                                    'compare-products-container__view-more-button'
                                ]
                            }
                        >
                            {expanded ? t('View Less') : t('View More')}
                        </Button>
                    </div>
                )}
            </Container>
        </section>
    )
}

export default React.memo(CompareProducts)
