.compare-products-container {
    transition: max-height 0.5s ease-in-out, opacity 0.4s ease-in-out;
    max-height: 5000px;
    opacity: 1;

    &--collapsed {
        position: relative;
        max-height: 80vh;
        @screen md {
            max-height: 130vh;
        }
        opacity: 1;

        &::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 100px;
            pointer-events: none;
        }
    }

    &__view-more-button {
        backdrop-filter: blur(2px);
        button {
            padding: 8px 16px;
            border-radius: 6px;
            transition: all 0.2s ease;
            border: 1px solid var(--primitive-gray-130) !important;
            background: white;
            @screen md {
                padding: 12px 24px;
            }
        }
    }
}
