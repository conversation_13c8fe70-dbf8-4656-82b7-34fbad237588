import React, { FC } from 'react'
import PrimaryText, {
    PrimaryTextProps
} from '@components/molecules/PrimaryText/PrimaryText'
import {
    SectionBgColor,
    SectionThemeDarkBgColors
} from '@components/templates/Section/Section'
import {
    Container,
    ContainerSize
} from '@components/organisms/Container/Container'
import { CardProps } from '../../templates/CardList/CardList'

interface ChallengesCardProps {
    title?: string
    className?: string
    variant?: string
    textPanel?: PrimaryTextProps
    headline?: string
    bgColor?: SectionBgColor
    id?: string
    subHeader?: string
    cards: CardProps[]
}

export interface ChallengesCardContent {
    content: ChallengesCardProps
}
export const ChallengesCard: FC<ChallengesCardContent> = ({ content }) => {
    const { textPanel, bgColor = SectionBgColor.TRANSPARENT, cards } = content
    const textColor = SectionThemeDarkBgColors.includes(bgColor)
        ? 'light'
        : 'dark'
    return (
        <div className="bg-white-smoke p-16px md:p-32 flex justify-center items-center w-full">
            {textPanel && (
                <Container size={ContainerSize.SMALL}>
                    <PrimaryText
                        headline={textPanel.headline}
                        bodyCopy={textPanel.bodyCopy}
                        subheader={textPanel.subheader}
                        textColor={textColor}
                    />
                </Container>
            )}
            {cards}
        </div>
    )
}

export default ChallengesCard
