import { SpecialAnimationContent } from '@components/templates/SpecialAnimation/SpecialAnimation'
import { gsap } from 'gsap'
import { FC, useEffect, useRef } from 'react'
import s from './ImageFadeOutTextFadeInAnimation.module.scss'

import ElgatoImage from '@components/common/ElgatoImage'
import cn from 'classnames'
import { decode } from 'he'
import { useMedia } from '@lib/hooks/useMedia'
import TextWithMedia from '@components/templates/TextWithMedia/TextWithMedia'
import { useLayoutContext } from '@components/layouts/MainLayout/LayoutContext'
import { useMobile } from '@pylot-data/hooks/use-mobile'
import { SectionBgColor } from '@components/templates/Section/Section'

export const ImageFadeOutTextFadeInAnimation: FC<SpecialAnimationContent> = ({
    content
}) => {
    const { textPanels, cloudinaryMedia, cloudinaryMobileMedia } = content
    const { isMobile } = useMobile()
    const mediaRef = useRef<HTMLDivElement>(null)
    // Use different Ref for Mobile if in case it is different from desktop
    const textRef = useRef<HTMLDivElement>(null)
    const containerRef = useRef<HTMLDivElement>(null)
    const timelineRef = useRef<gsap.core.Timeline | null>(null)
    const { pageTheme } = useLayoutContext()

    const { src, alt } = useMedia({ cloudinaryMedia, cloudinaryMobileMedia })

    const text = textPanels?.[0]?.headline

    useEffect(() => {
        // Have to Animation on Mobile and Desktop in case they should have different behavior
        timelineRef.current = gsap
            .timeline({
                scrollTrigger: {
                    trigger: containerRef.current,
                    start: '400px top',
                    end: 'bottom bottom',
                    scrub: true,
                    onLeave: () => {
                        timelineRef.current?.kill()
                    }
                }
            })
            .to(mediaRef.current, { opacity: 0.2 })
            .to(textRef.current, { opacity: 1 }, '<25%')

        return () => {
            timelineRef.current?.kill()
        }
    }, [textRef, mediaRef])

    return isMobile ? (
        <TextWithMedia
            content={{
                textPanel: content.textPanels?.[0],
                cloudinaryMedia: content.cloudinaryMedia,
                bgColor:
                    pageTheme === 'dark'
                        ? SectionBgColor.BLACK
                        : SectionBgColor.WHITE
            }}
        />
    ) : (
        <div
            className={cn(
                'relative',
                s['image-fade-out-text-fade-in-animation__container']
            )}
            ref={containerRef}
        >
            <div className={cn('sticky top-0 h-screen')} ref={mediaRef}>
                <div
                    ref={mediaRef}
                    className={cn('relative h-full opacity-100')}
                >
                    {src && (
                        <ElgatoImage
                            src={src}
                            alt={alt}
                            layout="fill"
                            objectFit="cover"
                        />
                    )}
                </div>
            </div>
            <div
                className="text-white sticky top-0 w-full h-screen flex items-center justify-center text-center md-max:px-12 opacity-0"
                ref={textRef}
            >
                {text && (
                    <h1
                        dangerouslySetInnerHTML={{
                            __html: decode(text)
                        }}
                    />
                )}
            </div>
        </div>
    )
}
export default ImageFadeOutTextFadeInAnimation
