import BadgeOnVideo from '@components/atoms/BadgeOnVideo/BadgeOnVideo'
import { Icon } from '@components/atoms/Icon/Icon'
import { CloudinaryMedia } from '@components/common/CloudinaryMedia/Cloudinary'
import ElgatoImage from '@components/common/ElgatoImage'
import ElgatoVideo from '@components/common/ElgatoVideo/ElgatoVideo'
import { useLayoutContext } from '@components/layouts/MainLayout/LayoutContext'
import { Button } from '@components/molecules/Button/Button'
import {
    HorizontalAlignmentEnum,
    PrimaryTextProps
} from '@components/molecules/PrimaryText/PrimaryText'
import TextPanel from '@components/organisms/TextPanel/TextPanel'
import {
    VideoOverlay,
    VideoOverlayProps
} from '@components/organisms/VideoOverlay/VideoOverlay'
import { CardProps } from '@components/templates/CardList/CardList'
import { PaddedTextFeatures } from '@components/templates/PaddedTextFeatures/PaddedTextFeatures'
import { PaddedTextTrioFeatures } from '@components/templates/PaddedTextTrioFeatures/PaddedTextTrioFeatures'
import {
    SectionBgColor,
    SectionThemeDarkBgColors
} from '@components/templates/Section/Section'
import TechSpecs from '@components/templates/TechSpecs/TechSpecs'
import MediaWithHotspot from '@components/templates/TextWithMedia/MediaWithHotspot'
import { TextWithMediaCard } from '@components/templates/TextWithMedia/TextWithMediaCard'
import { TextWithMediaHotspots } from '@components/templates/TextWithMedia/TextWithMediaHotspots'
import { useMedia } from '@lib/hooks/useMedia'
import { Player } from '@lottiefiles/react-lottie-player'
import {
    ImageType,
    VideoType
} from '@pylot-data/hooks/contentful/use-content-json'
import cn from 'classnames'
import { decode } from 'he'
import { focusController } from 'helpers/AdaHelpers'
import dynamic from 'next/dynamic'
import { CSSProperties, FC, useRef, useState } from 'react'
import FeatureList, { FeatureListProps } from '../FeatureList/FeatureList'
import s from './TextWithMedia.module.scss'
const TextWithMediaStickyAnimation = dynamic(
    () =>
        import(
            '@components/templates/TextWithMedia/TextWithMediaStickyAnimation'
        ),
    {
        ssr: false
    }
)

export interface TextWithMediaProps {
    title?: string
    textPanel?: PrimaryTextProps
    media?: ImageType | VideoType
    mobileMedia?: ImageType | VideoType
    cloudinaryMedia?: CloudinaryMedia[]
    cloudinaryMobileMedia?: CloudinaryMedia[]
    cloudinaryPosterImage?: CloudinaryMedia[]
    textColor?: 'dark' | 'light'
    textPosition?: 'left' | 'center' | 'right'
    fullWidth?: boolean
    fullWidthAndHeight?: boolean
    bgColor?: SectionBgColor
    heightAuto?: boolean
    videoOverlay?: VideoOverlayProps | null
    lazyLoading?: boolean
    id?: string
    posterImage?: ImageType
    children?: Array<FeatureListProps | CardProps | any>
    videoOptions?: Record<string, unknown>
    additionalOptions?: Record<string, unknown>
    hasHotspot?: boolean
    richText?: string
    tooltip?: string
    hidden?: boolean
    hasPadding?: boolean
    textWithMediaClass?: string
    lottieAnimation?: ImageType
    customClassName?: string
    mediaDisclaimerText?: string
    mediaStyle?: CSSProperties
    videoDescription?: string
    variant?: string
}

export interface TextWithMediaContent {
    content: TextWithMediaProps
}

const defaultVideoOptions = {
    autoPlay: true,
    preload: 'none',
    muted: true,
    loop: true
}

export const TextWithMedia: FC<TextWithMediaContent> = ({ content }) => {
    const {
        bgColor = SectionBgColor.TRANSPARENT,
        textPosition = 'center',
        fullWidth = false,
        fullWidthAndHeight,
        heightAuto = false,
        videoOverlay,
        lazyLoading = true,
        id,
        media,
        mobileMedia,
        cloudinaryMedia,
        cloudinaryMobileMedia,
        cloudinaryPosterImage,
        posterImage,
        children,
        videoOptions,
        additionalOptions,
        hasHotspot,
        richText,
        tooltip,
        hidden,
        hasPadding,
        textWithMediaClass,
        lottieAnimation,
        customClassName,
        mediaDisclaimerText,
        mediaStyle,
        variant
    } = content
    const containerRef = useRef<HTMLDivElement>(null)
    const textWrapperRef = useRef<HTMLDivElement>(null)
    const [videoOverlayOpen, setVideoOverlayOpen] = useState(false)
    const [isStickyTextShown, setIsStickyTextShown] = useState(false)
    let textColor = content.textColor ? content.textColor : 'dark'
    textColor = bgColor
        ? SectionThemeDarkBgColors.includes(bgColor)
            ? 'light'
            : 'dark'
        : textColor
    const headline = content.textPanel?.headline
    const headlineTag = content.textPanel?.headlineTag
    const headlineStyle = content.textPanel?.headlineStyle
    const calloutTitle = content.textPanel?.calloutTitle
    const bodyCopy = content.textPanel?.bodyCopy
    const link = content.textPanel?.link
    const featureList = content.textPanel?.children?.find(
        (el: any) =>
            el.meta?.contentType === 'organismFeatureList' &&
            el?.variant !== 'software-info-list'
    ) as FeatureListProps | undefined
    const logoImage = content.children?.find(
        (el: any) => el.meta?.contentType === 'logoimage'
    ) as FeatureListProps | undefined
    const logoIsAnimated = logoImage?.animateLogo
    const isSpecialPage = logoImage?.isSpecialPage
    let image,
        video = null
    const isVideo = media?.file?.contentType?.includes('video')
    if (isVideo && content.media) {
        video = content?.media
    } else if (content?.cloudinaryMedia?.length) {
        image = content?.cloudinaryMedia
    }
    const cards = content?.children?.filter(
        (child: any) => child?.meta?.contentType === 'organismCard'
    ) as CardProps[]

    const { src, type, alt, isMobile } = useMedia({
        media,
        mobileMedia,
        cloudinaryMedia,
        cloudinaryMobileMedia
    })
    const { pageTheme } = useLayoutContext()
    const badge = content?.children?.find(
        (el: any) => el.meta?.contentType === 'moleculeBadge'
    )

    const textWithMediaSticky =
        additionalOptions?.sticky && fullWidth && textPosition !== 'center'

    const textAlignment =
        textPosition === 'center' || textWithMediaSticky
            ? HorizontalAlignmentEnum.CENTER
            : HorizontalAlignmentEnum.LEFT

    const openVideoOverlay = () => {
        setVideoOverlayOpen(true)
    }
    const closeVideoOverlay = () => {
        setVideoOverlayOpen(false)
    }

    const topLeft =
        content.textPanel?.textAlignment === 'left' &&
        content.textPosition === 'center'
    let playButton = null
    if (videoOverlay && videoOverlay.playButtonText) {
        const videoPlayIcon = 'play'
        playButton = (
            <Button
                id={`text-with-media-btn-${id}`}
                onClick={openVideoOverlay}
                onKeyPress={(e: React.KeyboardEvent) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                        openVideoOverlay()
                        focusController(
                            `#text-with-media-btn-${id}`,
                            '.content__open',
                            '.content__open .overlay-close'
                        )
                    }
                }}
                tracking={false}
                className={cn(
                    s['text-with-media__video-overlay-button'],
                    'popup-youtube'
                )}
                label={videoOverlay.playButtonText}
            >
                <Icon name={videoPlayIcon} />
                {videoOverlay.playButtonText}
            </Button>
        )
    }
    const additionalStyle = {
        backgroundImage: additionalOptions?.backgroundImage as string,
        backgroundColor: additionalOptions?.backgroundColor as string,
        color: additionalOptions?.color as string,
        backgroundSize: 'cover',
        backgroundRepeat: 'no-repeat'
    }
    const FeatureListComponent = () => {
        if (children?.[0]?.meta?.contentType === 'organismFeatureList') {
            const featuresList = children[0] as FeatureListProps
            const featuresListVariant = featuresList.variant
            switch (featuresListVariant) {
                case 'light-spec-features': {
                    return (
                        <TechSpecs
                            className={s['text-with-media__tech-specs']}
                            featuresClassName={
                                s['text-with-media__light-spec-features']
                            }
                            featureClassName={
                                s['text-with-media__light-spec-feature']
                            }
                            textColor={textColor}
                            variant="light-spec-features"
                            features={featuresList.features}
                            standalone
                        />
                    )
                }
                case 'hardware-features': {
                    return (
                        <div
                            className={
                                s['text-with-media__hardware-features-wrapper']
                            }
                        >
                            <TechSpecs
                                className={s['text-with-media__tech-specs']}
                                featuresClassName={
                                    s['text-with-media__hardware-features']
                                }
                                textColor={textColor}
                                variant="hardware-features"
                                features={featuresList.features}
                                standalone
                            />
                        </div>
                    )
                }
                case 'padded-text': {
                    return (
                        <PaddedTextFeatures
                            className={
                                s['text-with-media__padded-text-features']
                            }
                            features={featuresList.features}
                        />
                    )
                }
                case 'padded-text-trio': {
                    return (
                        <PaddedTextTrioFeatures
                            className={
                                s['text-with-media__padded-text-features']
                            }
                            features={featuresList.features}
                            backgroundColor={
                                pageTheme === 'dark'
                                    ? SectionBgColor.BLACK
                                    : SectionBgColor.WHITE
                            }
                        />
                    )
                }
                default: {
                    return null
                }
            }
        }
        return null
    }
    const hasTextPanelContent =
        !!content.textPanel &&
        (!!content.textPanel.headline ||
            !!content.textPanel.bodyCopy ||
            !!content.textPanel.calloutTitle ||
            !!content.textPanel.link ||
            !!content.textPanel.logos ||
            !!content.textPanel.logoImage ||
            !!content.textPanel.logoSeparator ||
            !!content.textPanel.bodyCopy ||
            !!content.textPanel.disclaimerText ||
            !!content.textPanel.richText)

    return (
        <div
            className={cn(
                s['text-with-media'],
                {
                    [s[`text-with-media--full-width`]]: fullWidth,
                    [s[
                        'text-with-media__full-width-and-height'
                    ]]: fullWidthAndHeight,
                    [s[`text-with-media__media-with-hotspot`]]:
                        variant === 'media-with-hotspot',
                    [s[`text-with-media--height-auto`]]: heightAuto,
                    [s[`text-with-media--text-top-left`]]: topLeft,
                    // Remove padding bottom in capture card, to make it look as one component with the lower
                    [s['text-with-media__inner-padding']]:
                        textWithMediaClass === 'capture-4k',
                    'md-max:hidden': textWithMediaClass === 'capture-4k-2',
                    [s['text-with-media__inner-padding']]: hasPadding === false,
                    [s['text-with-media__inner-padding-top']]:
                        content.textPanel?.noPaddingTop,
                    [bgColor]: bgColor !== SectionBgColor.TRANSPARENT,
                    // sticky option only possible if left or right and full width
                    'md:hidden': hidden === true,
                    [s[`text-with-media__top-margin`]]:
                        textWithMediaClass === 'capture-4k-2'
                },
                s[`text-with-media--text-${textPosition}`],
                customClassName
            )}
            id={id}
            ref={containerRef}
            style={additionalStyle}
        >
            <div
                className={cn(s['text-with-media__inner'], {
                    [s['text-with-media__inner-padding']]:
                        variant === 'media-with-hotspot'
                })}
            >
                {content.textPanel && hasTextPanelContent && (
                    <div
                        className={cn(
                            s['text-with-media__text-wrapper'],
                            'text-with-media-text-wrapper',
                            {
                                [s[
                                    'text-with-media__text-wrapper--sticky-active'
                                ]]: isStickyTextShown,
                                'relative z-1':
                                    textWithMediaClass === 'game-capture-4k'
                            }
                        )}
                        ref={textWrapperRef}
                        style={content.textPanel?.backgroundCSS}
                    >
                        {content.textPanel.badgeText && (
                            <div>
                                <div
                                    className={cn(
                                        s['text-with-media__badge'],
                                        'text-white uppercase text-h5-md-max font-univers67BoldCondensed'
                                    )}
                                >
                                    {content.textPanel.badgeText}
                                </div>
                            </div>
                        )}

                        <TextPanel
                            content={{
                                headline: headline,
                                headlineTag: headlineTag,
                                headlineStyle: headlineStyle,
                                calloutTitle: calloutTitle,
                                bodyCopy: bodyCopy,
                                link: link,
                                textAlignment: topLeft
                                    ? HorizontalAlignmentEnum.LEFT
                                    : textAlignment,
                                textColor: textColor,
                                className: s['text-with-media__text'],
                                logos: content.textPanel.logos,
                                logoSeparator: content.textPanel.logoSeparator,
                                logoImage: content.textPanel.logoImage,
                                logoImageWidth:
                                    content.textPanel.logoImageWidth,
                                logoImageHeight:
                                    content.textPanel.logoImageHeight,
                                logoPlacement: content.textPanel.logoPlacement,
                                children: content.textPanel.children,
                                disclaimerText:
                                    content.textPanel.disclaimerText,
                                richText: content.textPanel.richText,
                                headlineClasses:
                                    content.textPanel.headlineClasses
                            }}
                        />
                        {logoImage?.cloudinaryImage && logoIsAnimated && (
                            <div
                                className={cn(
                                    s[
                                        'text-with-media__animated-logo-container'
                                    ]
                                )}
                            >
                                <div
                                    className={cn(
                                        s['text-with-media__animated-logo']
                                    )}
                                >
                                    <div
                                        className={cn(
                                            s['text-with-media__logo-image']
                                        )}
                                    >
                                        {Array(3).fill(
                                            <ElgatoImage
                                                layout="fill"
                                                objectFit="cover"
                                                src={
                                                    logoImage.cloudinaryImage[0]
                                                        ?.secure_url
                                                }
                                            />
                                        )}
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>
                )}
                {isSpecialPage && logoImage?.cloudinaryImage && (
                    <div className={cn(s['text-with-media__gift-guide'])}>
                        <ElgatoImage
                            layout="fill"
                            src={logoImage.cloudinaryImage[0]?.secure_url}
                        />
                    </div>
                )}
                {featureList && <FeatureList content={featureList} />}
                {variant === 'media-with-hotspot' && !isMobile && (
                    <MediaWithHotspot
                        mediaStyle={mediaStyle}
                        hasHotspot={hasHotspot}
                        tooltip={tooltip}
                        richText={richText}
                        mediaDisclaimerText={mediaDisclaimerText}
                        textWithMediaClass={textWithMediaClass}
                        heightAuto={heightAuto}
                        cloudinaryMedia={cloudinaryMedia}
                        cloudinaryPosterImage={cloudinaryPosterImage}
                        cloudinaryMobileMedia={cloudinaryMobileMedia}
                    >
                        {children}
                    </MediaWithHotspot>
                )}
                {src &&
                    type === 'image' &&
                    !isMobile &&
                    variant !== 'media-with-hotspot' && (
                        <div
                            className={cn(s['text-with-media__media'], {
                                [s['text-with-media__media-padding']]:
                                    textWithMediaClass === 'capture-4k-2'
                            })}
                            style={mediaStyle}
                        >
                            <span
                                className={cn({
                                    [s[
                                        'text-with-media__media-absolute'
                                    ]]: !heightAuto
                                })}
                            >
                                <ElgatoImage src={src} alt={alt} />
                            </span>
                            <div className={cn('md-max:hidden')}>
                                {hasHotspot === true && (
                                    <TextWithMediaHotspots
                                        hotspot1={
                                            children?.[0] as FeatureListProps
                                        }
                                        hotspot2={
                                            children?.[1] as FeatureListProps
                                        }
                                        hotspot3={
                                            children?.[2] as FeatureListProps
                                        }
                                        tooltip={tooltip}
                                        richText={richText}
                                    />
                                )}
                            </div>
                            {mediaDisclaimerText && (
                                <div className={cn('text-center mt-5 italic')}>
                                    {decode(mediaDisclaimerText)}
                                </div>
                            )}
                        </div>
                    )}
                {/* Added !cloudinaryMedia to prevent duplicated video from being displayed */}
                {src &&
                    type === 'video' &&
                    !isMobile &&
                    variant !== 'media-with-hotspot' && (
                        <ElgatoVideo
                            secure_url={src}
                            options={{
                                ...defaultVideoOptions,
                                ...videoOptions
                            }}
                            className={cn(s['text-with-media__media'], 'w-fit')}
                            videoClasses={cn(
                                s['text-with-media__video'],
                                'w-fit'
                            )}
                            fallbackImgUrl={posterImage?.file.url}
                            style={mediaStyle}
                            videoDescription={content?.videoDescription}
                            onClick={openVideoOverlay}
                            onKeyPress={(e: React.KeyboardEvent) => {
                                if (e.key === 'Enter' || e.key === ' ') {
                                    openVideoOverlay()
                                    focusController(
                                        `${s['text-with-media__media']}`,
                                        '.content__open',
                                        '.content__open .overlay-close'
                                    )
                                }
                            }}
                        >
                            {playButton}
                            {children && (
                                <div>
                                    {children &&
                                        children.map((child, i) => {
                                            return (
                                                <BadgeOnVideo
                                                    key={i}
                                                    child={child}
                                                    index={i}
                                                />
                                            )
                                        })}
                                </div>
                            )}
                        </ElgatoVideo>
                    )}

                {src && type === 'image' && isMobile && (
                    <div
                        className={cn(s['text-with-media__mobile-media'])}
                        style={mediaStyle}
                    >
                        <ElgatoImage src={src} alt={alt} />
                    </div>
                )}
                {src && type === 'video' && isMobile && (
                    <div
                        className={cn(s['text-with-media__mobile-media'])}
                        style={mediaStyle}
                    >
                        <ElgatoVideo
                            secure_url={src}
                            options={{
                                ...defaultVideoOptions,
                                ...videoOptions
                            }}
                            videoClasses={s['text-with-media__mobile-video']}
                            fallbackImgUrl={posterImage?.file.url}
                            videoDescription={content?.videoDescription}
                        >
                            {playButton}
                        </ElgatoVideo>
                        <div>
                            {children &&
                                children.map((child, i) => {
                                    return (
                                        <BadgeOnVideo
                                            key={i}
                                            child={child}
                                            index={i}
                                        />
                                    )
                                })}
                        </div>
                    </div>
                )}
                {mediaDisclaimerText && (
                    <div className={cn('text-center mt-5 small-copy italic')}>
                        {decode(mediaDisclaimerText)}
                    </div>
                )}
                {cards && (
                    <div
                        className={cn('flex flex-col gap-16px my-16px', {
                            [s['text-with-media__children']]: fullWidthAndHeight
                        })}
                    >
                        {cards?.map((card: CardProps, i) => {
                            return <TextWithMediaCard card={card} key={i} />
                        })}
                    </div>
                )}
                {videoOverlay && (
                    <VideoOverlay
                        video={videoOverlay.video}
                        isOpen={videoOverlayOpen}
                        setIsOpen={setVideoOverlayOpen}
                        onClose={closeVideoOverlay}
                        posterImage={videoOverlay.posterImage}
                        videoCaptionFile={videoOverlay.videoCaptionFile}
                        videoDescriptionFile={videoOverlay.videoDescriptionFile}
                        cloudinaryVideo={videoOverlay.cloudinaryVideo}
                    />
                )}
                <FeatureListComponent />
                {lottieAnimation?.file?.url && !src && (
                    <div className={cn(s['text-with-media__lottie-animation'])}>
                        <Player
                            autoplay
                            loop
                            src={lottieAnimation?.file?.url}
                        />
                    </div>
                )}
            </div>
            {additionalOptions?.sticky && (
                <TextWithMediaStickyAnimation
                    containerRef={containerRef}
                    textWrapperRef={textWrapperRef}
                />
            )}
        </div>
    )
}

export default TextWithMedia
