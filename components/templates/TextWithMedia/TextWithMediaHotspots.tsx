import { Icon } from '@components/atoms/Icon/Icon'
import AddCircleIcon from '@components/atoms/Icon/general/AddCircleIcon'
import { useLayoutContext } from '@components/layouts/MainLayout/LayoutContext'
import { TablePopup } from '@components/molecules/TablePopup/TablePopup'
import cn from 'classnames'
import { useTranslation } from 'next-i18next'
import { FC, useState } from 'react'
import { FeatureListProps } from '../FeatureList/FeatureList'
import s from './TextWithMedia.module.scss'

interface TextWithMediaHotspotsProps {
    hotspot1?: FeatureListProps
    hotspot2?: FeatureListProps
    hotspot3?: FeatureListProps
    tooltip?: string
    richText?: string
}

export const TextWithMediaHotspots: FC<TextWithMediaHotspotsProps> = ({
    hotspot1,
    hotspot2,
    hotspot3,
    tooltip,
    richText
}) => {
    const { t } = useTranslation(['common'])
    const { pageTheme } = useLayoutContext()
    const [popupOpened, setPopupOpened] = useState(false)
    const [isHovering, setIsHovering] = useState(false)
    const [isHoveringPopup2, setIsHoveringPopup2] = useState(false)
    const [isHoveringPopup3, setIsHoveringPopup3] = useState(false)

    return (
        <div
            className={cn(s['text-with-media__table'], {
                [s['text-with-media__table-color']]: pageTheme !== 'dark'
            })}
        >
            <div
                className={cn(
                    s['text-with-media__hotspot-1'],
                    s['text-with-media__hotspot']
                )}
            >
                <div className={cn('flex flex-col gap-4')}>
                    <div className={cn('flex flex-row items-center')}>
                        <div
                            onMouseEnter={() => {
                                setIsHovering(true)
                            }}
                            onMouseLeave={() => {
                                setIsHovering(false)
                            }}
                        >
                            <Icon
                                name="informationIcon"
                                className={cn(
                                    'pr-2',
                                    s['text-with-media__icon']
                                )}
                            />
                        </div>
                        {hotspot1 && <h4>{hotspot1.headline}</h4>}
                    </div>
                </div>
            </div>

            <div className={cn(s['text-with-media__hotspot-button'])}>
                <button
                    className={cn('bg-white text-charcoal')}
                    onClick={() => {
                        setPopupOpened(true)
                    }}
                >
                    <AddCircleIcon />
                    {t('Is my device compatible?')}
                </button>
                <div className={cn('flex flex-row lg-max:flex-col')}>
                    {tooltip && (
                        <TablePopup
                            open={popupOpened}
                            onClose={() => setPopupOpened(false)}
                            richText={richText}
                        >
                            <span
                                className={cn(s['table-popup__table'])}
                                dangerouslySetInnerHTML={{
                                    __html: unescape(
                                        tooltip.replace(/\n/g, `</br>`)
                                    )
                                }}
                            />
                        </TablePopup>
                    )}
                </div>
            </div>
            <div
                className={cn(
                    s['text-with-media__hotspot-2'],
                    s['text-with-media__hotspot']
                )}
            >
                <div className={cn('flex flex-col gap-4')}>
                    <div className={cn('flex flex-row items-center')}>
                        <div
                            onMouseEnter={() => {
                                setIsHoveringPopup2(true)
                            }}
                            onMouseLeave={() => {
                                setIsHoveringPopup2(false)
                            }}
                        >
                            <Icon
                                name="informationIcon"
                                className={cn(
                                    'pr-2',
                                    s['text-with-media__icon']
                                )}
                            />
                        </div>
                        {hotspot2 && <h4>{hotspot2.headline}</h4>}
                    </div>
                </div>
            </div>
            <div
                className={cn(
                    s['text-with-media__hotspot-3'],
                    s['text-with-media__hotspot']
                )}
            >
                <div className={cn('flex flex-col gap-4')}>
                    <div className={cn('flex flex-row items-center')}>
                        <div
                            onMouseEnter={() => {
                                setIsHoveringPopup3(true)
                            }}
                            onMouseLeave={() => {
                                setIsHoveringPopup3(false)
                            }}
                        >
                            <Icon
                                name="informationIcon"
                                className={cn(
                                    'pr-2',
                                    s['text-with-media__icon']
                                )}
                            />
                        </div>
                        {hotspot3 && <h4>{hotspot3.headline}</h4>}
                    </div>
                </div>
            </div>
            <div className={cn(s['text-with-media__hotspot-popup-main'])}>
                <div
                    className={cn(
                        s['text-with-media__hotspot-popup'],
                        s['text-with-media__hotspot-popup-1'],
                        {
                            [s[
                                'text-with-media__hotspot-popup-1-visible'
                            ]]: isHovering
                        }
                    )}
                >
                    {hotspot1?.featuresText && (
                        <div
                            dangerouslySetInnerHTML={{
                                __html: unescape(
                                    hotspot1.featuresText?.replace(
                                        /\n/g,
                                        `</br>`
                                    )
                                )
                            }}
                        >
                            {hotspot1?.featuresText}
                        </div>
                    )}
                    {hotspot1?.featureDisclaimerText && (
                        <div
                            className={cn(
                                'italic',
                                s['text-with-media__hotspot-popup-font']
                            )}
                        >
                            {hotspot1?.featureDisclaimerText}
                        </div>
                    )}
                </div>
                <div
                    className={cn(
                        s['text-with-media__hotspot-popup'],
                        s['text-with-media__hotspot-popup-2'],
                        {
                            [s[
                                'text-with-media__hotspot-popup-2-visible'
                            ]]: isHoveringPopup2
                        }
                    )}
                >
                    {hotspot2?.featuresText && (
                        <div
                            dangerouslySetInnerHTML={{
                                __html: unescape(
                                    hotspot2.featuresText.replace(
                                        /\n/g,
                                        `</br>`
                                    )
                                )
                            }}
                        >
                            {hotspot2?.featuresText}
                        </div>
                    )}
                    {hotspot2?.featureDisclaimerText && (
                        <div
                            className={cn(
                                s['text-with-media__hotspot-popup-font']
                            )}
                        >
                            {hotspot2?.featureDisclaimerText}
                        </div>
                    )}
                </div>
                <div
                    className={cn(
                        s['text-with-media__hotspot-popup'],
                        s['text-with-media__hotspot-popup-3'],
                        {
                            [s[
                                'text-with-media__hotspot-popup-3-visible'
                            ]]: isHoveringPopup3
                        }
                    )}
                >
                    {hotspot3?.featuresText && (
                        <div
                            dangerouslySetInnerHTML={{
                                __html: unescape(
                                    hotspot3.featuresText.replace(
                                        /\n/g,
                                        `</br>`
                                    )
                                )
                            }}
                        >
                            {hotspot3?.featuresText}
                        </div>
                    )}
                    {hotspot3?.featureDisclaimerText && (
                        <div
                            className={cn(
                                'italic',
                                s['text-with-media__hotspot-popup-font']
                            )}
                        >
                            {hotspot3?.featureDisclaimerText}
                        </div>
                    )}
                </div>
            </div>
        </div>
    )
}
