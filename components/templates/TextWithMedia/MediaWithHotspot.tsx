import { CloudinaryMedia } from '@components/common/CloudinaryMedia/Cloudinary'
import ElgatoMedia from '@components/common/ElgatoMedia/ElgatoMedia'
import cn from 'classnames'
import { decode } from 'he'
import { FC } from 'react'
import { FeatureListProps } from '../FeatureList/FeatureList'
import s from './TextWithMedia.module.scss'
import { TextWithMediaHotspots } from './TextWithMediaHotspots'

interface MediaWithHotspotProps {
    // src: string
    // alt: string
    mediaStyle?: React.CSSProperties
    hasHotspot?: boolean
    tooltip?: string
    richText?: string
    mediaDisclaimerText?: string
    textWithMediaClass?: string
    heightAuto?: boolean
    children?: Array<FeatureListProps | any>
    cloudinaryMedia?: CloudinaryMedia[]
    cloudinaryPosterImage?: CloudinaryMedia[]
    cloudinaryMobileMedia?: CloudinaryMedia[]
}

const MediaWithHotspot: FC<MediaWithHotspotProps> = ({
    hasHotspot,
    tooltip,
    richText,
    mediaDisclaimerText,
    children,
    cloudinaryMedia,
    cloudinaryPosterImage,
    cloudinaryMobileMedia
}) => {
    return (
        <div
            className={cn(
                s['text-with-media__media'],
                s['text-with-media__media-padding'],
                s['text-with-media__media-with-hotspot']
            )}
        >
            <span className={cn(s['text-with-media__media-absolute'])}>
                <ElgatoMedia
                    cloudinaryMedia={cloudinaryMedia}
                    cloudinaryPosterImage={cloudinaryPosterImage}
                    cloudinaryMobileMedia={cloudinaryMobileMedia}
                    loop
                    autoPlay
                />
            </span>
            <div className={cn('md-max:hidden')}>
                {hasHotspot === true && (
                    <TextWithMediaHotspots
                        hotspot1={children?.[0] as FeatureListProps}
                        hotspot2={children?.[1] as FeatureListProps}
                        hotspot3={children?.[2] as FeatureListProps}
                        tooltip={tooltip}
                        richText={richText}
                    />
                )}
            </div>
            {mediaDisclaimerText && (
                <div className={cn('text-center mt-5 italic')}>
                    {decode(mediaDisclaimerText)}
                </div>
            )}
        </div>
    )
}

export default MediaWithHotspot
