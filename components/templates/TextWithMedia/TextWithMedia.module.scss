.table-popup__table {
    display: flex;
    gap: 20px;
    flex-direction: row;
    flex-wrap: wrap;

    @media only screen and (max-width: 1399px) {
        justify-content: center;
    }

    @media only screen and (min-width: 1399px) {
        flex-wrap: nowrap;
    }
}

@keyframes bannermove {
    0% {
        transform: translate(0, 0);
    }

    100% {
        transform: translate3d(-200%, 0, 0);
    }
}
.text-with-media {
    &__hardware-features-wrapper {
        @screen md {
            margin-left: auto;
            margin-right: auto;
            max-width: 1490px;
        }
    }
    &__gift-guide {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-40%, -50%);
        z-index: 1;
        width: 100%;
        height: 100%;
        max-width: 800px;
        max-height: 500px;
        @screen md-max {
            width: 210px;
            height: 112px;
        }
        img {
            object-fit: contain;
        }
    }

    &__animated-logo-container {
        position: relative;
        overflow: hidden;
        width: 100%;
        margin-top: 40px;

        &::after {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            content: '';
            background: linear-gradient(
                    90deg,
                    #fff 0%,
                    transparent 5%,
                    transparent 95%,
                    #fff 100%
            );
        }

        img {
            display: inline-block;
            height: 100%;
            width: 100%;
            @screen md-max {
                height: 100%;
            }
        }
    }

    &__animated-logo {
        white-space: nowrap;
        animation: bannermove 18s linear infinite;
    }

    &__logo-image {
        width: 200%;
    }

    &__badge {
        display: flex;
        align-items: center;
        padding: 6px 8px;
        margin-bottom: 24px;
        width: max-content;
        border-radius: 2px;
        background-color: var(--primitive-purple-90);
        white-space: break-spaces;
        @screen md-max {
            margin: 0 auto 24px auto;
        }
    }

    @apply relative;

    /* BG Color */
    &--bg-blue {
        @apply bg-content-blue;
    }

    &__image-overlay {
        max-width: 640px;

        span {
            position: absolute !important;
            left: 50% !important;
            top: 50% !important;
            transform: translate(-50%, -50%) !important;
            max-width: 400px;
            z-index: 1;

            @screen md-max {
                max-width: 160px;
            }
        }

        img {
            object-fit: contain !important;
        }
    }

    &__inner {
        @apply px-16px py-16;
        @apply mx-auto w-full h-auto;

        @screen lg {
            @apply px-64px py-32;
        }

        @screen xxl {
            @apply max-w-8xl;
        }
    }

    &__inner-padding {
        @screen md {
            padding-bottom: 0;
        }
    }

    &.overlay {
        .text-with-media__inner {
            padding: 0;
        }
    }

    &__image {
        width: 100vw;
        height: auto;
        min-height: auto !important;
    }

    &__text-wrapper {
        @apply w-full mx-auto pb-16;

        @screen md {
            @apply pb-32;
        }

        @screen xl {
            padding-left: 0;
            padding-right: 0;
            max-width: 888px;
        }
    }

    &__table {
        display: flex;
        gap: 20px;
        flex-direction: row;
        flex-wrap: wrap;

        h4 {
            max-width: 400px;
            width: 100%;
        }

        @media screen and (max-width: 1200px) {
            h4 {
                font-size: 16px !important;
            }
        }
    }

    &__icon {
        @media screen and (max-width: 1200px) {
            width: 25px;
            height: 25px;
        }
    }
    // Case where we have a hotspot with a popup
    // ..............................................................
    .text-with-media__media-with-hotspot {
        @screen lg {
            max-width: 1920px !important;
            padding-bottom: 0 !important;
            width: 100% !important;
        }
    }
    &__table-color {
        .text-with-media__hotspot {
            color: black;
        }
        .text-with-media__hotspot-button {
            top: 45%;
        }
        .text-with-media__hotspot-popup {
            color: black;
        }
        button {
            color: white;
            background-color: black;
        }
        .text-with-media__icon {
            svg {
                color: black;
            }
        }
       
        .text-with-media__hotspot-popup {
            background-color: #f6f6f6;
        }
        .text-with-media__hotspot-button{
            @screen lg {
                top: 50%;
            }
        }
    }
    &__hotspot {
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: 8px;
        position: absolute;
        z-index: 10;
        color: white;
    }

    &__hotspot-1 {
        top: 10%;
        left: 7%;
        @screen lg {
            top: 20%;
        }
    }

    &__hotspot-2 {
        top: 10%;
        left: 72%;
        @screen lg {
            top: 20%;
            left: 76%;
        }
    }

    &__hotspot-3 {
        left: 50%;
        top: 52%;
        @screen lg {
        top: 65%;
       }
        transform: translate(-50%, 0);
    }

    &__hotspot-popup {
        position: absolute;
        margin-bottom: 20px;
        opacity: 0;
        border-radius: 4px;
        border: 1px solid var(--primitive-gray-100);
        background: var(--primitive-gray-120);
        color: white;
        padding: 20px;
        max-width: 314px;
        width: 100%;
    }

    &__hotspot-popup-1 {
        @screen lg {
            top: 25%;
        }
        left: 7.4%;
        top: 17%;
        &-visible {
            opacity: 1;
            z-index: 10;
        }
    }

    &__hotspot-popup-2 {
        @screen lg {
            right: 8%;
            top: 25%;
        }
        right: 1.3%;
        top: 16%;
        &-visible {
            opacity: 1;
            z-index: 10;
        }
    }

    &__hotspot-popup-3 {
        @screen lg {
            top: 70%;
            left: 50%;
        }
        top: 58%;
        left: 51.3%;
        transform: translate(-50%, 0);

        &-visible {
            opacity: 1;
            z-index: 10;
        }
    }

    &__hotspot-popup-font {
        @apply italic;
    }

    &__hotspot-button {
        position: absolute;
        top: 50%;
        left: 7%;

        @screen md {
            max-width: 279px;
            width: 100%;
        }

        @media screen and (max-width: 900px) {
            max-width: 230px;
        }

        button {
            padding: 10px 0 10px 0;
            display: flex;
            justify-content: center;
            align-items: center;
            grid-gap: 8px;
            gap: 8px;
            border-radius: 6px;
            min-width: 200px;
            width: 100%;
        }
    }
    &__media {
        @apply relative w-full h-full rounded-xxl overflow-hidden;
        /* safari fix */
        transform: translateZ(0);

        &-absolute {
            @apply absolute top-0 left-0 w-full h-full;
        }
    }
// ..............................................................

    &--text-center &__media {
        width: fit-content !important;
    }

    .text-with-media__mobile-media {
        position: relative;
        @apply rounded-xl overflow-hidden;
    }

    &__mobile-video {
        @apply rounded-xl overflow-hidden;
    }

    &__text {
        @media (max-width: 1023px) {
            @apply text-center;
        }

        > * {
            @media (max-width: 1023px) {
                @apply justify-center;
            }
        }

        :global {
            .primary-text > * {
                @media (max-width: 1023px) {
                    @apply justify-center;
                }
            }
        }
    }

    /* Video Overlay */
    &__video-overlay-button {
        @apply absolute top-1/2 left-1/2;
        transform: translate(-50%, -50%);
        z-index: 10;
    }

    /* Center */
    &--text-center {
        /* the image or video below, should always has height auto */
        .text-with-media__media {
            width: 100%;
            @apply mx-auto;
            max-width: 1490px;

            > div,
            > span,
            > span > img,
            > span > video {
                min-width: 0 !important;
                min-height: 0 !important;
                max-height: none !important;
                object-fit: contain;
                width: 100% !important;
                height: unset !important;
                position: unset !important;
                padding-top: 0 !important;
            }
        }

        .text-with-media__media-padding {
            padding-bottom: 92px;
        }
    }

    /* Left and right */
    &--text-left,
    &--text-right {
        .text-with-media__inner {
            @apply py-0 pb-16;

            @screen lg {
                @apply flex items-center px-64px py-32;
            }
        }

        .text-with-media__media {
            padding-bottom: 121%;

            @screen lg {
                @apply mt-0;
                flex: 0 0 auto;
                width: 50%;
                padding-bottom: 51.34%;
                /* (920 / 896) * (100 / 2) */
            }
        }

        .text-with-media__video {
            @apply absolute top-0 left-0 w-full h-full object-cover;
        }

        .text-with-media__text-wrapper {
            @apply py-16 px-0;

            @screen md {
                @apply py-32;
            }

            @screen md {
                max-width: none;
                flex: 1 1 auto;
            }

            @screen lg {
                max-width: 50%;
                flex: 0 0 auto;
                padding-left: var(--container-padding--md);
                padding-right: var(--container-padding--md);
            }

            @screen lg2 {
                padding-left: var(--container-padding--lg);
                padding-right: var(--container-padding--lg);
            }

            @screen xl {
                padding-left: calc(100% / var(--grid-cols--lg));
                padding-right: calc((100% / var(--grid-cols--lg)));
            }
        }

        .text-with-media__text {
            max-width: none !important;

            h2,
            h1 {
                @media (min-width: 1024px) and (max-width: 1200px) {
                    font-size: 41px;
                    line-height: 120%;
                }
            }

            :global .body-copy {
                @media (min-width: 1024px) and (max-width: 1200px) {
                    font-size: 20px;
                    line-height: 140%;
                }
            }
        }
    }

    /* Right */
    &--text-right {
        .text-with-media__text-wrapper {
            @screen lg {
                @apply order-1;
            }
        }
    }

    /* Top Left */
    &--text-top-left.text-with-media--text-center {
        .text-with-media__text-wrapper {
            @screen lg {
                margin-left: 0;
            }
        }
    }

    &--text-top-left {
        .text-with-media__inner {
            @apply max-w-1490px;
        }
    }

    /* no padding */
    &--full-width,
    &__full-width-and-height {
        .text-with-media__inner {
            @apply max-w-2560px;
            padding: 0;
        }

        .text-with-media__text-wrapper {
            padding-left: var(--container-padding);
            padding-right: var(--container-padding);
        }

        .text-with-media__media {
            border-radius: 12px;
        }

        /* Centered */
        &.text-with-media--text-center {
            .text-with-media__text-wrapper {
                @apply pt-16;
                max-width: none;

                @screen md {
                    @apply pt-32;
                    padding-left: var(--container-padding--md);
                    padding-right: var(--container-padding--md);
                }

                @screen lg2 {
                    padding-left: calc((100% / var(--grid-cols--lg)));
                    padding-right: calc((100% / var(--grid-cols--lg)));
                }
            }

            .text-with-media__text {
                max-width: 888px;
                margin: 0 auto;
            }

            .text-with-media__media {
                max-width: none;
            }
        }

        /* Left and right */
        &.text-with-media--text-left,
        &.text-with-media--text-right {
            .text-with-media__inner {
                @apply py-0;
            }

            .text-with-media__text-wrapper {
                @screen lg {
                    @apply absolute top-2/4 left-2/4 max-w-8xl px-32px;
                    @apply transform -translate-x-1/2 -translate-y-1/2;
                    margin: 0 auto;
                }

                @screen xl {
                    @apply px-64px;
                }
            }

            .text-with-media__text {
                @screen md {
                    padding-left: var(--container-padding--md);
                    padding-right: var(--container-padding--md);
                }

                @screen lg2 {
                    padding-left: calc((100% / var(--grid-cols--lg)));
                    padding-right: calc((100% / var(--grid-cols--lg)));
                }
            }

            .text-with-media__media {
                padding-bottom: 146.67%;

                @screen lg {
                    padding-bottom: 56.25%;
                    /* (1080 / 960) * (100 / 2) */
                }
            }
        }

        /* Right */
        &.text-with-media--text-right {
            .text-with-media__text {
                @screen lg {
                    margin-left: 50%;
                }
            }
        }

        /* Left */
        &.text-with-media--text-left {
            .text-with-media__text {
                @screen lg {
                    margin-right: 50%;
                }
            }

            .text-with-media__media {
                @screen lg {
                    margin-left: 50%;
                }
            }
        }
    }

    &__full-width-and-height {
        .text-with-media__inner {
            @apply max-w-2560px;
            padding: 0;
        }

        .text-with-media__media {
            border-radius: 0;
            padding: 0;
        }

        .text-with-media__mobile-media {
            border-radius: 0;
        }
    }

    &__children {
        @apply my-0;
    }
    &__inner-padding-top {
        .text-with-media__inner {
            @screen md-max {
                padding-top: 0 !important;
            }
        }
    }
    &__inner-padding {
        .text-with-media__inner {
            @screen md {
                padding-bottom: 0 !important;
                padding-top: 0 !important;
            }

            @screen md-max {
                padding-bottom: 0 !important;
            }
        }

        .text-with-media__mobile-media {
            @screen md-max {
                overflow: visible;
                transform: translateZ(0);
            }
        }
    }

    &__bottom-padding &--center {
        .text-with-media__media {
            @sceen md {
                padding-bottom: 92px;
            }
        }
    }

    /* height auto */
    &--height-auto,
    &--height-auto.text-with-media--text-left,
    &--height-auto.text-with-media--text-right {
        /* the image or video below, should always has height auto */
        .text-with-media__media {
            padding-bottom: 0;

            > div,
            > span,
            > span > img,
            > span > video {
                min-width: 0 !important;
                min-height: 0 !important;
                max-height: none !important;
                object-fit: contain;
                width: 100% !important;
                height: unset !important;
                position: unset !important;
                padding-top: 0 !important;
            }
        }

        .text-with-media__video {
            position: relative;
        }
    }

    &--height-auto.text-with-media--text-left,
    &--height-auto.text-with-media--text-right {
        .text-with-media__text-wrapper {
            @screen lg {
                @apply py-0;
            }
        }
    }

    &__tech-specs {
        .text-with-media {
            &__light-spec-features,
            &__hardware-features {
                margin-top: var(--spacing--sm);
                padding-bottom: 0;

                @screen lg {
                    margin-top: var(--spacing--lg);
                    padding-bottom: 0;
                }
            }

            &__light-spec-features {
                .text-with-media__light-spec-feature {
                    @screen lg {
                        width: calc(
                            (100% - 2 * 1.6rem) / 3
                        ); // 1.6rem: gap top of tech specs features
                    }
                }
            }

            &__hardware-features {
                @screen lg {
                    max-width: 1490px;
                }
            }
        }
    }

    &__padded-text-features {
        max-width: 1490px;
        margin-top: 40px;
        margin-left: auto;
        margin-right: auto;

        @screen md {
            margin-top: 80px;
        }
    }

    /* Sticky only for left and right and full width */
    &--sticky.text-with-media--full-width {
        position: relative;

        .text-with-media__inner {
            @screen md {
                display: flex;
                flex-direction: row;
                align-items: flex-start;
            }

            .text-with-media__text-wrapper {
                @screen md {
                    position: relative;
                    display: flex;
                    flex-direction: column;
                    flex: 1;
                    padding: 0;
                    transform: none;
                    top: 0;
                    left: 0;
                }

                .text-with-media__text {
                    @screen md {
                        margin: 0;
                    }
                }
            }

            .text-with-media__media {
                @screen md {
                    margin: 0;
                    flex: 1;
                }
            }
        }

        .text-with-media__media {
            @screen md {
                position: sticky;
                top: 89px; // TODO sticky nav
                left: 0;
                width: 100%;
                height: calc(50vh - 89px); // TODO minus sticky nav
            }
        }

        .text-with-media__media {
            @screen md {
                padding-bottom: calc(100vh - 89px); // TODO minus sticky nav
            }
        }

        .text-with-media__text {
            h2 {
                @screen md {
                    position: sticky;
                    top: 89px;
                    will-change: opacity, top;
                    opacity: 1;
                    display: flex;
                    height: calc(100vh - 89px);
                    justify-content: center;
                    align-items: center;
                }
            }

            :global {
                .body-copy {
                    @screen md {
                        will-change: opacity;
                        opacity: 0;
                        margin-top: 0;
                        padding-bottom: calc(50vh - 89px);
                    }
                }
            }
        }
    }

    &__card {
        @apply max-w-1490px mx-auto;
        max-width: 1190px;

        ul {
            font-size: 16px;
            padding-left: 32px;
            list-style-type: disc;

            @screen lg {
                font-size: 20px;
            }
        }
    }
    &__top-margin {
        margin-top: 88px;
    }
}
