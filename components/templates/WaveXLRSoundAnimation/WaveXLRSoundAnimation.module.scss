.wave-xlr-sound-animation {
    @apply relative;
    background-color: black;

    @screen md {
        height: 400vh;
    }

    &__item {
        @apply relative w-full h-auto;

        @screen md {
            @apply absolute top-0 left-0 w-full h-full;
        }
    }

    &__sticky {
        width: 100%;

        @screen md {
            position: sticky;
            top: 88px;
            left: 0;
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: flex-end;
            height: calc(100vh - 88px);
        }
    }

    &__anim-wrapper {
        position: relative;
        width: 100%;
        z-index: 1;

        @screen md {
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
        }
    }

    &__item {
        @screen md {
            opacity: 0;
            z-index: -1;
        }

        &--active {
            @screen md {
                opacity: 1;
                z-index: 1;
            }
        }
    }

    &__main-image {
        pointer-events: none;
        width: 100vw;
        z-index: 200;
        height: 38vh;
        transform: translate(0, -4vh);

        @screen md-max {
            top: auto;
            left: 0;
            position: absolute;
            bottom: 0;
        }

        @screen md {
            position: relative;
            height: 70vh;
            transform: none;
        }

        @media screen and (min-width: 768px) and (max-height: 1100px) {
            height: 50vh;
        }

        &--mobile {
            @screen md {
                display: none;
            }
        }

        &--desktop {
            display: none;

            @screen md {
                display: block;
            }
        }
    }

    &__anim-2 {
        background-color: #121212;

        > div > div:nth-child(2) > div {
            &::before {
                @screen md {
                    transform: translate(0, -20px);
                    height: 100%;
                    content: "";
                    width: 100%;
                    position: absolute;
                    bottom: 0;
                    top: 0;
                    left: 0;
                    right: 0;
                    background: black;
                    background: linear-gradient(180deg, #121212 0%, black 100%);
                    z-index: 0;
                }
            }
        }

        > div {
            z-index: 1;
        }

        video {
            pointer-events: none;
            z-index: 1;
            position: relative;
        }
    }
}
