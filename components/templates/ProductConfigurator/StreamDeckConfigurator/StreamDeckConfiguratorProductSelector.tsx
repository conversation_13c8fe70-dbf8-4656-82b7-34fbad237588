import s from './StreamDeckConfiguratorProductSelector.module.scss'
import cn from 'classnames'
import React, { FC, ReactNode, useCallback, useMemo } from 'react'
import { Price } from '@components/atoms/Price/Price'
import { ConfigurableProduct, SimpleProduct } from '@pylot-data/fwrdschema'
import { useRouter } from 'next/router'
import { usePrice } from '@corsairitshopify/pylot-price/index'
import { formatPriceSymbol } from '@lib/utils/priceUtils'
import { Checkbox } from '@components/molecules/Checkbox/Checkbox'
import { Badge } from '@components/atoms/Badge/Badge'
import { useTranslation } from 'next-i18next'
import { useProductUI } from '@pylot-data/hooks/product/use-product-ui'
import PrimaryText from '@components/molecules/PrimaryText/PrimaryText'
import { useStoreConfig } from '@config/index'
import { PageTheme } from '@components/layouts/MainLayout/LayoutContext'
import { prompterCustomPrice } from '@components/templates/ProductConfigurator/PrompterConfigurator/PrompterConfigurator'
import ElgatoImage from '@components/common/ElgatoImage'
import { IThirdPartyPromoMessage } from '@components/common/types'

export type StreamDeckProductSelectorProps = {
    className?: string
    variant?: 'checkbox' | 'radio' | 'disabled'
    selectable?: boolean
    product: SimpleProduct | ConfigurableProduct
    selected?: boolean
    onSelect?: (val: boolean) => void
    color?: 'blue' | 'dark'
    free?: boolean
    thumbnail?: string | undefined
    headline?: string
    subheadline?: string
    ignoreOutOfStock?: boolean
    ignoreRadioSelected?: boolean
    showFreeWhenNotSelected?: boolean
    isMobileCardStyle?: boolean
    imageClass?: string
    calloutmessage?: ReactNode
    richText?: string
    theme?: PageTheme
    customTag?: string | undefined
    customPrice?: prompterCustomPrice | undefined
    promoInfo?: IThirdPartyPromoMessage
    promoSelected?: boolean
    setPromoCampaignSelected: (selected: boolean) => void
}

export const StreamDeckConfiguratorProductSelector: FC<StreamDeckProductSelectorProps> = (
    props
) => {
    const {
        variant = 'checkbox',
        className,
        product,
        thumbnail = '',
        headline,
        selectable = true,
        selected = true,
        onSelect,
        color = 'blue',
        free = false,
        subheadline,
        calloutmessage = '',
        ignoreOutOfStock = false,
        imageClass = '',
        richText,
        theme = 'light',
        customTag = undefined,
        customPrice = undefined,
        promoSelected,
        promoInfo,
        setPromoCampaignSelected
    } = props
    const { locale } = useRouter()
    const { total, currency } = usePrice(product.price_range)
    const { isOutOfStock } = useProductUI(product)
    const { t } = useTranslation(['common'])
    const {
        base: { currencyConfig }
    } = useStoreConfig()
    const currencyMap = currencyConfig?.elgato?.currencyMap
    const formattedPrice = useMemo(() => {
        return total
            ? formatPriceSymbol(total, currency, locale, currencyMap)
            : ''
    }, [currency, locale, total, currencyMap])

    const disabled = useMemo(() => {
        return !selectable || (isOutOfStock && !ignoreOutOfStock)
    }, [selectable, isOutOfStock, ignoreOutOfStock])

    const updateSelected = useCallback(() => {
        if (!disabled) {
            onSelect?.(!selected)
        }
    }, [disabled, onSelect, selected])

    const hasPromo = !!product?.promo_campaigns?.[0]?.campaign

    return (
        <>
            <div
                className={cn(
                    s['selector'],
                    s[`selector--${variant}`],
                    {
                        'cursor-auto': disabled,
                        [s['selector--selected']]:
                            color === 'blue' && selected && !isOutOfStock
                    },
                    'flex gap-8px justify-between items-start h-full w-full rounded-md px-16px py-12px border',
                    className
                )}
                onClick={updateSelected}
                onKeyPress={updateSelected}
                role="button"
                tabIndex={0}
            >
                <div className="flex gap-16px items-start h-full">
                    {(!isOutOfStock || ignoreOutOfStock) && (
                        <Checkbox
                            variant="round"
                            checked={
                                selected && (!isOutOfStock || ignoreOutOfStock)
                            }
                            color={color}
                            disabled={disabled}
                            checkmark={variant === 'checkbox'}
                            isHaveSelectedAllOption
                            className={s['selector__checkbox']}
                        />
                    )}
                    <div
                        className={cn(s['selector__image-wrapper'], imageClass)}
                    >
                        {product.small_image?.url && !thumbnail && (
                            <ElgatoImage
                                alt={product.name?.toString() ?? ''}
                                src={product.small_image.url}
                                width={52}
                                height={39}
                                objectFit="cover"
                            />
                        )}
                        {!product.small_image?.url && thumbnail && (
                            <ElgatoImage
                                alt={product.name?.toString() ?? ''}
                                src={thumbnail}
                                width={52}
                                height={39}
                                objectFit="cover"
                            />
                        )}
                    </div>
                    <div data-sku={product.sku}>
                        {calloutmessage}
                        <h6
                            dangerouslySetInnerHTML={{
                                __html: `${headline || product.name}`
                            }}
                        />
                        {subheadline && (
                            <PrimaryText
                                className={cn(s['selector__subheadline'])}
                                subheader={subheadline}
                            />
                        )}
                        {richText && (
                            <div
                                className={s['selector__rich-text']}
                                dangerouslySetInnerHTML={{ __html: richText }}
                            />
                        )}
                    </div>
                </div>

                <div
                    className={cn(
                        `flex gap-8px h-full ${
                            customTag ? 'flex-col items-end' : 'items-center'
                        }`
                    )}
                >
                    {customTag && (
                        <Badge className="bg-purple-plum-1">
                            {t(customTag)}
                        </Badge>
                    )}
                    {isOutOfStock && !ignoreOutOfStock ? (
                        <span className="xs-copy text-mid-grey-1">
                            {t('Out of Stock')}
                        </span>
                    ) : !customPrice?.discountPrice ? (
                        <Price
                            price={formattedPrice}
                            size="small"
                            theme={theme === 'dark' ? 'light' : 'dark'}
                            variant={free && selected ? 'discount' : 'default'}
                        />
                    ) : (
                        <div className="flex gap-8px">
                            <Price
                                price={customPrice?.discountPrice!}
                                size="small"
                                theme={theme === 'dark' ? 'light' : 'dark'}
                                variant="default"
                            />
                            <Price
                                price={customPrice?.price!}
                                size="small"
                                theme={theme === 'dark' ? 'light' : 'dark'}
                                variant="discount"
                            />
                        </div>
                    )}
                    {free && selected && !customTag && (
                        <Badge className="bg-purple-plum-1">{t('Free')}</Badge>
                    )}
                </div>
            </div>
            {selected && hasPromo && (
                <div
                    className={cn(
                        s['selector'],
                        s['selector--promo'],
                        'ml-32px mt-8px flex gap-12px lg:gap-8px lg3:gap-12px items-start h-full rounded-md px-16px py-12px border',
                        {
                            [s['selector--selected']]: promoSelected
                        }
                    )}
                    onClick={(e) => {
                        e.stopPropagation()
                        setPromoCampaignSelected(!promoSelected)
                    }}
                    onKeyPress={(e) => {
                        e.stopPropagation()
                        setPromoCampaignSelected(!promoSelected)
                    }}
                    role="button"
                    tabIndex={0}
                >
                    <Checkbox
                        variant="squared"
                        checked={promoSelected}
                        color={color}
                        disabled={false}
                        checkmark
                        isHaveSelectedAllOption
                        className={s['selector__checkbox']}
                    />
                    <div className={cn(s['selector__image-wrapper'])}>
                        <ElgatoImage
                            alt={promoInfo?.title}
                            src={promoInfo?.image?.file?.url ?? ''}
                            width={84}
                            height={64}
                            objectFit="cover"
                            className="rounded-md"
                        />
                    </div>
                    <div>
                        <h6 className="mt-4px">{promoInfo?.title}</h6>
                        <p className={cn(s['selector__subheadline'])}>
                            {promoInfo?.description}
                        </p>
                    </div>
                    <Badge className="bg-purple-plum-1">{t('Free')}</Badge>
                </div>
            )}
        </>
    )
}
