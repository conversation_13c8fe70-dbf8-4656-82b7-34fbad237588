.stream-deck-configurator {
    &__selector {
        border-color: #eaeaea;
        min-height: 76px;
        margin-bottom: 0px;

        input[type='checkbox'] {
            border-color: var(--content-blue) !important;
        }

        input[type='checkbox']:checked {
            border-color: var(--content-blue) !important;
            background-color: var(--content-blue) !important;
        }
    }
    &-price,
    &-subprice {
        font-size: 1.8rem;
        @screen md-max {
            font-size: 1.6rem;
        }
    }
    &-subtotal {
        line-height: 150%;
    }
    &-headline {
        color: var(--white);
        font-size: 14px !important;
        font-style: normal !important;
        font-weight: 700 !important;
        line-height: 120% !important;
        text-transform: uppercase;
        @screen lg-max {
            font-size: 12px !important;
        }
    }
    &-subheader {
        color: var(--white);
        font-size: 14px !important;
        font-style: normal !important;
        font-weight: 700 !important;
        line-height: 120% !important;
        text-transform: uppercase;
        margin: 32px 0 8px 0;
        @screen lg-max {
            font-size: 12px !important;
            margin: 24px 0 8px 0;
        }
    }

    &__bundlepack {
        border-color: #eaeaea;
        min-height: 76px;
        margin-bottom: 0px;

        input[type='checkbox'] {
            border-color: #151515;
        }

        input[type='checkbox']:checked {
            border-color: #151515;
            background-color: #151515;
        }

        &__title {
            font-size: 14px;
            line-height: 16.8px;
            font-weight: 700;
            text-transform: uppercase;
        }

        &:first-child {
            height: 100%;
        }
    }

    &__imageselector {
        display: none !important;
    }

    &__calloutmessage {
        display: inline-block;
        height: 17px;
        padding: 2px;
        padding-left: 10px;
        padding-right: 10px;
        border-radius: 2px 0px 0px 0px;
        background: #f96800;
        font-size: 11px;
        font-weight: 700;
        line-height: 14.4px;
        margin-bottom: 5px;
    }

    & .ConfiguratorProductSelector_conf-product-selector--selected {
        border-color: var(#204cfe);
    }

    &__inner {
        max-width: 1445px;
        margin-left: auto;
        margin-right: auto;
        border-radius: 12px;
        @media screen and (max-width: 550px) {
            border-radius: 0 0 12px 12px;
        }
    }

    &__img-container {
        position: relative;
        width: 100%;
        max-width: 1024px;
        max-height: 1024px;
        overflow: hidden;
        border-radius: 12px;
        height: 100%;
        aspect-ratio: 1;
    }

    &__img {
        position: absolute;
        object-fit: cover;
        border-radius: 8px;
        width: 100%;
        max-width: 865px;
        max-height: 720px;
        aspect-ratio: 1 / 1;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        @media screen and (max-width: 550px) {
            border-radius: 0;
        }
        @screen lg-max {
            max-width: 500px;
        }
    }

    &__footer {
        min-height: 70px;
        height: min-content;
        padding: 16px;
        margin: 0 -16px;
        width: calc(100% + 32px);

        @screen md-max {
            border-radius-top-left-radius: 12px;
            border-radius-top-right-radius: 12px;
            padding: 16px;
        }
        @media screen and (max-width: 550px) {
            flex-direction: column;
            align-items: flex-start;
        }

        @screen lg {
            padding: 4px 14px;
            margin: 0;
            width: initial;
        }
    }

    &__selector-divider {
        @screen lg {
            max-height: calc(100vh - 400px);
            overflow-y: auto;
            flex: 1;
            min-height: 300px;
            &::-webkit-scrollbar-track {
                border-radius: 9999px;
                background-color: #525252;
                margin-left: 10px;
            }
            &::-webkit-scrollbar {
                width: 8px;
                background-color: #525252;
                border-radius: 9999px;
                margin-left: 10px;
            }
            &::-webkit-scrollbar-thumb {
                border-radius: 9999px;
                background-color: #dbdbdb;
                margin-left: 10px;
            }
        }
        @screen md-max {
            overflow-y: visible;
            max-height: none;
        }
    }

    &__price {
        font-size: 18px;
        color: white !important;
    }

    &__img-wrapper {
        height: 100%;
        width: 100%;
        max-width: 865px;
        max-height: 720px;
        aspect-ratio: 1 / 1;
        margin: 0 auto;
        @screen lg-max {
            max-width: 500px;
        }
    }

    &__selector-wrapper {
        position: relative;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    @media (max-width: 1024px) {
        padding-top: 15px;
        margin-top: 25px;
        padding-bottom: 0;

        &__inner {
            margin-top: 16px;
            margin-bottom: 16px;
            margin-left: unset;
            margin-right: unset;
        }

        & > div:first-child {
            color: white;
        }

        &__inner > div {
            flex-direction: column;
        }

        &__inner > div:first-child {
            padding: unset !important;
        }

        &__selector-wrapper {
            color: black !important;
            padding-left: 16px;
            padding-right: 16px;
            max-width: 128rem;
            @media screen and (min-width: 551px) {
                gap: 15px;
            }

            &-dark {
                color: white !important;
            }
        }

        &__img-wrapper > div {
            border-radius: unset;
        }
    }
}

.stream-deck-atc-btn-dark {
    background-color: var(--dark-grey-4) !important;
    color: var(--white) !important;
}
