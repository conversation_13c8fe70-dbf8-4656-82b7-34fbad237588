import s from './WaveDXConfigurator.module.scss'
import React, { FC } from 'react'
import { ConfiguratorProductSelector } from '@components/molecules/ConfiguratorProductSelector/ConfiguratorProductSelector'
import {
    WAVE_XLR,
    XLR_CABLE,
    WaveDXProductsData,
    WaveDXConfiguratorTypeEnum
} from '@components/templates/ProductConfigurator/WaveDXConfigurator/WaveDXConfigurator'
import { ConfigurableProduct, SimpleProduct } from '@pylot-data/fwrdschema'
import { useTranslation } from 'next-i18next'
import { loadCloudinaryFolder } from '@lib/cloudinary'

export type WaveDXConfiguratorSelectorsProps = {
    variant?: WaveDXConfiguratorTypeEnum
    label: string
    baseProducts: WaveDXProductsData
    xlrCable: SimpleProduct | ConfigurableProduct | null
    updateSelectedProducts: (sku: string) => void
    selectedSkus: string[]
    customizeProducts: WaveDXProductsData
    customizeOrder: string[]
    audioControllers: WaveDXProductsData
}

/* Thumbnail images */
const THUMBNAIL_ASSETS = {
    '10MAH9901': `${loadCloudinaryFolder(
        '/Assets/product-configurators'
    )}/wave-dx/wave-dx.jpg`,
    '10MAG9901': `${loadCloudinaryFolder(
        '/Assets/product-configurators'
    )}/wave-dx/wave-xlr.jpg`,
    '10CAL9901': `${loadCloudinaryFolder(
        '/Assets/product-configurators'
    )}/wave-dx/xlr-cable.jpg`,
    '10AAN9901': `${loadCloudinaryFolder(
        '/Assets/product-configurators'
    )}/wave-dx/mic-arm-lp.jpg`,
    '10AAM9901': `${loadCloudinaryFolder(
        '/Assets/product-configurators'
    )}/wave-dx/mic-arm.jpg`,
    '10AAP9901': `${loadCloudinaryFolder(
        '/Assets/product-configurators'
    )}/wave-dx/mini-mount.jpg`
} as {
    [key: string]: string
}

export const WaveDXConfiguratorSelectors: FC<WaveDXConfiguratorSelectorsProps> = (
    props
) => {
    const {
        variant,
        label,
        baseProducts,
        xlrCable,
        updateSelectedProducts,
        selectedSkus,
        customizeProducts,
        customizeOrder,
        audioControllers
    } = props
    const { t } = useTranslation(['common'])
    return (
        <div>
            <h3 className="mb-16px">{label}</h3>
            <div className={s['wave-dx-product-selectors']}>
                <div className="flex gap-8px flex-col">
                    {baseProducts &&
                        Object.keys(baseProducts).map((baseProduct) => {
                            const product = baseProducts[baseProduct]
                            if (product && product.sku) {
                                if (
                                    variant ===
                                        WaveDXConfiguratorTypeEnum.MIC_MOUNT &&
                                    product.sku === WAVE_XLR
                                )
                                    return
                                const thumbnail =
                                    THUMBNAIL_ASSETS[product.sku] ?? ''
                                return (
                                    <ConfiguratorProductSelector
                                        key={`product-selector-${product.sku}-${label}`}
                                        product={product}
                                        thumbnail={undefined}
                                        selectable={false}
                                        selected
                                        imageClass={s['wave-dx-image-wrapper']}
                                        onSelect={() => {
                                            if (product.sku) {
                                                updateSelectedProducts(
                                                    product.sku
                                                )
                                            }
                                        }}
                                        // eslint-disable-next-line i18next/no-literal-string
                                        color="dark"
                                    />
                                )
                            }
                        })}
                    {xlrCable && (
                        <ConfiguratorProductSelector
                            key={`product-selector-${XLR_CABLE}-${label}`}
                            product={xlrCable}
                            // thumbnail={THUMBNAIL_ASSETS['10CAL9901']}
                            thumbnail={undefined}
                            imageClass={s['wave-dx-image-wrapper']}
                            onSelect={() => {
                                updateSelectedProducts(XLR_CABLE)
                            }}
                            selected={selectedSkus.includes(XLR_CABLE)}
                            free
                        />
                    )}
                </div>
            </div>
            {Object.keys(customizeProducts).length > 0 &&
                variant !== WaveDXConfiguratorTypeEnum.XLR_INTERFACE && (
                    <div className="mt-24px">
                        <h5>{t('Add a mount')}</h5>
                        <div className={s['wave-dx-product-selectors']}>
                            <div className="flex gap-8px flex-col">
                                {customizeOrder.map((item) => {
                                    const product = customizeProducts[item]
                                    if (product && product.sku) {
                                        const thumbnail =
                                            THUMBNAIL_ASSETS[product.sku] ?? ''
                                        return (
                                            <ConfiguratorProductSelector
                                                variant="radio"
                                                key={`product-selector-${product.sku}-${label}`}
                                                product={product}
                                                thumbnail={undefined}
                                                imageClass={
                                                    s['wave-dx-image-wrapper']
                                                }
                                                selectable
                                                selected={selectedSkus.includes(
                                                    product.sku
                                                )}
                                                onSelect={() => {
                                                    if (product.sku) {
                                                        updateSelectedProducts(
                                                            product.sku
                                                        )
                                                    }
                                                }}
                                            />
                                        )
                                    }
                                })}
                            </div>
                        </div>
                    </div>
                )}
            {Object.keys(audioControllers).length > 0 &&
                variant !== WaveDXConfiguratorTypeEnum.MIC_MOUNT && (
                    <div className="mt-24px">
                        <h5>{t('Add an audio controller')}</h5>
                        <div className={s['wave-dx-product-selectors']}>
                            <div className="flex gap-8px flex-col">
                                {Object.keys(audioControllers).map(
                                    (customizeProduct) => {
                                        const product =
                                            audioControllers[customizeProduct]
                                        if (product && product.sku) {
                                            const thumbnail =
                                                THUMBNAIL_ASSETS[product.sku] ??
                                                ''
                                            return (
                                                <ConfiguratorProductSelector
                                                    variant="radio"
                                                    key={`product-selector-${product.sku}-${label}`}
                                                    product={product}
                                                    thumbnail={undefined}
                                                    imageClass={
                                                        s[
                                                            'wave-dx-image-wrapper'
                                                        ]
                                                    }
                                                    selectable
                                                    selected={selectedSkus.includes(
                                                        product.sku
                                                    )}
                                                    onSelect={() => {
                                                        if (product.sku) {
                                                            updateSelectedProducts(
                                                                product.sku
                                                            )
                                                        }
                                                    }}
                                                />
                                            )
                                        }
                                    }
                                )}
                            </div>
                        </div>
                    </div>
                )}
        </div>
    )
}
