import { Badge } from '@components/atoms/Badge/Badge'
import { HolidayGiftGuideBadge } from '@components/atoms/Badge/HolidayGiftGuideBadge'
import { Price } from '@components/atoms/Price/Price'
import { BannerProps } from '@components/molecules/Banner/Banner'
import { Button } from '@components/molecules/Button/Button'
import { NeoConfiguratorProductSelector } from '@components/molecules/NeoConfiguratorProductSelector/NeoConfiguratorProductSelector'
import { NeoConfiguratorAddToCart } from '@components/templates/ProductConfigurator/NeoConfigurator/NeoConfiguratorAddToCart'
import { ProductConfiguratorContent } from '@components/templates/ProductConfigurator/ProductConfigurator'
import {
    BundlesType,
    BundleTypeData
} from '@components/templates/ProductConfigurator/types'
import { useStoreConfig } from '@config/index'
import { usePrice } from '@corsairitshopify/pylot-price/index'
import { formatPriceSymbol } from '@lib/utils/priceUtils'
import getProductsBySkus from '@pylot-data/api/operations/get-products-by-skus'
import { ConfigurableProduct, SimpleProduct } from '@pylot-data/fwrdschema'
import {
    ButtonLabel,
    useProductUI
} from '@pylot-data/hooks/product/use-product-ui'
import { useMobile } from '@pylot-data/hooks/use-mobile'
import { ProductStockStatus } from '@pylot-data/pylotschema.d'
import cn from 'classnames'
import { decode } from 'he'
import { useTranslation } from 'next-i18next'
import { useRouter } from 'next/router'
import { FC, useCallback, useEffect, useMemo, useState } from 'react'
import s from './NeoConfigurator.module.scss'
import {
    getProductCategory,
    getSKUsOfCategory,
    PRODUCT_CATEGORIES,
    SkuCategory
} from './neoConstants'
import { NEO_BUNDLES } from './neoSkus'

const NEO_BUNDLE_ATC_LOCATION = 2

const EMPTY_IMAGE =
    'https://res.cloudinary.com/elgato-pwa/image/upload/v1749024828/2025/Product%20Configurators/Neo/empty.jpg'

interface Item {
    sku: string
    headline_key?: string
    subheadline?: string
}

const ALL_SKUS: BundlesType = {
    // Wave Neo
    [PRODUCT_CATEGORIES.WAVE_NEO.baseSku]: {
        image:
            'https://res.cloudinary.com/elgato-pwa/image/upload/v1749024828/2025/Product%20Configurators/Neo/neo_audio-default.jpg',
        product: null,
        discount: 0,
        freeCable: true,
        bundleProductSkus: []
    },
    // Stream Deck white
    [PRODUCT_CATEGORIES.STREAM_DECK.variants.white]: {
        image:
            'https://res.cloudinary.com/elgato-pwa/image/upload/v1749024828/2025/Product%20Configurators/Neo/neo_control.jpg',
        product: null,
        discount: 0,
        bundleProductSkus: []
    },
    // Stream Deck black
    [PRODUCT_CATEGORIES.STREAM_DECK.variants.black]: {
        image:
            'https://res.cloudinary.com/elgato-pwa/image/upload/v1749024828/2025/Product%20Configurators/Neo/neo_control-black.jpg',
        product: null,
        discount: 0,
        bundleProductSkus: []
    },
    // Facecam Neo
    [PRODUCT_CATEGORIES.FACECAM_NEO.baseSku]: {
        image:
            'https://res.cloudinary.com/elgato-pwa/image/upload/v1749024828/2025/Product%20Configurators/Neo/neo_cam.jpg',
        product: null,
        discount: 0,
        bundleProductSkus: []
    },
    // Key Light Neo White
    [PRODUCT_CATEGORIES.KEY_LIGHT_NEO.variants.white.base]: {
        image:
            'https://res.cloudinary.com/elgato-pwa/image/upload/v1749024828/2025/Product%20Configurators/Neo/neo_light.jpg',
        product: null,
        discount: 0,
        bundleProductSkus: []
    },
    // Game capture Neo
    [PRODUCT_CATEGORIES.GAME_CAPTURE_NEO.baseSku]: {
        image:
            'https://res.cloudinary.com/elgato-pwa/image/upload/v1749024828/2025/Product%20Configurators/Neo/neo_capture.jpg',
        product: null,
        discount: 0,
        bundleProductSkus: []
    },
    // Key Light Neo Black
    [PRODUCT_CATEGORIES.KEY_LIGHT_NEO.variants.black.base]: {
        image:
            'https://res.cloudinary.com/elgato-pwa/image/upload/v1749024828/2025/Product%20Configurators/Neo/neo_light-black.jpg',
        product: null,
        discount: 0,
        bundleProductSkus: []
    },
    // Neo Clamp
    [PRODUCT_CATEGORIES.KEY_LIGHT_NEO.variants.white.clamp]: {
        image:
            'https://res.cloudinary.com/elgato-pwa/image/upload/v1749024828/2025/Product%20Configurators/Neo/neo_light-clamp.jpg',
        product: null,
        discount: 0,
        bundleProductSkus: []
    },
    // Neo Stand
    [PRODUCT_CATEGORIES.KEY_LIGHT_NEO.variants.white.stand]: {
        image:
            'https://res.cloudinary.com/elgato-pwa/image/upload/v1749024828/2025/Product%20Configurators/Neo/neo_light-stand.jpg',
        product: null,
        discount: 0,
        bundleProductSkus: []
    },

    //bundles
    ...NEO_BUNDLES
}

async function fetchBundles(skus: string[], locale: string) {
    const chunkSize = 40
    const chunks = []
    for (let i = 0; i < skus.length; i += chunkSize) {
        chunks.push(skus.slice(i, i + chunkSize))
    }

    const bundles = await Promise.all(
        chunks.map((chunk) => getProductsBySkus(chunk, locale || ''))
    ).then((bundles) => {
        return bundles.flat()
    })

    return bundles
}

function getMatchingBundle(selectedSkus: string[], allSkus: BundlesType) {
    return Object.values(allSkus).find((bundle: BundleTypeData) => {
        const bundleSkus = bundle.bundleProductSkus

        if (
            bundleSkus.every((sku) => selectedSkus.includes(sku)) &&
            bundleSkus.length === selectedSkus.length
        ) {
            return bundle
        }

        return null
    })
}

// These are the products
const configuratorItems: Item[] = [
    { sku: PRODUCT_CATEGORIES.WAVE_NEO.baseSku },
    { sku: PRODUCT_CATEGORIES.STREAM_DECK.baseSku },
    { sku: PRODUCT_CATEGORIES.FACECAM_NEO.baseSku },
    { sku: PRODUCT_CATEGORIES.KEY_LIGHT_NEO.baseSku },
    { sku: PRODUCT_CATEGORIES.GAME_CAPTURE_NEO.baseSku }
]

function getSelectedSkus(selectedSkus: Record<SkuCategory, string>) {
    return
}

function isProductSelectorSelected(category: string[], sku: string) {
    return category.some((categorySku) => categorySku === sku)
}

export const NeoConfigurator: FC<ProductConfiguratorContent> = ({
    content
}) => {
    const [productsData, setProductsData] = useState<
        (SimpleProduct | ConfigurableProduct)[]
    >([])
    const { locale } = useRouter()
    const { t } = useTranslation('common')
    const {
        base: { currencyConfig }
    } = useStoreConfig()
    const [selectedSkus, setSelectedSkus] = useState<
        Record<SkuCategory, string>
    >({
        'wave-neo': '',
        'stream-deck': '',
        'facecam-neo': '',
        'key-light-neo': '',
        'game-capture-neo': ''
    })
    const [pop, setPop] = useState({} as SimpleProduct | ConfigurableProduct)
    const [allSkus, setAllSkus] = useState(ALL_SKUS)
    const [selectedBundle, setSelectedBundle] = useState({} as BundleTypeData)
    const currencyMap = currencyConfig?.elgato?.currencyMap
    const translatableContent = content.translatableContent
    const additionalTextPanel = content.additionalTextPanels?.[0]
    const linkOverride = additionalTextPanel?.link
    const isHGG = content.additionalOptions.customization === 'hgg'
    const { isMobile } = useMobile()
    const banner = content.children?.find(
        (child) => child.meta?.contentType === 'moleculeBanner'
    ) as BannerProps

    useEffect(() => {
        fetchBundles(Object.keys(ALL_SKUS), locale || '')
            .then((bundles) => {
                const copyOfBundleSkusDict = { ...ALL_SKUS }

                // review inventory based on notSellable
                for (const bundle of bundles) {
                    if (bundle.not_sellable) {
                        bundle.stock_status = ProductStockStatus.OutOfStock
                        bundle.inventory = 0
                        bundle.backorder = { available: false, date: null }
                    }

                    const sku = bundle.sku
                    const bundleProductsSkus: string[] = []

                    for (const bundleProduct of bundle.bundle_products ?? []) {
                        const bundleProductSku = bundleProduct?.sku
                        if (bundleProductSku) {
                            bundleProductsSkus.push(bundleProductSku)
                        }
                    }

                    if (sku) {
                        copyOfBundleSkusDict[sku] = {
                            ...copyOfBundleSkusDict[sku],
                            bundleProductSkus: bundleProductsSkus,
                            product: bundle
                        }
                    }
                }

                setAllSkus(copyOfBundleSkusDict)
                const productData: (
                    | SimpleProduct
                    | ConfigurableProduct
                )[] = bundles?.filter(
                    (item: SimpleProduct | ConfigurableProduct) =>
                        item.bundle_products?.length === 0
                )
                setProductsData(productData)
            })
            .catch((error) => {
                console.error('Error fetching bundles:', error)
            })
    }, [locale])

    useEffect(() => {
        // Find the matching bundle by comparing sorted SKUs
        const flatSelectedSkus = Object.values(selectedSkus).filter(
            (sku) => sku !== ''
        )
        const matchingBundle = getMatchingBundle(flatSelectedSkus, allSkus)

        if (matchingBundle) {
            const bundleWithDiscount = {
                ...matchingBundle,
                discount: matchingBundle.discount || 0
            }
            setSelectedBundle(bundleWithDiscount)
        } else if (flatSelectedSkus.length === 1) {
            // Handle single product case
            const singleProduct = Object.values(allSkus).find(
                (bundle) => bundle.product?.sku === flatSelectedSkus[0]
            )
            if (singleProduct) {
                setSelectedBundle(singleProduct)
            }
        } else {
            setSelectedBundle({} as BundleTypeData)
        }
    }, [allSkus, selectedSkus])

    const { notSellable } = useProductUI(selectedBundle?.product)
    const { total, subtotal, discount, currency } = usePrice(
        Object.values(selectedSkus).filter((sku) => sku !== '').length === 1
            ? productsData.find(
                  (p) =>
                      p.sku ===
                      Object.values(selectedSkus).filter((sku) => sku !== '')[0]
              )?.price_range
            : selectedBundle?.product?.price_range
    )

    const onSelect = useCallback(
        (category: SkuCategory, sku: string) => {
            const isSelected = selectedSkus[category] === sku

            setSelectedSkus({
                ...selectedSkus,
                [category]: isSelected ? '' : sku
            })
        },
        [selectedSkus]
    )

    const displayItems = useMemo(() => {
        return (
            <>
                <div className="pb-16px md:pb-24px">
                    <h3
                        className={cn('hidden lg2:block')}
                        style={{
                            fontFamily: isHGG
                                ? 'Univers67BoldCondensed'
                                : 'Univers55Roman',
                            textTransform: 'unset'
                        }}
                        dangerouslySetInnerHTML={{
                            __html: unescape(
                                translatableContent.bundlePackMessage
                            )
                        }}
                    />
                    <div className="flex flex-col gap-16px">
                        {additionalTextPanel &&
                            (additionalTextPanel?.calloutTitle ||
                                additionalTextPanel?.headline) && (
                                <div className="flex-col gap-16px items-start justify-start hidden md:flex">
                                    {additionalTextPanel?.calloutTitle && (
                                        <Badge
                                            className="bg-purple-plum-1"
                                            size="small"
                                        >
                                            <div
                                                dangerouslySetInnerHTML={{
                                                    __html: unescape(
                                                        additionalTextPanel.calloutTitle
                                                    )
                                                }}
                                            />
                                        </Badge>
                                    )}
                                    {additionalTextPanel?.headline && (
                                        <h3
                                            className={cn('hidden lg2:block')}
                                            dangerouslySetInnerHTML={{
                                                __html: unescape(
                                                    additionalTextPanel.headline
                                                )
                                            }}
                                        />
                                    )}
                                </div>
                            )}
                        {additionalTextPanel?.bodyCopy && (
                            <p
                                className={cn(
                                    'hidden lg2:block text-small-copy'
                                )}
                                dangerouslySetInnerHTML={{
                                    __html: unescape(
                                        additionalTextPanel.bodyCopy
                                    )
                                }}
                            />
                        )}
                    </div>
                </div>
                <div
                    className={cn(
                        'flex flex-col gap-16px overflow-y-auto overflow-x-hidden scrollbar-hidden',
                        s['neo-configurator__wrapper']
                    )}
                >
                    <div
                        className={cn(
                            s['neo-configurator__parent-selector'],
                            'flex flex-col gap-8px'
                        )}
                    >
                        {configuratorItems.map((item: Item, index: number) => {
                            const product = productsData.find(
                                (p) => p.sku === item.sku
                            )
                            if (product) {
                                const skuCategory = getProductCategory(item.sku)
                                const selectedSku =
                                    selectedSkus[skuCategory as SkuCategory]
                                const categorySkus = getSKUsOfCategory(
                                    skuCategory as SkuCategory
                                )
                                const isSelected =
                                    !!selectedSku &&
                                    isProductSelectorSelected(
                                        categorySkus,
                                        item.sku
                                    )
                                return (
                                    <NeoConfiguratorProductSelector
                                        key={`product-selector-${item.sku}`}
                                        product={product}
                                        className={cn(
                                            s['neo-configurator__selector']
                                        )}
                                        classNameSelected={cn(
                                            s[
                                                'neo-configurator__selector-selected'
                                            ],
                                            s[
                                                `neo-configurator__selector-selected-${index}`
                                            ]
                                        )}
                                        index={index}
                                        imageClass={cn(
                                            s['neo-configurator__imageselector']
                                        )}
                                        headline={
                                            translatableContent?.items[item.sku]
                                                ?.headline_key
                                        }
                                        category={skuCategory as SkuCategory}
                                        onSelect={onSelect}
                                        setPop={setPop}
                                        color="dark"
                                        subheadline={
                                            translatableContent?.items[item.sku]
                                                ?.subheadline
                                        }
                                        thumbnail={
                                            product?.image?.url
                                                ? product?.image?.url
                                                : ''
                                        }
                                        selectedSku={selectedSku}
                                        selected={isSelected}
                                        ignoreOutOfStock={false}
                                        variant="checkbox"
                                        isHGG={isHGG}
                                    />
                                )
                            }
                        })}
                    </div>
                </div>
            </>
        )
    }, [
        productsData,
        additionalTextPanel,
        translatableContent,
        isHGG,
        onSelect,
        selectedSkus
    ])

    const imageGallery = useMemo(() => {
        // Get the currently selected bundle image based on selected SKUs
        const selectedBundleImage = selectedBundle?.image

        let selectedBundleImageURL = selectedBundleImage || EMPTY_IMAGE

        // Handle single product case
        const flatSelectedSkus = Object.values(selectedSkus).filter(
            (sku) => sku !== ''
        )
        if (!selectedBundleImageURL && flatSelectedSkus.length === 1) {
            const singleProduct = Object.values(allSkus).find(
                (bundle) => bundle.product?.sku === flatSelectedSkus[0]
            )
            if (singleProduct) {
                selectedBundleImageURL = singleProduct.image
            }
        }

        // Handle Wave Neo color variants
        if (pop && selectedBundleImageURL) {
            const color = pop.url_key?.match(
                /(blue|lavender|green|grey|pink)/
            )?.[0]
            if (color) {
                if (selectedBundleImageURL.includes('default')) {
                    selectedBundleImageURL = selectedBundleImageURL.replace(
                        'default',
                        color
                    )
                } else if (
                    selectedBundleImageURL.includes('neo_creator-bundle_')
                ) {
                    const [
                        firstPart,
                        secondPart
                    ] = selectedBundleImageURL.split('neo_creator-bundle_')

                    if (secondPart === '.jpg') {
                        selectedBundleImageURL = [firstPart, secondPart].join(
                            `neo_creator-bundle_audio-${color}`
                        )
                    } else {
                        selectedBundleImageURL = [firstPart, secondPart].join(
                            `neo_creator-bundle_audio-${color}_`
                        )
                    }
                }
            }
        }

        return (
            <div
                className={cn(
                    s['neo-configurator__img-wrapper'],
                    'flex justify-start items-center relative'
                )}
            >
                {banner && (
                    <HolidayGiftGuideBadge
                        text={banner?.headline ?? ''}
                        background={
                            banner.customOptions?.backgroundColor as string
                        }
                    />
                )}
                <div className={s['neo-configurator__img-container']}>
                    {Object.entries(allSkus).map(([bundleSku, bundle]) => {
                        if (!bundle) return null

                        // Base selection on bundle SKU match instead of image URL
                        const isSelected =
                            selectedBundle?.product?.sku === bundleSku

                        return (
                            <div
                                key={bundleSku}
                                className={cn(s['neo-configurator__image'], {
                                    [s[
                                        'neo-configurator__image--current'
                                    ]]: isSelected
                                })}
                            >
                                <img
                                    src={selectedBundleImageURL}
                                    alt={bundle.product?.name ?? ''}
                                    className={s['neo-configurator__img']}
                                />
                            </div>
                        )
                    })}
                </div>
            </div>
        )
    }, [pop, selectedSkus, allSkus, banner, selectedBundle])
    return (
        content && (
            <>
                <div className="flex flex-col gap-16px m-16px lg2:m-32px">
                    <h3
                        className={cn('block lg2:hidden', {
                            hidden: isHGG
                        })}
                        style={{
                            fontFamily: 'Univers55Roman',
                            fontSize: '31px',
                            textTransform: 'unset',
                            marginTop: '64px'
                        }}
                        dangerouslySetInnerHTML={{
                            __html: decode(
                                translatableContent.bundlePackMessage
                            )
                        }}
                    />
                    <div
                        className={cn('flex flex-col gap-16px', {
                            hidden: isHGG
                        })}
                    >
                        {additionalTextPanel &&
                            (additionalTextPanel?.calloutTitle ||
                                additionalTextPanel?.headline) && (
                                <div className="flex-col gap-16px items-start justify-start flex md:hidden">
                                    {additionalTextPanel?.calloutTitle && (
                                        <Badge
                                            className="bg-purple-plum-1"
                                            size="small"
                                        >
                                            <div
                                                dangerouslySetInnerHTML={{
                                                    __html: decode(
                                                        additionalTextPanel.calloutTitle
                                                    )
                                                }}
                                            />
                                        </Badge>
                                    )}
                                    {additionalTextPanel?.headline && (
                                        <h3
                                            className={cn('block lg2:hidden')}
                                            dangerouslySetInnerHTML={{
                                                __html: decode(
                                                    additionalTextPanel.headline
                                                )
                                            }}
                                        />
                                    )}
                                </div>
                            )}
                        {additionalTextPanel?.bodyCopy && (
                            <p
                                className={cn(
                                    'block lg2:hidden text-small-copy'
                                )}
                                style={{ fontSize: '20px', color: '#595959' }}
                                dangerouslySetInnerHTML={{
                                    __html: decode(additionalTextPanel.bodyCopy)
                                }}
                            />
                        )}
                    </div>
                </div>
                <div
                    className={cn(s['neo-configurator'], {
                        [s['neo-configurator__hgg-customization']]: isHGG
                    })}
                >
                    <div className={cn(s['neo-configurator__inner'])}>
                        <div
                            className={cn(
                                s['neo-configurator__between-wrapper']
                            )}
                        >
                            {imageGallery}
                            <div
                                className={cn(
                                    s['neo-configurator__selector-wrapper'],
                                    'flex flex-col'
                                )}
                            >
                                {!!productsData.length && (
                                    <>
                                        {displayItems}

                                        {selectedBundle?.product && (
                                            <div
                                                className={cn(
                                                    s[
                                                        'neo-configurator__summary'
                                                    ],
                                                    'mt-4px flex flex-col justify-end flex-grow-1 lg2:h-auto'
                                                )}
                                            >
                                                <div
                                                    className={cn(
                                                        s[
                                                            'neo-configurator__footer'
                                                        ],
                                                        'flex flex-row justify-between items-center p-10px flex-wrap'
                                                    )}
                                                >
                                                    {discount &&
                                                        Object.values(
                                                            selectedSkus
                                                        ).filter(
                                                            (sku) => sku !== ''
                                                        ).length > 1 && (
                                                            <div
                                                                className={cn(
                                                                    s[
                                                                        'neo-configurator__saving-wrapper'
                                                                    ],
                                                                    {
                                                                        [s[
                                                                            'neo-configurator__saving-wrapper--visible'
                                                                        ]]:
                                                                            Number(
                                                                                selectedBundle.discount
                                                                            ) >
                                                                            0
                                                                    }
                                                                )}
                                                            >
                                                                <div
                                                                    className={cn(
                                                                        s[
                                                                            'neo-configurator__saving'
                                                                        ]
                                                                    )}
                                                                >
                                                                    <div className="flex flex-row items-center gap-6px">
                                                                        <span
                                                                            className="button-text-small button-text-bold font-univers65Bold uppercase"
                                                                            style={{
                                                                                fontSize:
                                                                                    '10px'
                                                                            }}
                                                                        >
                                                                            {t(
                                                                                'prompterConfigurator|Saving'
                                                                            )}
                                                                        </span>
                                                                        {isHGG ? (
                                                                            <HolidayGiftGuideBadge
                                                                                text={`${selectedBundle?.discount}%`}
                                                                                size="small"
                                                                                background="linear-gradient(270deg, #FEDE1E 0%, #F09EFB 100%)"
                                                                                noAbsolutePosition
                                                                                noIcon
                                                                            />
                                                                        ) : (
                                                                            <Badge
                                                                                size="small"
                                                                                className={cn(
                                                                                    s[
                                                                                        'neo-configurator__discount'
                                                                                    ]
                                                                                )}
                                                                            >
                                                                                {`${selectedBundle?.discount}%`}
                                                                            </Badge>
                                                                        )}
                                                                    </div>
                                                                    <Price
                                                                        price={formatPriceSymbol(
                                                                            discount,
                                                                            currency,
                                                                            locale,
                                                                            currencyMap
                                                                        )}
                                                                        theme={
                                                                            isHGG
                                                                                ? 'dark'
                                                                                : 'neo-blue'
                                                                        }
                                                                        size="medium"
                                                                    />
                                                                </div>
                                                            </div>
                                                        )}
                                                    <div
                                                        className={cn(
                                                            s[
                                                                'neo-configurator__price-display'
                                                            ],
                                                            'flex flex-col flex-1 pr-16px xs:pr-0 items-end lg2:items-start justify-center'
                                                        )}
                                                    >
                                                        <p
                                                            className="button-text-small uppercase font-univers65"
                                                            style={{
                                                                color: isHGG
                                                                    ? 'var(--primitive-blue-30)'
                                                                    : '#0C2588',
                                                                fontSize: '11px'
                                                            }}
                                                            role="presentation"
                                                        >
                                                            {t('Subtotal')}
                                                        </p>
                                                        <div className="flex flex-row-reverse xs:flex-col gap-x-4px xs:gap-x-0 content-end items-end xs:content-start lg2:items-start">
                                                            <Price
                                                                price={formatPriceSymbol(
                                                                    total,
                                                                    currency,
                                                                    locale,
                                                                    currencyMap
                                                                )}
                                                                size="medium"
                                                                theme={
                                                                    isHGG
                                                                        ? 'light'
                                                                        : 'neo-blue'
                                                                }
                                                            />
                                                            {subtotal !==
                                                                total &&
                                                                Object.values(
                                                                    selectedSkus
                                                                ).filter(
                                                                    (sku) =>
                                                                        sku !==
                                                                        ''
                                                                ).length >
                                                                    1 && (
                                                                    <Price
                                                                        price={formatPriceSymbol(
                                                                            subtotal,
                                                                            currency,
                                                                            locale,
                                                                            currencyMap
                                                                        )}
                                                                        theme={
                                                                            isHGG
                                                                                ? 'light'
                                                                                : 'neo-blue-discount'
                                                                        }
                                                                        size="small"
                                                                        variant="neo"
                                                                        themeDiscount="neo"
                                                                        promoType={
                                                                            isHGG
                                                                                ? 'hgg'
                                                                                : undefined
                                                                        }
                                                                    />
                                                                )}
                                                        </div>
                                                    </div>
                                                    {notSellable &&
                                                    linkOverride ? (
                                                        <Button
                                                            variant={
                                                                linkOverride.style
                                                            }
                                                            color={
                                                                linkOverride.styleColor
                                                            }
                                                            iconAlignment={
                                                                linkOverride.iconAlignment
                                                            }
                                                            href={
                                                                linkOverride.linkUrl
                                                            }
                                                            newTab={
                                                                linkOverride.newTab
                                                            }
                                                        >
                                                            {
                                                                linkOverride.linkTitle
                                                            }
                                                        </Button>
                                                    ) : (
                                                        <NeoConfiguratorAddToCart
                                                            product={
                                                                selectedBundle.product
                                                            }
                                                            popFilter={pop}
                                                            cmsATCLocation={
                                                                NEO_BUNDLE_ATC_LOCATION
                                                            }
                                                            className="w-full lg2:w-auto"
                                                            buttonLabel={t(
                                                                `cart|${ButtonLabel.ADD_TO_CART}`
                                                            )}
                                                            isHGG={isHGG}
                                                        />
                                                    )}
                                                </div>
                                                {additionalTextPanel?.richText &&
                                                    additionalTextPanel?.disclaimerText && (
                                                        <div
                                                            className={cn(
                                                                'flex justify-center'
                                                            )}
                                                            style={{
                                                                marginTop: !isMobile
                                                                    ? '10px'
                                                                    : '34px',
                                                                fontSize:
                                                                    '12px',
                                                                color: '#0C2588'
                                                            }}
                                                        >
                                                            {isMobile ? (
                                                                <p
                                                                    className="text-center"
                                                                    dangerouslySetInnerHTML={{
                                                                        __html: decode(
                                                                            additionalTextPanel?.richText
                                                                        )
                                                                    }}
                                                                />
                                                            ) : (
                                                                <p
                                                                    className="text-center w-full"
                                                                    dangerouslySetInnerHTML={{
                                                                        __html: decode(
                                                                            additionalTextPanel?.disclaimerText
                                                                        )
                                                                    }}
                                                                />
                                                            )}
                                                        </div>
                                                    )}
                                            </div>
                                        )}
                                    </>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            </>
        )
    )
}
