.merch-configurator {
    margin: 0 auto;
    max-width: 1190px;
    width: 100%;
    padding: 20px 0;
    position: relative;

    @screen md {
        padding: 40px 0;
    }

    &__wrapper {
        background: var(--white);

        @screen md {
            padding: 32px 6px 32px 32px;
            border-radius: 12px;
        }
    }

    &__max-height {
        @screen lg {
            max-height: 779px;
            overflow: hidden;
        }
    }

    &__inner {
        display: flex;
        flex-direction: column;
        gap: 40px;
        position: relative;
        padding: 0 16px;

        @screen md {
            padding: 0;
            flex-direction: row;
            gap: 32px;
        }

        @screen lg {
            height: 785px;
            overflow: hidden;
            overflow-y: auto;
        }

        &--gallery-right {
            .merch-configurator__gallery {
                @screen md {
                    order: 2;
                }
            }

            .merch-configurator__content {
                @screen md {
                    order: 1;
                }
            }
        }
    }


    &__gallery {
        width: 100%;
        height: auto;
        flex: 1 1 auto;

        @screen md {
            width: calc(50% - 32px);
            flex: 0 0 auto;
            position: -webkit-sticky;
            position: sticky;
            top: 0;
            height: 100%;
        }

        @screen lg {
            width: calc(100% - 556px - 32px - 32px);
            max-height: 779px;
        }

        @media screen and (min-width: 1190px) {
            width: 538px;
        }
    }

    &__content {
        width: 100%;

        @screen md {
            width: calc(50% - 32px);
            flex: 0 0 auto;
        }

        @screen lg {
            width: 556px;
            max-height: 779px;
        }
    }

    &__description {
        @screen md {
            order: 2;
            margin-top: 16px;
        }
    }

    &__add-to-cart {
        @screen md {
            order: 1;
        }
    }

    &__dropdowns {
        margin-top: 24px;
        display: flex;
        flex-direction: column;
        gap: 8px;

        @screen md {
            order: 3;
            padding-bottom: 10px;
        }

        &--open {
            @screen md {
                padding-bottom: 10px;
            }
        }
    }
}

.merch-configurator-gallery {
    position: relative;

    &__slider {
        div > span, div > img, div > span > span > img {
            @screen md-max {
                width: 100% !important;
            }
        }

        :global {
            .swiper {
                border-radius: 8px;
                overflow: hidden;
            }

            div[data-nav="prev"] {
                left: 16px;
            }

            div[data-nav="next"] {
                right: 16px;
            }
        }
    }

    &__overlay {
        @apply absolute z-1 inset-0 flex items-center justify-center pointer-events-none;
        background: rgba(21, 21, 21, 0.3);
        z-index: 9;
    }

    &__image-badge {
        background-color: var(--white);
        border: 2px solid var(--content-blue);
        color: var(--content-blue) !important;
    }

    &__slider-item-image {
        aspect-ratio: 538/779;
    }

    &__thumbs {
        @screen lg-max {
            margin-top: 10px;
            margin-left: -6px;
            margin-right: -6px;
            width: 100%;
        }

        :global {
            .swiper-slide {
                cursor: pointer;

                @screen md-max {
                    width: 65px;
                    height: 79px;
                    margin: 0 3px;
                    position: relative;
                    padding: 2px;
                }
            }

            .swiper-slide-thumb-active, .swiper-slide:focus {
                outline: none;

                /* merch-configurator-gallery__thumb-image */
                > div > div {
                    border: 2px solid var(--content-blue);
                }
            }
        }

        @media screen and (min-width: 995px) {
            position: absolute;
            top: auto;
            left: 16px;
            right: 16px;
            bottom: 16px;
            width: calc(100% - 32px);
        }
    }

    &__thumb-inner {
        @screen md {
            width: auto;
            height: auto;
            margin: 0 3px;
            position: relative;
            padding: 2px;
        }
    }

    &__thumb-image {
        border: 1px solid var(--light-grey-2);
        box-sizing: border-box;
        width: 100%;
        height: 100%;
        border-radius: 8px;
        overflow: hidden;

        div, span {
            width: 100% !important;
            height: 100% !important;
            display: block !important;
        }
    }
}

.merch-conf-size-chart {
    margin: 24px 0;

    @screen md {
        margin: 16px 0 0;
    }
}

.merch-config-tile {
    background: var(--bg-grey);
    border-radius: 4px;
    padding: 12px 16px;
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
}

.merch-config-tile-variant {
    padding: 3px;
    border: 1px solid transparent;
    border-radius: 6px;
    overflow: hidden;
    @apply transition-colors duration-300;

    &--active {
        border: 1px solid var(--black);

        .merch-config-tile-variant__inner {
            box-shadow: none;
        }
    }

    &.merch-config-tile-variant--disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }

    &--size {
        width: 64px;

        @media screen and (min-width: 995px) {
            width: 70px;
        }

        @screen lg {
            width: 115px;
        }

    }

    &--fit-small {
        width: 70px;
        border: 1px solid var(--light-grey-2);
        color: var(--light-grey-2);

        &.merch-config-tile-variant--active {
            border: 1px solid var(--black);
            color: var(--black);
        }

        .merch-config-tile-variant__inner {
            box-shadow: none;
        }

        &.merch-config-tile-variant--disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
    }

    &--size-dropdown {
        width: 100%;
        border: 1px solid var(--black);
        color: var(--black);
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-right: 20px;

        .merch-config-tile-variant__inner {
            box-shadow: none;
            padding-right: 0;
        }
    }

    &__inner {
        background: var(--white);
        border-radius: 3px;
        height: 35px;
        white-space: nowrap;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 0 18px;
        box-shadow: 0 0 1px rgba(0, 0, 0, 0.25);
    }

    &:focus {
        outline: none;
        border: 1px solid var(--content-blue);
    }
}
