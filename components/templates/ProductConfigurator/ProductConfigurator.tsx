import React, { FC, useMemo } from 'react'
import PrimaryText, {
    HorizontalAlignmentEnum,
    PrimaryTextProps
} from '@components/molecules/PrimaryText/PrimaryText'
import {
    SectionBgColor,
    SectionThemeDarkBgColors
} from '@components/templates/Section/Section'
import {
    Container,
    ContainerSize
} from '@components/organisms/Container/Container'
import { WaveDXConfigurator } from '@components/templates/ProductConfigurator/WaveDXConfigurator/WaveDXConfigurator'
import { MerchConfigurator } from '@components/templates/ProductConfigurator/MerchConfigurator/MerchConfigurator'
import {
    ImageType,
    VideoType
} from '@pylot-data/hooks/contentful/use-content-json'
import cn from 'classnames'
import { PartnerConfigurator } from '@components/templates/ProductConfigurator/PartnerConfigurator/PartnerConfigurator'
import {
    CardListProps,
    CardProps
} from '@components/templates/CardList/CardList'
import { PrompterConfigurator } from '@components/templates/ProductConfigurator/PrompterConfigurator/PrompterConfigurator'
import { KeyLightMk2Configurator } from '@components/templates/ProductConfigurator/KeyLightMk2Configurator/KeyLightMk2Configurator'
import { FalloutConfigurator } from '@components/templates/ProductConfigurator/FalloutConfigurator/FalloutConfigurator'
import s from './ProductConfigurator.module.scss'
import { useRouter } from 'next/router'
import { CodConfigurator } from '@components/templates/ProductConfigurator/CodConfigurator/CodConfigurator'
import { CloudinaryMedia } from '@components/common/CloudinaryMedia/Cloudinary'
import { Meta } from '@components/common/types'
import { NeoConfigurator } from '@components/templates/ProductConfigurator/NeoConfigurator/NeoConfigurator'
import { BannerProps } from '@components/molecules/Banner/Banner'
import { GiftGuideNeoConfigurator } from '@components/templates/ProductConfigurator/GiftGuideNeoConfigurator/GiftGuideNeoConfigurator'
import { useMobile } from '@pylot-data/hooks/use-mobile'
import { StreamDeckConfigurator } from '@components/templates/ProductConfigurator/StreamDeckConfigurator/StreamDeckConfigurator'
import { ElgatoMediaProps } from '@components/common/ElgatoMedia/ElgatoMedia'

export type MediaProps = {
    image?: ImageType | VideoType
    cloudinaryImage?: CloudinaryMedia[]
    mobileImage?: ImageType | VideoType
    cloudinaryMobileImage?: CloudinaryMedia[]
    id?: string
    thumbnail?: ImageType
    cloudinaryThumbnail?: CloudinaryMedia[]
    cloudinaryMobileThumbnail?: CloudinaryMedia[]
    alternativeText?: string
    description?: string
    meta?: Meta
}

export type ProductConfiguratorProps = {
    title?: string
    textPanel?: PrimaryTextProps
    backgroundColor?: SectionBgColor
    badgeTextColor?: 'orange' | 'blue' | 'green' | 'black' | 'white'
    id?: string
    variant?:
        | 'wave-dx'
        | 'merch'
        | 'partner-configurator'
        | 'prompter'
        | 'fallout-drop'
        | 'key-light-mk2'
        | 'cod'
        | 'meet-neo'
        | 'gift-guide'
        | 'stream-deck'
    additionalTextPanels?: PrimaryTextProps[]
    media?: MediaProps[]
    skus?: string[]
    additionalOptions?: any
    children?: (CardListProps | CardProps | BannerProps)[]
    cmsATCLocation?: number
    regionRestriction?: [
        {
            identifier: string
            name: string
        }
    ]
    translatableContent?: any
    psdMedia?: ElgatoMediaProps[]
}

export interface ProductConfiguratorContent {
    content: ProductConfiguratorProps
}

export const ProductConfigurator: FC<ProductConfiguratorContent> = ({
    content
}) => {
    const {
        variant,
        title,
        textPanel,
        backgroundColor = SectionBgColor.TRANSPARENT,
        additionalOptions,
        id,
        cmsATCLocation = 1,
        additionalTextPanels
    } = content

    const { locale } = useRouter()
    const { isMobile } = useMobile()
    const regions = content?.regionRestriction?.find(
        (item) => item.identifier === locale?.substring(3).toLowerCase()
    )
    const showContent: boolean =
        !!regions || content?.regionRestriction === undefined

    let textColor = content.textPanel?.textColor
    if (!textColor) {
        textColor = SectionThemeDarkBgColors.includes(backgroundColor)
            ? 'light'
            : 'dark'
    }
    const richText = additionalTextPanels?.[0]?.richText
    const configurator = useMemo(() => {
        switch (variant) {
            case 'wave-dx': {
                return <WaveDXConfigurator richText={richText} />
            }
            case 'merch': {
                return <MerchConfigurator content={content} />
            }
            case 'partner-configurator': {
                return <PartnerConfigurator content={content} />
            }
            case 'fallout-drop': {
                return <FalloutConfigurator content={content} />
            }
            case 'meet-neo': {
                return <NeoConfigurator content={content} />
            }
            case 'gift-guide': {
                return <GiftGuideNeoConfigurator content={content} />
            }
            case 'prompter': {
                return <PrompterConfigurator content={content} />
            }
            case 'key-light-mk2': {
                return <KeyLightMk2Configurator content={content} />
            }
            case 'stream-deck': {
                return <StreamDeckConfigurator content={content} />
            }
            case 'cod': {
                // cod configurator component
                return <CodConfigurator content={content as any} />
            }
            default: {
                return null
            }
        }
    }, [variant, title])

    const bgColor = useMemo(() => {
        switch (variant) {
            case 'wave-dx': {
                return backgroundColor
            }
            case 'merch': {
                const additionalClasses = additionalOptions?.additionalClasses
                    ? ` ${additionalOptions?.additionalClasses}`
                    : ''
                return `bg-white md:${backgroundColor}${additionalClasses}`
            }
            case 'partner-configurator': {
                return backgroundColor
            }
            case 'prompter': {
                return backgroundColor
            }
            case 'fallout-drop': {
                return cn(s['product-configurator'], s['bg-fallout'])
            }
            case 'meet-neo': {
                return cn(s['product-configurator'], s['bg-neo'])
            }
            case 'gift-guide': {
                return cn(s['product-configurator'], s['bg-neo'])
            }
            case 'cod': {
                return backgroundColor
            }
            case 'key-light-mk2': {
                return backgroundColor
            }
            default: {
                return ''
            }
        }
    }, [backgroundColor, variant])

    const subheaderStyle = useMemo(() => {
        switch (variant) {
            case 'fallout-drop': {
                return cn(s['product-configurator'], s['bg-fallout-subheader'])
            }
            default: {
                return ''
            }
        }
    }, [backgroundColor, variant])

    const primaryTextClass = useMemo(() => {
        switch (variant) {
            case 'fallout-drop': {
                return cn(s['product-configurator'], s['header-fallout'])
            }
            default: {
                return ''
            }
        }
    }, [backgroundColor, variant])

    const additionalStyle = {
        backgroundImage: additionalOptions?.backgroundImage as string,
        backgroundColor: isMobile
            ? (additionalOptions?.mobileBackgroundColor as string)
            : (additionalOptions?.backgroundColor as string),
        color: additionalOptions?.color as string,
        backgroundSize: 'cover',
        backgroundRepeat: 'no-repeat'
    }

    return (
        <>
            {showContent && (
                <div className={bgColor} id={id} style={additionalStyle}>
                    {textPanel && variant !== 'partner-configurator' && (
                        <div
                            className={cn('pt-16 md:pt-32', {
                                ['mb-16']: variant === 'merch'
                            })}
                        >
                            <Container
                                size={ContainerSize.SMALL}
                                className={cn({
                                    [s['product-configurator__container']]:
                                        textPanel.variant === 'wave-neo'
                                })}
                            >
                                <PrimaryText
                                    className={cn(
                                        {
                                            'ml-0-important':
                                                textPanel.variant ===
                                                'wave-neo',
                                            [s['text-left-important']]:
                                                textPanel.variant === 'wave-neo'
                                        },
                                        primaryTextClass
                                    )}
                                    cmsATCLocation={cmsATCLocation}
                                    headline={
                                        textPanel.headline
                                            ? textPanel.headline
                                            : textPanel.longHeadline
                                    }
                                    bodyCopy={textPanel.bodyCopy}
                                    subheader={textPanel.subheader}
                                    textAlignment={
                                        HorizontalAlignmentEnum.CENTER
                                    }
                                    textColor={textColor}
                                    logos={textPanel.logos}
                                    logoPlacement={textPanel.logoPlacement}
                                    variant={textPanel.variant}
                                />
                            </Container>
                        </div>
                    )}
                    {configurator}
                </div>
            )}
        </>
    )
}

export default ProductConfigurator
