import { BREAKPOINT_MOBILE_MAX } from '@components/layouts/MainLayout/breakpoints'
import PrimaryText, {
    HeadlineStyleEnum,
    HorizontalAlignmentEnum,
    PrimaryTextProps
} from '@components/molecules/PrimaryText/PrimaryText'
import {
    Container,
    ContainerSize
} from '@components/organisms/Container/Container'
import {
    InfoCard,
    InfoCardProps,
    InfoCardType
} from '@components/organisms/InfoCard/InfoCard'
import { SwiperSliderTheme } from '@components/organisms/Slider/SwiperSlider'
import {
    SectionBgColor,
    SectionThemeDarkBgColors
} from '@components/templates/Section/Section'
import { SliderAdditionalOptions } from '@components/templates/Slider/Slider'
import { useOnScreen } from '@lib/hooks/useOnScreen'
import cn from 'classnames'
import dynamic from 'next/dynamic'
import { FC, useEffect, useMemo, useRef, useState } from 'react'
import { A11y, Autoplay, Mousewheel, Navigation, Pagination } from 'swiper'
import { SwiperSlide } from 'swiper/react'
import { Swiper as SwiperClass } from 'swiper/types'
import Swiper from 'swiper/types/swiper-class'
import { SwiperOptions } from 'swiper/types/swiper-options'
import s from './InfoCardSlider.module.scss'

const SwiperSlider = dynamic(
    () => import('@components/organisms/Slider/SwiperSlider'),
    {
        ssr: false
    }
)

export type InfoCardSliderProps = {
    title?: string
    textPanel?: PrimaryTextProps
    cards?: InfoCardProps[]
    variant?: InfoCardType
    bgColor?: SectionBgColor
    id?: string
    additionalOptions?: SliderAdditionalOptions
}
export interface InfoCardSliderContent {
    content: InfoCardSliderProps
}

export const InfoCardSlider: FC<InfoCardSliderContent> = ({ content }) => {
    const {
        cards = [],
        textPanel,
        variant = InfoCardType.DEFAULT,
        bgColor = SectionBgColor.WHITE_SMOKE,
        id,
        additionalOptions = {}
    } = content
    const sliderRef = useRef<HTMLDivElement | null>(null)
    const { isOnScreen } = useOnScreen(sliderRef, true, { threshold: 0.8 })
    const [swiper, setSwiper] = useState<SwiperClass | null>(null)
    const [slidedIn, setSlidedIn] = useState(false)
    const [realIndex, setRealIndex] = useState(0)
    const [sliderButtonPos, setSliderButtonPos] = useState(0)
    const [stopPlayingAudio, setStopPlayingAudio] = useState(false)
    const updateActiveItem = (swiperCore: Swiper) => {
        const { realIndex } = swiperCore
        setRealIndex(realIndex)
        // stop playing audio
        setStopPlayingAudio(!stopPlayingAudio)
    }
    const defaultModules = [A11y, Navigation, Pagination, Mousewheel]
    const defaultSliderSettings: SwiperOptions = {
        spaceBetween: 16,
        slidesPerView: 'auto',
        centeredSlides: true,
        roundLengths: true,
        loop: true,
        loopAdditionalSlides: 30,
        modules: defaultModules,
        autoplay: true,
        pagination: {
            clickable: true,
            el: '.swiper-pagination',
            bulletClass: 'swiper-pagination-bullet',
            bulletActiveClass: 'swiper-pagination-bullet-active',
            type: 'bullets'
        },
        navigation: {
            nextEl: '.swiper-button-next',
            prevEl: '.swiper-button-prev'
        },
        initialSlide: cards.length > 1 ? -1 : 0,
        mousewheel: {
            forceToAxis: true
        }
    }

    const sliderSettings: SwiperOptions = additionalOptions.autoplay
        ? {
              ...defaultSliderSettings,
              modules: [...defaultModules, Autoplay],
              autoplay: additionalOptions.autoplay
                  ? {
                        delay: 3000,
                        disableOnInteraction: false,
                        waitForTransition: true
                    }
                  : false
          }
        : defaultSliderSettings

    const textColor = SectionThemeDarkBgColors.includes(bgColor)
        ? 'light'
        : 'dark'
    const paginationTheme = SectionThemeDarkBgColors.includes(bgColor)
        ? SwiperSliderTheme.LIGHT
        : SwiperSliderTheme.DARK

    useEffect(() => {
        const handleResize = () => {
            const imageHeightFactorHalf =
                BREAKPOINT_MOBILE_MAX < window.innerWidth ? 0.140625 : 0.253125
            const buttonSizeHalf =
                BREAKPOINT_MOBILE_MAX < window.innerWidth ? 25 : 20
            setSliderButtonPos(
                (sliderRef.current?.clientWidth ?? 0) * imageHeightFactorHalf -
                    buttonSizeHalf
            )
        }

        handleResize()

        window.addEventListener('resize', handleResize)

        return () => window.removeEventListener('resize', handleResize)
    }, [sliderRef])

    useEffect(() => {
        if (swiper && !slidedIn && isOnScreen) {
            setSlidedIn(true)
            swiper.slideNext()
        }
    }, [isOnScreen, slidedIn])
    const primaryHeadline = useMemo(() => {
        if (
            additionalOptions.swapHeadline &&
            cards[realIndex] &&
            cards[realIndex].infoBox?.headline
        ) {
            return {
                headline: cards[realIndex].infoBox?.headline,
                headlineClasses: cards[realIndex].infoBox?.headlineClasses
            }
        }
        return {
            headline: textPanel?.headline,
            headlineClasses: textPanel?.headlineClasses
        }
    }, [
        additionalOptions.swapHeadline,
        cards,
        realIndex,
        textPanel?.headline,
        textPanel?.headlineClasses
    ])
    return (
        <div className={cn(s['info-card-slider'], bgColor)} id={id}>
            {textPanel && (
                <Container size={ContainerSize.SMALL}>
                    <div className={s['info-card-slider__text-wrapper']}>
                        <PrimaryText
                            calloutTitle={textPanel.calloutTitle}
                            headline={primaryHeadline.headline}
                            headlineStyle={
                                variant === InfoCardType.SOUND
                                    ? HeadlineStyleEnum.H2
                                    : HeadlineStyleEnum.H1
                            }
                            bodyCopy={textPanel.bodyCopy}
                            textAlignment={HorizontalAlignmentEnum.CENTER}
                            textColor={textColor}
                            headlineClasses={primaryHeadline.headlineClasses}
                        />
                    </div>
                </Container>
            )}
            {cards && cards.length > 0 && (
                <div ref={sliderRef}>
                    <SwiperSlider
                        parentSetSwiper={setSwiper}
                        settings={sliderSettings}
                        className={cn(s['info-card-slider__slider'], {
                            [s[
                                'info-card-slider__slider--md-max-pagination-hidden'
                            ]]: cards.length > 8
                        })}
                        paginationTheme={paginationTheme}
                        navigationTheme={SwiperSliderTheme.LIGHT}
                        onSlideChange={updateActiveItem}
                        loop={additionalOptions.autoplay}
                    >
                        {cards.map((card, i) => {
                            const infoBox = !additionalOptions.swapHeadline
                                ? { ...card.infoBox }
                                : undefined
                            if (infoBox) {
                                infoBox.textAlignment =
                                    HorizontalAlignmentEnum.CENTER
                            }
                            return (
                                <SwiperSlide
                                    key={`info-card-gallery-item-${i}`}
                                >
                                    <InfoCard
                                        cloudinaryMedia={card.cloudinaryMedia}
                                        cloudinaryPosterImage={
                                            card.cloudinaryPosterImage
                                        }
                                        className={
                                            s['info-card-slider__info-card']
                                        }
                                        media={card.media}
                                        posterImage={card.posterImage}
                                        variant={variant}
                                        infoBox={infoBox}
                                        icon={card.icon}
                                        link={card.link}
                                        backgroundColor={card.backgroundColor}
                                        textColor={card.textColor}
                                        active={realIndex === i}
                                        stopPlayingAudio={stopPlayingAudio}
                                        preloadAudio={
                                            (i === 0 && isOnScreen) ||
                                            swiper?.realIndex === i
                                        }
                                        imageAspectRatio={16 / 9}
                                        customOptions={card?.customOptions}
                                    />
                                </SwiperSlide>
                            )
                        })}
                        <div className={s['info-card-slider__navigation']}>
                            <div
                                className="swiper-button-prev"
                                slot="button-prev"
                            />
                            <div
                                className="swiper-pagination"
                                slot="pagination"
                            />
                            <div
                                className="swiper-button-next"
                                slot="button-next"
                            />
                        </div>
                    </SwiperSlider>
                </div>
            )}
        </div>
    )
}

export default InfoCardSlider
