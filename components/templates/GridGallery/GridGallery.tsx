import s from './GridGallery.module.scss'
import React, { CSSProperties, FC } from 'react'
import { GalleryCardProps } from '@components/organisms/GalleryCard/GalleryCard'
import { GridGalleryCard } from '@components/organisms/GridGalleryCard/GridGalleryCard'
import PrimaryText, {
    HorizontalAlignmentEnum,
    PrimaryTextProps
} from '@components/molecules/PrimaryText/PrimaryText'
import {
    SectionBgColor,
    SectionThemeDarkBgColors
} from '@components/templates/Section/Section'
import { VideoOverlayProps } from '@components/organisms/VideoOverlay/VideoOverlay'
import {
    Container,
    ContainerSize
} from '@components/organisms/Container/Container'
import cn from 'classnames'
import { useMobile } from '@pylot-data/hooks/use-mobile'

export type GridGalleryProps = {
    title?: string
    textPanel?: PrimaryTextProps
    items: GalleryCardProps[]
    backgroundColor?: SectionBgColor
    id?: string
    additionalOptions?: Record<string, unknown>
    fullScreen?: boolean
}

export const GridGallery: FC<GridGalleryProps> = ({
    textPanel,
    items,
    backgroundColor = SectionBgColor.TRANSPARENT,
    id,
    additionalOptions,
    fullScreen
}) => {
    let textColor = textPanel?.textColor
    const { isMobile } = useMobile()
    if (!textColor) {
        textColor = SectionThemeDarkBgColors.includes(backgroundColor)
            ? 'light'
            : 'dark'
    }
    const backgroundImage = isMobile
        ? additionalOptions?.backgroundImageMobile ||
          additionalOptions?.backgroundImage ||
          'unset'
        : additionalOptions?.backgroundImage || 'unset'
    const additionalStyle: CSSProperties = {
        backgroundImage: `url(${backgroundImage})`,
        backgroundColor: additionalOptions?.backgroundColor as string,
        color: additionalOptions?.color as string,
        backgroundSize: 'cover',
        backgroundRepeat: 'repeat'
    }

    return (
        <div
            className={cn(backgroundColor, {
                'lg:pt-48': fullScreen
            })}
            id={id}
            style={additionalStyle}
        >
            {textPanel && (
                <div className="pt-32 pb-16 md:py-16">
                    <Container size={ContainerSize.SMALL}>
                        <PrimaryText
                            headline={textPanel.headline}
                            headlineTag={textPanel.headlineTag}
                            headlineStyle={textPanel.headlineStyle}
                            bodyCopy={textPanel.bodyCopy}
                            textAlignment={HorizontalAlignmentEnum.CENTER}
                            textColor={textColor}
                            logos={textPanel.logos}
                            logoPlacement={textPanel.logoPlacement}
                        />
                    </Container>
                </div>
            )}
            {items && items.length > 0 && (
                <div
                    className={cn(s['grid-gallery'], {
                        [s['grid-gallery--fullscreen']]: fullScreen
                    })}
                >
                    {items.map((item, i) => (
                        <GridGalleryCard
                            id={item.id}
                            // eslint-disable-next-line react/no-children-prop
                            children={item.children}
                            key={`${item.headline}${i}`}
                            text={item.textPanel?.bodyCopy}
                            headline={item.textPanel?.headline}
                            inlinePlayButtonName={item.textPanel?.calloutTitle}
                            link={item.link}
                            cloudinaryMedia={item.cloudinaryMedia}
                            cloudinaryMobileMedia={item.cloudinaryMobileMedia}
                            size={item.size}
                            textPanel={item.textPanel}
                            textColor={item.textColor}
                            textHoverColor={item.textHoverColor}
                            backgroundColor={item.backgroundColor}
                            textVerticalAlignment={item.textVerticalAlignment}
                            className="py-8px md:px-8px"
                            videoOverlay={
                                item.children && item.children[0]
                                    ? {
                                          ...(item
                                              .children[0] as VideoOverlayProps),
                                          mediaEmbedded: item.mediaEmbedded
                                      }
                                    : undefined
                            }
                            videoOptions={item.videoOptions}
                            borderColor={item.borderColor}
                            customOptions={item?.customOptions}
                        />
                    ))}
                </div>
            )}
        </div>
    )
}

export default GridGallery
