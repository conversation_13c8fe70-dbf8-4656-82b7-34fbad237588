import { SpecialAnimationContent } from '@components/templates/SpecialAnimation/SpecialAnimation'
import { useOnScreen } from '@lib/hooks/useOnScreen'
import cn from 'classnames'
import dynamic from 'next/dynamic'
import { useRouter } from 'next/router'
import { FC, useEffect, useRef, useState } from 'react'
import Video from '../../molecules/Video/Video'
import s from './TextImageMaskAnimation.module.scss'
import {
    getCloudinaryImageAspectRatio,
    getImageAspectRatio
} from '@config/hooks/useGetImageAspectRatio'
import { useMobile } from '@pylot-data/hooks/use-mobile'
import { CloudinaryMedia } from '@components/common/CloudinaryMedia/Cloudinary'
import { ImageType } from '@pylot-data/hooks/contentful/use-content-json'
const TextImageMaskAnimationAnimation = dynamic(
    () =>
        import(
            '@components/templates/TextImageMaskAnimation/TextImageMaskAnimationAnimation'
        ),
    {
        ssr: false
    }
)

function getImageProps(
    cloudinaryMedia?: CloudinaryMedia[],
    cloudinaryMobileMedia?: CloudinaryMedia[],
    images?: ImageType[],
    isMobile?: boolean
): {
    media: CloudinaryMedia[] | ImageType[]
    aspectRatio: number
} {
    let media: CloudinaryMedia[] | ImageType[] = [],
        aspectRatio = 1

    if (isMobile) {
        if (cloudinaryMobileMedia && cloudinaryMobileMedia[0]) {
            aspectRatio =
                getCloudinaryImageAspectRatio({
                    cloudinaryImageOrVideo: cloudinaryMobileMedia[0]
                }) || 1
            media = cloudinaryMobileMedia
        } else if (images && images[0]) {
            aspectRatio = getImageAspectRatio({ imageOrVideo: images[0] }) || 1
            media = images
        }

        return { media, aspectRatio }
    }

    if (cloudinaryMedia && cloudinaryMedia[0]) {
        aspectRatio =
            getCloudinaryImageAspectRatio({
                cloudinaryImageOrVideo: cloudinaryMedia[0]
            }) || 1
        media = cloudinaryMedia
    } else if (images && images[0]) {
        aspectRatio = getImageAspectRatio({ imageOrVideo: images[0] }) || 1
        media = images
    }

    return { media, aspectRatio }
}

export const TextImageMaskAnimation: FC<SpecialAnimationContent> = ({
    content
}) => {
    const { locale } = useRouter()
    const currentLangCode = locale?.substring(0, 2) || ''
    const containerRef = useRef<HTMLDivElement | null>(null)
    const imageOverlayRef = useRef<HTMLDivElement | null>(null)
    const textMaskRef = useRef<HTMLDivElement | null>(null)
    const overlayRef = useRef<HTMLDivElement | null>(null)
    const videoRef = useRef<HTMLDivElement | null>(null)
    const video2Ref = useRef<HTMLDivElement | null>(null)
    const video2TextRef = useRef<HTMLDivElement | null>(null)
    const [playVideo, setPlayVideo] = useState(false)
    const { isTablet, isMobile } = useMobile()
    const { isOnScreen } = useOnScreen(containerRef, true, {
        threshold: 0,
        rootMargin: '300px'
    })
    const {
        videos = [],
        images = [],
        textPanels = [],
        cloudinaryMedia,
        cloudinaryMobileMedia,
        additionalOptions,
        videoDescription
    } = content

    useEffect(() => {
        if (isOnScreen) {
            setPlayVideo(true)
        }
    }, [isOnScreen])

    const { media } = getImageProps(
        cloudinaryMedia,
        cloudinaryMobileMedia,
        images,
        isMobile || isTablet
    )

    const imageURL =
        (media?.[0] as CloudinaryMedia).secure_url ||
        (media?.[0] as ImageType).file.url

    return (
        <div
            className={cn(s['text-image-mask-animation'], 'relative')}
            style={additionalOptions?.style || {}}
            ref={containerRef}
        >
            <div
                className={cn(
                    'sticky left-0 w-full flex justify-center items-end top-0 bg-no-repeat',
                    s['text-image-mask-animation__sticky']
                )}
                style={{
                    backgroundImage: `url(${imageURL})`,
                    backgroundSize: 'cover'
                }}
                ref={imageOverlayRef}
            >
                <div
                    className="absolute left-0 right-0 top-0 bottom-0 hidden opacity-0 transition-opacity duration-200"
                    ref={videoRef}
                >
                    <Video
                        video={videos[0]}
                        options={{
                            autoPlay: true,
                            preload: 'none',
                            muted: true,
                            loop: false
                        }}
                        className=" h-full flex justify-center;"
                        videoClasses={
                            s['text-image-mask-animation__video-content']
                        }
                        videoDescription={videoDescription}
                    />
                </div>
                <div
                    className="absolute left-0 right-0 top-0 bottom-0 opacity-0 items-center justify-center flex"
                    ref={video2Ref}
                >
                    <Video
                        video={videos[1]}
                        options={{
                            autoPlay: true,
                            preload: 'none',
                            muted: true,
                            loop: true
                        }}
                        className={cn(
                            'h-full flex justify-center w-full',
                            s['text-image-mask-animation__video2']
                        )}
                        videoClasses={
                            s['text-image-mask-animation__video-content']
                        }
                        play={playVideo}
                        videoDescription={videoDescription}
                    />
                    {textPanels[1].headline && (
                        <h1
                            className="relative opacity-0 text-white text-center"
                            ref={video2TextRef}
                        >
                            {textPanels[1]?.headline}
                        </h1>
                    )}
                    {!textPanels[1].headline && textPanels[1].subheader && (
                        <h2
                            className="relative opacity-0 text-white text-center w-3/5 max-w-6xl"
                            ref={video2TextRef}
                            style={{
                                transform: 'translateY(100%)'
                            }}
                        >
                            {textPanels[1]?.subheader}
                        </h2>
                    )}
                </div>
                <div
                    className="relative h-full w-full bg-white md-max:overflow-x-hidden"
                    ref={overlayRef}
                >
                    <h1
                        className={cn(
                            'whitespace-nowrap absolute uppercase text-transparent h-full w-full overflow-hidden flex items-center justify-center text-h3-md-max',
                            s['text-image-mask-animation__text-mask'],
                            {
                                [s[
                                    'text-image-mask-animation__text-mask--small'
                                ]]: currentLangCode === 'ru',
                                [s[
                                    'text-image-mask-animation__text-mask--medium'
                                ]]:
                                    currentLangCode === 'pl' ||
                                    currentLangCode === 'es'
                            }
                        )}
                        style={{
                            backgroundImage: `url(${imageURL})`,
                            backgroundSize: 'cover'
                        }}
                        ref={textMaskRef}
                    >
                        {textPanels[0]?.headline}
                    </h1>
                </div>
            </div>
            <TextImageMaskAnimationAnimation
                containerRef={containerRef}
                imageOverlayRef={imageOverlayRef}
                textMaskRef={textMaskRef}
                overlayRef={overlayRef}
                videoRef={videoRef}
                video2Ref={video2Ref}
                video2TextRef={video2TextRef}
            />
        </div>
    )
}

export default TextImageMaskAnimation
