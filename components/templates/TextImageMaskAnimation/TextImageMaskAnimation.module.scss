.text-image-mask-animation {
    height: 360vh;

    &__sticky {
        height: 100vh;
        background-position: 50% center;
    }

    .text-image-mask-animation__video-content {
        @apply h-full object-cover;
        max-width: unset;
    }

    & &__video2 {
        @apply absolute;
    }

    &__text-mask {
        letter-spacing: -0.9px;
        background-clip: text;
        -webkit-background-clip: text;
        background-position: 50% center;
        background-repeat: no-repeat;
        will-change: font-size;
        font-size: 24px;

        @screen md {
            font-size: 72px;
        }

        &--small,
        &--small:lang(ru) {
            font-size: 18px;
            letter-spacing: -0.5px;
            line-height: 100%;

            @screen md {
                font-size: 40px;
            }
        }

        &--medium {
            @screen md-max {
                font-size: 20px;
            }
        }
    }

    @screen md-max {
        h2 {
            width: 90%;
        }
    }
}
