.facecam-pro-sidescroll {
    @apply bg-black relative;
    @apply py-16 max-w-8xl mx-auto;

    @screen md {
        @apply max-w-full;
    }

    &--ended {
        @apply bg-white-smoke;
    }

    &__anim-wrapper {
        height: 450vh;
        position: relative;
    }

    &__sticky {
        position: sticky;
        top: var(--sticky-nav-height--sm);
        left: 0;
        width: 100%;
        overflow: hidden;
        height: calc(100vh - var(--sticky-nav-height--sm));

        @screen md {
            top: var(--sticky-nav-height--lg);
            height: calc(100vh - var(--sticky-nav-height--lg));
        }
    }

    &__inner {
        @apply overscroll-none h-full flex items-center;
        flex-wrap: nowrap;
    }

    &__text {
        max-width: 585px;
    }

    .facecam-pro-sidescroll__text {
        color: inherit !important;
    }

    &__group {
        position: relative;
        width: 100%;
        height: 100%;
        flex: 0 0 100%;
        padding: 0 16px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        @screen md {
            display: flex;
            flex-direction: row;
            flex: 0 0 100%;
            padding: 0 32px;
            justify-content: flex-start;
        }

        @screen lg {
            padding: 0 64px;
        }
    }

    &__media {
        height: 100vw;
        flex: 0 0 auto;
        display: flex;
        justify-content: center;
        align-items: center;
        position: relative;
        width: 100%;

        @screen lg {
            flex: 1 1 auto;
            width: 50%;
            height: auto;
            padding-bottom: 51.34%;
        }
    }

    &__text-wrapper {
        @apply py-16 px-0 text-white;

        @screen md {
            @apply py-32;
        }

        @screen md {
            max-width: none;
            flex: 1 1 auto;
        }

        @screen lg {
            max-width: 50%;
            flex: 0 0 auto;
            padding-left: var(--container-padding--md);
            padding-right: var(--container-padding--md);
        }

        @screen lg2 {
            padding-left: var(--container-padding--lg);
            padding-right: var(--container-padding--lg);
        }

        @screen xl {
            padding-left: calc(100% / var(--grid-cols--lg));
            padding-right: calc((100% / var(--grid-cols--lg)));
        }
    }

    &__media-2 {
        padding-bottom: 0;
        height: auto;

        @screen md {
            @apply w-screen h-auto absolute top-1/2 left-1/2;
            @apply transform -translate-x-1/2 -translate-y-1/2;
            margin-left: calc(585px + 6.4rem + 6.4rem);
        }

        .facecam-pro-sidescroll__media-inner {
            @apply mx-auto;
            max-width: 85vw;

            @screen md {
                max-width: 135vh;
                @apply px-32px;
            }

            @screen xxl {
                @apply px-64px;
            }
        }
    }
}
