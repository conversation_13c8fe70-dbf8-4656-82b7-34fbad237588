import ElgatoMedia, {
    ElgatoMediaProps
} from '@components/common/ElgatoMedia/ElgatoMedia'
import { useLayoutContext } from '@components/layouts/MainLayout/LayoutContext'
import {
    PrimaryTextProps,
    VerticalAlignmentEnum,
    HorizontalAlignmentEnum
} from '@components/molecules/PrimaryText/PrimaryText'
import TextPanel from '@components/organisms/TextPanel/TextPanel'
import { ContentOverlayProps } from '@components/templates/ContentOverlay/ContentOverlay'
import { useMobile } from '@pylot-data/hooks/use-mobile'
import cn from 'classnames'
import { CSSProperties, FC, useState } from 'react'
import dynamic from 'next/dynamic'
import { PositionContainer } from '@components/common/PositionContainer/PositionContainer'
import { Button } from '@components/molecules/Button/Button'
import { Icon } from '@components/atoms/Icon/Icon'
import { CloudinaryMedia } from '@components/common/CloudinaryMedia/Cloudinary'
import s from './MediaWithContent.module.scss'

const ContentOverlay = dynamic(
    () => import('@components/templates/ContentOverlay/ContentOverlay'),
    {
        ssr: false
    }
)

export type MediaWithContentProps = {
    title?: string
    textPanel: PrimaryTextProps
    media: ElgatoMediaProps
    overlay?: ContentOverlayProps
    verticalAlignment?: VerticalAlignmentEnum
    horizontalAlignment?: HorizontalAlignmentEnum
    mobileVerticalAlignment?: VerticalAlignmentEnum
    contentLayout?: 'horizontal' | 'vertical'
    contentOrder?: 'media-first' | 'text-first'
    textAndMediaSeperated?: boolean
    textAndMediaSeperatedMobile?: boolean
    size?: 'hero' | 'fullscreen' | 'small' | 'large'
    id?: string
    className?: string
    style?: CSSProperties
    cloudinaryBackgroundImage?: CloudinaryMedia[]
    cloudinaryMobileBackgroundImage?: CloudinaryMedia[]
    columnsDesktop?: number
    columnsMobile?: number
}

const MediaWithContentOverlay: FC<{
    overlay: ContentOverlayProps
    isOpen: boolean
    setIsOpen: (isOpen: boolean) => void
}> = ({ overlay, isOpen, setIsOpen }) => {
    return (
        <div className="pt-16px md:pt-40px">
            <Button
                linkResponse={overlay.toggleButton}
                className="relative z-20"
                variant={overlay.toggleButton?.style}
                color={overlay.toggleButton?.styleColor}
                onClick={() => setIsOpen(true)}
            >
                {overlay.toggleButton?.linkTitle}
                <Icon name={overlay.toggleButton?.icon || 'addCircle'} />
            </Button>
            <ContentOverlay
                {...overlay}
                open={isOpen}
                onClose={(e) => {
                    e.stopPropagation()
                    setIsOpen(false)
                }}
            />
        </div>
    )
}
const HorizontalMediaWithContent: FC<MediaWithContentProps> = ({
    textPanel,
    media,
    overlay,
    contentOrder,
    id,
    className,
    style,
    cloudinaryBackgroundImage,
    cloudinaryMobileBackgroundImage,
    size,
    ...rest
}) => {
    const [isOpen, setIsOpen] = useState(false)
    const { isMobile } = useMobile()
    const backgroundImage = isMobile
        ? cloudinaryMobileBackgroundImage?.[0]?.secure_url ||
          cloudinaryBackgroundImage?.[0]?.secure_url ||
          'unset'
        : cloudinaryBackgroundImage?.[0]?.secure_url || 'unset'
    const textFirst = contentOrder === 'text-first' && !isMobile
    const mediaFirst = contentOrder === 'media-first' && !isMobile
    const { pageTheme } = useLayoutContext()

    return (
        <div
            className={cn(
                'flex flex-col items-center w-full py-40px md:py-80px px-16px md:px-64px',
                className
            )}
            id={id}
            style={{
                backgroundImage: `url(${backgroundImage})`,
                backgroundSize: 'cover',
                ...style
            }}
        >
            <div
                className={cn('w-full', {
                    'grid grid-cols-12 items-center gap-x-16px': !isMobile,
                    'flex flex-col gap-40px': isMobile,
                    [s['media-with-content']]: size !== 'hero'
                })}
            >
                {textPanel && (
                    <TextPanel
                        content={{
                            ...textPanel,
                            className: !isMobile
                                ? cn('', {
                                      'col-start-1 col-span-5 lg3:col-start-2 lg3:col-span-4 order-first': textFirst,
                                      'col-start-8 col-span-5 lg3:col-start-8 lg3:col-span-4 order-last': mediaFirst
                                  })
                                : 'p-0',
                            textColor: pageTheme === 'dark' ? 'light' : 'dark',
                            badgeText: textPanel?.badgeText || ''
                        }}
                    />
                )}
                <div
                    className={cn({
                        'col-start-1 col-span-6 lg3:col-start-2 lg3:col-span-5 order-first': mediaFirst,
                        'col-start-7 col-span-6 lg3:col-start-7 lg3:col-span-5 order-last': textFirst
                    })}
                >
                    <ElgatoMedia
                        {...media}
                        className={cn(
                            'overflow-hidden rounded-xxxl',
                            media.className
                        )}
                    />
                </div>
            </div>
            {overlay && (
                <MediaWithContentOverlay
                    overlay={overlay}
                    isOpen={isOpen}
                    setIsOpen={setIsOpen}
                />
            )}
        </div>
    )
}

const VerticalMediaWithContent: FC<MediaWithContentProps> = ({
    textPanel,
    media,
    overlay,
    contentOrder,
    size,
    id,
    className,
    style,
    cloudinaryBackgroundImage,
    cloudinaryMobileBackgroundImage
}) => {
    const [isOpen, setIsOpen] = useState(false)
    const { isMobile } = useMobile()
    const backgroundImage = isMobile
        ? cloudinaryMobileBackgroundImage?.[0]?.secure_url ||
          cloudinaryBackgroundImage?.[0]?.secure_url ||
          'unset'
        : cloudinaryBackgroundImage?.[0]?.secure_url || 'unset'
    return (
        <div
            className={cn(
                'flex flex-col items-center w-full',
                {
                    'py-40px md:py-80px px-16px md:px-64px':
                        size === 'large' || size === 'small'
                },
                className
            )}
            id={id}
            style={{
                backgroundImage: `url(${backgroundImage})`,
                backgroundSize: 'cover',
                ...style
            }}
        >
            <div
                className={cn('flex w-full flex-col items-center', {
                    'flex-col-reverse': contentOrder === 'media-first'
                })}
            >
                <div
                    className={cn('w-full grid grid-cols-12 gap-x-16px', {
                        [s['media-with-content']]: size !== 'hero',
                        'md-max:flex md-max:flex-col':
                            (size === 'fullscreen' || size === 'large') &&
                            isMobile
                    })}
                >
                    <div
                        className={cn({
                            'col-start-1 col-span-12 lg3:col-start-2 lg3:col-span-10':
                                size === 'large',
                            'col-start-1 col-span-12 lg3:col-start-3 lg3:col-span-8':
                                size === 'small',
                            'md:col-start-1 md:col-span-12':
                                size === 'fullscreen',
                            [s['media-with-content--fullscreen']]:
                                size === 'fullscreen'
                        })}
                    >
                        {textPanel && <TextPanel content={textPanel} />}
                    </div>
                    <ElgatoMedia
                        {...media}
                        className={cn(
                            {
                                'col-start-1 col-span-12 lg3:col-start-2 lg3:col-span-10 ':
                                    size === 'large',
                                'col-start-1 col-span-12 lg3:col-start-3 lg3:col-span-8 md-max:h-fit':
                                    size === 'small',
                                'overflow-hidden rounded-xxxl':
                                    size === 'small' || size === 'large',
                                'md:col-start-1 md:col-span-12':
                                    size === 'fullscreen',
                                [s['media-with-content--fullscreen']]:
                                    size === 'fullscreen'
                            },
                            media.className
                        )}
                    />
                </div>
            </div>
            {overlay && (
                <MediaWithContentOverlay
                    overlay={overlay}
                    isOpen={isOpen}
                    setIsOpen={setIsOpen}
                />
            )}
        </div>
    )
}

const MediaWithContent: FC<MediaWithContentProps> = ({
    textPanel,
    media,
    overlay,
    verticalAlignment,
    horizontalAlignment,
    mobileVerticalAlignment,
    contentLayout,
    contentOrder,
    textAndMediaSeperated,
    textAndMediaSeperatedMobile = true,
    size = 'fullscreen',
    id,
    className,
    style,
    cloudinaryBackgroundImage,
    cloudinaryMobileBackgroundImage,
    columnsDesktop = 6,
    columnsMobile = 12
}: MediaWithContentProps) => {
    const [isOpen, setIsOpen] = useState(false)
    const { isMobile, isTablet } = useMobile()

    const isSeperated = isMobile
        ? textAndMediaSeperatedMobile
        : textAndMediaSeperated

    if (isSeperated) {
        if (contentLayout === 'horizontal') {
            return (
                <HorizontalMediaWithContent
                    textPanel={textPanel}
                    media={media}
                    overlay={overlay}
                    contentOrder={contentOrder}
                    size={size}
                    id={id}
                    className={className}
                    style={style}
                    cloudinaryBackgroundImage={cloudinaryBackgroundImage}
                    cloudinaryMobileBackgroundImage={
                        cloudinaryMobileBackgroundImage
                    }
                />
            )
        }
        return (
            <VerticalMediaWithContent
                textPanel={textPanel}
                media={media}
                overlay={overlay}
                contentOrder={contentOrder}
                size={size}
                id={id}
                className={className}
                style={style}
                cloudinaryBackgroundImage={cloudinaryBackgroundImage}
                cloudinaryMobileBackgroundImage={
                    cloudinaryMobileBackgroundImage
                }
            />
        )
    }

    return (
        <div
            id={id}
            className={cn(
                'flex flex-col items-center',
                {
                    'py-40px md:py-80px px-16px md:px-64px':
                        size === 'large' || size === 'small'
                },
                className
            )}
            style={style}
        >
            <div
                className={cn({
                    [s['media-with-content']]: size !== 'hero',
                    'grid grid-cols-12 gap-x-16px': size !== 'hero'
                })}
            >
                <ElgatoMedia
                    {...media}
                    className={cn(
                        {
                            'col-start-1 col-span-12 lg3:col-start-2 lg3:col-span-10':
                                size === 'large',
                            'col-start-1 col-span-12 lg3:col-start-3 lg3:col-span-8 md-max:h-fit':
                                size === 'small',
                            'overflow-hidden rounded-xxxl':
                                size === 'large' || size === 'small',
                            'col-start-1 col-span-12': size === 'fullscreen',
                            [s['media-with-content--fullscreen']]:
                                size === 'fullscreen'
                        },
                        media.className
                    )}
                >
                    {textPanel ? (
                        <PositionContainer
                            columnsDesktop={columnsDesktop}
                            columnsMobile={columnsMobile}
                            className="py-40px px-16px md:py-48 md:px-64px"
                            horizontalPosition={horizontalAlignment}
                            verticalPosition={verticalAlignment}
                            mobileVerticalPosition={mobileVerticalAlignment}
                            element={{
                                identifier: 'text-panel',
                                meta: { contentType: 'organismHeadlineMedia' },
                                ...textPanel,
                                className: 'p-0'
                            }}
                        />
                    ) : null}
                </ElgatoMedia>
                {overlay && (
                    <MediaWithContentOverlay
                        overlay={overlay}
                        isOpen={isOpen}
                        setIsOpen={setIsOpen}
                    />
                )}
            </div>
        </div>
    )
}

export default MediaWithContent
