.detail-view-panel {
    &__text {
        max-width: 888px;
        margin-left: auto;
        margin-right: auto;
    }

    &__list {
        @apply flex gap-16px flex-wrap justify-center;
        max-width: 1792px;
        @screen md {
            column-gap: 91.75px;
            row-gap: 40px;
            margin: 0 auto;
            max-width: 1792px;
        }
    }

    &__feature {
        flex-basis: calc((100% - 1.6rem) / 2);
        @screen md {
            flex-basis: unset;
        }
    }

    &--gap-md {
        .detail-view-panel__list {
            @screen md {
                max-width: 1490px;
                column-gap: 116px;
            }
        }
    }

    &--gap-lg {
        .detail-view-panel__list {
            @screen md {
                column-gap: 166px;
            }
        }
    }

    &--mobile-small {
        .detail-view-panel__feature {
            flex-basis: calc((100% - 3.2rem) / 3);
            @screen md {
                flex-basis: unset;
            }
        }
    }

    &--text-top {
        @apply w-full mx-auto py-32 px-16px;

        @screen md {
            @apply px-32px;
        }

        .detail-view-panel__text + .detail-view-panel__list {
            @apply mt-16;
        }
    }
}
