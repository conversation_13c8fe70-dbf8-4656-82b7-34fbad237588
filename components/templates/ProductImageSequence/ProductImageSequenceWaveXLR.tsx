import React, { FC } from 'react'
import ProductImageSequence, {
    ProductImageSequenceProps
} from '@components/templates/ProductImageSequence/ProductImageSequence'
import { useCreateCallouts } from '@components/templates/ProductImageSequence/useCreateCallouts'
import { SpecialAnimationContent } from '@components/templates/SpecialAnimation/SpecialAnimation'
import { ProductCalloutProps } from '@components/organisms/ProductCallout/ProductCallout'

const waveXLRProductCallouts: ProductCalloutProps[] = [
    {
        // Frame 0
        headline: 'Multifunctional control dial',
        text: '<ul><li>Set input gain</li></ul>',
        textPosition: {
            top: '64%',
            left: '2%',
            width: '25%'
        },
        mobileHotspotPosition: {
            top: '54%',
            left: '11%'
        },
        frameStart: 11,
        mobileFrameStart: 11,
        customTitleStyle: { flex: '0 1 auto' },
        transitionIn: 'translate',
        transitionOut: 'seamless',
        groupEndFrame: 3
    },
    {
        // Frame 1
        headline: 'Multifunctional control dial',
        text: '<ul><li>Set input gain</li><li>Adjust output volume</li></ul>',
        textPosition: {
            top: '64%',
            left: '2%',
            width: '25%'
        },
        mobileHotspotPosition: {
            top: '54%',
            left: '11%'
        },
        frameStart: 31,
        mobileFrameStart: 31,
        customTitleStyle: { flex: '0 1 auto' },
        transitionIn: 'seamless',
        transitionOut: 'seamless',
        groupEndFrame: 3
    },
    {
        // Frame 2
        headline: 'Multifunctional control dial',
        text:
            '<ul><li>Set input gain</li><li>Adjust output volume</li><li>Crossfade between mic and PC mix</li></ul>',
        textPosition: {
            top: '64%',
            left: '2%',
            width: '25%'
        },
        mobileHotspotPosition: {
            top: '54%',
            left: '11%'
        },
        frameStart: 52,
        mobileFrameStart: 52,
        customTitleStyle: { flex: '0 1 auto' },
        transitionIn: 'seamless',
        transitionOut: 'seamless',
        groupEndFrame: 3
    },
    {
        // Frame 3
        headline: 'Multifunctional control dial',
        text:
            '<ul><li>Set input gain</li><li>Adjust output volume</li><li>Crossfade between mic and PC mix</li><li>Toggle phantom power</li></ul>',
        textPosition: {
            top: '64%',
            left: '2%',
            width: '25%'
        },
        mobileHotspotPosition: {
            top: '54%',
            left: '11%'
        },
        frameStart: 73,
        mobileFrameStart: 73,
        customTitleStyle: { flex: '0 1 auto' },
        transitionIn: 'seamless',
        transitionOut: 'translate'
    },
    {
        headline: 'Capacitive sensor',
        text: 'Tap to mute',
        textPosition: {
            top: '30%',
            left: '2%',
            width: '37%'
        },
        mobileHotspotPosition: {
            top: '32%',
            left: '45%'
        },
        frameStart: 113, // 33 + (4 * 20)
        mobileFrameStart: 113
    },
    {
        headline: 'XLR input',
        text: 'Connect your microphone',
        textPosition: {
            top: '55%',
            left: '2%',
            width: '39%'
        },
        mobileHotspotPosition: {
            top: '47%',
            left: '77%'
        },
        frameStart: 180, // 80 + (5 * 20)
        mobileFrameStart: 180
    },
    {
        headline: 'High-Power headphone output',
        text: 'Crystal clear, <br />zero-latency monitoring',
        textPosition: {
            top: '59%',
            left: '2%',
            width: '39%'
        },
        mobileHotspotPosition: {
            top: '52%',
            left: '62%'
        },
        frameStart: 215, // 95 + (6 * 20)
        mobileFrameStart: 215,
        customTitleStyle: { flex: '0 1 auto' }
    },
    {
        headline: 'USB Type-C',
        text: 'State-of-the-art connection',
        textPosition: {
            top: '59%',
            left: '2%',
            width: '39%'
        },
        mobileHotspotPosition: {
            top: '53%',
            left: '52%'
        },
        frameStart: 235, // 95 + (7 * 20)
        frameEnd: 300,
        mobileFrameStart: 235,
        mobileFrameEnd: 300
    }
]

export const ProductImageSequenceWaveXLR: FC<SpecialAnimationContent> = ({
    content
}) => {
    // first text Panel is intro text panel
    // children are product callouts
    const { textPanels = [], children = [] } = content
    const introTextPanel = textPanels.length ? textPanels[0] : null

    const productCallouts = useCreateCallouts(children, waveXLRProductCallouts)

    return (
        <ProductImageSequence
            content={
                {
                    sequenceName: 'wave-xlr-product-image-sequence',
                    path:
                        'https://res.cloudinary.com/elgato-pwa/image/upload/v1683638316/Products/10MAG9901/product-image-sequence/desktop/All_In_One',
                    pathMobile:
                        'https://res.cloudinary.com/elgato-pwa/image/upload/v1683638335/Products/10MAG9901/product-image-sequence/mobile/All_In_One_Mobile',
                    imageCount: 111,
                    imageWidth: 1920,
                    imageHeight: 1080,
                    mobileImageWidth: 818,
                    mobileImageHeight: 1200,
                    mobileImageCount: 111,
                    height: '2200vh',
                    padLength: 3,
                    pauseFrames: [0, 1, 2, 3, 33, 80, 95, 110],
                    fadeFrames: [20, 40, 60, 80],
                    textPanel: introTextPanel,
                    productCallouts: productCallouts,
                    customStyles: {
                        backgroundColor: '#F4F4F4'
                    }
                } as ProductImageSequenceProps
            }
        />
    )
}

export default ProductImageSequenceWaveXLR
