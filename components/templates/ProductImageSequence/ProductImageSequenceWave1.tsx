import React, { FC } from 'react'
import ProductImageSequence, {
    ProductImageSequenceProps
} from '@components/templates/ProductImageSequence/ProductImageSequence'
import { useCreateCallouts } from '@components/templates/ProductImageSequence/useCreateCallouts'
import { SectionBgColor } from '@components/templates/Section/Section'
import { SpecialAnimationContent } from '@components/templates/SpecialAnimation/SpecialAnimation'
import { ProductCalloutProps } from '@components/organisms/ProductCallout/ProductCallout'
import { ImageType } from '@pylot-data/hooks/contentful/use-content-json'
import { loadCloudinaryFolder } from '@lib/cloudinary'

const wave1ProductCallouts: ProductCalloutProps[] = [
    {
        headline: 'Steel grille',
        text:
            '<ul><li>Smooth sound diffusion</li><li>Ultimate capsule protection</li></ul>',
        textPosition: {
            top: '35%',
            left: '2%',
            width: '38%'
        },
        mobileHotspotPosition: {
            top: '29%',
            left: '47.5%'
        },
        frameStart: 7,
        frameEnd: 30,
        mobileTheme: 'light',
        mobileFrameStart: 7
    },
    {
        headline: 'Heavy duty desk stand',
        text: '<ul><li>U-mount</li><li>Padded base</li></ul>',
        textPosition: {
            top: '79%',
            right: '2%',
            width: '39%'
        },
        mobileHotspotPosition: {
            top: '83%',
            left: '12%'
        },
        frameStart: 15,
        frameEnd: 36,
        textAlignment: 'right',
        mobileTheme: 'light',
        mobileVariant: 'overlay-top',
        mobileFrameStart: 28
    },
    {
        headline: 'Control Dial',
        text: '<ul><li>Adjust headphone volume</li></ul>',
        textPosition: {
            top: '64%',
            right: '2%',
            width: '40%'
        },
        mobileHotspotPosition: {
            top: '63%',
            left: '68%'
        },
        frameStart: 65,
        textAlignment: 'right',
        theme: 'light',
        mobileTheme: 'light',
        mobileFrameStart: 90, // 37 + 2 * 20 pause frames
        transitionIn: 'translate',
        transitionOut: 'seamless',
        groupEndFrame: 3
    },
    {
        headline: 'Control Dial',
        text:
            '<ul><li>Adjust headphone volume</li><li>Push to mute input</li></ul>',
        textPosition: {
            top: '64%',
            right: '2%',
            width: '40%'
        },
        mobileHotspotPosition: {
            top: '63%',
            left: '68%'
        },
        frameStart: 86, // 46 + 20 * 2
        textAlignment: 'right',
        theme: 'light',
        mobileTheme: 'light',
        mobileFrameStart: 104,
        transitionIn: 'seamless',
        transitionOut: 'translate'
    },
    {
        headline: 'Multilayered Noise shield',
        text: 'Keeps plosive noise at bay',
        textPosition: {
            top: '10%',
            right: '2%',
            width: '40%'
        },
        mobileHotspotPosition: {
            top: '32%',
            left: '45%'
        },
        frameStart: 165,
        frameEnd: 200,
        textAlignment: 'right',
        theme: 'light',
        mobileTheme: 'light',
        mobileFrameStart: 178,
        mobileVariant: 'overlay-top'
    },
    {
        headline: 'Premium Condenser Capsule',
        text: 'Capture verbal nuances with precision',
        textPosition: {
            top: '41%',
            left: '2%',
            width: '46%'
        },
        mobileHotspotPosition: {
            top: '25%',
            left: '56%'
        },
        frameStart: 180,
        frameEnd: 200,
        theme: 'light',
        mobileTheme: 'light',
        mobileFrameStart: 198,
        mobileVariant: 'overlay-top'
    },
    {
        headline: 'cardioid polar pattern',
        text: 'Fine tuned for speech',
        image: {
            title: 'CARDIOID POLAR PATTERN',
            description: '',
            file: {
                details: {
                    image: {
                        width: 130,
                        height: 132
                    }
                },
                url:
                    'https://images.ctfassets.net/h50kqpe25yx1/6cDqkOz1mnFj107mkSpWg1/c5d9b6e994448ebe0fbdcbae34862de1/cardioid-polar-pattern.png'
            }
        } as ImageType,
        textPosition: {
            top: '49%',
            right: '2%',
            width: '38%'
        },
        mobileHotspotPosition: {
            top: '27%',
            left: '56%'
        },
        frameStart: 190,
        textAlignment: 'right',
        theme: 'light',
        mobileTheme: 'light',
        mobileFrameStart: 222,
        mobileVariant: 'overlay-top'
    },
    {
        headline: 'Superior Circuitry',
        text:
            '<ul><li>24-bit / 48 kHz analog to digital conversion</li><li>Broadcast-grade detail and clarity</li></ul>',
        textPosition: {
            top: '48%',
            left: '2%',
            width: '35%'
        },
        mobileHotspotPosition: {
            top: '56%',
            left: '22%'
        },
        frameStart: 235,
        theme: 'light',
        mobileTheme: 'light',
        mobileFrameStart: 267
    },
    {
        headline: 'USB TYPE-C',
        text: 'State-of-the-art-connection',
        textPosition: {
            top: '56%',
            left: '14%',
            width: '38%'
        },
        mobileHotspotPosition: {
            top: '60%',
            left: '42%'
        },
        frameStart: 351,
        mobileTheme: 'light',
        mobileFrameStart: 373
    },
    {
        headline: 'High-power headphone output',
        text: 'Crystal clear, zero-latency monitoring',
        textPosition: {
            top: '58%',
            right: '2%',
            width: '34%'
        },
        mobileHotspotPosition: {
            top: '57%',
            left: '64%'
        },
        frameStart: 355,
        textAlignment: 'right',
        mobileTheme: 'light',
        mobileFrameStart: 406 // 206 + 10 * 20 pause frames
    },
    {
        headline: 'pop filter',
        text:
            '<ul><li>Dual-layer metal mesh</li><li>Maximum protection from plosive noise</li><li>Sold separately</li></ul>',
        textPosition: {
            top: '17%',
            left: '2%',
            width: '35%'
        },
        mobileHotspotPosition: {
            top: '33%',
            left: '24%'
        },
        frameStart: 440,
        frameEnd: 500,
        btnText: 'Learn more',
        btnTargetBlank: true,
        btnUrl: 'https://www.elgato.com/en/wave-3',
        mobileTheme: 'light',
        mobileFrameStart: 495 // 255 + 12 * 20 pause frames
    },
    {
        headline: 'Shock Mount',
        text:
            '<ul><li>Robust metal chassis with reinforced elastic suspension</li><li>Isolates microphone from mechanically transferred noise and impact</li><li>Sold separately</li></ul>',
        textPosition: {
            top: '47%',
            left: '2%',
            width: '30%'
        },
        mobileHotspotPosition: {
            top: '28%',
            left: '77%'
        },
        frameStart: 455,
        frameEnd: 500,
        btnText: 'Learn more',
        btnTargetBlank: true,
        btnUrl: 'https://www.elgato.com/en/wave-3',
        mobileTheme: 'light',
        mobileFrameStart: 516 // 256 + 13 * 20 pause frames
    },
    {
        headline: 'Multi Adapter',
        text: 'Works with all mainstream boom arms',
        textPosition: {
            top: '50%',
            right: '2%',
            width: '32%'
        },
        mobileHotspotPosition: {
            top: '45%',
            left: '81%'
        },
        frameStart: 475,
        frameEnd: 500,
        textAlignment: 'right',
        btnText: 'Learn more',
        btnTargetBlank: true,
        btnUrl: 'https://www.elgato.com/en/wave-3',
        mobileTheme: 'light',
        mobileFrameStart: 537 // 257 + 14 * 20 pause frames
    }
]

export const ProductImageSequenceWave1: FC<SpecialAnimationContent> = ({
    content
}) => {
    // first text Panel is intro text panel
    // children are product callouts
    const { textPanels = [], children = [] } = content
    const introTextPanel = textPanels.length ? textPanels[0] : null
    // children are product callouts
    const productCallouts = useCreateCallouts(children, wave1ProductCallouts)

    return (
        <ProductImageSequence
            content={
                {
                    sequenceName: 'wave-1-product-image-sequence',
                    path: `${loadCloudinaryFolder(
                        '/Assets/image-sequences'
                    )}/products/wave1/desktop/Wave1_Web_Animation_Desktop`,
                    pathMobile: `${loadCloudinaryFolder(
                        '/Assets/image-sequences'
                    )}/products/wave1/mobile/Wave1_Web_Animation_Mobile`,
                    imageCount: 297,
                    imageWidth: 2400,
                    imageHeight: 1200,
                    mobileImageWidth: 818,
                    mobileImageHeight: 1200,
                    mobileImageCount: 299,
                    height: '2200vh',
                    padLength: 5,
                    pauseFrames: [
                        6,
                        45, // white ring
                        46, // red ring
                        47, // white ring
                        110,
                        142,
                        244,
                        294,
                        295,
                        296
                    ],
                    fadeFrames: [65, 85, 378, 427, 447, 467],
                    mobilePauseFrames: [
                        6,
                        7,
                        45,
                        46,
                        47,
                        110,
                        115,
                        145,
                        240,
                        246,
                        296,
                        297,
                        298
                    ],
                    mobileFadeFrames: [103, 123, 437, 486, 506, 526],
                    textPanel: introTextPanel,
                    bgColor: SectionBgColor.BG_GREY,
                    mobileBgColor: SectionBgColor.BLACK,
                    productCallouts: productCallouts
                } as ProductImageSequenceProps
            }
        />
    )
}

export default ProductImageSequenceWave1
