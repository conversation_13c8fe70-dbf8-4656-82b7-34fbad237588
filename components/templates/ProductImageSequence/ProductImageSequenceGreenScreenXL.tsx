import React, { FC, useCallback, useRef, useState } from 'react'
import { SpecialAnimationContent } from '@components/templates/SpecialAnimation/SpecialAnimation'
import ProductImageSequence, {
    ProductImageSequenceProps
} from '@components/templates/ProductImageSequence/ProductImageSequence'
import cn from 'classnames'
import s from '@components/templates/ProductImageSequence/ProductImageSequenceGreenScreenXL.module.scss'
import PrimaryText, {
    HorizontalAlignmentEnum
} from '@components/molecules/PrimaryText/PrimaryText'
import {
    Container,
    ContainerSize
} from '@components/organisms/Container/Container'
import dynamic from 'next/dynamic'
const ProductImageSequenceGreenScreenXLAnimation = dynamic(
    () =>
        import(
            '@components/templates/ProductImageSequence/ProductImageSequenceGreenScreenXLAnimation'
        ),
    {
        ssr: false
    }
)

export const ProductImageSequenceGreenScreenXL: FC<SpecialAnimationContent> = ({
    content
}) => {
    const { textPanels = [] } = content
    const [activeTextIndex, setActiveTextIndex] = useState(0)
    const textPanelsRef = useRef<Array<HTMLDivElement | null>>([])
    const headlineText1Ref = useRef<HTMLDivElement | null>(null)
    const headlineText1WrapperRef = useRef<HTMLDivElement | null>(null)
    const headlineText1BodyCopyRef = useRef<HTMLDivElement | null>(null)
    const specialTextDesktopRef = useRef<HTMLDivElement | null>(null)
    const [frame, setFrame] = useState(0)
    const onFrameUpdate = useCallback((frame: number) => {
        setFrame(frame)
    }, [])
    const children = (
        <div className={s['green-screen-xl-animation__children']}>
            {textPanels.map((text, i) =>
                i === 0 ? (
                    <>
                        {/* hidden div (for text width reference) */}
                        <div className="flex h-0 opacity-0 pointer-events-none">
                            <div
                                className="flex no-wrap gap-12px"
                                ref={headlineText1WrapperRef}
                            >
                                <div className="flex-none whitespace-nowrap">
                                    <h1>{text.headline}</h1>
                                </div>
                                <div
                                    className="flex-none whitespace-nowrap"
                                    ref={headlineText1BodyCopyRef}
                                >
                                    <h1>{text.bodyCopy}</h1>
                                </div>
                            </div>
                        </div>
                        <div
                            key={`green-screen-xl-animation__text-${i}}`}
                            ref={(el) => (textPanelsRef.current[i] = el)}
                            className={cn(
                                s['green-screen-xl-animation__text'],
                                {
                                    [s[
                                        'green-screen-xl-animation__text--active'
                                    ]]: activeTextIndex === i
                                }
                            )}
                        >
                            <h1 className="md:hidden text-center px-16px">
                                {text.headline}
                            </h1>
                            <div
                                className={
                                    s['green-screen-xl-animation__text-inner']
                                }
                            >
                                <div
                                    className="overflow-hidden"
                                    ref={specialTextDesktopRef}
                                >
                                    <div className="flex no-wrap gap-12px">
                                        <div
                                            className="hidden md:flex flex-none whitespace-nowrap"
                                            ref={headlineText1Ref}
                                        >
                                            <h1>{text.headline}</h1>
                                        </div>
                                        <div className="flex-none whitespace-nowrap">
                                            <h1>{text.bodyCopy}</h1>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </>
                ) : (
                    <div
                        key={`green-screen-xl-animation__text-${i}}`}
                        ref={(el) => (textPanelsRef.current[i] = el)}
                        className={cn(s['green-screen-xl-animation__text'], {
                            [s['green-screen-xl-animation__text--active']]:
                                activeTextIndex === i
                        })}
                    >
                        <Container size={ContainerSize.SMALL}>
                            <div className="pt-16 md:pt-32">
                                <PrimaryText
                                    bodyCopy={text.bodyCopy}
                                    headline={text.headline}
                                    textAlignment={
                                        HorizontalAlignmentEnum.CENTER
                                    }
                                />
                            </div>
                        </Container>
                    </div>
                )
            )}
        </div>
    )
    return (
        <div className={s['green-screen-xl-animation']}>
            <ProductImageSequence
                content={
                    {
                        debug: false,
                        className:
                            s['green-screen-xl-image-sequence__sequence'],
                        sequenceName: 'green-screen-xl-image-sequence',
                        path:
                            'https://res.cloudinary.com/elgato-pwa/image/upload/v1683637041/Products/10GBG9901/product-image-sequence/desktop/GreenScreenXL_anim',
                        pathMobile:
                            'https://res.cloudinary.com/elgato-pwa/image/upload/v1683637066/Products/10GBG9901/product-image-sequence/mobile/GreenScreenXL_anim_Mobile',
                        imageCount: 160,
                        imageWidth: 1920,
                        imageHeight: 1920,
                        mobileImageWidth: 800,
                        mobileImageHeight: 1500,
                        mobileImageCount: 160,
                        height: '580vh',
                        padLength: 3,
                        pauseFrames: [66, 99, 159],
                        mobilePauseFrames: [66, 99, 159],
                        children: children,
                        onFrameUpdate: onFrameUpdate
                    } as ProductImageSequenceProps
                }
            />
            <ProductImageSequenceGreenScreenXLAnimation
                headlineText1WrapperRef={headlineText1WrapperRef}
                headlineText1BodyCopyRef={headlineText1BodyCopyRef}
                textPanelsRef={textPanelsRef}
                frame={frame}
                setActiveTextIndex={setActiveTextIndex}
                headlineText1Ref={headlineText1Ref}
                specialTextDesktopRef={specialTextDesktopRef}
            />
        </div>
    )
}

export default ProductImageSequenceGreenScreenXL
