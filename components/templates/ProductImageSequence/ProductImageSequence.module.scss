.product-image-sequence {
    @apply relative h-auto;
    min-height: 100vh;

    &__text-wrapper {
        position: absolute;
        top: 0;
        left: 16px;
        right: 16px;
        width: calc(100% - 32px);
        height: 100vh;
        z-index: 20;
        padding-top: 70px;
        opacity: 1;
        -webkit-transform: translate(0, 0);
        transform: translate(0, 0);
        will-change: opacity, transform;
        pointer-events: none;
        max-width: 888px;
        margin: 0 auto;

        a {
            pointer-events: auto;
        }

        @screen lg {
            padding-top: 80px;
        }
    }

    &__overlay,
    &__fade-wrapper {
        height: 100vh;
        overflow: hidden;
        position: sticky;
        top: 0;
        bottom: 0;
        left: 0;
        width: 100%;
    }

    &__callouts {
        opacity: 1;
        position: absolute;
        top: 50%;
        left: 50%;
        right: auto;
        --element-height: 100%;
        --element-width: 100%;
        transform: translate(-50%, -50%);
        z-index: 1;
        height: var(--element-height);
        width: var(--element-width);
        max-width: 1920px;
    }

    &__callout {
        position: absolute !important;
        @apply opacity-0;
        transform: translate(0, 20px, 0);
        z-index: -1;
    }

    &__container {
        position: relative;
        width: 100%;
    }

    &__animation-wrapper {
        position: sticky;
        left: 0;
        top: 0;
        width: 100%;
        height: 100vh;
        overflow: hidden;

        /* If there is a text panel, translate the wrapper */
        &--translate {
            /* transform: translate(0, 15%); facecam, wave-xlr 15%, wave-3 + wave-1 transform: translate(0,10%) translate(0,200px); */
            transform: translate(0, 31vh);
            @screen md {
                transform: translate(0, 20vh);
            }
        }
    }

    &__canvas-wrapper {
        @apply relative h-full;
    }

    &__canvas,
    &__fade-img {
        @apply w-full absolute top-1/2 left-1/2;
        @apply transform -translate-x-1/2 -translate-y-1/2;
        @apply h-screen object-cover;
    }

    &__fade-img {
        opacity: 0;
        will-change: opacity;
        &--inactive {
            opacity: 0 !important;
        }
    }

    &--with-sticky-nav {
        .product-image-sequence__canvas,
        .product-image-sequence__fade-img,
        .product-image-sequence__animation-wrapper,
        .product-image-sequence__text-wrapper,
        .product-image-sequence__overlay,
        .product-image-sequence__fade-wrapper {
            height: calc(100vh - var(--sticky-nav-height--sm));

            @screen md {
                height: calc(100vh - var(--sticky-nav-height--lg));
            }
        }
        .product-image-sequence__animation-wrapper,
        .product-image-sequence__overlay,
        .product-image-sequence__fade-wrapper {
            top: var(--sticky-nav-height--sm);
            @screen md {
                top: var(--sticky-nav-height--lg);
            }
        }
    }

    :global {
        #cam-link-pro-advanced-broadcasting {
            @screen md-max {
                width: 200%;
                margin-left: -50%;
            }
            canvas {
                @screen md-max {
                    object-fit: contain;
                }
            }
        }

        #mic-arm-lp-product-image-sequence {
            canvas {
                @screen md-max {
                    object-fit: contain;
                }
            }
        }
    }
}
