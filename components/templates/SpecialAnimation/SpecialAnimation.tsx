/* TODO global Special animation to be used for all special animations */
import type { PrimaryTextProps } from '@components/molecules/PrimaryText/PrimaryText'
import type { ConfiguratorTeaserProps } from '@components/templates/ConfiguratorTeaser/ConfiguratorTeaser'
import { SectionBgColor } from '@components/templates/Section/Section'
import type { SetupCardsHeroProps } from '@components/templates/SetupCards/SetupCardsHero/SetupCardsHero'
import type { StreamDeckInteractionPanelProps } from '@components/templates/StreamDeckInteractionPanel/StreamDeckInteractionPanel'
import type { TextWithMediaSoundProps } from '@components/templates/TextWithMediaSound/TextWithMediaSound'
import type {
    AudioType,
    ImageType,
    VideoType
} from '@pylot-data/hooks/contentful/use-content-json'
import { FC, useMemo } from 'react'

import { AudioHeroProps } from '@components/templates/AudioHero/AudioHero'
import ColorfulPanelsWithVideos from '@components/templates/ColorfulPanelsWithVideos/ColorfulPanelsWithVideos'
import dynamic from 'next/dynamic'
import CardTextAnimation from '@components/templates/CardTextAnimation/CardTextAnimation'
import ProductImageSequenceImageRotation from '@components/templates/ProductImageSequence/ProductImageSequenceImageRotation'
import { CloudinaryMedia } from '@components/common/CloudinaryMedia/Cloudinary'
import DockXLRSideScrollAnimation from '@components/templates/DockXLRSideScrollAnimation/DockXLRSideScrollAnimation'
import { ImageExpand } from '@components/templates/ImageExpand/ImageExpand'
import { FullScreenImageWithTextFade } from '@components/templates/FullScreenImageWithTextFade/FullScreenImageWithTextFade'
import StickyImageTextRevealIrreversible from '@components/templates/StickyImageTextRevealIrreversible/StickyImageTextRevealIrreversible'
import ProductImageSequenceScrollAnimationWithSpecialCallout from '@components/templates/ProductImageSequence/ProductImageSequenceScrollAnimationWithSpecialCallout'
import TextSlidingImageSwapping from '@components/templates/TextSlidingImageSwapping/TextSlidingImageSwapping'
import { TextWithLoopingVideo } from '@components/templates/TextWithLoopingVideo/TextWithLoopingVideo'
import { TextWithToggleVideos } from '@components/templates/TextWithToggleVideos/TextWithToggleVideos'
import { CardProps } from '@components/templates/CardList/CardList'
import { MediaWithContentProps } from '@components/templates/MediaWithContent/MediaWithContent'

const WaveDxAnimation = dynamic(
    () => import('@components/templates/WaveDxAnimation/WaveDxAnimation')
)
const WaveLinkAnimation = dynamic(
    () => import('@components/templates/WaveLinkAnimation/WaveLinkAnimation')
)
const SetupCardsHero = dynamic(
    () =>
        import('@components/templates/SetupCards/SetupCardsHero/SetupCardsHero')
)
const WaveXLRSoundAnimation = dynamic(
    () =>
        import(
            '@components/templates/WaveXLRSoundAnimation/WaveXLRSoundAnimation'
        )
)
const ConfiguratorTeaser = dynamic(
    () => import('@components/templates/ConfiguratorTeaser/ConfiguratorTeaser')
)
const TextWithImageSidescroll = dynamic(
    () =>
        import(
            '@components/templates/TextWithImageSidescroll/TextWithImageSidescroll'
        )
)
const TextWithMediaSound = dynamic(
    () => import('@components/templates/TextWithMediaSound/TextWithMediaSound')
)
const TextWithMediaWithSelector = dynamic(
    () =>
        import(
            '@components/templates/TextWithMediaWithSelector/TextWithMediaWithSelector'
        )
)
const StickyImageTextRevealAnimation = dynamic(
    () =>
        import(
            '@components/templates/StickyImageTextRevealAnimation/StickyImageTextRevealAnimation'
        )
)
const StickyVideoTextRevealAnimation = dynamic(
    () =>
        import(
            '@components/templates/StickyVideoTextRevealAnimation/StickyVideoTextRevealAnimation'
        ),
    { ssr: false }
)
const StreamDeckInteractionPanel = dynamic(
    () =>
        import(
            '@components/templates/StreamDeckInteractionPanel/StreamDeckInteractionPanel'
        )
)
const ProductImageSequenceWave3 = dynamic(
    () =>
        import(
            '@components/templates/ProductImageSequence/ProductImageSequenceWave3'
        )
)
const ProductImageSequenceFacecam = dynamic(
    () =>
        import(
            '@components/templates/ProductImageSequence/ProductImageSequenceFacecam'
        )
)
const ProductImageSequenceWave1 = dynamic(
    () =>
        import(
            '@components/templates/ProductImageSequence/ProductImageSequenceWave1'
        )
)
const ProductImageSequenceWavePanels = dynamic(
    () =>
        import(
            '@components/templates/ProductImageSequence/ProductImageSequenceWavePanels'
        )
)
const ProductImageSequenceMicArmLP = dynamic(
    () =>
        import(
            '@components/templates/ProductImageSequence/ProductImageSequenceMicArmLP'
        )
)
const ProductImageSequenceMicArm = dynamic(
    () =>
        import(
            '@components/templates/ProductImageSequence/ProductImageSequenceMicArm'
        )
)
const ProductImageSequenceRingLight = dynamic(
    () =>
        import(
            '@components/templates/ProductImageSequence/ProductImageSequenceRingLight'
        )
)
const ProductImageSequenceKeyLightAir = dynamic(
    () =>
        import(
            '@components/templates/ProductImageSequence/ProductImageSequenceKeyLightAir'
        )
)
const ProductImageSequenceKeyLightMk2 = dynamic(
    () =>
        import(
            '@components/templates/ProductImageSequence/ProductImageSequenceKeyLightMk2'
        )
)
const ProductImageSequenceWaveXLR = dynamic(
    () =>
        import(
            '@components/templates/ProductImageSequence/ProductImageSequenceWaveXLR'
        )
)
const ProductImageSequenceGreenScreenXL = dynamic(
    () =>
        import(
            '@components/templates/ProductImageSequence/ProductImageSequenceGreenScreenXL'
        )
)
const ProductImageSequencePrompter = dynamic(
    () =>
        import(
            '@components/templates/ProductImageSequence/ProductImageSequencePrompter'
        )
)
const CamLinkProAdvancedBroadcasting = dynamic(
    () =>
        import(
            '@components/templates/CamLinkProAdvancedBroadcasting/CamLinkProAdvancedBroadcasting'
        )
)
const TextImageMaskAnimation = dynamic(
    () =>
        import(
            '@components/templates/TextImageMaskAnimation/TextImageMaskAnimation'
        )
)
const MultipleImageSplitAnimation = dynamic(
    () =>
        import(
            '@components/templates/MultipleImageSplitAnimation/MultipleImageSplitAnimation'
        )
)
const StackCards = dynamic(
    () => import('@components/templates/StackCards/StackCards')
)
const MediaSequenceZoomIn = dynamic(
    () =>
        import('@components/templates/MediaSequenceZoomIn/MediaSequenceZoomIn')
)
const VideosZoomIn = dynamic(
    () => import('@components/templates/VideosZoomIn/VideosZoomIn')
)
const StreamDeckPedalAnimation = dynamic(
    () =>
        import(
            '@components/templates/StreamDeckPedalAnimation/StreamDeckPedalAnimation'
        )
)
const FacecamProSideScrollAnimation = dynamic(
    () =>
        import(
            '@components/templates/FacecamProSideScrollAnimation/FacecamProSideScrollAnimation'
        )
)
const StreamDeckPlusDialsCollection = dynamic(
    () =>
        import(
            '@components/templates/StreamDeckPlusDialsCollection/StreamDeckPlusDialsCollection'
        )
)
const TeaserBoxGalleryZoom = dynamic(
    () =>
        import(
            '@components/templates/TeaserBoxGalleryZoom/TeaserBoxGalleryZoom'
        )
)
const CaseStudiesHero = dynamic(
    () => import('@components/templates/CaseStudiesHero/CaseStudiesHero')
)

const UserManualHero = dynamic(
    () => import('@components/templates/UserManualHero/UserManualHero')
)

const LottieHero = dynamic(
    () => import('@components/templates/LottieHero/LottieHero')
)

const PrompterWidestLenses = dynamic(
    () =>
        import(
            '@components/templates/PrompterWidestLenses/PrompterWidestLenses'
        )
)

const Hotspots360 = dynamic(
    () => import('@components/templates/Hotspots360/Hotspots360')
)

const CardsZoomSlider = dynamic(
    () => import('@components/templates/CardsZoomSlider/CardsZoomSlider')
)

const AudioHero = dynamic(
    () => import('@components/templates/AudioHero/AudioHero')
)
const TextOverlayMedia = dynamic(
    () => import('@components/templates/TextOverlayMedia/TextOverlayMedia')
)
const ProductImageSequenceRemote = dynamic(
    () =>
        import(
            '@components/templates/ProductImageSequenceRemote/ProductImageSequenceRemote'
        )
)
const ImageFadeOutTextFadeInAnimation = dynamic(
    () =>
        import(
            '@components/templates/ImageFadeOutTextFadeInAnimation/ImageFadeOutTextFadeInAnimation'
        )
)
const TextRevealAnimation = dynamic(
    () =>
        import('@components/templates/TextRevealAnimation/TextRevealAnimation')
)
const SideScrollingAnimation = dynamic(
    () =>
        import(
            '@components/templates/SideScrollingAnimation/SideScrollingAnimation'
        )
)
const FloatingMediaAnimation = dynamic(
    () =>
        import(
            '@components/templates/FloatingMediaAnimation/FloatingMediaAnimation'
        )
)

export interface SpecialAnimationProps {
    title?: string
    textPanels?: PrimaryTextProps[]
    images?: ImageType[]
    medias?: ImageType[]
    mobileMedias?: ImageType[]
    cloudinaryMedia?: CloudinaryMedia[]
    cloudinaryMedia2?: CloudinaryMedia[]
    cloudinaryVideo?: CloudinaryMedia[]
    cloudinaryMobileMedia?: CloudinaryMedia[]
    videos?: VideoType[]
    mobileVideos?: VideoType[]
    videoDescription?: string
    animation?:
        | 'wave-dx'
        | 'wave-link'
        | 'wave-xlr-sound'
        | 'wave-xlr-style-chooser'
        | 'text-image-mask'
        | 'multiple-image-split'
        | 'stack-cards'
        | 'media-sequence-zoom-in'
        | 'videos-zoom-in'
        | 'product-image-sequence-wave-3'
        | 'product-image-sequence-facecam'
        | 'text-with-media-sound'
        | 'text-with-image-sidescroll'
        | 'text-with-media-with-selector'
        | '3d-configurator-teaser'
        | 'stream-deck-interaction-panel-cam-link-pro'
        | 'stream-deck-interaction-panel-stream-deck-mk2'
        | 'stream-deck-pedal-at-your-feet'
        | 'cam-link-pro-advanced-broadcasting'
        | 'product-image-sequence-mic-arm-lp'
        | 'product-image-sequence-mic-arm'
        | 'product-image-sequence-key-light-air'
        | 'product-image-sequence-ring-light'
        | 'product-image-sequence-green-screen-xl'
        | 'product-image-sequence-prompter'
        | 'facecam-pro-text-with-image-sidescroll'
        | 'sd-plus-dials-collection'
        | 'teaser-box-gallery-zoom'
        | 'case-studies-hero'
        | 'text-overlay-media'
        | 'setup-lp-hero'
        | 'audio-hero'
        | 'product-image-sequence-remote'
        | 'product-image-sequence-mk2'
        | 'sticky-image-text-reveal'
        | 'sticky-video-text-reveal'
        | 'card-text-animation'
        | 'dock-xlr'
        | 'image-expand'
        | 'image-fade-out-text-fade-in'
        | 'fullscreen-image-with-text-fade'
        | 'sticky-image-text-reveal-irreversible'
        | 'text-with-looping-video'
        | 'text-with-toggle-videos'
        | 'text-reveal-animation'
        | 'side-scrolling-animation'
        | 'floating-media'
    children?: any[]
    additionalOptions?: any
    audios?: AudioType[]
    id?: string
    mediaWithContent?: MediaWithContentProps[]
}

export interface SpecialAnimationContent {
    content: SpecialAnimationProps
}

export const SpecialAnimation: FC<SpecialAnimationContent> = ({ content }) => {
    const {
        animation,
        textPanels = [],
        images = [],
        videos = [],
        additionalOptions,
        audios = []
    } = content
    const getAnimation = (selectedAnimation: string | undefined) => {
        switch (selectedAnimation) {
            case 'wave-dx': {
                return <WaveDxAnimation content={content} />
            }
            case 'wave-link': {
                return <WaveLinkAnimation content={content} />
            }
            case 'setup-lp-hero': {
                const setupCardsHeroContent: SetupCardsHeroProps = {
                    views: content.children
                }
                return <SetupCardsHero content={setupCardsHeroContent} />
            }
            case 'wave-xlr-sound': {
                const items: TextWithMediaSoundProps[] = []
                if (content.children) {
                    content.children.forEach((child: any) => {
                        items.push({
                            toggleMedia: child.videos,
                            toggleLabels: child.additionalOptions?.toggleLabels,
                            textPanel: child.textPanels
                                ? child.textPanels[0]
                                : child.textPanel,
                            toggleColor: child.additionalOptions?.toggleColor
                        })
                    })
                }
                content.children = items
                return <WaveXLRSoundAnimation content={content} />
            }
            case 'product-image-sequence-wave-xlr': {
                return <ProductImageSequenceWaveXLR content={content} />
            }
            case 'product-image-sequence-wave-3': {
                return <ProductImageSequenceWave3 content={content} />
            }
            case 'product-image-sequence-wave-1': {
                return <ProductImageSequenceWave1 content={content} />
            }
            case 'product-image-sequence-facecam': {
                return <ProductImageSequenceFacecam content={content} />
            }
            case 'product-image-sequence-wave-panels': {
                return <ProductImageSequenceWavePanels content={content} />
            }
            case 'product-image-sequence-mic-arm-lp': {
                return <ProductImageSequenceMicArmLP content={content} />
            }
            case 'product-image-sequence-mic-arm': {
                return <ProductImageSequenceMicArm content={content} />
            }
            case 'product-image-sequence-key-light-air': {
                return <ProductImageSequenceKeyLightAir content={content} />
            }
            case 'product-image-sequence-mk2': {
                return <ProductImageSequenceKeyLightMk2 content={content} />
            }
            case 'product-image-sequence-ring-light': {
                return <ProductImageSequenceRingLight content={content} />
            }
            case 'product-image-sequence-green-screen-xl': {
                return <ProductImageSequenceGreenScreenXL content={content} />
            }
            case 'product-image-sequence-prompter': {
                return <ProductImageSequencePrompter content={content} />
            }
            case 'product-image-sequence-remote': {
                return <ProductImageSequenceRemote content={content} />
            }
            case 'image-fade-out-text-fade-in': {
                return <ImageFadeOutTextFadeInAnimation content={content} />
            }
            case 'new-product-images-sequence-scroll-animation': {
                return (
                    <ProductImageSequenceScrollAnimationWithSpecialCallout
                        content={content}
                    />
                )
            }
            case 'text-with-media-sound': {
                const withAudio = audios && audios.length >= 2
                let textWithMediaProps: TextWithMediaSoundProps = {
                    textPanel: textPanels[0],
                    media: images[0],
                    toggleMedia:
                        videos && videos.length >= 2
                            ? [videos[0], videos[1]]
                            : undefined,
                    audioSrc1: withAudio ? audios[0].file.url : undefined,
                    audioSrc2: withAudio ? audios[1].file.url : undefined,
                    videoDescription: content?.videoDescription
                }
                if (additionalOptions) {
                    textWithMediaProps = {
                        ...textWithMediaProps,
                        ...additionalOptions
                    }
                }
                return <TextWithMediaSound content={textWithMediaProps} />
            }
            case 'text-with-image-sidescroll': {
                let textWithImageSidescrollProps = {
                    textPanel: textPanels[0],
                    image: images[0]
                }
                if (additionalOptions) {
                    textWithImageSidescrollProps = {
                        ...textWithImageSidescrollProps,
                        ...additionalOptions
                    }
                }
                return (
                    <TextWithImageSidescroll
                        content={textWithImageSidescrollProps}
                    />
                )
            }
            case 'audio-hero': {
                const setupCardsHeroContent: AudioHeroProps = {
                    views: content.children
                }
                return <AudioHero content={setupCardsHeroContent} />
            }

            case 'text-with-media-with-selector':
                return <TextWithMediaWithSelector content={content} />
            case '3d-configurator-teaser': {
                const configuratorTeaserContent: ConfiguratorTeaserProps = {
                    textPanel:
                        content.textPanels && content.textPanels[0]
                            ? content.textPanels[0]
                            : undefined,
                    image:
                        content.images && content.images[0]
                            ? content.images[0]
                            : undefined,
                    hoverImage:
                        content.images && content.images[1]
                            ? content.images[1]
                            : undefined
                }
                return (
                    <ConfiguratorTeaser content={configuratorTeaserContent} />
                )
            }
            case 'sticky-image-text-reveal': {
                return <StickyImageTextRevealAnimation content={content} />
            }
            case 'sticky-video-text-reveal': {
                return <StickyVideoTextRevealAnimation content={content} />
            }
            case 'stream-deck-interaction-panel-cam-link-pro': {
                let labels = []
                if (additionalOptions && additionalOptions.labels) {
                    labels = additionalOptions.labels
                }
                const interactionImages = []
                if (content.images && content.images.length) {
                    for (let i = 2; i <= 6; i++) {
                        interactionImages.push(content.images[i])
                    }
                }
                const sdInteractionPanelContent: StreamDeckInteractionPanelProps = {
                    textPanel:
                        content.textPanels && content.textPanels[0]
                            ? content.textPanels[0]
                            : undefined,
                    baseMedia:
                        content.images && content.images[0]
                            ? content.images[0]
                            : undefined,
                    streamDeckImage:
                        content.images && content.images[1]
                            ? content.images[1]
                            : undefined,
                    bgColor: SectionBgColor.WHITE_SMOKE,
                    labels: labels,
                    interactionImages: interactionImages,
                    imageKey1:
                        content.images && content.images[7]
                            ? content.images[7]
                            : undefined,
                    imageKey2:
                        content.images && content.images[8]
                            ? content.images[8]
                            : undefined,
                    imageKey3:
                        content.images && content.images[9]
                            ? content.images[9]
                            : undefined,
                    imageKey4:
                        content.images && content.images[10]
                            ? content.images[10]
                            : undefined,
                    imageKey5:
                        content.images && content.images[11]
                            ? content.images[11]
                            : undefined
                }
                return (
                    <StreamDeckInteractionPanel
                        variant="cam-link-pro"
                        content={sdInteractionPanelContent}
                    />
                )
            }
            case 'stream-deck-interaction-panel-stream-deck-mk2': {
                const interactionImages = []
                if (content.images && content.images.length) {
                    for (let i = 1; i <= content.images.length; i++) {
                        if (i <= 4) {
                            interactionImages.push(content.images[i])
                        }
                    }
                }
                const sdInteractionPanelContent: StreamDeckInteractionPanelProps = {
                    textPanel:
                        content.textPanels && content.textPanels[0]
                            ? content.textPanels[0]
                            : undefined,
                    baseMedia:
                        content.videos && content.videos[0]
                            ? content.videos[0]
                            : undefined,
                    streamDeckImage:
                        content.images && content.images[0]
                            ? content.images[0]
                            : undefined,
                    interactionImages: interactionImages,
                    audio:
                        content.audios && content.audios[0]
                            ? content.audios[0].file.url
                            : undefined,
                    imageKey2:
                        content.images && content.images[5]
                            ? content.images[5]
                            : undefined,
                    videoKey3:
                        content.videos && content.videos[1]
                            ? content.videos[1]
                            : undefined,
                    videoKey4:
                        content.videos && content.videos[2]
                            ? content.videos[2]
                            : undefined,
                    videoDescription: content?.videoDescription
                }
                return (
                    <StreamDeckInteractionPanel
                        variant="stream-deck-mk2"
                        content={sdInteractionPanelContent}
                    />
                )
            }
            case 'text-image-mask': {
                return <TextImageMaskAnimation content={content} />
            }
            case 'text-overlay-media': {
                return <TextOverlayMedia content={content} />
            }
            case 'multiple-image-split': {
                return <MultipleImageSplitAnimation content={content} />
            }
            case 'stack-cards': {
                return <StackCards content={content} />
            }
            case 'media-sequence-zoom-in': {
                return <MediaSequenceZoomIn content={content} />
            }
            case 'videos-zoom-in': {
                return <VideosZoomIn content={content} />
            }
            case 'stream-deck-pedal-at-your-feet': {
                return <StreamDeckPedalAnimation content={content} />
            }
            case 'cam-link-pro-advanced-broadcasting': {
                return <CamLinkProAdvancedBroadcasting content={content} />
            }
            case 'facecam-pro-text-with-image-sidescroll': {
                return <FacecamProSideScrollAnimation content={content} />
            }
            case 'sd-plus-dials-collection': {
                return <StreamDeckPlusDialsCollection content={content} />
            }
            case 'teaser-box-gallery-zoom': {
                return <CardsZoomSlider content={content} />
            }
            case 'case-studies-hero': {
                return <CaseStudiesHero content={content} />
            }
            case 'user-manual-hero': {
                return <UserManualHero content={content} />
            }
            case 'lottie-hero': {
                return <LottieHero content={content} />
            }
            case 'prompter-widest-lenses': {
                return <PrompterWidestLenses content={content} />
            }
            case 'hotspots-360': {
                return <Hotspots360 content={content} />
            }
            case 'colorful-panels-with-videos': {
                return <ColorfulPanelsWithVideos content={content} />
            }
            case 'card-text-animation': {
                return <CardTextAnimation content={content} />
            }
            case 'text-sliding-image-swapping': {
                return <TextSlidingImageSwapping content={content} />
            }
            case 'image-rotation': {
                return <ProductImageSequenceImageRotation content={content} />
            }
            case 'dock-xlr-side-scroll': {
                return <DockXLRSideScrollAnimation content={content} />
            }
            case 'image-expand': {
                return <ImageExpand content={content} />
            }
            case 'fullscreen-image-with-text-fade': {
                return <FullScreenImageWithTextFade content={content} />
            }
            case 'sticky-image-text-reveal-irreversible': {
                return <StickyImageTextRevealIrreversible content={content} />
            }
            case 'text-with-looping-video': {
                return <TextWithLoopingVideo {...content} />
            }
            case 'text-with-toggle-videos': {
                return (
                    <TextWithToggleVideos
                        {...content}
                        cards={content.children as CardProps[]}
                    />
                )
            }
            case 'text-reveal-animation': {
                return <TextRevealAnimation content={content} />
            }
            case 'side-scrolling-animation': {
                return <SideScrollingAnimation content={content} />
            }
            case 'floating-media': {
                return <FloatingMediaAnimation content={content} />
            }
            default: {
                return null
            }
        }
    }
    return useMemo(() => getAnimation(animation), [animation])
}

export default SpecialAnimation
