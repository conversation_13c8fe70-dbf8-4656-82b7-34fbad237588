.special-day-sales-card {
    @apply rounded-xl;
    width: 100%;
    gap: 32px;
    // max-height: 80px;
    &__discount-label {
        @screen md-max {
            left: 16px !important;
            top: 16px !important;
            padding: 8px 12px !important;
            font-size: 20px !important;
            border-radius: 6px !important;
        }
    }
    
    button {
        min-width: 100px !important;
        padding: 12px 16px !important;
        span {
            font-size: 14px !important;
            line-height: 16px !important;
        }
        height: 40px !important;
    }

    &__product__price {
        > div:first-child {
            @screen md-max {
                flex-direction: column;
                display: flex;
            }
            > div:first-child {
                font-size: 24px !important;
                font-weight: 700 !important;
            }
            > div:nth-child(2) {
                font-size: 24px !important;
                color: #9E9E9E !important;
                font-weight: 700 !important;
                @screen md-max {
                    font-size: 18px !important;
                }
            }
        }
    }
    // This is a new font that we do not have yet in the tailwind config, also in our main.css file 
    &__product__body-copy {
        font-family: 'univers55Roman';
        font-size: 20px;
        font-weight: 500;
        line-height: 30px;
        color: #151515 !important;
        @screen md-max {
            font-size: 16px !important;
            line-height: 24px !important;
            font-weight: 400 !important;
        }
    }

    &__promotion-popup {
        position: absolute !important;
        transform: none !important;
        right: 24px !important;
        bottom: 24px !important;
        top: unset !important;
        .promotion-popup__links-logo {
            @screen md-max {
                display: none;
            }
        }
    }

    &__inner {
        width: 100%;
        border-radius: 8px;
        @screen md {
            border-radius: 16px;
        }

        .special-day-sales-card__product-copy-body {
            margin-top: 0;
            @screen md {
                opacity: 0;
            }
        }

        &:hover {
            @screen md {
                .special-day-sales-card__product {
                    transform: translate(0, -80px);
                    transition-timing-function: ease-in-out;
                    transition-duration: 0.2s;
                }

                .special-day-sales-card__poster {
                    opacity: 1;
                }

                .special-day-sales-card__product-copy-body {
                    opacity: 1;
                    transition: opacity 0.5s ease-in-out;
                }
            }
        }
    }

    /* sizes */
    &--size-8 {
        width: 100%;
    }

    &--size-6 {
        width: 100%;
        @screen xl {
            width: calc(50% - 16px);
        }
    }

    &--size-4 {
        width: 100%;

        @screen md {
            width: calc(33.333% - 8px);
        }

        .special-day-sales-card__product {
            @media screen and (max-width: 1180px) {
                flex-direction: column;
                align-items: flex-start;
            }
        }
    }

    &__product {
        &__add-to-cart {
            padding: 0 !important;
            border: 0 !important;
            height: 43px;
            min-width: 140px;

            svg {
                width: 100px !important;
                height: auto !important;
            }
        }

        &__links {
            display: flex;
            flex-direction: row;
            align-items: center;

            &-elgatologo {
                max-width: 20px;
                max-height: 20px;
            }

            &-link {
                min-width: 140px;

                &-logo {
                    border-radius: 6px;
                    height: 43px;

                    svg {
                        width: fit-content !important;
                    }
                    img {
                        height: 200%; // TEMPORARILY ADDED TO ADD RETAILERS AS IMAGES
                    }
                }
            }

            &__elgato-logo {
                width: 140px;
                height: 43px;
                border: none !important;
                background-color: black !important;
                border-radius: 6px !important;

                svg {
                    width: 100% !important;
                    height: 100% !important;
                }
            }
        }

        &__alignment {
            display: flex;
            @screen md {
                flex-direction: column;
            }
            &_4 {
                align-items: flex-start;
                @media screen and (min-width: 1180px) {
                    align-items: flex-end;
                }
            }

            &__6 {
                min-width: 150px;
            }

            &_6,
            &_8 {
                align-items: center;
                @screen md {
                    align-items: flex-end;
                }
            }
        }
    }
}
