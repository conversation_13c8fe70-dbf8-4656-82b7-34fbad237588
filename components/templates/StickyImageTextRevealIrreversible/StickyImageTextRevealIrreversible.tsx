import { FeatureIconText } from '@components/molecules/FeatureIconText/FeatureIconText'
import { SpecialAnimationContent } from '@components/templates/SpecialAnimation/SpecialAnimation'
import cn from 'classnames'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/dist/ScrollTrigger'
import { FC, useCallback, useEffect, useRef, useState } from 'react'
import s from './StickyImageTextRevealIrreversible.module.scss'
import ElgatoImage from '@components/common/ElgatoImage'
import TextPanel from '@components/organisms/TextPanel/TextPanel'
import { useMobile } from '@pylot-data/hooks/use-mobile'
import { Button } from '@components/molecules/Button/Button'
import { DetailCardsOverlay } from '@components/templates/DetailCards/DetailCardsOverlay/DetailCardsOverlay'
import { useLayoutContext } from '@components/layouts/MainLayout/LayoutContext'
import { SectionBgColor } from '@components/templates/Section/Section'

gsap.registerPlugin(ScrollTrigger)

const FeatureBox: FC<{ feature: any }> = ({ feature }) => {
    const { isMobile } = useMobile()
    const ref = useRef<HTMLDivElement>(null)
    const timelineRef = useRef<gsap.core.Timeline | null>(null)

    useEffect(() => {
        timelineRef.current = gsap
            .timeline({
                scrollTrigger: {
                    trigger: ref.current,
                    start: 'top center',
                    end: 'bottom center',
                    scrub: true
                }
            })
            .to(ref.current, { opacity: 1 })
    }, [])

    const handleScroll = useCallback(() => {
        const rect = ref.current?.getBoundingClientRect()
        if (rect && rect.bottom) {
            // console.log(feature.label, rect.bottom)
            if (rect.bottom < 0) {
                timelineRef.current?.kill()
            }
        }
    }, [timelineRef])

    useEffect(() => {
        window.addEventListener('scroll', handleScroll)

        return () => {
            window.removeEventListener('scroll', handleScroll)
        }
    }, [handleScroll])

    return (
        <div
            className={cn(
                s['sticky-image-text-reveal__features__feature'],
                'gap-24px flex opacity-0 pt-16px'
            )}
            style={
                isMobile
                    ? {}
                    : {
                          borderTop: '1px solid #525252'
                      }
            }
            ref={ref}
        >
            <FeatureIconText
                label={feature.label}
                icon={feature.icon}
                image={feature.image}
                text={feature.richText ? feature.richText : feature.text}
                className={cn(
                    s['sticky-image-text-reveal__features__item'],
                    'flex-1 gap-24px w-full'
                )}
            />
        </div>
    )
}

/**
 * Displays an animation that shows image, h3 text and a list of features
 * This is a scroll animation in which while scrolling the image is sticky to the bottom/top for large/small screens.
 * While scrolling features are slowly appearing and image is shown over them.
 * Example of this animation: url + /us/en/p/game-capture-4k-x.
 * @param {SpecialAnimationContent} content - content for the Special Animation
 */
const StickyImageTextRevealIrreversible: FC<SpecialAnimationContent> = ({
    content
}) => {
    const { isMobile } = useMobile()
    const textPanelRef = useRef<HTMLDivElement>(null)
    const imageRef = useRef<HTMLDivElement>(null)
    const sectionRef = useRef<HTMLElement>(null)
    const timelineRef = useRef<gsap.core.Timeline | null>(null)
    const [overlayOpen, setOverlayOpen] = useState(false)
    const { pageTheme } = useLayoutContext()

    const cloudinaryMedia = content.cloudinaryMedia?.[0]
    const cloudinaryMobileMedia = content.cloudinaryMobileMedia?.[0]

    const media = isMobile
        ? cloudinaryMobileMedia
            ? cloudinaryMobileMedia
            : cloudinaryMedia
        : cloudinaryMedia

    const featureList = content.children?.find(
        (child) => child.meta?.contentType === 'organismFeatureList'
    )
    const overlayCard = content.children?.find(
        (child) => child.meta?.contentType === 'organismCard'
    )

    function handleScroll() {
        const rect = sectionRef.current?.getBoundingClientRect()
        if (rect && rect.bottom) {
            if (rect.bottom < window.innerHeight / 2) {
                imageRef.current?.classList.remove('sticky')
                timelineRef.current?.kill()
            }
        }
    }

    useEffect(() => {
        window.addEventListener('scroll', handleScroll)

        return () => {
            window.removeEventListener('scroll', handleScroll)
        }
    }, [])

    const { link, ...textPanelProps } = content.textPanels?.[0] || {}

    return (
        <section
            ref={sectionRef}
            className={cn(
                s['sticky-image-text-reveal'],
                'text-white relative bg-primitive-black'
            )}
        >
            <div ref={textPanelRef} className="opacity-1 pb-32">
                {content.textPanels?.[0] && (
                    <TextPanel content={textPanelProps} />
                )}
                <div className="flex justify-center w-full">
                    <Button
                        variant="primary"
                        onClick={() => setOverlayOpen(true)}
                        label={link?.linkTitle}
                    >
                        {link?.linkTitle}
                    </Button>
                </div>
            </div>
            <div className="relative">
                <div
                    className={cn(
                        'grid grid-cols-2 md:grid-cols-3 gap-8 md:gap-40px md:gap-x-40 justify-center md:max-w-1490px mx-auto lg:px-80 pb-32',
                        s['sticky-image-text-reveal__features']
                    )}
                >
                    {featureList.features?.map((feature: any, i: number) => (
                        <FeatureBox key={i} feature={feature} />
                    ))}
                </div>
                <div
                    ref={imageRef}
                    className={cn(
                        s['sticky-image-text-reveal__media'],
                        'sticky bottom-0 z-1 bg-black'
                    )}
                >
                    <ElgatoImage
                        src={media?.secure_url as string}
                        alt={media?.context?.custom.alt}
                        width={1024}
                        height={550}
                        objectFit="contain"
                        /* eslint-disable-next-line i18next/no-literal-string */
                        objectPosition="bottom"
                    />
                </div>
            </div>
            <DetailCardsOverlay
                isOpen={overlayOpen}
                onClose={() => setOverlayOpen(false)}
                contentEntries={overlayCard?.children}
                additionalSwiperSettings={{
                    slidesOffsetAfter: 0
                }}
                overlayBackgroundColor={
                    pageTheme === 'dark'
                        ? SectionBgColor.BLACK
                        : SectionBgColor.WHITE
                }
            />
        </section>
    )
}

export default StickyImageTextRevealIrreversible
