import React, { Dispatch, FC, useCallback, useMemo } from 'react'
import cn from 'classnames'
import s from './StickyNavigationSubpage.module.scss'
import unescape from 'lodash.unescape'
import { Button } from '@components/molecules/Button/Button'
import { Icon } from '@components/atoms/Icon/Icon'
import ChevronRight from '@components/icons/ChevronRight'
import { useLayoutContext } from '@components/layouts/MainLayout/LayoutContext'
export type StickyNavigationSubPageProps = {
    text?: string
    className?: string
    headline?: string
    textColor?: 'dark' | 'light' | 'blue' | undefined
    buttonColor?: 'light' | 'dark' | 'blue'
    onClose?: () => void
    setIsOpen?: Dispatch<React.SetStateAction<boolean>>
    url?: string
    newTab?: boolean
    ariaLabel?: string
}

export const StickyNavigationSubPage: FC<StickyNavigationSubPageProps> = (
    props
) => {
    const {
        text,
        headline,
        textColor = 'light',
        buttonColor = 'light',
        onClose,
        setIsOpen,
        url,
        newTab
    } = props
    const { pageTheme } = useLayoutContext()
    const closeSticky = useCallback(() => {
        onClose?.()
        setIsOpen?.(false)
    }, [onClose, setIsOpen])

    return (
        <div
            className={cn(
                s['sticky-navigation-subpage'],
                s[`sticky-navigation-subpage__text-color-${textColor}`],
                'flex flex-col items-start gap-16px justify-center',
                {
                    'bg-black': pageTheme === 'dark'
                }
            )}
        >
            <div className="flex justify-between items-center flex-1 w-full">
                <div className="flex items-center gap-16px">
                    <div
                        className={cn(
                            'flex justify-center items-center',
                            s['sticky-navigation-subpage__button']
                        )}
                    >
                        <button className="h4">
                            {headline && <h4>{headline}</h4>}
                        </button>
                    </div>
                    <div className="flex flex-end items-center">
                        {/* eslint-disable-next-line i18next/no-literal-string */}
                        <div>
                            {text && (
                                <Button
                                    variant="primary"
                                    // eslint-disable-next-line i18next/no-literal-string
                                    color={
                                        buttonColor === textColor
                                            ? 'light'
                                            : 'blue'
                                    }
                                    href={url}
                                    newTab={newTab}
                                    label={text}
                                >
                                    <span className="text-small-copy whitespace-pre-line">
                                        <div className="hidden md:block">
                                            {text}
                                        </div>
                                        <div className="block md:hidden">
                                            <ChevronRight />
                                        </div>
                                    </span>
                                </Button>
                            )}
                        </div>

                        <button
                            className={cn(
                                'md:hidden',
                                s['sticky-navigation-subpage__icon'],
                                {
                                    [s['sticky-navigation-subpage__icon-blue']]:
                                        textColor === 'dark'
                                }
                            )}
                            key="closeSticky"
                            onClick={closeSticky}
                        >
                            {/* eslint-disable-next-line i18next/no-literal-string */}
                        </button>
                    </div>
                </div>
                <div
                    className={cn('rounded-full flex items-center', {
                        'bg-white text-charcoal': textColor === 'light'
                    })}
                >
                    {/* eslint-disable-next-line i18next/no-literal-string */}
                    <button key="closeSticky" onClick={closeSticky}>
                        {textColor === 'dark' ? (
                            <Icon
                                // eslint-disable-next-line i18next/no-literal-string
                                name="closeIconDark"
                            />
                        ) : (
                            <Icon
                                // eslint-disable-next-line i18next/no-literal-string
                                name="stickyNavClose"
                            />
                        )}
                    </button>
                </div>
            </div>
        </div>
    )
}
