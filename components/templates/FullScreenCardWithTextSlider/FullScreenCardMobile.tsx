import { SliderProps } from '@components/templates/Slider/Slider'
import { FC, useState } from 'react'
import s from './FullScreenCardMobile.module.scss'
import InfoBoxText from '@components/organisms/InfoBox/InfoBoxText'
import { InfoBoxColors } from '@components/organisms/InfoCard/InfoCard'
import cn from 'classnames'
import { getCloudinaryImageAspectRatio } from '@config/hooks/useGetImageAspectRatio'
import { FullScreenCardMobileViewToggles } from '@components/templates/FullScreenCardWithTextSlider/FullScreenCardMobileViewToggles'
import ElgatoImage from '@components/common/ElgatoImage'
import ElgatoVideo from '@components/common/ElgatoVideo/ElgatoVideo'

type FullScreenCardMobileProps = {
    content: SliderProps
    currentSlide: number
    onSlideChange: (index: number) => void
}

export const FullScreenCardMobile: FC<FullScreenCardMobileProps> = ({
    content,
    onSlideChange,
    currentSlide
}) => {
    const { sliderItems } = content

    const currentSlideItem = sliderItems[currentSlide]

    // const media = currentSlideItem?.cloudinaryMobileMedia?.[0]
    // const isVideo = media?.resource_type === 'video'

    // const aspectRatio = getCloudinaryImageAspectRatio({
    //     cloudinaryImageOrVideo: currentSlideItem?.cloudinaryMobileMedia?.[0]
    // })

    return (
        <div className={cn(s['full-screen-card-mobile'])}>
            {/* <div
                className={s['full-screen-card-mobile__image']}
                style={{ aspectRatio: aspectRatio?.toString() }}
            >
                {isVideo ? (
                    <ElgatoVideo
                        secure_url={media?.secure_url}
                        options={{
                            autoPlay: true,
                            preload: 'none',
                            muted: true
                        }}
                        // className={s['dynamic-gallery-card__video']}
                        visibleThreshold={0.95}
                    />
                ) : (
                    <ElgatoImage
                        src={media?.secure_url}
                        alt={media?.context?.custom?.alt || ''}
                        width={media?.width}
                        height={media?.height}
                        layout="fill"
                        objectFit="cover"
                    />
                )}
            </div> */}
            {sliderItems.length > 1 && (
                <FullScreenCardMobileViewToggles
                    content={content}
                    currentSlide={currentSlide}
                    setCurrentSlide={onSlideChange}
                />
            )}
            <InfoBoxText
                title={currentSlideItem.textPanel.headline}
                variant={InfoBoxColors.DARK}
                text={currentSlideItem.textPanel.bodyCopy}
                wordWrap
            />
        </div>
    )
}
