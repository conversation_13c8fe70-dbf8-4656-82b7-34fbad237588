import s from './TeaserBoxGalleryZoom.module.scss'
import React, { FC, useMemo, useRef } from 'react'
import { SpecialAnimationContent } from '@components/templates/SpecialAnimation/SpecialAnimation'
import {
    Container,
    ContainerSize
} from '@components/organisms/Container/Container'
import cn from 'classnames'
import { useLayoutContext } from '@components/layouts/MainLayout/LayoutContext'
import dynamic from 'next/dynamic'
import { CardProps } from '@components/templates/CardList/CardList'

const TeaserBoxGalleryZoomAnimation = dynamic(
    () =>
        import(
            '@components/templates/TeaserBoxGalleryZoom/TeaserBoxGalleryZoomAnimation'
        ),
    {
        ssr: false
    }
)

export const TeaserBoxGalleryZoom: FC<SpecialAnimationContent> = ({
    content
}) => {
    const containerRef = useRef<HTMLDivElement>(null)
    const { navStickyNav: stickyNav } = useLayoutContext()
    const textPanel =
        content.textPanels && content.textPanels.length > 0
            ? content.textPanels[0]
            : null
    const { bgColor } = content.additionalOptions
    const { children } = content
    const panelStyle = useMemo(() => {
        if (children && children.length) {
            const length = children.length
            return {
                '--panelHeight': `${(children.length + 1) * 100}vh`,
                '--cards': length
            } as React.CSSProperties
        }
        return undefined
    }, [children])
    return (
        <div
            className={cn(
                s['teaser-box-gallery-zoom'],
                {
                    [s['teaser-box-gallery-zoom--sticky-nav']]: stickyNav
                },
                bgColor
            )}
            ref={containerRef}
            style={panelStyle}
            id={content.id}
        >
            <div className={s['teaser-box-gallery-zoom__sticky']}>
                <div className={s['teaser-box-gallery-zoom__text-wrapper']}>
                    <Container size={ContainerSize.LARGE}>
                        {textPanel?.headline && (
                            <h2
                                className={
                                    s['teaser-box-gallery-zoom__headline']
                                }
                            >
                                {textPanel.headline}
                            </h2>
                        )}
                    </Container>
                </div>
                <div
                    className={s['teaser-box-gallery-zoom__animation-wrapper']}
                >
                    <TeaserBoxGalleryZoomAnimation
                        containerRef={containerRef}
                        cards={children as CardProps[]}
                    />
                </div>
            </div>
        </div>
    )
}

export default TeaserBoxGalleryZoom
