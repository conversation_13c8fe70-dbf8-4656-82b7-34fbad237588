import { CloudinaryMedia } from '@components/common/CloudinaryMedia/Cloudinary'
import ElgatoMedia, {
    ElgatoMediaProps
} from '@components/common/ElgatoMedia/ElgatoMedia'
import { ContentOverlayProps } from '@components/templates/ContentOverlay/ContentOverlay'
import dynamic from 'next/dynamic'
const ContentOverlay = dynamic(
    () => import('@components/templates/ContentOverlay/ContentOverlay'),
    {
        ssr: false
    }
)
import {
    CSSProperties,
    FC,
    useState,
    useRef,
    MouseEvent as ReactMouseEvent,
    KeyboardEvent as ReactKeyboardEvent,
    useEffect
} from 'react'
import cn from 'classnames'
import { useMedia } from '@lib/hooks/useMedia'
import { Icon } from '@components/atoms/Icon/Icon'
import { useMobile } from '@pylot-data/hooks/use-mobile'
import { useTranslation } from 'next-i18next'
import { pushLinkToDataLayer } from 'helpers/pushLinkToDataLayer'
import { useLayoutContext } from '@components/layouts/MainLayout/LayoutContext'
import { decode } from 'he'
import { LinkResponse } from '@components/molecules/Link/Link'
import { Button } from '@components/molecules/Button/Button'
import s from './FlexibleGallery.module.scss'

export type FlexibleGalleryCardProps = {
    title?: string
    media?: ElgatoMediaProps
    colSpan?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10
    logo?: CloudinaryMedia[]
    inlineLogo?: boolean
    headline?: string
    subheadline?: string
    bodyCopy?: string
    overlay?: ContentOverlayProps
    cardBack?: FlexibleGalleryCardProps
    textVerticalAlignment?: 'top' | 'center' | 'bottom'
    textHorizontalAlignment?: 'left' | 'center' | 'right'
    textColor?: 'white' | 'black'
    textContainerWidth?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10
    textContainerWidthMobile?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10
    overlayIndicatorColor?: 'white' | 'black'
    className?: string
    style?: CSSProperties
    link?: LinkResponse
}

const colSpans = {
    1: 'col-span-10 md:col-span-1',
    2: 'col-span-10 md:col-span-2',
    3: 'col-span-10 md:col-span-3',
    4: 'col-span-10 md:col-span-4',
    5: 'col-span-10 md:col-span-5',
    6: 'col-span-10 md:col-span-6',
    7: 'col-span-10 md:col-span-7',
    8: 'col-span-10 md:col-span-8',
    9: 'col-span-10 md:col-span-9',
    10: 'col-span-10 md:col-span-10'
}

type UserActionIndicatorProps = {
    style?: CSSProperties
    active?: boolean
    color?: 'black' | 'white'
}

const UserActionIndicator: FC<UserActionIndicatorProps> = ({
    style,
    active,
    color = 'black'
}) => {
    const { t } = useTranslation()
    const [isHovered, setIsHovered] = useState(false)
    const { isMobile } = useMobile()
    const textRef = useRef<HTMLSpanElement>(null)
    const [textWidth, setTextWidth] = useState(0)

    const isActive = active || isHovered

    const backgroundColor = color === 'white' ? '#fff' : '#000'

    useEffect(() => {
        if (textRef.current) {
            // Add padding to text width for better spacing
            setTextWidth(textRef.current.offsetWidth + 32)
        }
    }, [])

    return (
        <div
            className="absolute top-16px right-16px md:top-32px md:right-32px z-10"
            style={style}
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
        >
            <div className="relative">
                <div
                    className="absolute right-0 rounded-lg flex items-center overflow-hidden"
                    style={{
                        width:
                            isActive && !isMobile ? `${textWidth}px` : '40px',
                        height: '40px',
                        transition: 'all 300ms ease',
                        pointerEvents: 'none',
                        backgroundColor
                    }}
                >
                    <span
                        ref={textRef}
                        className={cn('text-white whitespace-nowrap px-16px', {
                            'text-black': color === 'white'
                        })}
                    >
                        {t('Learn more')}
                    </span>
                </div>
                <div
                    className="rounded-lg relative z-10 flex items-center justify-center"
                    style={{
                        width: '40px',
                        height: '40px',
                        paddingRight: isActive && !isMobile ? 12 : 0,
                        transition: 'all 300ms ease',
                        backgroundColor
                    }}
                >
                    <Icon
                        name="add"
                        className={cn('w-24px h-24px flex-shrink-0', {
                            'text-black': color === 'white',
                            'text-white': color === 'black'
                        })}
                    />
                </div>
            </div>
        </div>
    )
}

const CardContent: FC<FlexibleGalleryCardProps> = ({
    media,
    logo,
    inlineLogo,
    headline,
    subheadline,
    bodyCopy,
    textVerticalAlignment,
    textHorizontalAlignment,
    textColor,
    textContainerWidth,
    textContainerWidthMobile,
    className,
    style,
    link
}) => {
    const { isMobile } = useMobile()
    const { src: logoSrc, alt: logoAlt } = useMedia({
        cloudinaryMedia: logo
    })

    const cardWidthStyle = textContainerWidth
        ? { width: `${(textContainerWidth / 12) * 100}%` }
        : { width: '100%' }

    const cardWidthStyleMobile = textContainerWidthMobile
        ? { width: `${(textContainerWidthMobile / 12) * 100}%` }
        : { width: '100%' }

    const cardWidth = isMobile ? cardWidthStyleMobile : cardWidthStyle
    return (
        <div
            className={cn(
                'relative w-full h-full rounded-xxxl overflow-hidden',
                {
                    'bg-primitive-gray-50': !media && !style?.backgroundColor
                },
                className
            )}
            style={style}
        >
            {media && <ElgatoMedia {...media} />}
            {(logo || headline || bodyCopy) && (
                <div
                    className={cn('absolute', s['flexible-gallery__item'], {
                        'top-0': textVerticalAlignment === 'top',
                        'top-1/2 transform -translate-y-1/2':
                            textVerticalAlignment === 'center',
                        'bottom-0': textVerticalAlignment === 'bottom',
                        'left-0 text-left': textHorizontalAlignment === 'left',
                        'left-1/2 transform -translate-x-1/2 text-center':
                            textHorizontalAlignment === 'center',
                        'right-0 text-right':
                            textHorizontalAlignment === 'right',
                        'text-white': textColor === 'white',
                        'text-black': textColor === 'black'
                    })}
                    style={cardWidth}
                >
                    {inlineLogo && logoSrc && headline ? (
                        <div className="flex items-center gap-8px mb-8px">
                            <img
                                src={logoSrc}
                                alt={logoAlt}
                                className="max-h-24px"
                            />
                            <h3
                                className={cn(
                                    'mb-16px',
                                    s['flexible-gallery__item-headline']
                                )}
                                dangerouslySetInnerHTML={{
                                    __html: decode(headline)
                                }}
                            />
                        </div>
                    ) : (
                        <>
                            {logoSrc && (
                                <img
                                    src={logoSrc}
                                    alt={logoAlt}
                                    className="max-h-40px md:max-h-64px mb-16px"
                                />
                            )}
                            {headline && (
                                <h3
                                    className={cn(
                                        'mb-16px',
                                        s['flexible-gallery__item-headline']
                                    )}
                                    dangerouslySetInnerHTML={{
                                        __html: decode(headline)
                                    }}
                                />
                            )}
                        </>
                    )}
                    {subheadline && (
                        <div
                            className={cn(
                                'font-univers65Bold mb-12px',
                                s['flexible-gallery__item-subheadline']
                            )}
                            dangerouslySetInnerHTML={{
                                __html: decode(subheadline)
                            }}
                        />
                    )}
                    {bodyCopy && (
                        <p
                            className={cn(
                                'text-16px',
                                s['flexible-gallery__item-body']
                            )}
                            dangerouslySetInnerHTML={{
                                __html: decode(bodyCopy)
                            }}
                        />
                    )}
                    {link && (
                        <Button
                            href={link.linkUrl}
                            aria-label={link.ariaLabel}
                            variant={link.style}
                            iconAlignment={link.iconAlignment}
                            newTab={link.newTab}
                            className="mt-16px md:mt-20px"
                            downloadableContent={link?.downloadableContent}
                        >
                            {link.linkTitle}
                            {link.icon && (
                                <Icon
                                    name={link.icon}
                                    className="w-24px h-24px"
                                />
                            )}
                        </Button>
                    )}
                </div>
            )}
        </div>
    )
}

export const FlexibleGalleryCard: FC<FlexibleGalleryCardProps> = ({
    media,
    colSpan = 4,
    logo,
    inlineLogo,
    headline,
    subheadline,
    bodyCopy,
    overlay,
    cardBack,
    textVerticalAlignment = 'center',
    textHorizontalAlignment = 'center',
    textContainerWidth,
    textContainerWidthMobile,
    textColor,
    overlayIndicatorColor,
    className,
    style,
    link
}) => {
    const [isFlipped, setIsFlipped] = useState(false)
    const [isHovered, setIsHovered] = useState(false)
    const [overlayOpen, setOverlayOpen] = useState(false)
    const cardRef = useRef<HTMLDivElement>(null)

    function handleCardClick() {
        if (cardBack) {
            setIsFlipped((prev) => !prev)
            pushLinkToDataLayer(cardRef, { headline: headline })
        } else if (overlay) {
            setOverlayOpen((prev) => !prev)
            // Added on both conditions so we don't track pause/play events
            pushLinkToDataLayer(cardRef, { headline: headline })
        }
    }

    const hasUserAction = cardBack || overlay

    // Prevent them from triggering card clicks in overlay content
    const handleOverlayClick = (e: ReactMouseEvent<HTMLDivElement>) => {
        // If the overlay is open, prevent any clicks inside it from propagating
        if (overlayOpen) {
            e.stopPropagation()
        }
    }

    // Separate handler for keyboard events
    const handleOverlayKeyDown = (e: ReactKeyboardEvent<HTMLSpanElement>) => {
        if (overlayOpen) {
            e.stopPropagation()
        }
    }

    return (
        <div
            ref={cardRef}
            className={cn(
                'relative',
                colSpans[colSpan],
                {
                    'cursor-pointer': cardBack || overlay
                },
                className
            )}
            style={{
                ...style,
                perspective: cardBack ? '1000px' : undefined
            }}
            onClick={handleCardClick}
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
            onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    handleCardClick()
                }
            }}
            role={hasUserAction ? 'button' : undefined}
            aria-label={hasUserAction ? 'Learn More' : undefined}
            tabIndex={0}
        >
            {cardBack ? (
                <div
                    className={cn(
                        'relative w-full transition-transform duration-500 transform select-none',
                        cardBack.className
                    )}
                    style={{
                        transformStyle: 'preserve-3d',
                        transform: `rotateY(${isFlipped ? '180deg' : '0deg'})`,
                        ...cardBack.style
                    }}
                >
                    <div
                        className="relative"
                        style={{ backfaceVisibility: 'hidden' }}
                    >
                        <CardContent {...cardBack} />
                        <UserActionIndicator
                            style={{ backfaceVisibility: 'hidden' }}
                            active={isHovered}
                            color={cardBack.overlayIndicatorColor}
                        />
                    </div>
                    <div
                        className="absolute top-0 w-full h-full"
                        style={{
                            backfaceVisibility: 'hidden',
                            transform: 'rotateY(180deg)'
                        }}
                    >
                        <CardContent {...cardBack} />
                        <UserActionIndicator
                            active={isHovered}
                            style={{ backfaceVisibility: 'hidden' }}
                            color={cardBack.overlayIndicatorColor}
                        />
                    </div>
                </div>
            ) : (
                <>
                    <CardContent
                        media={media}
                        logo={logo}
                        headline={headline}
                        subheadline={subheadline}
                        bodyCopy={bodyCopy}
                        textVerticalAlignment={textVerticalAlignment}
                        textHorizontalAlignment={textHorizontalAlignment}
                        textContainerWidth={textContainerWidth}
                        textColor={textColor}
                        textContainerWidthMobile={textContainerWidthMobile}
                        inlineLogo={inlineLogo}
                        link={link}
                    />
                    {overlay && (
                        <UserActionIndicator
                            active={isHovered}
                            style={{ backfaceVisibility: 'hidden' }}
                            color={overlayIndicatorColor}
                        />
                    )}
                    {overlay && (
                        <span
                            onClick={handleOverlayClick}
                            onKeyDown={handleOverlayKeyDown}
                            role="presentation"
                        >
                            <ContentOverlay
                                {...overlay}
                                open={overlayOpen}
                                onClose={(e) => {
                                    e.stopPropagation()
                                    setOverlayOpen(false)
                                }}
                            />
                        </span>
                    )}
                </>
            )}
        </div>
    )
}
