import { CSSProperties, FC } from 'react'
import cn from 'classnames'
// import s from './FlexibleGallery.module.scss'

import {
    FlexibleGalleryCard,
    FlexibleGalleryCardProps
} from './FlexibleGalleryCard'

export type FlexibleGalleryProps = {
    title?: string
    id?: string
    items: FlexibleGalleryCardProps[]
    size?: 'small' | 'large'
    className?: string
    style?: CSSProperties
    meta?: {
        contentType: 'gallery'
    }
}
type TFlexibleGallery = {
    content: FlexibleGalleryProps
}
const FlexibleGallery: FC<TFlexibleGallery> = ({ content }) => {
    const { id, items, className, style } = content
    return (
        <div className="flex justify-center px-16px md:px-64px gap-16px py-40px md:py-32">
            <div
                id={id}
                className={cn(
                    // [s['flexible-gallery']],
                    'grid grid-cols-12',
                    className
                )}
                style={style}
            >
                <div className="grid grid-cols-10 col-span-12 lg3:col-start-2 lg3:col-span-10 gap-16px">
                    {items.map((item, index) => (
                        <FlexibleGalleryCard key={index} {...item} />
                    ))}
                </div>
            </div>
        </div>
    )
}

export default FlexibleGallery
