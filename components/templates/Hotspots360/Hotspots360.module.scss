.hotspots-360 {
    height: 150vh;

    &__inner {
        position: sticky;
        top: 0;
        display: flex;
        flex-direction: column;
        padding-top: var(--spacing--sm);

        @screen md {
            height: 100vh !important;
            padding-top: var(--spacing--lg);
        }
    }

    &__img-wrapper {
        position: relative;
        cursor: url('data:image/svg+xml,<svg width="44" height="40" viewBox="0 0 44 40" fill="none" xmlns="http://www.w3.org/2000/svg"><rect x="1" y="1" width="42" height="38" rx="19" fill="white"/><path d="M15.8536 16.1464C16.0488 16.3417 16.0488 16.6583 15.8536 16.8536L12.7071 20L15.8536 23.1464C16.0488 23.3417 16.0488 23.6583 15.8536 23.8536C15.6583 24.0488 15.3417 24.0488 15.1464 23.8536L11.6464 20.3536C11.4512 20.1583 11.4512 19.8417 11.6464 19.6464L15.1464 16.1464C15.3417 15.9512 15.6583 15.9512 15.8536 16.1464Z" fill="%23151515"/><path d="M28.1464 23.8536C27.9512 23.6583 27.9512 23.3417 28.1464 23.1464L31.2929 20L28.1464 16.8536C27.9512 16.6583 27.9512 16.3417 28.1464 16.1464C28.3417 15.9512 28.6583 15.9512 28.8536 16.1464L32.3536 19.6464C32.5488 19.8417 32.5488 20.1583 32.3536 20.3536L28.8536 23.8536C28.6583 24.0488 28.3417 24.0488 28.1464 23.8536Z" fill="%23151515"/><rect x="1" y="1" width="42" height="38" rx="19" stroke="black" stroke-width="2"/></svg>'), grab;
        height: 80%;
        width: auto;

        @screen md {
            height: 100%;
            width: auto;
        }
    }

    &__drag-text {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        display: inline-flex;
        padding: 8px;
        gap: 10px;
        justify-content: center;
        align-items: center;
        border-radius: 4px;
        background: rgba(255, 255, 255, 0.40);
        backdrop-filter: blur(2px);
    }

    &__content {
        position: fixed;
        left: 0;
        right: 0;
        bottom: 0;
        padding-top: 32px;
        padding-bottom: 32px;
        background-color: var(--primitive-white);
        transform: translateY(200%);
        opacity: 1;
        will-change: transform, opacity;
        transition: transform 0.3s ease, opacity 0.3s ease;

        @screen md {
            background-color: var(--primitive-gray-10);
            border-radius: 16px;
            overflow: hidden;
            margin: 0 32px 32px 32px;
        }
    }

    &__text-outer {
        display: flex;
        flex: 1;
        flex-direction: column;
        text-align: center;
        gap: 24px;
        padding-bottom: 60px;
        @screen md {
            text-align: left;
            flex-direction: row;
            padding-bottom: 0;
        }
    }

    &__text-inner {
        position: absolute;
        top: 0;
        right: 0;
        left: 0;
        display: flex;
        align-items: center;
        flex-direction: column;
        gap: 24px;

        @screen md {
            align-items: flex-start;
        }

        @screen lg {
            flex-direction: row;
            top: 50%;
            transform: translateY(-50%);
        }
    }
}
