import React, { FC, useCallback, useMemo, useRef } from 'react'
import ProductImageSequence, {
    ProductImageSequenceProps
} from '@components/templates/ProductImageSequence/ProductImageSequence'
import { useCreateCallouts } from '@components/templates/ProductImageSequence/useCreateCallouts'
import { SpecialAnimationContent } from '@components/templates/SpecialAnimation/SpecialAnimation'
import { ProductCalloutProps } from '@components/organisms/ProductCallout/ProductCallout'
import s from './ProductImageSequenceKeyLightRemote.module.scss'
import {
    Container,
    ContainerSize
} from '@components/organisms/Container/Container'
import TextPanel from '@components/organisms/TextPanel/TextPanel'

const fallbackCallouts: ProductCalloutProps[] = [
    {
        headline: 'Adjust brightness automatically',
        headlineClasses: ['text-gamer-rush'],
        theme: 'transparent',
        textPosition: {
            top: '45%',
            left: '54%',
            maxWidth: '615px'
        },
        lineWidth: { '--line-width': '0' } as React.CSSProperties,
        frameStart: 40,
        frameEnd: 50,
        mobileFrameStart: 22,
        mobileFrameEnd: 32,
        variant: 'big-headline',
        mobileVariant: 'animated-hotspot'
    },
    {
        headline: 'Adjust brightness',
        headlineClasses: ['text-gamer-rush'],
        theme: 'transparent',
        textPosition: {
            top: '45%',
            left: '54%',
            maxWidth: '615px'
        },
        lineWidth: { '--line-width': '0' } as React.CSSProperties,
        frameStart: 59,
        frameEnd: 69,
        mobileFrameStart: 41,
        mobileFrameEnd: 51,
        variant: 'big-headline',
        mobileVariant: 'animated-hotspot'
    },
    {
        headline: 'Toggle between favorite light settings',
        headlineClasses: ['text-gamer-rush'],
        theme: 'transparent',
        textPosition: {
            top: '45%',
            left: '54%',
            maxWidth: '797px'
        },
        lineWidth: { '--line-width': '0' } as React.CSSProperties,
        frameStart: 77,
        frameEnd: 82,
        mobileFrameStart: 59,
        mobileFrameEnd: 69,
        variant: 'big-headline',
        mobileVariant: 'animated-hotspot'
    },
    {
        headline: 'Set color temperature',
        headlineClasses: ['text-gamer-rush'],
        theme: 'transparent',
        textPosition: {
            top: '45%',
            left: '54%',
            maxWidth: '615px'
        },
        lineWidth: { '--line-width': '0' } as React.CSSProperties,
        frameStart: 92,
        frameEnd: 100,
        mobileFrameStart: 77,
        mobileFrameEnd: 87,
        variant: 'big-headline',
        mobileVariant: 'animated-hotspot'
    },
    {
        headline: 'Select the light you want to control',
        headlineClasses: ['text-gamer-rush'],
        theme: 'transparent',
        textPosition: {
            top: '45%',
            left: '54%',
            maxWidth: '615px'
        },
        lineWidth: { '--line-width': '0' } as React.CSSProperties,
        frameStart: 111,
        frameEnd: 116,
        mobileFrameStart: 95,
        mobileFrameEnd: 100,
        variant: 'big-headline',
        mobileVariant: 'animated-hotspot'
    }
]

export const ProductImageSequenceRemote: FC<SpecialAnimationContent> = ({
    content
}) => {
    const { textPanels = [], children = [] } = content
    const firstTextPanel = textPanels.length ? textPanels[0] : null
    const productCallouts = useCreateCallouts(children, fallbackCallouts)
    const textPanelContainerRef = useRef<HTMLDivElement>(null)
    const onFrameUpdate = useCallback((frame: number) => {
        const textPanelElement = textPanelContainerRef.current
        if (!textPanelElement) {
            return
        }
        const calculateOpacity = (frame: number) => {
            if (frame >= 0 && frame <= 5) {
                return 1
            } else if (frame >= 5 && frame <= 7) {
                return 0.75
            } else if (frame >= 7 && frame <= 9) {
                return 0.5
            } else if (frame >= 9 && frame <= 11) {
                return 0.25
            } else {
                return 0
            }
        }
        textPanelElement.style.opacity = calculateOpacity(frame).toString()
    }, [])
    const textPanel = useMemo(() => {
        if (firstTextPanel) {
            return (
                <div
                    className={s['text-panel-container-remote']}
                    ref={textPanelContainerRef}
                >
                    <Container size={ContainerSize.MEDIUM}>
                        <TextPanel content={firstTextPanel} />
                    </Container>
                </div>
            )
        }
        return null
    }, [firstTextPanel])
    return (
        <ProductImageSequence
            content={
                {
                    sequenceName: 'light-remote-product-image-sequence',
                    path:
                        'https://res.cloudinary.com/elgato-pwa/image/upload/v1699373675/Products/10LAI9901/product-image-sequence/desktop/Remote_Scroll_Animation',
                    pathMobile:
                        'https://res.cloudinary.com/elgato-pwa/image/upload/v1699373872/Products/10LAI9901/product-image-sequence/mobile/Remote_Scroll_Animation_Mobile',
                    imageCount: 60,
                    imageWidth: 2880,
                    imageHeight: 1620,
                    mobileImageWidth: 1080,
                    mobileImageHeight: 1920,
                    mobileImageCount: 46,
                    height: '1000vh',
                    debug: false,
                    padLength: 5,
                    pauseFrames: [40, 41, 42, 43, 44],
                    mobilePauseFrames: [22, 23, 24, 25, 26],
                    pauseDuration: 15, // end of last - start of 1st + 10
                    productCallouts: productCallouts,
                    imageFormat: 'jpg',
                    onFrameUpdate: onFrameUpdate,
                    children: textPanel,
                    className: s['product-image-sequence-key-light-remote'],
                    lastFramePauseDuration: 15,
                    lastFrameMobilePauseDuration: 10
                } as ProductImageSequenceProps
            }
        />
    )
}

export default ProductImageSequenceRemote
