import { CompatibilityTextSpecsRowProps } from '@components/molecules/CompatibilityTextSpecsRow/CompatibilityTextSpecsRow'
import { CameraType } from '@components/organisms/CameraCheckDropdowns/CameraCheckDropdowns'
import { CompatibilitySpecsDropdownProps } from '@components/organisms/CompatibilitySpecsDropdown/CompatibilitySpecsDropdown'
import { CompatibilitySpecsDropdownList } from '@components/organisms/CompatibilitySpecsDropdownList/CompatibilitySpecsDropdownList'
import { TeleprompterCompatibilityLegend } from '@components/organisms/TeleprompterCompatibilityLegend/TeleprompterCompatibilityLegend'
import { SpecialPageContent } from '@components/templates/SpecialPage/SpecialPage'
import { useTranslation } from 'next-i18next'
import dynamic from 'next/dynamic'
import React, { FC, useEffect, useMemo, useState } from 'react'
import cn from 'classnames'
import s from './TeleprompterCameraCheck.module.scss'
import { DropdownPanelProps } from '@components/templates/DropdownPanel/DropdownPanel'
import { FeatureListProps } from '@components/templates/FeatureList/FeatureList'
import { PrimaryTextProps } from '@components/molecules/PrimaryText/PrimaryText'
import { CameraCheckHeader } from '@components/organisms/CameraCheckHeader/CameraCheckHeader'
import { useCameraCheck } from '@components/templates/CameraCheck/CameraCheckContext'
import { buildSpecifications } from '@components/organisms/CameraCheckDropdowns/cameraCheckDropdownsUtils'

const DropdownPanel = dynamic(
    () => import('@components/organisms/DropdownPanel/DropdownPanel'),
    { ssr: false }
)

export type SpecRowsLabelsType = {
    [key: string]: CompatibilityTextSpecsRowProps
}

export type CompatibilitySpecsDropdownListProps = {
    headline: string
    items: CompatibilitySpecsDropdownProps[]
}
type PrompterCustomOptions = {
    showFilterByResolution: boolean
}
export const TeleprompterCameraCheck: FC<SpecialPageContent> = ({
    content
}) => {
    const { skus = [], customOptions } = content
    const customOptionsParsed = customOptions as PrompterCustomOptions
    const children = (content?.children || []) as DropdownPanelProps[]
    const [initialData, setInitialData] = useState<
        DropdownPanelProps[] | undefined
    >(content?.children)
    const headline = content?.children?.[0]?.headline
    const { t } = useTranslation(['common'])
    const {
        updateCameras,
        updateBrands,
        searchFilter,
        resolutionFilter
    } = useCameraCheck()
    const initialDropdownData = useMemo(() => {
        return content?.children
    }, [])
    const specRowsLabels: SpecRowsLabelsType = {
        max_resolution: {
            label: t('cameraCheck|max_resolution.label')
        },
        clean_HDMI_human_readable: {
            label: t('cameraCheck|clean_HDMI_human_readable.label')
        },
        unlimited_runtime_human_readable: {
            label: t('cameraCheck|unlimited_runtime_human_readable.label'),
            tooltip: t('cameraCheck|unlimited_runtime_human_readable.tooltip')
        },
        connection_type: {
            label: t('cameraCheck|connection_type.label')
        },
        power: {
            label: t('cameraCheck|power.label')
        },
        not_fully_compatible: {
            label: t('cameraCheck|not_fully_compatible.label')
        },
        ring_backplate_compatible: {
            label: t('cameraCheck|ring_backplate_compatible.label')
        },
        universal_backplate_compatible: {
            label: t('cameraCheck|universal_backplate_compatible.label')
        },
        ring_adapter_size: {
            label: t('cameraCheck|ring_adapter_size.label')
        },
        verified_by: {
            label: t('cameraCheck|verified_by.label'),
            tooltip: t('cameraCheck|verified_by.tooltip'),
            badge: true
        },
        notes: {
            label: t('cameraCheck|notes.label')
        },
        setup_guide: {
            label: t('cameraCheck|setup_guide.label')
        },
        quality_sample: {
            label: t('cameraCheck|quality_sample.label')
        }
    }
    useEffect(() => {
        const fetchCameras = () => {
            const data: any[][] = []
            children.map((child) => {
                if (child.contentEntries && child.contentEntries[0].meta) {
                    if (
                        child.contentEntries[0].meta.contentType ===
                        'organismFeatureList'
                    ) {
                        const featuresList = child
                            .contentEntries[0] as FeatureListProps
                        if (featuresList.features) {
                            data.push(featuresList.features)
                        }
                    }
                }
            })
            updateCameras(data.flat())
            const brands = children.map((child) => child.headline)
            updateBrands(brands)
        }
        fetchCameras()
    }, [children, updateBrands, updateCameras])

    useEffect(() => {
        // Filter Via Search
        const filterCameras = (search: string, resolution: string) => {
            if (search !== '' && !resolution) {
                // Check if there is data in the dropdowns
                if (initialDropdownData) {
                    const filteredData = initialDropdownData
                        .map((child) => {
                            let updatedChild = { ...child } // Create a copy of the child object
                            if (
                                child.headline &&
                                child.headline
                                    .toLowerCase()
                                    .includes(search.toLowerCase())
                            ) {
                                // If the headline matches the search, include the entire child object
                                return updatedChild
                            }
                            let matchedFeaturesList = null
                            if (
                                child.contentEntries &&
                                child.contentEntries[0]?.meta &&
                                child.contentEntries[0]?.meta.contentType ===
                                    'organismFeatureList'
                            ) {
                                const featuresList = child
                                    .contentEntries[0] as FeatureListProps
                                if (featuresList.features) {
                                    const matchedFeatures = featuresList.features.filter(
                                        (feature) =>
                                            feature.model.toLowerCase() ===
                                            search
                                    )
                                    if (matchedFeatures.length > 0) {
                                        matchedFeaturesList = {
                                            ...featuresList,
                                            features: matchedFeatures
                                        }
                                    }
                                }
                                if (
                                    updatedChild &&
                                    updatedChild.contentEntries
                                ) {
                                    // Update the contentEntries array in the copy of the child object
                                    updatedChild = {
                                        ...updatedChild,
                                        contentEntries: [
                                            matchedFeaturesList as any
                                        ]
                                    }
                                }
                            }

                            return matchedFeaturesList
                                ? updatedChild
                                : undefined
                        })
                        .filter(Boolean) // Remove undefined entries

                    if (filteredData) {
                        setInitialData(filteredData as any) // Set the filtered data
                    }
                }
            }
            // When search is reset, update full content data
            else if (search === '' && !resolution) {
                setInitialData(initialDropdownData)
            }
        }
        filterCameras(searchFilter, resolutionFilter)
    }, [content, initialDropdownData, searchFilter, resolutionFilter])

    // Filter Via toggle
    useEffect(() => {
        const filterToggle = (resolution: string, search: string) => {
            if (resolution && search === '') {
                // Check if there is data in the dropdowns
                if (initialDropdownData) {
                    const filteredData = initialDropdownData
                        .map((child) => {
                            // Create a copy of the child object
                            let updatedChild = { ...child }
                            if (
                                child.contentEntries &&
                                child.contentEntries.contentEntries?.features &&
                                child.contentEntries[0]?.features?.max_resolution.includes(
                                    resolution
                                )
                            ) {
                                // If the resolution matches the toggle, include the entire child object
                                return updatedChild
                            }
                            let matchedFeaturesList = null
                            if (
                                child.contentEntries &&
                                child.contentEntries[0]?.meta &&
                                child.contentEntries[0]?.meta.contentType ===
                                    'organismFeatureList'
                            ) {
                                const featuresList = child
                                    .contentEntries[0] as FeatureListProps
                                if (featuresList.features) {
                                    const matchedFeatures = featuresList.features.filter(
                                        (feature) =>
                                            feature.max_resolution ===
                                            resolution
                                    )
                                    if (matchedFeatures.length > 0) {
                                        matchedFeaturesList = {
                                            ...featuresList,
                                            features: matchedFeatures
                                        }
                                    }
                                }
                                if (
                                    updatedChild &&
                                    updatedChild.contentEntries
                                ) {
                                    // Update the contentEntries array in the copy of the child object
                                    updatedChild = {
                                        ...updatedChild,
                                        contentEntries: [
                                            matchedFeaturesList as any
                                        ]
                                    }
                                }
                            }

                            return matchedFeaturesList
                                ? updatedChild
                                : undefined
                        })
                        .filter(Boolean) // Remove undefined entries

                    if (filteredData) {
                        setInitialData(filteredData as any) // Set the filtered data
                    }
                }
            }
            // When toggle is reset, update full content data
            else if (search !== '' && resolution) {
                setInitialData(initialDropdownData)
            }
        }
        filterToggle(resolutionFilter, searchFilter)
    }, [initialDropdownData, resolutionFilter, searchFilter])
    const DropdownPanels: React.FC = () => {
        if (!initialData?.length) return null
        return (
            <div className={resolutionFilter ? s['dropdown-wrapper'] : ''}>
                {initialData.map((child, index) => {
                    const childHeadline =
                        child?.headline || `Child ${index + 1}`
                    const childSpecifications =
                        ((child?.contentEntries as FeatureListProps[])?.[0]
                            ?.features as CameraType[]) || []
                    const items = [] as CompatibilitySpecsDropdownProps[]

                    childSpecifications?.map((specification: CameraType) => {
                        items.push(
                            buildSpecifications(specification, specRowsLabels)
                        )
                    })
                    return (
                        <DropdownPanel
                            key={index}
                            headline={childHeadline}
                            bodyCopy={
                                (child?.children as PrimaryTextProps)?.bodyCopy
                            }
                            logo={child?.logo}
                            additionalHeaderContent={
                                <TeleprompterCompatibilityLegend />
                            }
                            variant="camera-check"
                        >
                            {!!child &&
                                child?.contentEntries?.map(
                                    (contentEntry, index) => {
                                        const items = [] as CompatibilitySpecsDropdownProps[]
                                        const featureList = contentEntry as FeatureListProps
                                        featureList?.features?.map(
                                            (specification: CameraType) => {
                                                items.push(
                                                    buildSpecifications(
                                                        specification,
                                                        specRowsLabels
                                                    )
                                                )
                                            }
                                        )
                                        return (
                                            <div
                                                key={`compatibility-specs-dropdowns-group-${headline}-${index}`}
                                                className={cn(
                                                    s['teleprompter-dropdown']
                                                )}
                                            >
                                                {featureList?.headline && (
                                                    <h4
                                                        className={cn(
                                                            s[
                                                                'teleprompter-dropdown__headline'
                                                            ]
                                                        )}
                                                    >
                                                        {featureList.headline}
                                                    </h4>
                                                )}
                                                <CompatibilitySpecsDropdownList
                                                    headline={
                                                        featureList?.headline
                                                    }
                                                    items={items}
                                                    key={index}
                                                />
                                            </div>
                                        )
                                    }
                                )}
                        </DropdownPanel>
                    )
                })}
            </div>
        )
    }
    return (
        <div>
            <CameraCheckHeader
                skus={skus}
                showFilterByResolution={
                    customOptionsParsed
                        ? customOptionsParsed.showFilterByResolution
                        : true
                }
            />
            <DropdownPanels />
        </div>
    )
}
