import s from './ProductQuizView4k.module.scss'
import { FC } from 'react'
import { ProductQuizView } from '@components/templates/ProductQuiz/ProductQuiz'
import { IconButton } from '../ProductQuizSD/IconButton'
import cn from 'classnames'
import { useTranslation } from 'next-i18next'
import ElgatoMedia, {
    ElgatoMediaProps
} from '@components/common/ElgatoMedia/ElgatoMedia'

type ProductQuizViewSDProps = ProductQuizView & {
    currentSelectedViewIndex: number | null
    isNextDisabled: boolean
    isBackDisabled: boolean
    onSelectViewIndex: (index: number) => void
    onNextClick: () => void
    onBackClick: () => void
    onRestartClick: () => void
}

const AnswerOption = ({
    onClick,
    selectorText,
    isSelected,
    media
}: {
    onClick: () => void
    selectorText: string | undefined
    isSelected: boolean
    media?: ElgatoMediaProps
}) => {
    if (media) {
        return (
            <AnswerOptionMedia
                onClick={onClick}
                selectorText={selectorText}
                isSelected={isSelected}
                media={media}
            />
        )
    }
    return (
        <AnswerOptionLabel
            onClick={onClick}
            selectorText={selectorText}
            isSelected={isSelected}
        />
    )
}

const AnswerOptionLabel = ({
    onClick,
    selectorText,
    isSelected
}: {
    onClick: () => void
    selectorText: string | undefined
    isSelected: boolean
}) => {
    return (
        <button
            onClick={onClick}
            className={cn(s['answer-option-label'], 'button-text', {
                [s['answer-option-label--selected']]: isSelected
            })}
        >
            {selectorText}
        </button>
    )
}

const AnswerOptionMedia = ({
    onClick,
    selectorText,
    isSelected,
    media
}: {
    onClick: () => void
    selectorText: string | undefined
    isSelected: boolean
    media?: ElgatoMediaProps
}) => {
    return (
        <button
            onClick={onClick}
            className={cn(s['answer-option-media'], {
                [s['answer-option-media--selected']]: isSelected
            })}
        >
            <div className={s['answer-option-media__media-container']}>
                <ElgatoMedia
                    {...media}
                    objectFit="cover"
                    sizing="constrain-ratio"
                    className={s['answer-option-media__media']}
                />
            </div>
            <div
                className={cn(s['answer-option-media__button'], 'button-text')}
            >
                {selectorText}
            </div>
        </button>
    )
}

const ProductQuizView4k: FC<ProductQuizViewSDProps> = ({
    header,
    subtitle,
    productQuizView,
    currentSelectedViewIndex,
    isBackDisabled,
    isNextDisabled,
    onSelectViewIndex,
    onNextClick,
    onBackClick,
    onRestartClick
}) => {
    const { t } = useTranslation(['common'])
    return (
        <div className="flex flex-col flex-1 gap-40px">
            <div className="flex flex-col gap-40px md:gap-64px m-auto items-center">
                <div className="flex flex-col gap-16px text-center">
                    {!!header && <h3>{header}</h3>}
                    {!!subtitle && <p>{subtitle}</p>}
                </div>
                {!!productQuizView?.length && (
                    <div className="flex flex-wrap justify-center gap-16px">
                        {productQuizView.map((view, index) => (
                            <AnswerOption
                                isSelected={currentSelectedViewIndex === index}
                                key={index}
                                onClick={() => onSelectViewIndex(index)}
                                selectorText={view.selectorText}
                                media={view.media}
                            />
                        ))}
                    </div>
                )}
            </div>
            <div className="flex gap-8px md:gap-16px justify-between">
                <div className="flex gap-8px md:gap-16px">
                    <IconButton
                        iconName="chevronLeft"
                        text={t('Back')}
                        iconAlignment="left"
                        isDisabled={isBackDisabled}
                        onClick={onBackClick}
                    />
                    <IconButton
                        className={cn(s['refresh-icon'], 'scale-x-[-1]')}
                        iconName="refreshIcon"
                        onClick={onRestartClick}
                    />
                </div>

                <IconButton
                    iconName="chevronRight"
                    text={t('Next')}
                    iconAlignment="right"
                    color="blue"
                    isDisabled={isNextDisabled}
                    onClick={onNextClick}
                />
            </div>
        </div>
    )
}

export default ProductQuizView4k
