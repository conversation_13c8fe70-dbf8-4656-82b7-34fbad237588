.product-quiz-sd {
    width: 100%;
    @screen md-max {
        display: flex;
        flex-direction: column;
        padding-top: 16px;
        padding-bottom: 40px;
        padding-left: 16px;
        padding-right: 16px;
        min-height: 100vh;
        background-color: #F6F6F6;
    }
    @screen md {
        min-height: 100vh;
        background-color: #F6F6F6;
        display: grid;
        grid-template-columns: repeat(12, minmax(0, 1fr));
        gap: 16px;
        padding-left: 68px;
        padding-right: 68px;
        padding-top: 120px;
        padding-bottom: 120px;
    }
    &__quiz-container {
        display: flex;
        flex-direction: column;
        gap: 40px;

        @screen md-max {
            flex: 1;
            min-height: 100vh;
        }


        @screen md {
            padding: 40px;
            grid-column: 2 / 12;
            background: var(--white);
            border-radius: 40px;
        }
    }
}

.progress-bar {
    height: 4px;
    background-color: var(--primitive-gray-20);
    width: 100%;
    border-radius: 4px;

    &__progress-indicator {
        border-radius: 4px;
        height: 100%;
        background-color: var(--content-blue);
        transition: width 0.3s ease;
    }
}


.icon-button {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    border: 1px solid var(--primitive-gray-130);
    border-radius: 6px;

    gap: 4px;

    padding: 8px;
    height: 36px;

    @screen md {
        padding: 12px;
        height: 44px;
    }

    &:hover {
        color: var(--content-blue);
        border-color: var(--content-blue);
    }

    &--text {
        padding: 8px 16px;
        @screen md {
            padding: 12px 24px;
        }
    }

    &:disabled {
        color: var(--mid-grey-1);
        border-color: var(--mid-grey-1);
    }

    &--blue {
        color: var(--white);
        border-color: var(--content-blue);
        background-color: var(--content-blue);
    }

    &--blue:hover {
        color: var(--white);
        border-color: var(--persian-blue-1);
        background-color: var(--persian-blue-1);
    }

    &--blue:disabled {
        color: var(--mid-grey-1);
        border-color: var(--light-grey-1);
        background-color: var(--light-grey-1);
    }

    &__icon {
        width: 20px;
        height: 20px;
    }

    &__text {
        font-family: 'UniversLTStd';
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
        letter-spacing: 0.25px;
        font-size: 14px;
        margin-top: 3px;
        @screen md {
            font-size: 16px;
        }
        @screen lg-max {
            display: none;
        }
    }

    &--icon-right {
        flex-direction: row-reverse;
    }
    &--refresh-icon {
        transform: scaleX(-1);
    }
}

