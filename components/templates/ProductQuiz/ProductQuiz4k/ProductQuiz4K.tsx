import s from './ProductQuiz4K.module.scss'
import { FC, useEffect, useState } from 'react'
import {
    ProductQuizProps,
    ProductQuizView
} from '@components/templates/ProductQuiz/ProductQuiz'
import ProductQuizView4K from './ProductQuizView4K'
import ProductQuizViewSDResult from '@components/templates/ProductQuiz/ProductQuizSD/ProductQuizViewSDResult'
import { ConfigurableProduct, SimpleProduct } from '@pylot-data/fwrdschema'
import getProductsBySkus from '@pylot-data/api/operations/get-products-by-skus'
import { useRouter } from 'next/router'
import { useCartItemHelper } from '../../../../helpers/cartItemHelper'
import { useAddToCart } from '@pylot-data/hooks/cart/use-add-to-cart'
import { IconButton } from '@components/templates/ProductQuiz/ProductQuizSD/IconButton'
import dynamic from 'next/dynamic'

const MediaWithContent = dynamic(
    () => import('@components/templates/MediaWithContent/MediaWithContent'),
    {
        ssr: false
    }
)

const getAllSkus = (node: any) => {
    const skusSet = new Set<string>()

    const recurse = (obj: any) => {
        if ('products' in obj) {
            for (const product of obj.products) {
                if (product.sku) {
                    skusSet.add(product.sku)
                }
            }
        }

        if ('additionalProducts' in obj) {
            for (const additionalProduct of obj.additionalProducts) {
                if (additionalProduct.sku) {
                    skusSet.add(additionalProduct.sku)
                }
            }
        }

        for (const key in obj) {
            const value = obj[key]
            if (value && typeof value === 'object') {
                recurse(value)
            }
        }
    }

    recurse(node)
    return [...skusSet]
}

const ProgressBar = ({ progress }: { progress: number }) => {
    return (
        <div className={s['progress-bar']}>
            <div
                className={s['progress-bar__progress-indicator']}
                style={{ width: `${progress}%` }}
            />
        </div>
    )
}

const ProductQuiz4K: FC<ProductQuizProps> = ({ content }) => {
    const [isQuizPreview, setIsQuizPreview] = useState(true)
    const [selectedViewIndexes, setSelectedViewIndexes] = useState([0]) // TODO // [0, 1, 2, 1]
    const [currentSelectedViewIndex, setCurrentSelectedViewIndex] = useState<
        number | null
    >(null)
    const [selectedViewOptionIndex, setSelectedViewOptionIndex] = useState(0)
    const [selectedProductIndex, setSelectedProductIndex] = useState(0)
    const [
        selectedExtraProductsIndexes,
        setSelectedExtraProductsIndexes
    ] = useState<number[]>([])

    const getCurrentView = () => {
        let view = content.productQuizView?.[0]
        for (let i = 1; i < selectedViewIndexes.length; i++) {
            const index = selectedViewIndexes[i]
            view = view?.productQuizView?.[index]
        }
        return view
    }

    const getMaxDepth = (view: ProductQuizView | undefined): number => {
        if (!view || !view.productQuizView?.length) return 1
        return 1 + Math.max(...view.productQuizView.map(getMaxDepth))
    }

    const currentView = getCurrentView()
    const currentDepth = selectedViewIndexes.length
    const totalDepth = currentDepth - 1 + getMaxDepth(currentView)
    const progressPercent = Math.round((currentDepth / totalDepth) * 100)
    const isNextDisabled = currentSelectedViewIndex === null
    const isBackDisabled = selectedViewIndexes.length <= 1

    const hasCurrentViewExtraOptions = !!currentView?.extraOptions?.length

    const isNextOptionDisabled =
        !!currentView?.extraOptions &&
        selectedViewOptionIndex === currentView.extraOptions.length - 1

    const isPrevOptionDisabled = selectedViewOptionIndex === 0

    const onSelectViewIndex = (index: number) => {
        setCurrentSelectedViewIndex(index)
    }

    const onNextClick = () => {
        if (currentSelectedViewIndex === null) return
        setSelectedViewIndexes((prev) => [...prev, currentSelectedViewIndex])
        setCurrentSelectedViewIndex(null)
        setSelectedExtraProductsIndexes([])
    }

    const onBackClick = () => {
        if (isBackDisabled) return
        const lastSelectedIndex =
            selectedViewIndexes[selectedViewIndexes.length - 1]
        setSelectedViewIndexes((prev) => prev.slice(0, -1))
        setCurrentSelectedViewIndex(lastSelectedIndex)
        setSelectedViewOptionIndex(0)
        setSelectedProductIndex(0)
        setSelectedExtraProductsIndexes([])
    }

    const onRestartClick = () => {
        setSelectedViewIndexes([0])
        setCurrentSelectedViewIndex(null)
        setSelectedViewOptionIndex(0)
        setSelectedProductIndex(0)
        setSelectedExtraProductsIndexes([])
    }

    const onCloseClick = () => {
        setIsQuizPreview(true)
        setSelectedViewIndexes([0])
        setCurrentSelectedViewIndex(null)
        setSelectedViewOptionIndex(0)
        setSelectedProductIndex(0)
        setSelectedExtraProductsIndexes([])
    }

    const onQuestionClick = (index: number) => {
        setSelectedViewIndexes((prev) => [...prev, index])
        setCurrentSelectedViewIndex(null)
        setSelectedViewOptionIndex(0)
        setSelectedProductIndex(0)
        setSelectedExtraProductsIndexes([])
    }

    const onPrevOptionClick = () => {
        if (isPrevOptionDisabled) return
        setSelectedViewOptionIndex((prev) => prev - 1)
        setSelectedProductIndex(0)
        setSelectedExtraProductsIndexes([])
    }

    const onNextOptionClick = () => {
        if (isNextOptionDisabled) return
        setSelectedViewOptionIndex((prev) => prev + 1)
        setSelectedProductIndex(0)
        setSelectedExtraProductsIndexes([])
    }

    const onProductColorClick = (index: number) => {
        setSelectedProductIndex(index)
    }

    const onExtraProductClick = (index: number) => {
        setSelectedExtraProductsIndexes((prev) => {
            if (prev.includes(index)) {
                return prev.filter((i) => i !== index)
            } else {
                return [...prev, index]
            }
        })
    }

    const { locale } = useRouter()
    const [fetchedProducts, setFetchedProducts] = useState<
        Record<string, ConfigurableProduct | SimpleProduct>
    >({})

    useEffect(() => {
        const fetchAndSetAllProducts = async () => {
            const allSkus = getAllSkus(content)
            // console.log('allSkus', allSkus)

            const allProducts: (
                | ConfigurableProduct
                | SimpleProduct
            )[] = await getProductsBySkus(allSkus, locale ?? '')
            // console.log('allProducts', allProducts)

            const fetchedProductsTemp: Record<
                string,
                ConfigurableProduct | SimpleProduct
            > = {}

            allProducts.forEach((product) => {
                if (product.sku) {
                    fetchedProductsTemp[product.sku] = product
                }
            })

            setFetchedProducts(fetchedProductsTemp)
        }
        fetchAndSetAllProducts()
    }, [content, locale])

    const selectedCurrentView = currentView?.extraOptions
        ? currentView.extraOptions[selectedViewOptionIndex]
        : currentView

    const selectedCurrentSku =
        selectedCurrentView?.products?.[selectedProductIndex]?.sku

    // console.log('selectedCurrentSku', selectedCurrentSku)

    const selectedCurrentProduct = selectedCurrentSku
        ? fetchedProducts[selectedCurrentSku]
        : undefined

    const selectedCurrentResult =
        selectedCurrentView?.products?.[selectedProductIndex]

    const mediaWithContentPreview = content.mediaWithContents?.[0]

    // console.log('selectedCurrentView', selectedCurrentView)

    const extraProducts = (selectedCurrentView?.additionalProducts ?? [])
        .map((additionalProduct) =>
            additionalProduct.sku && fetchedProducts[additionalProduct.sku]
                ? fetchedProducts[additionalProduct.sku]
                : null
        )
        .filter((additionalProduct) => additionalProduct !== null) as (
        | ConfigurableProduct
        | SimpleProduct
    )[]

    const extraOptionsCount = currentView?.extraOptions?.length ?? 0
    const extraOptionsCurrentSelected = selectedViewOptionIndex + 1

    const selectedExtraProducts = extraProducts.filter((product, index) =>
        selectedExtraProductsIndexes.includes(index)
    )

    const updateCartItemsLocalStorage = useCartItemHelper()
    const { isAdding, addToCart } = useAddToCart()

    const onAddToCartClick = async () => {
        if (!selectedCurrentProduct || !selectedCurrentProduct.sku) return
        const prod = selectedCurrentProduct as any
        const extraProd = selectedExtraProducts as any[]
        const cartItemsFromSelectedExtraProducts = selectedExtraProducts.map(
            (extraProduct) => {
                return {
                    sku: extraProduct.sku as string,
                    uid: extraProduct.uid,
                    quantity: 1
                }
            }
        )

        updateCartItemsLocalStorage(prod, '')
        selectedExtraProducts?.forEach((product) => {
            updateCartItemsLocalStorage(product as any, '')
        })

        await addToCart(
            [
                {
                    sku: prod.sku,
                    uid: prod.uid,
                    quantity: 1
                },
                ...cartItemsFromSelectedExtraProducts
            ],
            [prod, ...extraProd]
        )
    }

    if (isQuizPreview && mediaWithContentPreview) {
        const textPanel = {
            ...mediaWithContentPreview.textPanel,
            onLinkClick: () => setIsQuizPreview(false)
        }
        const mediaWithContentProps = { ...mediaWithContentPreview, textPanel }
        return <MediaWithContent {...mediaWithContentProps} />
    }

    return (
        <div className={s['product-quiz-sd']}>
            <div className={s['product-quiz-sd__quiz-container']}>
                <div className="flex gap-16px md:gap-40px items-center">
                    <ProgressBar progress={progressPercent} />
                    <IconButton onClick={onCloseClick} iconName="close" />
                </div>
                {/* TODO only when variant is sd-question, TODO in Contentful first */}
                {currentView &&
                    (!currentView.variant ||
                        currentView.variant === 'sd-question') && (
                        <ProductQuizView4K
                            {...currentView}
                            onSelectViewIndex={onSelectViewIndex}
                            currentSelectedViewIndex={currentSelectedViewIndex}
                            onNextClick={onNextClick}
                            onBackClick={onBackClick}
                            onRestartClick={onRestartClick}
                            isNextDisabled={isNextDisabled}
                            isBackDisabled={isBackDisabled}
                        />
                    )}
                {currentView?.variant === 'sd-result' && (
                    <ProductQuizViewSDResult
                        {...selectedCurrentView}
                        productQuizView={currentView.productQuizView}
                        products={selectedCurrentView?.products}
                        result={selectedCurrentResult}
                        onQuestionClick={onQuestionClick}
                        onBackClick={onBackClick}
                        onRestartClick={onRestartClick}
                        isBackDisabled={isBackDisabled}
                        product={selectedCurrentProduct}
                        onPrevOptionClick={onPrevOptionClick}
                        onNextOptionClick={onNextOptionClick}
                        isPrevOptionDisabled={isPrevOptionDisabled}
                        isNextOptionDisabled={isNextOptionDisabled}
                        onProductColorClick={onProductColorClick}
                        selectedProductIndex={selectedProductIndex}
                        hasExtraOptions={hasCurrentViewExtraOptions}
                        extraOptionsCount={extraOptionsCount}
                        extraOptionsCurrentSelected={
                            extraOptionsCurrentSelected
                        }
                        extraProducts={extraProducts}
                        selectedExtraProductsIndexes={
                            selectedExtraProductsIndexes
                        }
                        onExtraProductClick={onExtraProductClick}
                        onAddToCartClick={onAddToCartClick}
                    />
                )}
            </div>
        </div>
    )
}

export default ProductQuiz4K
