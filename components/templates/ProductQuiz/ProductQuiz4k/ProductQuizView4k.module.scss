.answer-option-label {
    box-sizing: border-box;
    height: 52px;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    border: 1.5px solid #B3B3B3;

    padding: 12px;
    gap: 8px;
    border-radius: 6px;

    @screen md {
        padding: 16px;
        gap: 16px;
        border-radius: 8px;
    }

    &:hover {
        color: var(--black);
        border-color: var(--black);
    }

    &--selected,
    &--selected:hover {
        color: var(--content-blue);
        border-color: var(--content-blue);
    }
}

.answer-option-media {
    display: inline-flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 16px;

    &__media-container {
        display: flex;
        padding: 6px;
        align-items: center;
        gap: 10px;
        border-radius: 9999px;
        border: 2px solid transparent;

        .answer-option-media__media {
            width: 104px;
            height: 104px;
        }
    }

    &__button {
        box-sizing: border-box;
        height: 52px;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        border: 1.5px solid #B3B3B3;

        padding: 12px;
        gap: 8px;
        border-radius: 6px;
        width: 160px;

        @screen md {
            padding: 16px;
            gap: 16px;
            border-radius: 8px;
            width: 200px;
        }
    }


    &:hover {
        .answer-option-media__button {
            color: var(--black);
            border-color: var(--black);
        }
    }

    &--selected,
    &--selected:hover {
        .answer-option-media__button {
            color: var(--content-blue);
            border-color: var(--content-blue);
        }

        .answer-option-media__media-container {
            border-color: var(--content-blue);
        }
    }
}

.refresh-icon {
    transform: scaleX(-1);
}
