import { ProductQuizView } from '@components/templates/ProductQuiz/ProductQuiz'
import cn from 'classnames'
import { useTranslation } from 'next-i18next'
import { FC } from 'react'
import { IconButton } from '../ProductQuizSD/IconButton'
import s from './ProductQuizView4kResult.module.scss'

type ProductQuizViewSDResultProps = ProductQuizView & {
    isBackDisabled: boolean
    onBackClick: () => void
    onRestartClick: () => void
}

const ProductQuizView4kResult: FC<ProductQuizViewSDResultProps> = ({
    calloutTitle,
    header,
    subtitle,
    isBackDisabled,
    onBackClick,
    onRestartClick
}) => {
    const { t } = useTranslation(['common'])
    return (
        <div className="flex flex-col flex-1 gap-40px">
            <div className="flex flex-col gap-40px md:gap-64px m-auto items-center">
                <div className="flex flex-col gap-16px md:gap-24px text-center max-w-textContainer">
                    <div className="flex flex-col gap-8px md:gap-16px">
                        {!!calloutTitle && <h4>{calloutTitle}</h4>}
                        {!!header && <h1>{header}</h1>}
                    </div>
                    {!!subtitle && <p>{subtitle}</p>}
                </div>
            </div>
            <div className="flex gap-8px md:gap-16px justify-between">
                <div className="flex gap-8px md:gap-16px">
                    <IconButton
                        iconName="chevronLeft"
                        text={t('Back')}
                        iconAlignment="left"
                        isDisabled={isBackDisabled}
                        onClick={onBackClick}
                    />
                    <IconButton
                        className={cn(s['refresh-icon'], 'scale-x-[-1]')}
                        iconName="refreshIcon"
                        onClick={onRestartClick}
                    />
                </div>
            </div>
        </div>
    )
}

export default ProductQuizView4kResult
