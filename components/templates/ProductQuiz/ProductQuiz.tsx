import { FC } from 'react'
import dynamic from 'next/dynamic'
import { MediaWithContentProps } from '@components/templates/MediaWithContent/MediaWithContent'
import { ElgatoMediaProps } from '@components/common/ElgatoMedia/ElgatoMedia'
import { BannerProps } from '@components/molecules/Banner/Banner'
import { LinkResponse } from '@components/molecules/Link/Link'

const ProductQuizOld = dynamic(
    () => import('@components/organisms/ProductQuiz/ProductQuiz')
)

const ProductQuizSD = dynamic(() => import('./ProductQuizSD/ProductQuizSD'))

const ProductQuiz4K = dynamic(() => import('./ProductQuiz4k/ProductQuiz4k'))

export type ProductQuizView = {
    title?: string
    calloutTitle?: string
    header?: string
    selectorText?: string
    subtitle?: string
    media?: ElgatoMediaProps
    buttonWithMedia?: any[]
    background?: string
    id?: string
    productQuizView?: ProductQuizView[]
    productsLabel?: string
    products?: ProductQuizManualResult[]
    additionalProductsLabel?: string
    additionalProducts?: ProductQuizManualResult[]
    extraOptions?: ProductQuizView[]
    variant?: 'sd-question' | 'sd-result' | '4k-result'
}

export type ProductQuizManualResult = {
    title?: string
    id?: string
    result?: string
    productTitle?: string
    productTitleIcon?: string
    productSubheadline?: string
    productDescription?: string
    productColor?: 'white' | 'black'
    sku?: string
    medias?: ElgatoMediaProps[]
    banners?: BannerProps[]
    learnMoreLink?: LinkResponse
}

type ProductQuizContent = {
    title?: string
    productQuizView?: ProductQuizView[]
    mediaWithContents?: MediaWithContentProps[]
    variant?: string
    id?: string
    productResults?: any[]
}

export type ProductQuizProps = {
    content: ProductQuizContent
}

const ProductQuiz: FC<ProductQuizProps> = ({ content }) => {
    if (
        content.variant === '4kPro' ||
        content.variant === '4kX' ||
        content.variant === 'capture'
    ) {
        return <ProductQuizOld content={content} />
    }
    if (content.variant === 'sd') {
        return <ProductQuizSD content={content} />
    }
    if (content.variant === '4k') {
        return <ProductQuiz4K content={content} />
    }
    return null
}

export default ProductQuiz
