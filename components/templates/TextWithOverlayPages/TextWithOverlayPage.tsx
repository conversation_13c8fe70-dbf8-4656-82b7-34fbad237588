import { Icon } from '@components/atoms/Icon/Icon'
import { CloudinaryMedia } from '@components/common/CloudinaryMedia/Cloudinary'
import ElgatoImage from '@components/common/ElgatoImage'
import ElgatoVideo from '@components/common/ElgatoVideo/ElgatoVideo'
import { CardProps } from '@components/templates/CardList/CardList'
import { useMedia } from '@lib/hooks/useMedia'
import { useMobile } from '@pylot-data/hooks/use-mobile'
import { useTranslation } from 'next-i18next'
import { FC, useEffect, useRef, useState } from 'react'

interface Props {
    title?: string
    cloudinaryMedia?: CloudinaryMedia[]
    playing?: boolean
    setPlaying?: (playing: boolean) => void
}

const AudioBlock: FC<Props> = ({
    title,
    cloudinaryMedia,
    playing,
    setPlaying
}) => {
    const { t } = useTranslation('common')
    const ref = useRef<HTMLAudioElement>(null)

    const audioSrc = cloudinaryMedia?.[0]?.secure_url
    const isAudio = cloudinaryMedia?.[0]?.resource_type === 'video'

    function play() {
        if (ref.current) {
            ref.current.play()
        }
    }

    function stop() {
        if (ref.current) {
            ref.current.pause()
            ref.current.currentTime = 0
        }
    }

    useEffect(() => {
        if (playing) {
            play()
        } else {
            stop()
        }
    }, [playing])

    if (!isAudio) {
        return null
    }

    return (
        <div
            className="bg-primitive-gray-10 flex items-center gap-16px px-8 py-6 rounded-lg cursor-pointer"
            onClick={() => setPlaying?.(!playing)}
            onKeyPress={() => setPlaying?.(!playing)}
            role="button"
            tabIndex={0}
        >
            {playing ? <Icon name="pause" /> : <Icon name="play" />}
            <div className="text-small-copy">{title}</div>
            <audio
                controls={false}
                src={audioSrc}
                ref={ref}
                preload="auto"
                autoPlay={false}
            >
                <track kind="captions" />
                {t('Your browser does not support the audio element.')}
            </audio>
        </div>
    )
}

export const TextWithOverlayPage: FC<CardProps> = ({
    textPanel,
    cloudinaryMedia,
    cloudinaryMobileMedia,
    children: audioBlocks
}) => {
    const { isMobile } = useMobile()
    const [playingElement, setPlayingElement] = useState<number | null>(null)
    const { src, alt, type } = useMedia({
        cloudinaryMedia: cloudinaryMedia,
        cloudinaryMobileMedia: cloudinaryMobileMedia
    })

    let selectedSource = src
    let selectedAlt = alt

    if (playingElement !== null) {
        const audioBlock = audioBlocks?.[playingElement]

        const relatedImage = audioBlock?.cloudinaryMedia?.find(
            (cm: CloudinaryMedia) => {
                return cm.resource_type === 'image'
            }
        )

        selectedSource = relatedImage?.secure_url
            ? relatedImage?.secure_url
            : src
        selectedAlt = relatedImage?.alt ? relatedImage?.alt : alt
    }

    return (
        <div
            className="flex flex-col lg:flex-row lg:gap-32 px-16px lg:px-64px py-40px h-screen lg:justify-between"
            style={{
                width: isMobile ? '100%' : '90%'
            }}
        >
            <div className="pt-36 lg:pt-64 lg:w-1/3">
                <h2 className="text-h2-md-max lg:text-h2 pb-32px">
                    {textPanel?.headline}
                </h2>
                <div className="text-body-copy-md-max lg:text-body-copy text-greyscale-black-65">
                    {textPanel?.bodyCopy}
                </div>
                <div className="flex flex-col gap-8px py-40px lg:pt-32">
                    {audioBlocks?.map((child, index) => {
                        const title = child?.textPanel?.headline
                        const cloudinaryMedia = child?.cloudinaryMedia
                        return (
                            <AudioBlock
                                key={index}
                                title={title}
                                cloudinaryMedia={cloudinaryMedia}
                                playing={playingElement === index}
                                setPlaying={(playing) =>
                                    setPlayingElement(playing ? index : null)
                                }
                            />
                        )
                    })}
                </div>
            </div>
            <div className="rounded-xxl overflow-hidden lg:w-2/3 mb-56 lg:mb-0 relative">
                {audioBlocks?.map((child, index) => {
                    const relatedImage = child?.cloudinaryMedia?.find(
                        (cm: CloudinaryMedia) => {
                            return cm.resource_type === 'image'
                        }
                    )

                    if (!relatedImage) {
                        return null
                    }

                    return (
                        <div
                            key={index}
                            className={`absolute top-0 left-0 right-0 bottom-0 transition-opacity duration-300 ${
                                playingElement === index
                                    ? 'opacity-100'
                                    : 'opacity-0'
                            }`}
                        >
                            <ElgatoImage
                                key={index}
                                src={relatedImage.secure_url}
                                alt={relatedImage.alt}
                                className="h-full"
                            />
                        </div>
                    )
                })}
                {type === 'image' ? (
                    <ElgatoImage
                        src={selectedSource}
                        alt={selectedAlt}
                        className="h-full"
                    />
                ) : (
                    <ElgatoVideo
                        secure_url={src}
                        className="h-full w-full"
                        videoClasses=""
                    />
                )}
            </div>
        </div>
    )
}
