import { Icon } from '@components/atoms/Icon/Icon'
import ElgatoVideo from '@components/common/ElgatoVideo/ElgatoVideo'
import PlayIcon from '@components/icons/PlayIcon'
import { Button } from '@components/molecules/Button/Button'
import {
    Container,
    ContainerSize
} from '@components/organisms/Container/Container'
import { VideoOverlay } from '@components/organisms/VideoOverlay/VideoOverlay'
import { ModelTypeEnum } from '@config/base'
import { useNeoAction } from '@lib/gtm/neoActions'
import { useProductUrlBuilder } from '@lib/hooks/useBuildProductUrl'
import getProductsBySkus from '@pylot-data/api/operations/get-products-by-skus'
import cn from 'classnames'
import { focusController } from 'helpers/AdaHelpers'
import dynamic from 'next/dynamic'
import { useRouter } from 'next/router'
import React, { FC, useEffect, useRef, useState } from 'react'
import SwiperCore, {
    A11y,
    Keyboard,
    Mousewheel,
    Navigation,
    Pagination,
    Swiper
} from 'swiper'
import { SwiperSlide } from 'swiper/react'
import { SwiperOptions } from 'swiper/types/swiper-options'
import s from './TextImageSlider.module.scss'
const SwiperSlider = dynamic(
    () => import('@components/organisms/Slider/SwiperSlider'),
    {
        ssr: false
    }
)
interface TextImageSliderMobileProps {
    content: any
}

const TextImageSliderMobile: FC<TextImageSliderMobileProps> = ({ content }) => {
    const views = content.textImageSlider

    const { locale, events } = useRouter()

    const [
        visibleImageIndexMobile,
        setVisibleImageIndexMobile
    ] = useState<number>(0)
    const containerRefMobile = useRef<HTMLDivElement>(null)
    const pagination = useRef<HTMLDivElement>(null)
    const [showPopup, setShowPopup] = useState('')
    const { pushNeoAction, handleNeoSlideScroll } = useNeoAction()
    const productUrlBuilder = useProductUrlBuilder({
        page: ModelTypeEnum.PRODUCT
    })

    const sliderSettings: SwiperOptions = {
        spaceBetween: 16,
        slidesOffsetBefore: 16,
        slidesOffsetAfter: 16,
        slidesPerView: 'auto',
        centeredSlides: false,
        loop: false,
        modules: [A11y, Navigation, Mousewheel, Keyboard, Pagination],
        allowTouchMove: true,
        navigation: false,
        shortSwipes: true,
        mousewheel: {
            forceToAxis: true
        },
        keyboard: true,
        pagination: {
            el: pagination.current,
            clickable: true
        },
        breakpoints: {
            768: {
                slidesOffsetBefore: 32,
                slidesOffsetAfter: 32
            }
        }
    }

    const handlePushNeoAction = ({
        videoUrl,
        videoTitle
    }: {
        videoUrl: string
        videoTitle: string
    }) => {
        pushNeoAction({
            cta: 'VideoCTA',
            component: document.title,
            action: 'click',
            clickText: videoTitle,
            clickUrl: videoUrl
        })
    }
    const handleOnSlideChange = (swiper: Swiper) => {
        handleNeoSlideScroll(
            {
                cta: 'VideoCTA',
                component: document.title
            },
            swiper,
            views.length
        )
    }

    const handleResize = () => {
        document.body.style.overflow = ''
    }

    useEffect(() => {
        if (typeof window !== 'undefined') {
            handleResize()
            window.addEventListener('resize', handleResize)
        }

        return () => {
            window.removeEventListener('resize', handleResize)
        }
    }, [])

    return (
        <div>
            <div
                className={cn(s['text-image-slider'], s['mobile'], {
                    [s['active']]: showPopup !== ''
                })}
            >
                <SwiperSlider
                    loop={false}
                    settings={sliderSettings}
                    className={s['text-image-slider-view-wrapper']}
                    onSlideChange={(swiper: SwiperCore) => {
                        setVisibleImageIndexMobile(swiper.activeIndex)
                        handleOnSlideChange(swiper)
                    }}
                >
                    {views &&
                        views.map(
                            (view: any, i: React.Key | null | undefined) => (
                                <SwiperSlide key={i}>
                                    <div
                                        ref={containerRefMobile}
                                        className={cn(
                                            s[
                                                'text-image-slider-view-wrapper__image'
                                            ]
                                        )}
                                    >
                                        <div
                                            className={cn(
                                                s[
                                                    'text-image-slider-view-wrapper__image__container'
                                                ],
                                                'relative image-container'
                                            )}
                                        >
                                            {view.icon && (
                                                <div
                                                    className={cn(
                                                        'flex flex-row',
                                                        s[
                                                            'text-image-slider-view-wrapper__image__badge'
                                                        ]
                                                    )}
                                                >
                                                    <span
                                                        className={cn(
                                                            s[
                                                                'text-image-slider-view-wrapper__image__icon'
                                                            ]
                                                        )}
                                                    >
                                                        {view.icon && (
                                                            <Icon
                                                                name={view.icon}
                                                            />
                                                        )}
                                                    </span>
                                                </div>
                                            )}
                                            {!view.video && (
                                                <img
                                                    alt={
                                                        view
                                                            .cloudinaryImage?.[0]
                                                            ?.context?.custom
                                                            ?.alt || ''
                                                    }
                                                    src={
                                                        view
                                                            .cloudinaryImage?.[0]
                                                            ?.secure_url
                                                    }
                                                />
                                            )}
                                            {view.cloudinaryMedia && (
                                                <ElgatoVideo
                                                    className="object-cover h-full w-full"
                                                    secure_url={
                                                        view
                                                            .cloudinaryMedia?.[0]
                                                            ?.secure_url
                                                    }
                                                    options={{
                                                        autoPlay: true,
                                                        preload: 'true',
                                                        muted: true,
                                                        loop: true
                                                    }}
                                                    videoDescription={
                                                        view?.videoDescription
                                                    }
                                                />
                                            )}
                                        </div>
                                    </div>
                                    {view.children && (
                                        <div
                                            className={cn(
                                                'visible 1024c:hidden'
                                            )}
                                            style={{
                                                position: 'absolute',
                                                width: '40px',
                                                height: '40px',
                                                bottom: 20,
                                                right: 20,
                                                borderRadius: '40px',
                                                boxShadow: '0px 0px 20px white',
                                                cursor: 'pointer'
                                            }}
                                            role="presentation"
                                            onClick={() => {
                                                document.body.style.overflow =
                                                    'hidden'
                                                setShowPopup(
                                                    view.title
                                                        .replaceAll(' ', '_')
                                                        .toLowerCase()
                                                )
                                            }}
                                        >
                                            <svg
                                                width="40"
                                                height="40"
                                                viewBox="0 0 40 40"
                                                fill="none"
                                                xmlns="http://www.w3.org/2000/svg"
                                            >
                                                <rect
                                                    y="40"
                                                    width="40"
                                                    height="40"
                                                    rx="20"
                                                    transform="rotate(-90 0 40)"
                                                    fill="black"
                                                />
                                                <path
                                                    d="M12.75 19.25C12.3358 19.25 12 19.5858 12 20C12 20.4142 12.3358 20.75 12.75 20.75L19.25 20.75L19.25 27.25C19.25 27.6642 19.5858 28 20 28C20.4142 28 20.75 27.6642 20.75 27.25L20.75 20.75L27.25 20.75C27.6642 20.75 28 20.4142 28 20C28 19.5858 27.6642 19.25 27.25 19.25L20.75 19.25L20.75 12.75C20.75 12.3358 20.4142 12 20 12C19.5858 12 19.25 12.3358 19.25 12.75L19.25 19.25L12.75 19.25Z"
                                                    fill="white"
                                                />
                                            </svg>
                                        </div>
                                    )}
                                </SwiperSlide>
                            )
                        )}
                    <div
                        ref={pagination}
                        className={cn(s['swiper-pagination'])}
                    >
                        {' '}
                    </div>
                    <Container size={ContainerSize.XLARGE}>
                        {views &&
                            views.map((view: any, i: number) => {
                                const [
                                    videoOverlayOpen,
                                    setVideoOverlayOpen
                                    // eslint-disable-next-line react-hooks/rules-of-hooks
                                ] = useState(false)

                                const openVideoOverlay = () => {
                                    handlePushNeoAction({
                                        videoTitle: view?.title || '',
                                        videoUrl:
                                            view?.videoOverlay?.file?.url || ''
                                    })
                                    setVideoOverlayOpen(true)
                                }

                                const closeVideoOverlay = () => {
                                    setVideoOverlayOpen(false)
                                }
                                const notice = view.notice
                                const adjusted_notice = notice
                                    ? notice
                                          .replace('&quot;', '"')
                                          .replace('&quot;', '"')
                                    : ''

                                return (
                                    <div
                                        className={cn(
                                            s['text-image-slider-view-wrapper'],
                                            s[
                                                'text-image-slider-view-wrapper-mobile-text'
                                            ],
                                            visibleImageIndexMobile === i
                                                ? s['active']
                                                : s['']
                                        )}
                                        key={i}
                                    >
                                        <h2
                                            className={
                                                s[
                                                    'text-image-slider-view-wrapper__content__title'
                                                ]
                                            }
                                        >
                                            {view.title}
                                        </h2>

                                        <div
                                            className={
                                                s[
                                                    'text-image-slider-view-wrapper__content__text'
                                                ]
                                            }
                                        >
                                            {view.text}
                                        </div>
                                        {view.link && (
                                            <Button
                                                id={`text-image-slider-mobile-video-btn-${i}`}
                                                variant="secondary"
                                                onClick={
                                                    view.videoOverlay
                                                        ? openVideoOverlay
                                                        : undefined
                                                }
                                                onKeyPress={(
                                                    e: React.KeyboardEvent
                                                ) => {
                                                    if (
                                                        e.key === 'Enter' ||
                                                        e.key === ' '
                                                    ) {
                                                        openVideoOverlay()
                                                        focusController(
                                                            `#text-image-slider-mobile-video-btn-${i}`,
                                                            '.content__open',
                                                            '.content__open .overlay-close'
                                                        )
                                                    }
                                                }}
                                                isNeo
                                                href={
                                                    !view.videoOverlay
                                                        ? view.link.linkUrl
                                                        : undefined
                                                }
                                                newTab={
                                                    !view.videoOverlay
                                                        ? view.link.linkUrl
                                                        : view.link.newTab
                                                }
                                                iconAlignment={
                                                    view.link.iconAlignment
                                                }
                                                className={
                                                    s[
                                                        'text-image-slider-view-wrapper__content__button'
                                                    ]
                                                }
                                                label={view.link.linkTitle}
                                            >
                                                {view.link.icon && (
                                                    <Icon
                                                        name={view.link.icon}
                                                    />
                                                )}
                                                {view.link.linkTitle}
                                                {view.videoOverlay && (
                                                    <>
                                                        {view.link.linkTitle}
                                                        <PlayIcon />
                                                    </>
                                                )}
                                            </Button>
                                        )}
                                        {adjusted_notice && (
                                            <div
                                                className={cn(
                                                    s[
                                                        'text-image-slider-view-wrapper__content__notice'
                                                    ]
                                                )}
                                            >
                                                {adjusted_notice}
                                            </div>
                                        )}
                                        {view.autor && (
                                            <div
                                                className={cn(
                                                    s[
                                                        'text-image-slider-view-wrapper__content__autor'
                                                    ]
                                                )}
                                            >
                                                {view.autor}
                                            </div>
                                        )}
                                        {view.videoOverlay && (
                                            <VideoOverlay
                                                video={view.videoOverlay}
                                                isOpen={videoOverlayOpen}
                                                setIsOpen={setVideoOverlayOpen}
                                                onClose={closeVideoOverlay}
                                                posterImage={
                                                    view.videoOverlay
                                                        .posterImage
                                                }
                                                videoCaptionFile={
                                                    view?.videoCaptionFile
                                                }
                                                videoDescriptionFile={
                                                    view?.videoDescriptionFile
                                                }
                                            />
                                        )}
                                    </div>
                                )
                            })}
                    </Container>
                </SwiperSlider>
            </div>
            {views &&
                views.map((view: any, i: number) => {
                    return (
                        view.children && (
                            <div
                                key={i}
                                className={cn(
                                    'visible 1024c:visible',
                                    s[`text-image-popup`],
                                    showPopup ==
                                        view.title
                                            .replaceAll(' ', '_')
                                            .toLowerCase()
                                        ? s[`text-image-popup_visible`]
                                        : s[`text-image-popup_hidden`]
                                )}
                            >
                                <div
                                    style={{
                                        position: 'absolute',
                                        top: 24,
                                        left: 24,
                                        cursor: 'pointer'
                                    }}
                                    role="button"
                                    aria-hidden
                                    onClick={() => {
                                        document.body.style.overflow = ''
                                        setShowPopup('')
                                    }}
                                >
                                    <svg
                                        width="40"
                                        height="40"
                                        viewBox="0 0 40 40"
                                        fill="none"
                                        xmlns="http://www.w3.org/2000/svg"
                                    >
                                        <rect
                                            x="40"
                                            y="40"
                                            width="40"
                                            height="40"
                                            rx="20"
                                            transform="rotate(180 40 40)"
                                            fill="black"
                                        />
                                        <path
                                            d="M26.7803 14.2803C27.0732 13.9874 27.0732 13.5126 26.7803 13.2197C26.4874 12.9268 26.0126 12.9268 25.7197 13.2197L20 18.9393L14.2803 13.2197C13.9874 12.9268 13.5126 12.9268 13.2197 13.2197C12.9268 13.5126 12.9268 13.9874 13.2197 14.2803L18.9393 20L13.2197 25.7197C12.9268 26.0126 12.9268 26.4874 13.2197 26.7803C13.5126 27.0732 13.9874 27.0732 14.2803 26.7803L20 21.0607L25.7197 26.7803C26.0126 27.0732 26.4874 27.0732 26.7803 26.7803C27.0732 26.4874 27.0732 26.0126 26.7803 25.7197L21.0607 20L26.7803 14.2803Z"
                                            fill="white"
                                        />
                                    </svg>
                                </div>
                                {view.children[0]?.children.map(
                                    (child: any, i: number) => {
                                        getProductsBySkus(
                                            child?.sku
                                                ? [child?.sku as string]
                                                : null,
                                            locale ?? ''
                                        ).then((products) => {
                                            if (child && products.length > 0)
                                                child.product = products[0]
                                        })
                                        return (
                                            <a
                                                key={i}
                                                href={
                                                    child?.product
                                                        ? productUrlBuilder({
                                                              product:
                                                                  child?.product,
                                                              url_key:
                                                                  child?.product
                                                                      ?.url_key
                                                          })
                                                        : child.textPanel?.link
                                                              ?.linkUrl
                                                }
                                                target="_blank"
                                                aria-hidden
                                                style={{
                                                    display: 'flex',
                                                    flexDirection: 'row',
                                                    height: '56px',
                                                    borderRadius: '14px',
                                                    backgroundColor: '#FFF',
                                                    gap: '16px',
                                                    marginLeft: '24px',
                                                    marginRight: '24px',
                                                    alignItems: 'center',
                                                    cursor: 'pointer',
                                                    padding: '8px'
                                                }}
                                                rel="noreferrer"
                                            >
                                                <div>
                                                    <div
                                                        style={{
                                                            width: '52px',
                                                            backgroundColor:
                                                                '#EAF2FF',
                                                            borderRadius: '8px'
                                                        }}
                                                    >
                                                        <img
                                                            style={{
                                                                height: '39px',
                                                                width: '39px',
                                                                margin: 'auto'
                                                            }}
                                                            alt={child.title}
                                                            src={
                                                                child?.media
                                                                    ?.file?.url
                                                            }
                                                        />
                                                    </div>
                                                </div>
                                                <div style={{ flexGrow: 1 }}>
                                                    {child?.textPanel.headline}
                                                </div>
                                                <div>
                                                    <svg
                                                        width="40"
                                                        height="40"
                                                        viewBox="0 0 40 40"
                                                        fill="none"
                                                        xmlns="http://www.w3.org/2000/svg"
                                                    >
                                                        <rect
                                                            width="40"
                                                            height="40"
                                                            rx="20"
                                                            fill="#EAF2FF"
                                                        />
                                                        <path
                                                            d="M24.8709 21.6792C24.8709 22.0934 25.2067 22.4292 25.6209 22.4292C26.0351 22.4292 26.3709 22.0934 26.3709 21.6792L26.3709 14.2545C26.3709 13.8403 26.0351 13.5045 25.6209 13.5045L18.1963 13.5045C17.782 13.5045 17.4463 13.8403 17.4463 14.2545C17.4463 14.6688 17.782 15.0045 18.1963 15.0045L23.8102 15.0045L13.6001 25.2147C13.3072 25.5076 13.3072 25.9825 13.6001 26.2754C13.893 26.5682 14.3678 26.5682 14.6607 26.2754L24.8709 16.0652L24.8709 21.6792Z"
                                                            fill="#0C2588"
                                                        />
                                                    </svg>
                                                </div>
                                            </a>
                                        )
                                    }
                                )}
                            </div>
                        )
                    )
                })}
        </div>
    )
}
export default TextImageSliderMobile
