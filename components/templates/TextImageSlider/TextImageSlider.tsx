import React, { FC } from 'react'
import TextImageSliderDesktop from '@components/templates/TextImageSlider/TextImageSliderDesktop'
import TextImageSliderMobile from '@components/templates/TextImageSlider/TextImageSliderMobile'
import { useMobile } from '@pylot-data/hooks/use-mobile'

interface TextImageSliderProps {
    content: any
}

const TextImageSlider: FC<TextImageSliderProps> = ({ content }) => {
    const { isMobile } = useMobile()

    return (
        <div>
            {!isMobile && <TextImageSliderDesktop content={content} />}
            {isMobile && <TextImageSliderMobile content={content} />}
        </div>
    )
}
export default TextImageSlider
