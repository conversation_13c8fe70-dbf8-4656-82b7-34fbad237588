.promo-campaign-section {
    .deal-tag {
        display: inline-block;
        background: #323232;
        color: white;
        font-size: 12px;
        font-weight: 700;
        padding: 4px 8px;
        border-radius: 2px;
        margin-bottom: 8px;
        text-transform: uppercase;
    }

    .main-title {
        font-weight: 700;
        margin-bottom: 16px;
        line-height: 1.2;
        @screen md-max {
            margin-bottom: 8px;
        }
    }

    .promo-campaign-item {
        width: 100%;
        position: relative;
        
        @media (max-width: 768px) {
            display: flex;
            flex-direction: column;
        }
        
        &__image {
            width: 100%;
            position: relative;
            aspect-ratio: 16/9;
            border-radius: 4px;
            overflow: hidden;
            
            @media (max-width: 768px) {
                order: 2;
            }
        }

        .description-box {
            @media (min-width: 769px) {
                position: absolute;
                left: 50%;
                bottom: -40px;
                transform: translateX(-50%);
                background: white;
                padding: 16px 24px;
                border-radius: 8px;
                box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
                width: 90%;
                max-width: 400px;
                opacity: 0;
                transition: all .5s cubic-bezier(0.4, 0, 0.2, 1);
                text-align: left;
                z-index: -1;
            }

            @media (max-width: 768px) {
                order: 1;
                position: static;
                padding: 0 0 16px 0;
                opacity: 1;
                transform: none;
                background: none;
                box-shadow: none;
                width: 100%;
            }

            .description-text {
                font-size: 12px;
                line-height: 150%;
                @screen md {
                    color: #525252;
                }
            }
        }

        @media (min-width: 769px) {
            &:hover {
                .description-box {
                    opacity: 1;
                    bottom: 20px;
                    transform: translateX(-50%);
                    z-index: 2;
                }
            }
        }
    }

    .bottom-card {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px;

        .add-deal-button {
            display: flex;
            align-items: center;
            gap: 8px;
            background: none;
            border: none;
            cursor: pointer;
            padding: 0;
            font-size: 14px;
            color: #151515;
            label {
                min-height: 0;
            }
        }

        .learn-more-link {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            font-size: 12px;
            text-decoration: none;
            .chevron-icon {
                width: 16px;
                height: 16px;
                
            }
        }
    }

    padding: 16px;
    border-top: 1px solid var(--light-grey-1);
    @screen md {
        @apply max-w-full;
        padding-left: 48px;
        padding-right: 48px;
    }

    @media (max-width: 768px) {
        .main-title {
            font-size: 24px;
        }

        .bottom-card {
            padding: 12px 16px;
        }
    }
}

.promo-campaign-section.page-theme-neo {
    border-top-color: var(--primaries-mid-blue);
}
.promo-campaign-section.page-theme-dark {
    border-top-color: var(--primitive-gray-90);
}
