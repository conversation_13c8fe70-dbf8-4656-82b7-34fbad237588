import ChevronRightIcon from '@components/atoms/Icon/general/ChevronRightIcon'
import { IThirdPartyPromoMessage } from '@components/common/types'
import { Checkbox } from '@components/molecules/Checkbox/Checkbox'
import Image from '@corsairitshopify/corsair-image'
import cn from 'classnames'
import { useTranslation } from 'next-i18next'
import Link from 'next/link'
import { Dispatch, SetStateAction } from 'react'
import s from './PromoCampaignSection.module.scss'
// import { getAriaLabel } from 'helpers/accessibilityHelper'

export const SPLIT_CHAR = `||`

export interface PromoCampaignProps {
    data3rdPartyPromoContentful: IThirdPartyPromoMessage
    isPromoCampaign: boolean
    setPromoCampaign: Dispatch<SetStateAction<boolean>>
    pageTheme?: string
}

export const PromoCampaignSection = ({
    data3rdPartyPromoContentful,
    isPromoCampaign,
    setPromoCampaign,
    pageTheme
}: PromoCampaignProps) => {
    const { t } = useTranslation(['common'])

    const {
        badgeText,
        title,
        description,
        image,
        titleViewMore,
        urlViewMore
    } = data3rdPartyPromoContentful
    return (
        <div
            className={cn(s['promo-campaign-section'], {
                [s[`page-theme-${pageTheme}`]]: pageTheme
            })}
        >
            <div className={cn(s['deal-tag'], 'uppercase')}>{badgeText}</div>
            <h4
                className={cn(
                    s['main-title'],
                    pageTheme === 'dark'
                        ? 'text-white'
                        : 'text-primitive-gray-140'
                )}
            >
                {title}
            </h4>

            <div className={cn(s['promo-campaign-item'])}>
                <div
                    className={cn(s['promo-campaign-item__image'], {
                        [s[
                            'promo-campaign-item__image--selected'
                        ]]: isPromoCampaign,
                        [s['promo-campaign-item__image--in-dark-mode']]:
                            pageTheme === 'dark'
                    })}
                    onClick={() => setPromoCampaign(!isPromoCampaign)}
                    onKeyPress={() => setPromoCampaign(!isPromoCampaign)}
                    role="checkbox"
                    aria-checked={!isPromoCampaign}
                    tabIndex={0}
                    aria-hidden="true"
                >
                    <div className={s['item-promo__img']}>
                        <Image
                            src={
                                image?.file?.url ||
                                '/images/default-product-image.png'
                            }
                            alt={
                                (image?.description as string | undefined) || ''
                            }
                            width={1200}
                            height={675}
                            layout="responsive"
                            objectFit="cover"
                        />
                    </div>
                </div>

                <div className={cn(s['description-box'])}>
                    <div
                        className={cn(
                            s['description-text'],
                            pageTheme === 'dark'
                                ? 'text-white'
                                : 'text-dark-grey-3',
                            'body'
                        )}
                    >
                        {description}
                    </div>
                </div>
            </div>

            <div className={cn(s['bottom-card'])}>
                <button
                    className={cn(s['add-deal-button'])}
                    onClick={() => setPromoCampaign(!isPromoCampaign)}
                >
                    <Checkbox
                        className={s['checkbox']}
                        variant="squared"
                        checked={isPromoCampaign}
                        isHaveSelectedAllOption
                    />
                    <span
                        className={cn(
                            'text-xs-copy md:text-small-copy text-left',
                            pageTheme === 'dark'
                                ? 'text-white'
                                : 'text-primitive-gray-140'
                        )}
                    >
                        {t('Add deal to cart')}
                    </span>
                </button>

                <Link href={urlViewMore}>
                    <a
                        className={cn(
                            s['learn-more-link'],
                            pageTheme === 'dark'
                                ? 'text-primitive-blue-50'
                                : 'text-primitive-blue-90',
                            'elgato-links'
                        )}
                        target={
                            urlViewMore?.includes('s/mmhmm')
                                ? '_self'
                                : '_blank'
                        }
                    >
                        {titleViewMore}
                        <ChevronRightIcon className={cn(s['chevron-icon'])} />
                    </a>
                </Link>
            </div>
        </div>
    )
}
