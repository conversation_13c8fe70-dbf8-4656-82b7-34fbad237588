.stack-cards-container {
    position: relative;
    @apply py-16;

    @screen md-max {
        height: auto !important;
    }
    @screen md {
        @apply py-32;
    }
}

.stack-cards {
    width: 100%;

    @screen md {
        position: sticky;
        top: calc(var(--sticky-nav-height--lg));
        height: calc(100vh - var(--sticky-nav-height--lg));
    }

    &__inner {
        display: flex;
        flex: 1;
        height: 100%;
        flex-direction: column;
        gap: 40px;

        padding-top: var(--spacing--sm);
        padding-bottom: var(--spacing--sm);

        @screen md {
            padding-top: var(--spacing--lg);
            padding-bottom: var(--spacing--lg);
        }
    }

    &__text-gradient {
        background-clip: text !important;
        -webkit-background-clip: text !important;
        -webkit-text-fill-color: transparent !important;
    }

    &__cards-container {
        position: relative;
        display: flex;
        flex-direction: column;
        flex: 1;
        width: 100%;

        @screen md-max {
            gap: 0;
        }
    }

    &__card-container {
        @screen md {
            position: absolute;
            top: 0;
            bottom: 0;
            left: 64px;
            right: 64px;
            transform-origin: top center;
            max-width: 1920px;
            margin-left: auto;
            margin-right: auto;
            justify-content: center;
            align-items: stretch;
            height: 100%;
        }

        @screen md-max {
            margin-bottom: 40px;
            padding: 0 16px;
        }
    }
}

.stack-card {
    height: 100%;
    max-height: 100%;
    width: auto;
    overflow: hidden;
    background-color: var(--white);
    display: flex;
    flex-direction: column-reverse;
    justify-content: space-between;
    border-radius: 24px;
    gap: 8px;
    padding-bottom: 24px;
    // box-shadow: inset 0px 0px 0px 1px rgba(0, 0, 0, 1);

    @screen lg {
        display: grid;
        grid-template-columns: repeat(12, minmax(0, 1fr));
        grid-template-rows: 100%;
        column-gap: 16px;
        border-radius: 48px;
        padding: 48px 0;
    }

    &__text-container {
        display: flex;
        flex-direction: column;
        // flex: 1;
        justify-content: flex-end;
        align-items: center;
        text-align: center;
        padding: 16px;
        gap: 12px;

        @screen md {
            padding: 80px 16px;
        }
        @screen lg {
            justify-content: center;
            align-items: flex-start;
            text-align: left;
            gap: 24px;
            grid-column: 2 / 7;
            padding: 0;
            padding: 80px 40px;
        }
        @media screen and (min-width: 1440px) {
            &-headline {
                @apply text-h2;
            }
            &-body-copy {
                font-size: 20px;
            }
        }
    }

    &__media-container {
        display: flex;
        justify-content: center;
        align-items: center;
        margin: -0.5px; // used to fix a bug where a thin line is visible on the sides of the image where overflow hidden should be applied.
        @screen lg {
            grid-column: 7 / 12;
        }
    }

    &__image {
        position: relative;
        border-radius: 24px;
        overflow: hidden;
        display: flex;
        flex: 1;
        max-height: 100%;
        max-width: 100%;
        display: none;
        @media screen and (min-width: 1440px) {
            display: block;
            border-radius: 16px;
        }
    }
    &__mobile-image {
        position: relative;
        border-radius: 24px;
        overflow: hidden;
        display: flex;
        flex: 1;
        max-height: 100%;
        max-width: 100%;
        display: block;

        @screen lg {
            display: none;
        }
    }

    &__tablet-image {
        position: relative;
        border-radius: 24px;
        overflow: hidden;
        display: flex;
        flex: 1;
        max-height: 100%;
        max-width: 100%;
        display: none;
        @media screen and (min-width: 1024px) and (max-width: 1439px) {
            display: block;
        }
    }

    &__video {
        border-radius: 16px;
        overflow: hidden;
        display: none;
        @media screen and (min-width: 1440px) {
            display: block;
            border-radius: 16px;
        }
    }
    &__mobile-video {
        border-radius: 16px;
        overflow: hidden;
        display: block;
        width: max-content;
        height: max-content;
        @screen lg {
            display: none;
        }
    }
    &__tablet-video {
        border-radius: 16px;
        overflow: hidden;
        display: none;

        @media screen and (min-width: 1024px) and (max-width: 1439px) {
            display: block;
        }
    }
}
