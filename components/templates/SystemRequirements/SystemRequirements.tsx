import React, { FC, ReactNode, useState } from 'react'
import { PrimaryTextProps } from '@components/molecules/PrimaryText/PrimaryText'
import cn from 'classnames'
const DropdownPanel = dynamic(
    () => import('@components/organisms/DropdownPanel/DropdownPanel'),
    {
        ssr: false
    }
)
import s from './SystemRequirements.module.scss'
import dynamic from 'next/dynamic'
import { CloudinaryMedia } from '@components/common/CloudinaryMedia/Cloudinary'
import ElgatoImage from '@components/common/ElgatoImage'
import { parseNoBreakLines } from '@config/hooks/useParseNoBreakLines'
import { useLayoutContext } from '@components/layouts/MainLayout/LayoutContext'
import { decode } from 'he'
import { LinkResponse } from '@components/molecules/Link/Link'

export type SystemRequirementsProps = {
    meta: { contentType: 'templateSystemRequirements' }
    textPanels?: PrimaryTextProps[]
    theme?: 'white' | 'black' | 'neo'
    title?: ReactNode
    headline?: ReactNode
    cloudinaryLogoImage?: CloudinaryMedia
    logoImageWidth?: number
    logoImageHeight?: number
    linkItems?: LinkResponse[]
}

export type SystemRequirementContent = {
    content: SystemRequirementsProps
}

export const SystemRequirements: FC<SystemRequirementContent> = ({
    content
}) => {
    const [textPanelActive, setTextPanelActive] = useState(0)
    const { pageTheme } = useLayoutContext()
    return (
        <div className={cn('relative flex')}>
            <DropdownPanel
                headline={content.title}
                className={cn('w-full')}
                multipleLinks={content.linkItems}
            >
                <div className={cn('flex md:flex-row gap-10')}>
                    {content?.textPanels?.map((textPanel, index) => {
                        return (
                            <div
                                key={index}
                                onClick={() => setTextPanelActive(index)}
                                tabIndex={0}
                                role="button"
                                onKeyPress={() => setTextPanelActive(index)}
                                className={cn(
                                    s['system-requirements'],
                                    'flex flex-col items-start gap-10',
                                    {
                                        'text-black': pageTheme !== 'dark',
                                        'text-white': pageTheme === 'dark'
                                    }
                                )}
                            >
                                <div
                                    key={index}
                                    className={cn(
                                        {
                                            'opacity-50':
                                                textPanelActive !== index,
                                            'opacity-100':
                                                textPanelActive === index,
                                            [s[
                                                'system-requirements__textPanel-active'
                                            ]]: textPanelActive === index
                                        },
                                        s['system-requirements__header'],
                                        'flex flex-row items-center'
                                    )}
                                >
                                    {textPanel.cloudinaryLogoImage && (
                                        <div className={cn('w-32px h-32px')}>
                                            <ElgatoImage
                                                src={
                                                    textPanel
                                                        ?.cloudinaryLogoImage?.[0]
                                                        ?.secure_url
                                                }
                                            />
                                        </div>
                                    )}
                                    <h3>{textPanel.headline}</h3>
                                </div>
                            </div>
                        )
                    })}
                </div>
                <div>
                    <div
                        className={cn(
                            'flex flex-col md:flex-row gap-20 mt-10 overflow-hidden'
                        )}
                    >
                        {content?.textPanels?.map((child: any, index) => {
                            return child.children?.map((features: any) => (
                                <div
                                    className={cn(
                                        {
                                            hidden: textPanelActive !== index,
                                            visible: textPanelActive === index,
                                            [s['slide-in-left']]:
                                                index % 2 === 0 &&
                                                textPanelActive === index,
                                            [s['slide-in-right']]:
                                                index % 2 !== 0 &&
                                                textPanelActive === index
                                        },
                                        'w-full md:w-1/2'
                                    )}
                                    key={index}
                                >
                                    <h4>{features?.headline}</h4>
                                    <div
                                        dangerouslySetInnerHTML={{
                                            __html: decode(
                                                parseNoBreakLines(
                                                    features?.featuresText
                                                )
                                            )
                                        }}
                                    />
                                </div>
                            ))
                        })}
                    </div>
                </div>
            </DropdownPanel>
        </div>
    )
}
export default SystemRequirements
