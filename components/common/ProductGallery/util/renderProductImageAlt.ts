import { Maybe } from '@pylot-data/pylotschema'

export const renderProductImageAlt = (image: Maybe<any>): string => {
    if (!image) return 'placeholder'
    if (image.label) {
        return image.label
    }
    if (image.url) {
        const alt = image.url.split('/').pop().replace(/_/g, ' ')
        const lastDotPosition = alt.lastIndexOf('.')
        if (lastDotPosition === -1) {
            return alt
        } else {
            return alt.substr(0, lastDotPosition)
        }
    }
    return 'placeholder'
}
