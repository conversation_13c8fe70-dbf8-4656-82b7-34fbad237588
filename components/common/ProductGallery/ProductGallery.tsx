import type {
    Maybe,
    MediaGalleryInterface,
    ProductImage,
    ProductVideo
} from '@pylot-data/pylotschema'
import cn from 'classnames'
import dynamic from 'next/dynamic'
import React, {
    Dispatch,
    ReactElement,
    SetStateAction,
    useEffect,
    useMemo,
    useState
} from 'react'

import type { TypeComponentsTab } from '@components/common/Tabs/ProductTabContent'

import { IProductContentfulResponse } from '../types'

import { ProductFeatures } from '@components/corra/ProductFeatures'
import { useMobile } from '@pylot-data/hooks/use-mobile'
import s from '@styles/common/product-gallery/ProductGallery.module.scss'
import { useRouter } from 'next/router'

const ScrollGallery = dynamic(() => import('./ScrollGallery'))

export type ProductGalleryType = {
    galleryControlDimensions?: { width: number; height: number }
    mediaGalleryEntries: Maybe<MediaGalleryInterface>[]
    setAdditionalImages: Dispatch<SetStateAction<MediaGalleryInterface[]>>
    additionalImages: MediaGalleryInterface[]
    productContentful?: IProductContentfulResponse<TypeComponentsTab> | null
    config?: { mobile?: GalleryConfigType; desktop?: GalleryConfigType }
    isAnimated?: boolean
    pageTheme?: string
}

export enum GalleryTypeEnum {
    SCROLL = 'SCROLL',
    STACKED = 'STACKED'
}

export type GalleryConfigType = {
    gallerytype: string //type
    zoom: boolean
    loop?: boolean
    fullscreen: boolean
    thumb?: boolean
    sliderProps?: {
        main?: {
            allowTouchMove?: boolean
            spaceBetween?: number
            maxSlides?: number
            navigation?: boolean
            pagination?:
                | boolean
                | {
                      clickable: boolean
                  }
        }
        thumbs?: {
            spaceBetween?: number
            slidesPerView?: number
        }
    }
}

export type GalleryType = {
    galleryControlDimensions?: { width: number; height: number }
    galleryConfig: GalleryConfigType
    mediaGalleryEntries: Maybe<ProductVideo | ProductImage>[]
    setMobileBarPosition: React.Dispatch<React.SetStateAction<number>>
    pageTheme?: string
}

const defaultGalleryConfig = {
    mobile: {
        gallerytype: GalleryTypeEnum.SCROLL,
        loop: true,
        zoom: true,
        fullscreen: false,
        thumb: false
    },
    desktop: {
        gallerytype: GalleryTypeEnum.STACKED,
        loop: true,
        zoom: false,
        fullscreen: true
    }
}

export const Gallery = ({
    galleryConfig,
    mediaGalleryEntries,
    setMobileBarPosition,
    pageTheme
}: GalleryType): ReactElement => {
    const router = useRouter()
    return (
        <div className={s['scroll-gallery']}>
            <ScrollGallery
                key={router.asPath}
                mediaGalleryEntries={mediaGalleryEntries}
                zoom={galleryConfig.zoom}
                showFullscreen={galleryConfig.fullscreen}
                thumb={galleryConfig.thumb}
                sliderProps={galleryConfig.sliderProps}
                loop={galleryConfig.loop}
                setMobileBarPosition={setMobileBarPosition}
                pageTheme={pageTheme}
            />
        </div>
    )
}

export const ProductGallery = (props: ProductGalleryType): ReactElement => {
    const {
        galleryControlDimensions = { height: 22, width: 22 },
        mediaGalleryEntries,
        setAdditionalImages,
        additionalImages,
        productContentful,
        config = defaultGalleryConfig,
        pageTheme
    } = props
    const isAnimated = productContentful?.features?.[0]?.isAnimated

    const [mobileBarPosition, setMobileBarPosition] = useState(0)
    const { isMobile } = useMobile(1080)
    const filteredAnimatedFeatures = productContentful?.features?.filter(
        (feature) => feature.isAnimated
    )

    useEffect(() => {
        const sku = productContentful?.sku
        const length1 = additionalImages.length
        const length2 = additionalImages.filter(
            (e) => sku && e?.type?.includes(sku)
        ).length

        // fall back for when additionalImages were set and people leave to another PDP. Unset additionalImages
        if (!!productContentful?.sku && length1 > 0 && length1 !== length2) {
            setAdditionalImages([])
        }
    }, [productContentful?.sku, additionalImages, setAdditionalImages])

    const galleryEntries = useMemo(() => {
        const videoEntries =
            (productContentful?.videos?.map(
                ({
                    title,
                    specialVideo,
                    url,
                    thumbnailUrl,
                    mobileUrl,
                    mobileThumbnailUrl,
                    videoDescription
                }) => {
                    // const { videoUrl, videoThumbnail } = getVideoData(url)

                    return {
                        label: title,
                        specialVideo: specialVideo,
                        url:
                            isMobile && mobileThumbnailUrl
                                ? mobileThumbnailUrl
                                : thumbnailUrl,
                        video_content: {
                            video_url: isMobile && mobileUrl ? mobileUrl : url
                        },
                        __typename: 'ProductVideo',
                        videoDescription: videoDescription
                            ? videoDescription
                            : undefined
                    }
                }
            ) as Maybe<ProductVideo>[]) || []

        const galleryEntries: Maybe<
            ProductVideo | ProductImage
        >[] = productContentful?.productMedia?.length
            ? productContentful?.productMedia.map((item) => {
                  const { cloudinaryImage, cloudinaryMobileImage } = item
                  const media = isMobile
                      ? cloudinaryMobileImage?.[0]
                          ? cloudinaryMobileImage?.[0]
                          : cloudinaryImage?.[0]
                      : cloudinaryImage?.[0]
                  const thumbnail = isMobile
                      ? item.cloudinaryMobileThumbnail?.[0]
                          ? item.cloudinaryMobileThumbnail?.[0]
                          : item.cloudinaryThumbnail?.[0]
                      : item.cloudinaryThumbnail?.[0]

                  if (media?.resource_type === 'video') {
                      return {
                          label: media?.context?.custom?.alt,
                          url: thumbnail?.secure_url,
                          video_content: {
                              video_url: media?.secure_url
                          },
                          __typename: 'ProductVideo'
                      }
                  }
                  return {
                      label: media?.context?.custom?.alt,
                      url: media?.secure_url,
                      __typename: 'ProductImage'
                  }
              })
            : [
                  ...(additionalImages || []),
                  ...((mediaGalleryEntries as Maybe<ProductImage>[]) || [])
              ]

        if (productContentful?.videoPosition === 'First') {
            videoEntries.map((item) => {
                galleryEntries.unshift(item)
            })
        } else if (productContentful?.videoPosition === 'Second') {
            videoEntries.map((item) => {
                galleryEntries.splice(1, 0, item)
            })
        } else if (productContentful?.videoPosition === 'Last') {
            videoEntries.map((item) => {
                galleryEntries.push(item)
            })
        }

        return galleryEntries
    }, [mediaGalleryEntries, productContentful, isMobile, additionalImages])

    return (
        <>
            <div
                className={cn(s['pdp-gallery'], {
                    [s[`page-theme-${pageTheme}`]]: pageTheme,
                    [s['pdp-gallery-video']]: galleryEntries?.[0]?.specialVideo
                })}
            >
                {/* Animated Features */}
                {isAnimated && (
                    <div className="absolute w-auto h-auto top-0 right-0 z-10 hidden md:block">
                        {filteredAnimatedFeatures &&
                            filteredAnimatedFeatures.length > 0 && (
                                <ProductFeatures
                                    productFeaturesTitle={
                                        productContentful?.productFeaturesTitle
                                    }
                                    features={filteredAnimatedFeatures}
                                />
                            )}
                    </div>
                )}

                <div className={s['gallery-container']}>
                    {(config.mobile || config.desktop) && (
                        <div
                            className={cn({
                                [s['desktop-gallery']]:
                                    config.desktop && !isMobile,
                                [s['desktop-mobile']]: config.mobile && isMobile
                            })}
                        >
                            <Gallery
                                galleryControlDimensions={
                                    config.mobile && isMobile
                                        ? galleryControlDimensions
                                        : undefined
                                }
                                galleryConfig={
                                    config.mobile && isMobile
                                        ? config.mobile
                                        : config.desktop ||
                                          defaultGalleryConfig.desktop
                                }
                                mediaGalleryEntries={galleryEntries}
                                setMobileBarPosition={setMobileBarPosition}
                                pageTheme={pageTheme}
                            />
                        </div>
                    )}
                </div>
            </div>
            {/* {pageTheme !== 'neo' && (
                <div className={cn(s['mobile-slider-dots'])}>
                    <span
                        style={{
                            left: `${
                                (100 / galleryEntries?.length) *
                                mobileBarPosition
                            }%`,
                            width: `${100 / galleryEntries?.length}%`
                        }}
                    />
                </div>
            )} */}
            {/* {pageTheme === 'neo' && ( */}
            <div className={s['mobile-slider-dots']}>
                {galleryEntries.map((e, index) => (
                    <span
                        key={index}
                        className={cn(s['dot'], {
                            [s['dot--active']]: mobileBarPosition === index
                        })}
                    />
                ))}
            </div>
            {/* )} */}
        </>
    )
}
export default React.memo(ProductGallery)
