import { useAnimationAndVideosToggle } from '@components/common/AnimationAndVideosToggle/AnimationAndVideoContext'
import ElgatoMediaVideo from '@components/common/ElgatoMedia/ElgatoMediaVideo'
import Image from '@corsairitshopify/pylot-image'
import type { Maybe } from '@pylot-data/pylotschema'
import cn from 'classnames'
import { nanoid } from 'nanoid'
import { useTranslation } from 'next-i18next'
import { FC, ReactElement, useMemo, useRef } from 'react'

type VideoPlayerPropType = {
    url: string
    index?: number
    image?: Maybe<string>
    isThumbnail?: boolean
    className?: string
    sliderIndex?: number
    onClick?: () => void
    videoDescription?: string
    pageTheme?: string
}

type RenderVideoPropType = {
    url: string
    playEmbedVideo?: boolean
    thumbnailUrl: Maybe<string> | undefined
    videoDescription?: string
    pageTheme?: string
    sliderIndex?: number
}

export type TVideoPlayer = VideoPlayerPropType

const RenderVideo = ({
    url,
    playEmbedVideo,
    thumbnailUrl,
    videoDescription,
    pageTheme,
    sliderIndex
}: RenderVideoPropType): ReactElement | null => {
    const videoRef = useRef<HTMLVideoElement>(null)
    const videoDescriptionId = useMemo(() => nanoid(), [])
    const { isAnimationStopped } = useAnimationAndVideosToggle()

    const isMainSlide = useMemo(() => {
        if (isAnimationStopped) {
            return false
        }
        const r1 = videoRef?.current?.closest('.swiper-slide-active')
        return !!r1 && !!playEmbedVideo && sliderIndex === 0
    }, [isAnimationStopped, playEmbedVideo, sliderIndex])

    if (!url || !thumbnailUrl) {
        return null
    }

    let videoDescAttributes
    if (videoDescription) {
        videoDescAttributes = {
            tabIndex: 0,
            role: 'img',
            'aria-describedby': `video-description-${videoDescriptionId}`
        }
    }
    return (
        <div {...videoDescAttributes} className="relative h-full w-full">
            {playEmbedVideo ? (
                <>
                    <ElgatoMediaVideo
                        ref={videoRef}
                        intersectionDisabled
                        cloudinaryMedia={[
                            {
                                secure_url: url,
                                context: {
                                    custom: {
                                        alt: '',
                                        title: ''
                                    }
                                },
                                resource_type: 'video',
                                width: 1920,
                                height: 1080,
                                public_id: '',
                                bytes: 0,
                                logoImageWidth: 0,
                                logoImageHeight: 0,
                                original_secure_url: url
                            }
                        ]}
                        cloudinaryPosterImage={[
                            {
                                secure_url: thumbnailUrl,
                                context: {
                                    custom: {
                                        alt: '',
                                        title: ''
                                    }
                                },
                                resource_type: 'image',
                                width: 1920,
                                height: 1080,
                                public_id: '',
                                bytes: 0,
                                logoImageWidth: 0,
                                logoImageHeight: 0,
                                original_secure_url: thumbnailUrl
                            }
                        ]}
                        autoPlay={isMainSlide}
                        muted
                        loop
                        playsInline
                        preload="auto"
                        showControls={false}
                        showPauseButton
                        controlsPosition="bottom-left"
                        controlsBackground="white"
                        size="large"
                        showProgressIndicator
                        objectFit="cover"
                        className="w-full h-full"
                    />
                    {videoDescription && (
                        <p
                            className="sr-only"
                            id={`video-description-${videoDescriptionId}`}
                        >
                            {videoDescription}
                        </p>
                    )}
                </>
            ) : (
                <Image
                    layout="fill"
                    objectFit={pageTheme === 'neo' ? 'cover' : undefined}
                    quality="85"
                    src={thumbnailUrl}
                    loader={undefined}
                    alt=""
                />
            )}
        </div>
    )
}

export const VideoPlayer: FC<TVideoPlayer> = ({
    url,
    index,
    image,
    isThumbnail,
    className,
    sliderIndex,
    onClick,
    videoDescription,
    pageTheme
}) => {
    const { t } = useTranslation(['common'])

    if (isThumbnail) {
        return (
            <div className="thumbnail-container">
                <Image
                    src={image ?? ''}
                    alt={t('Product Thumbnail')}
                    width={96}
                    height={96}
                    objectFit="cover"
                    quality="85"
                />
            </div>
        )
    }

    return (
        <div
            className={cn('video-container', className)}
            key={`url-${index}`}
            onClick={onClick}
            onKeyPress={onClick}
            role={onClick ? 'button' : 'div'}
            aria-label={videoDescription}
        >
            <RenderVideo
                url={url}
                thumbnailUrl={image}
                playEmbedVideo={sliderIndex === index}
                videoDescription={videoDescription}
                pageTheme={pageTheme}
                sliderIndex={sliderIndex}
            />
        </div>
    )
}
