import dynamic from 'next/dynamic'
import { CustomizableCard } from '@components/organisms/CustomizableCard/CustomizableCard'
import NewsLetterForm from '@components/common/NewsLetterForm/NewsLetterForm'

const ProductCallouts = dynamic(
    () => import('@components/common/ProductCallouts/ProductCallouts')
)
const SmartHome = dynamic(() => import('@components/common/SmartHome'))
const LinkListTemplate = dynamic(
    () => import('@components/organisms/LinkList/LinkList')
)
const ABTest = dynamic(() => import('@components/templates/ABTest/ABTest'))
const PageSection = dynamic(
    () => import('@components/common/PageSection/PageSection')
)
const CarouselHow = dynamic(() => import('@components/common/CarouselHow'))
const Carousel = dynamic(() => import('@components/common/Carousel'))
const HeroBanner = dynamic(() => import('@components/common/HeroBanner'))
const CategorySlider = dynamic(
    () => import('@components/common/CategorySlider')
)
const AnimatedText = dynamic(
    () => import('@components/templates/AnimatedText/AnimatedText')
)
const Banner3Tile = dynamic(() => import('@components/common/Banner3Tile'))
const Banner2Tile = dynamic(() => import('@components/common/Banner2Tile'))
const FAQModuleCollection = dynamic(
    () => import('@components/common/FAQModule/FAQModuleCollection')
)
const CopyBlocksWrapper = dynamic(
    () => import('@components/common/CopyBlocks/CopyBlocksWrapper')
)
const ProductBlocksWrapper = dynamic(
    () => import('@components/common/ProductBlocks/ProductBlocksWrapper')
)
const GridContentCards = dynamic(
    () => import('@components/common/GridContentCards')
)

const BundleBlocks = dynamic(() => import('@components/common/BundleBlocks'))

const FullPanelCards = dynamic(
    () => import('@components/common/FullPanelCards')
)
const VidGallery = dynamic(() => import('@components/common/VidGallery'))
const TestimonialCarousel = dynamic(
    () => import('@components/common/TestimonialCarousel/TestimonialCarousel')
)
const CarouselWrapper = dynamic(
    () => import('@components/common/CarouselWrapper/CarouselWrapper')
)
const ContentCardTiles = dynamic(
    () => import('@components/common/ContentCardTiles')
)
const OverlayProductBlock = dynamic(
    () => import('@components/common/OverlayProductBlock/OverlayProductBlock')
)

/* New Components */
const TextPanel = dynamic(
    () => import('@components/organisms/TextPanel/TextPanel')
)
const QuotesPanel = dynamic(
    () => import('@components/templates/QuotesPanel/QuotesPanel')
)
const MediaWithTextOverlay = dynamic(
    () =>
        import(
            '@components/templates/MediaWithTextOverlay/MediaWithTextOverlay'
        )
)
const TextWithMedia = dynamic(
    () => import('@components/templates/TextWithMedia/TextWithMedia')
)
const TextWithImageSidescroll = dynamic(
    () =>
        import(
            '@components/templates/TextWithImageSidescroll/TextWithImageSidescroll'
        )
)
const DropdownPanel = dynamic(
    () => import('@components/templates/DropdownPanel/DropdownPanel')
)
const SystemRequirements = dynamic(
    () => import('@components/templates/SystemRequirements/SystemRequirements')
)
const FAQ = dynamic(() => import('@components/templates/FAQ/FAQ'))
const TechnicalSpecifications = dynamic(
    () =>
        import(
            '@components/templates/TechnicalSpecifications/TechnicalSpecifications'
        )
)
const HardwareHotspotPanel = dynamic(
    () =>
        import(
            '@components/templates/HardwareHotspotPanel/HardwareHotspotPanel'
        )
)
const ConfiguratorTeaser = dynamic(
    () => import('@components/templates/ConfiguratorTeaser/ConfiguratorTeaser')
)
const HomeHeroSlider = dynamic(
    () => import('@components/templates/HomeHeroSlider/HomeHeroSlider')
)
const AnimatedHero = dynamic(
    () => import('@components/templates/AnimatedHero/AnimatedHero')
)
const HomeGallery = dynamic(
    () => import('@components/templates/HomeGallery/HomeGallery')
)
const SocialMediaGallery = dynamic(
    () => import('@components/templates/SocialMediaGallery/SocialMediaGallery')
)
const InfoCardSlider = dynamic(
    () => import('@components/templates/InfoCardSlider/InfoCardSlider')
)
const SpecialAnimation = dynamic(
    () => import('@components/templates/SpecialAnimation/SpecialAnimation')
)
const ExternalContent = dynamic(
    () => import('@components/templates/ExternalContent/ExternalContent')
)
const Slider = dynamic(() => import('@components/templates/Slider/Slider'))
const CardList = dynamic(
    () => import('@components/templates/CardList/CardList')
)
const ProductQuiz = dynamic(
    () => import('@components/organisms/ProductQuiz/ProductQuiz')
)
const TextImageSlider = dynamic(
    () => import('@components/templates/TextImageSlider/TextImageSlider')
)
const PanelList = dynamic(
    () => import('@components/templates/PanelList/PanelList')
)
const Gallery = dynamic(() => import('@components/templates/Gallery/Gallery'))
const FeatureList = dynamic(
    () => import('@components/templates/FeatureList/FeatureList')
)

const Tabs = dynamic(() => import('@components/templates/Tabs/Tabs'))

const StickyNav = dynamic(
    () => import('@components/organisms/StickyNav/StickyNav')
)
const AnchorStickyNav = dynamic(
    () => import('@components/organisms/StickyNav/AnchorStickyNav')
)
const TemplateForms = dynamic(
    () => import('@components/templates/TemplateForms/TemplateForms')
)
const SpecialPage = dynamic(
    () => import('@components/templates/SpecialPage/SpecialPage')
)
// const CustomizableCard = dynamic(
//     () => import('@components/organisms/CustomizableCard/CustomizableCard')
// )
const Banner = dynamic(() => import('@components/molecules/Banner/Banner'))
const ProductConfigurator = dynamic(
    () =>
        import('@components/templates/ProductConfigurator/ProductConfigurator')
)
const ComparisonTableContentful = dynamic(
    () =>
        import(
            '@components/templates/ComparisonTable/ComparisonTableContentful'
        )
)
const CompareProducts = dynamic(
    () => import('@components/templates/CompareProducts/CompareProducts')
)

const CustomPaddings = dynamic(
    () => import('@components/molecules/CustomPaddings/CustomPaddings')
)

export type DynamicLoadPanelProps = {
    identifier: string
    [k: string]: unknown
    meta: { contentType: string }
}

export interface DynamicLoadPanelContent {
    content: DynamicLoadPanelProps
}

export const DynamicLoadPanel: React.FC<DynamicLoadPanelContent> = ({
    content
}) => {
    switch (content.meta.contentType) {
        case 'newsLetterForm': {
            return <NewsLetterForm content={content as any} />
        }
        case 'banners': {
            return <Carousel content={content as any} />
        }
        case 'carouselHow': {
            return <CarouselHow content={content as any} />
        }
        case 'categorySlider': {
            return <CategorySlider content={content as any} />
        }
        case 'templateAbTest': {
            return <ABTest content={content as any} />
        }
        case 'carouselppContainer': {
            return <CarouselWrapper content={content as any} />
        }
        case 'smartHome': {
            return <SmartHome content={content as any} />
        }
        case 'banner3Tile': {
            return <Banner3Tile content={content as any} />
        }
        case 'banner2Tile': {
            return <Banner2Tile content={content as any} />
        }
        case 'faqModuleCollection': {
            return (
                <FAQModuleCollection
                    faqModuleCollection={content.faqModuleCollection as any}
                />
            )
        }
        case 'heroBanner': {
            return <HeroBanner content={content as any} />
        }
        case 'templateAnimatedHero': {
            return <AnimatedHero content={content as any} />
        }
        case 'copyBlocksWrapper': {
            return <CopyBlocksWrapper content={content as any} />
        }
        case 'nonInteractiveProductBlock': {
            return <ProductBlocksWrapper productBlocks={content as any} />
        }
        case 'gridContentCards': {
            return <GridContentCards content={content} />
        }

        case 'bundleBlocks': {
            return <BundleBlocks content={content as any} />
        }

        case 'fullPanelCards': {
            return <FullPanelCards content={content as any} />
        }
        case 'vidGallery': {
            return <VidGallery content={content as any} />
        }
        case 'testimonialCarousel': {
            return <TestimonialCarousel content={content as any} />
        }
        case 'contentCardTiles': {
            return <ContentCardTiles content={content} />
        }
        case 'productCallout': {
            return <ProductCallouts productCallouts={content as any} />
        }
        case 'overlayProductBlock': {
            return <OverlayProductBlock content={content as any} />
        }
        case 'organismHeadlineMedia': {
            return <TextPanel content={content as any} />
        }
        case 'pageSection': {
            return <PageSection content={content as any} />
        }

        case 'animatedText': {
            return (
                <AnimatedText
                    content={content as any}
                    key={content.title as string}
                />
            )
        }
        case 'templateQuotesPanel': {
            return <QuotesPanel content={content as any} />
        }
        case 'templateMediaWithTextOverlay': {
            return <MediaWithTextOverlay content={content as any} />
        }
        case 'templateTextWithMedia': {
            return <TextWithMedia content={content as any} />
        }
        case 'templateTextWithImageSidescroll': {
            return <TextWithImageSidescroll content={content as any} />
        }
        case 'templateDropdownPanel': {
            return (
                <DropdownPanel
                    content={content as any}
                    key={content.title as string}
                />
            )
        }
        case 'templateSystemRequirements': {
            return (
                <SystemRequirements
                    content={content as any}
                    key={content.title as string}
                />
            )
        }
        case 'templateFaq': {
            return (
                <FAQ content={content as any} key={content.title as string} />
            )
        }
        case 'templateTechnicalSpecifications': {
            return (
                <TechnicalSpecifications
                    content={content as any}
                    key={content.title as string}
                />
            )
        }
        case 'templateHardwareHotspotPanel': {
            return <HardwareHotspotPanel content={content as any} />
        }
        case 'templateConfiguratorTeaser': {
            return <ConfiguratorTeaser content={content as any} />
        }
        case 'templateHomeHeroSlider': {
            return <HomeHeroSlider {...(content as any)} />
        }
        case 'templateHomeGallery': {
            return (
                <HomeGallery
                    items={content.items as any}
                    id={content.id as string}
                    title={content.title as string}
                />
            )
        }
        case 'templateSocialMediaGallery': {
            return <SocialMediaGallery content={content as any} />
        }
        case 'templateInfoCardSlider': {
            return <InfoCardSlider content={content as any} />
        }
        case 'templateSpecialAnimation': {
            return (
                <SpecialAnimation
                    content={content as any}
                    key={content.title as string}
                />
            )
        }
        case 'templateExternalContent': {
            return <ExternalContent content={content as any} />
        }
        case 'templateSlider': {
            return <Slider content={content as any} />
        }
        case 'templateCardList': {
            return <CardList content={content as any} />
        }
        case 'productQuiz': {
            return <ProductQuiz content={content as any} />
        }
        case 'templateTextImageSlider': {
            return <TextImageSlider content={content as any} />
        }

        case 'templatePanelList': {
            return <PanelList content={content as any} />
        }
        case 'templateGallery': {
            return (
                <Gallery
                    content={content as any}
                    key={content.title as string}
                />
            )
        }
        case 'organismFeatureList': {
            return <FeatureList content={content as any} />
        }
        case 'templateTabs': {
            return <Tabs content={content as any} />
        }
        case 'organismStickyNavigation': {
            return <StickyNav content={content as any} />
        }

        case 'moleculeAnchorStickyNavigation': {
            return <AnchorStickyNav links={content as any} />
        }
        case 'organismLinkList': {
            return <LinkListTemplate content={content as any} />
        }
        case 'templateForms': {
            return <TemplateForms content={content as any} />
        }
        case 'info-overlay': {
            return <CustomizableCard />
        }
        case 'templateSpecialPages': {
            return <SpecialPage content={content as any} />
        }
        case 'moleculeBanner': {
            return <Banner content={content as any} />
        }
        case 'productConfigurator': {
            return (
                <ProductConfigurator
                    content={content as any}
                    key={content.title as string}
                />
            )
        }
        case 'templateComparisonTable': {
            return <ComparisonTableContentful content={content as any} />
        }
        case 'productComparisonDropdown': {
            return <CompareProducts content={content as any} />
        }
        case 'customPadding': {
            return <CustomPaddings content={content as any} />
        }
        default: {
            return null
        }
    }
}
