import {
    PageTheme,
    useLayoutContext
} from '@components/layouts/MainLayout/LayoutContext'
import type {
    ContentJsonItem,
    ImageType
} from '@pylot-data/hooks/contentful/use-content-json'
import dynamic from 'next/dynamic'
import { useEffect } from 'react'
import type { IVideoSchema } from '../types'
import { CustomizableCard } from '@components/organisms/CustomizableCard/CustomizableCard'
import ABTest from '@components/templates/ABTest/ABTest'

const MediaWithContent = dynamic(
    () => import('@components/templates/MediaWithContent/MediaWithContent')
)
const ElgatoMedia = dynamic(
    () => import('@components/common/ElgatoMedia/ElgatoMedia')
)
const NewsLetterForm = dynamic(
    () => import('@components/common/NewsLetterForm/NewsLetterForm')
)

const FlexibleGallery = dynamic(
    () => import('@components/templates/FlexibleGallery/FlexibleGallery')
)

const ProductCallouts = dynamic(
    () => import('@components/common/ProductCallouts/ProductCallouts')
)
const SmartHome = dynamic(() => import('@components/common/SmartHome'))
const LinkListTemplate = dynamic(
    () => import('@components/organisms/LinkList/LinkList')
)
const PageSection = dynamic(
    () => import('@components/common/PageSection/PageSection')
)
const CarouselHow = dynamic(() => import('@components/common/CarouselHow'))
const Carousel = dynamic(() => import('@components/common/Carousel'))
const HeroBanner = dynamic(() => import('@components/common/HeroBanner'))
const CategorySlider = dynamic(
    () => import('@components/common/CategorySlider')
)
const AnimatedText = dynamic(
    () => import('@components/templates/AnimatedText/AnimatedText')
)
const Banner3Tile = dynamic(() => import('@components/common/Banner3Tile'))
const Banner2Tile = dynamic(() => import('@components/common/Banner2Tile'))
const FAQModuleCollection = dynamic(
    () => import('@components/common/FAQModule/FAQModuleCollection')
)
const CopyBlocksWrapper = dynamic(
    () => import('@components/common/CopyBlocks/CopyBlocksWrapper')
)
const ProductBlocksWrapper = dynamic(
    () => import('@components/common/ProductBlocks/ProductBlocksWrapper')
)
const GridContentCards = dynamic(
    () => import('@components/common/GridContentCards')
)

const BundleBlocks = dynamic(() => import('@components/common/BundleBlocks'))

const FullPanelCards = dynamic(
    () => import('@components/common/FullPanelCards')
)
const VidGallery = dynamic(() => import('@components/common/VidGallery'))
const TestimonialCarousel = dynamic(
    () => import('@components/common/TestimonialCarousel/TestimonialCarousel')
)
const CarouselWrapper = dynamic(
    () => import('@components/common/CarouselWrapper/CarouselWrapper')
)
const ContentCardTiles = dynamic(
    () => import('@components/common/ContentCardTiles')
)
const OverlayProductBlock = dynamic(
    () => import('@components/common/OverlayProductBlock/OverlayProductBlock')
)

/* New Components */
const TextPanel = dynamic(
    () => import('@components/organisms/TextPanel/TextPanel')
)
const QuotesPanel = dynamic(
    () => import('@components/templates/QuotesPanel/QuotesPanel')
)
const MediaWithTextOverlay = dynamic(
    () =>
        import(
            '@components/templates/MediaWithTextOverlay/MediaWithTextOverlay'
        )
)
const TextWithMedia = dynamic(
    () => import('@components/templates/TextWithMedia/TextWithMedia')
)
const TextWithImageSidescroll = dynamic(
    () =>
        import(
            '@components/templates/TextWithImageSidescroll/TextWithImageSidescroll'
        )
)
const DropdownPanel = dynamic(
    () => import('@components/templates/DropdownPanel/DropdownPanel')
)
const SystemRequirements = dynamic(
    () => import('@components/templates/SystemRequirements/SystemRequirements')
)
const FAQ = dynamic(() => import('@components/templates/FAQ/FAQ'))
const TechnicalSpecifications = dynamic(
    () =>
        import(
            '@components/templates/TechnicalSpecifications/TechnicalSpecifications'
        )
)
const HardwareHotspotPanel = dynamic(
    () =>
        import(
            '@components/templates/HardwareHotspotPanel/HardwareHotspotPanel'
        )
)
const ConfiguratorTeaser = dynamic(
    () => import('@components/templates/ConfiguratorTeaser/ConfiguratorTeaser')
)
const HomeHeroSlider = dynamic(
    () => import('@components/templates/HomeHeroSlider/HomeHeroSlider')
)
const AnimatedHero = dynamic(
    () => import('@components/templates/AnimatedHero/AnimatedHero')
)
const HomeGallery = dynamic(
    () => import('@components/templates/HomeGallery/HomeGallery')
)
const SocialMediaGallery = dynamic(
    () => import('@components/templates/SocialMediaGallery/SocialMediaGallery')
)
const InfoCardSlider = dynamic(
    () => import('@components/templates/InfoCardSlider/InfoCardSlider')
)
const SpecialAnimation = dynamic(
    () => import('@components/templates/SpecialAnimation/SpecialAnimation')
)
const ExternalContent = dynamic(
    () => import('@components/templates/ExternalContent/ExternalContent')
)
const Slider = dynamic(() => import('@components/templates/Slider/Slider'))
const CardList = dynamic(
    () => import('@components/templates/CardList/CardList')
)
const ProductQuiz = dynamic(
    () => import('@components/templates/ProductQuiz/ProductQuiz')
)
const TextImageSlider = dynamic(
    () => import('@components/templates/TextImageSlider/TextImageSlider')
)
const PanelList = dynamic(
    () => import('@components/templates/PanelList/PanelList')
)
const Gallery = dynamic(() => import('@components/templates/Gallery/Gallery'))
const FeatureList = dynamic(
    () => import('@components/templates/FeatureList/FeatureList')
)

const Tabs = dynamic(() => import('@components/templates/Tabs/Tabs'))

const StickyNav = dynamic(
    () => import('@components/organisms/StickyNav/StickyNav')
)
const AnchorStickyNav = dynamic(
    () => import('@components/organisms/StickyNav/AnchorStickyNav')
)
const TemplateForms = dynamic(
    () => import('@components/templates/TemplateForms/TemplateForms')
)
const SpecialPage = dynamic(
    () => import('@components/templates/SpecialPage/SpecialPage')
)
// const CustomizableCard = dynamic(
//     () => import('@components/organisms/CustomizableCard/CustomizableCard')
// )
const Banner = dynamic(() => import('@components/molecules/Banner/Banner'))
const ProductConfigurator = dynamic(
    () =>
        import('@components/templates/ProductConfigurator/ProductConfigurator')
)
const ComparisonTableContentful = dynamic(
    () =>
        import(
            '@components/templates/ComparisonTable/ComparisonTableContentful'
        )
)
const CompareProducts = dynamic(
    () =>
        import(
            '@components/templates/DropdownCompareProducts/DropdownCompareProducts'
        )
)

const CustomPaddings = dynamic(
    () => import('@components/molecules/CustomPaddings/CustomPaddings')
)

const EOLPopup = dynamic(
    () => import('@components/templates/EOLPopup/EOLPopup')
)

const StickySidebar = dynamic(
    () => import('@components/organisms/StickySidebar/StickySidebar')
)

const BazaarLoader = dynamic(
    () => import('@components/molecules/BazaarComponents/BazaarLoader'),
    { ssr: false }
)

export type ContentPageProps = Array<{
    identifier: string
    [k: string]: unknown
    meta: { contentType: string }
}>

export type ContentPage = {
    title: string
    identifier: string
    useFaqSchema: boolean
    pageContentEntries: Array<{
        identifier: string
        [k: string]: unknown
        meta: { contentType: string }
    }>
    metaTitle?: string
    metaDescription?: string
    socialDefaultImage?: ImageType
    navOverlay?: boolean
    enableCanonical?: boolean
    canonicalOverride?: string
    metaRobotsOverride?: boolean
    metaRobotsOverrideFollow?: boolean
    videoSchema?: IVideoSchema[]
    pageTheme?: PageTheme
    showFooter?: boolean
    showBazaarRating?: boolean
}
export const ContentPage: React.FC<{
    pageContent: Omit<ContentJsonItem<ContentPage>, 'entries'>
}> = ({ pageContent }) => {
    const { setNavHeaderOverlay } = useLayoutContext()
    useEffect(() => {
        setNavHeaderOverlay(pageContent.parsedEntries?.navOverlay ?? false)
    }, [pageContent.parsedEntries?.navOverlay, setNavHeaderOverlay])
    return (
        <div
            className={`contentful-page contentful-page-${pageContent.identifier}`}
        >
            {pageContent.parsedEntries?.showBazaarRating && <BazaarLoader />}
            {pageContent.parsedEntries?.pageContentEntries?.map(
                (entryContent) => {
                    // `as any` is used on each of these because this PageContent component
                    // should not care about the type of the data passed in.
                    // There is no benefit to casting to `as CarouselPropType`.
                    // In fact, as long as the component prop type matches the Contentful
                    // data model, the prop data will always be in the correct type because
                    // Contentful always returns the same format for a given content-type.

                    switch (entryContent.meta.contentType) {
                        case 'newsLetterForm': {
                            return (
                                <NewsLetterForm content={entryContent as any} />
                            )
                        }
                        case 'banners': {
                            return <Carousel content={entryContent as any} />
                        }
                        case 'carouselHow': {
                            return <CarouselHow content={entryContent as any} />
                        }
                        case 'categorySlider': {
                            return (
                                <CategorySlider content={entryContent as any} />
                            )
                        }
                        case 'templateAbTest': {
                            return <ABTest content={entryContent as any} />
                        }
                        case 'carouselppContainer': {
                            return (
                                <CarouselWrapper
                                    content={entryContent as any}
                                />
                            )
                        }
                        case 'smartHome': {
                            return <SmartHome content={entryContent as any} />
                        }
                        case 'banner3Tile': {
                            return <Banner3Tile content={entryContent as any} />
                        }
                        case 'banner2Tile': {
                            return <Banner2Tile content={entryContent as any} />
                        }
                        case 'faqModuleCollection': {
                            return (
                                <FAQModuleCollection
                                    faqModuleCollection={
                                        entryContent.faqModuleCollection as any
                                    }
                                />
                            )
                        }
                        case 'heroBanner': {
                            return <HeroBanner content={entryContent as any} />
                        }
                        case 'templateAnimatedHero': {
                            return (
                                <AnimatedHero content={entryContent as any} />
                            )
                        }
                        case 'copyBlocksWrapper': {
                            return (
                                <CopyBlocksWrapper
                                    content={entryContent as any}
                                />
                            )
                        }
                        case 'nonInteractiveProductBlock': {
                            return (
                                <ProductBlocksWrapper
                                    productBlocks={entryContent as any}
                                />
                            )
                        }
                        case 'gridContentCards': {
                            return <GridContentCards content={entryContent} />
                        }

                        case 'bundleBlocks': {
                            return (
                                <BundleBlocks content={entryContent as any} />
                            )
                        }

                        case 'fullPanelCards': {
                            return (
                                <FullPanelCards content={entryContent as any} />
                            )
                        }
                        case 'vidGallery': {
                            return <VidGallery content={entryContent as any} />
                        }
                        case 'testimonialCarousel': {
                            return (
                                <TestimonialCarousel
                                    content={entryContent as any}
                                />
                            )
                        }
                        case 'contentCardTiles': {
                            return <ContentCardTiles content={entryContent} />
                        }
                        case 'productCallout': {
                            return (
                                <ProductCallouts
                                    productCallouts={entryContent as any}
                                />
                            )
                        }
                        case 'overlayProductBlock': {
                            return (
                                <OverlayProductBlock
                                    content={entryContent as any}
                                />
                            )
                        }
                        case 'organismHeadlineMedia': {
                            return <TextPanel content={entryContent as any} />
                        }
                        case 'pageSection': {
                            return <PageSection content={entryContent as any} />
                        }

                        case 'animatedText': {
                            return (
                                <AnimatedText
                                    content={entryContent as any}
                                    key={entryContent.title as string}
                                />
                            )
                        }
                        case 'templateQuotesPanel': {
                            return <QuotesPanel content={entryContent as any} />
                        }
                        case 'templateMediaWithTextOverlay': {
                            return (
                                <MediaWithTextOverlay
                                    content={entryContent as any}
                                />
                            )
                        }
                        case 'templateTextWithMedia': {
                            return (
                                <TextWithMedia content={entryContent as any} />
                            )
                        }
                        case 'templateTextWithImageSidescroll': {
                            return (
                                <TextWithImageSidescroll
                                    content={entryContent as any}
                                />
                            )
                        }
                        case 'templateDropdownPanel': {
                            return (
                                <DropdownPanel
                                    content={entryContent as any}
                                    key={entryContent.title as string}
                                />
                            )
                        }
                        case 'templateSystemRequirements': {
                            return (
                                <SystemRequirements
                                    content={entryContent as any}
                                    key={entryContent.title as string}
                                />
                            )
                        }
                        case 'templateFaq': {
                            return (
                                <FAQ
                                    content={entryContent as any}
                                    key={entryContent.title as string}
                                />
                            )
                        }
                        case 'templateTechnicalSpecifications': {
                            return (
                                <TechnicalSpecifications
                                    content={entryContent as any}
                                    key={entryContent.title as string}
                                />
                            )
                        }
                        case 'templateHardwareHotspotPanel': {
                            return (
                                <HardwareHotspotPanel
                                    content={entryContent as any}
                                />
                            )
                        }
                        case 'templateConfiguratorTeaser': {
                            return (
                                <ConfiguratorTeaser
                                    content={entryContent as any}
                                />
                            )
                        }
                        case 'templateHomeHeroSlider': {
                            return <HomeHeroSlider {...(entryContent as any)} />
                        }
                        case 'templateHomeGallery': {
                            return (
                                <HomeGallery
                                    items={entryContent.items as any}
                                    id={entryContent.id as string}
                                    title={entryContent.title as string}
                                />
                            )
                        }
                        case 'templateSocialMediaGallery': {
                            return (
                                <SocialMediaGallery
                                    content={entryContent as any}
                                />
                            )
                        }
                        case 'templateInfoCardSlider': {
                            return (
                                <InfoCardSlider content={entryContent as any} />
                            )
                        }
                        case 'templateSpecialAnimation': {
                            return (
                                <SpecialAnimation
                                    content={entryContent as any}
                                    key={entryContent.title as string}
                                />
                            )
                        }
                        case 'templateExternalContent': {
                            return (
                                <ExternalContent
                                    content={entryContent as any}
                                />
                            )
                        }
                        case 'templateSlider': {
                            return <Slider content={entryContent as any} />
                        }
                        case 'templateCardList': {
                            return <CardList content={entryContent as any} />
                        }
                        case 'productQuiz': {
                            return <ProductQuiz content={entryContent as any} />
                        }
                        case 'templateTextImageSlider': {
                            return (
                                <TextImageSlider
                                    content={entryContent as any}
                                />
                            )
                        }

                        case 'templatePanelList': {
                            return <PanelList content={entryContent as any} />
                        }
                        case 'templateGallery': {
                            return (
                                <Gallery
                                    content={entryContent as any}
                                    key={entryContent.title as string}
                                />
                            )
                        }
                        case 'organismFeatureList': {
                            return <FeatureList content={entryContent as any} />
                        }
                        case 'templateTabs': {
                            return <Tabs content={entryContent as any} />
                        }
                        case 'organismStickyNavigation': {
                            return <StickyNav content={entryContent as any} />
                        }

                        case 'moleculeAnchorStickyNavigation': {
                            return (
                                <AnchorStickyNav links={entryContent as any} />
                            )
                        }
                        case 'organismLinkList': {
                            return (
                                <LinkListTemplate
                                    content={entryContent as any}
                                />
                            )
                        }
                        case 'templateForms': {
                            return (
                                <TemplateForms content={entryContent as any} />
                            )
                        }
                        case 'info-overlay': {
                            return <CustomizableCard />
                        }
                        case 'templateSpecialPages': {
                            return <SpecialPage content={entryContent as any} />
                        }
                        case 'moleculeBanner': {
                            return <Banner content={entryContent as any} />
                        }
                        case 'productConfigurator': {
                            return (
                                <ProductConfigurator
                                    content={entryContent as any}
                                    key={entryContent.title as string}
                                />
                            )
                        }
                        case 'templateComparisonTable': {
                            return (
                                <ComparisonTableContentful
                                    content={entryContent as any}
                                />
                            )
                        }
                        case 'productComparisonDropdown': {
                            return (
                                <CompareProducts
                                    content={entryContent as any}
                                />
                            )
                        }
                        case 'customPadding': {
                            return (
                                <CustomPaddings content={entryContent as any} />
                            )
                        }
                        case 'eolPopup': {
                            return <EOLPopup content={entryContent as any} />
                        }
                        case 'elgatoMedia': {
                            return <ElgatoMedia {...(entryContent as any)} />
                        }
                        case 'mediaWithContent': {
                            return (
                                <MediaWithContent {...(entryContent as any)} />
                            )
                        }
                        case 'gallery': {
                            return (
                                <FlexibleGallery
                                    content={entryContent as any}
                                />
                            )
                        }
                        case 'stickySidebar': {
                            return (
                                <StickySidebar content={entryContent as any} />
                            )
                        }
                        default: {
                            return null
                        }
                    }
                }
            )}
        </div>
    )
}
