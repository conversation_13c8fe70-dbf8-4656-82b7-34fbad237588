.accesibility-dropdown {
    @apply text-charcoal;
    min-width: 70px;
  
    &__button {
      @apply text-charcoal flex items-center w-full min-w-min-content py-3;
    }
  
    &__icon-wrapper {
      @apply rounded-lg inline-flex items-center justify-center h-16px w-16px;
      @apply text-charcoal;
    }
  
    &__icon {
      @apply fill-current transition-transform duration-300;
      width: 16px;
      height: 16px;
    }
  
    &__list {
      @apply overflow-hidden transition-all duration-300 border-0 absolute right-0 bg-white rounded-md shadow-xl;
      min-width: 250px;
      top: 35px;
      z-index: 200;
      max-height: 0;
      opacity: 0;
    }
  
    &__list--open {
      max-height: 200px; /* Adjust as needed */
      opacity: 1;
    }
  
    &--open {
      .accesibility-dropdown__icon {
        @apply transform -rotate-180;
      }
    }
  
    &--up {
      .accesibility-dropdown__list {
        @apply top-auto;
        bottom: 35px;
        left: 0px;
      }
    }
  
    &--icon-left {
      @apply flex-row;
  
      .accesibility-dropdown__button__label {
        @apply mr-0 text-left;
        margin-left: 24px;
  
        &--centered {
          @apply m-0;
        }
      }
      .accesibility-dropdown__icon-wrapper {
        @apply right-auto;
        left: 2px;
      }
      .accesibility-dropdown__list {
        @apply right-auto left-0;
      }
    }
  }
  
  /* page theme dark */
  .accesibility-dropdown.page-theme-dark {
    color: var(--white);
  
    .accesibility-dropdown__button {
      color: var(--white);
    }
  
    .accesibility-dropdown__button__label {
      color: var(--white);
    }
  
    .accesibility-dropdown__icon-wrapper {
      color: var(--white);
    }
  }
  