import { useAnimationAndVideosToggle } from '@components/common/AnimationAndVideosToggle/AnimationAndVideoContext'
import { ElgatoMediaProps } from '@components/common/ElgatoMedia/ElgatoMedia'
import { useIntersectionObserver } from '@components/common/ElgatoMedia/useIntersectionObserver'
import { VideoControlButton } from '@components/common/ElgatoMedia/VideoControlButton'
import { useMedia } from '@lib/hooks/useMedia'
import { useMobile } from '@pylot-data/hooks/use-mobile'
import cn from 'classnames'
import {
    forwardRef,
    MouseEvent,
    RefObject,
    useCallback,
    useEffect,
    useRef,
    useState
} from 'react'

interface ElgatoMediaVideoProps extends ElgatoMediaProps {
    play?: boolean
}

const ElgatoMediaVideo = forwardRef<HTMLVideoElement, ElgatoMediaVideoProps>(
    (
        {
            cloudinaryMedia,
            cloudinaryMobileMedia,
            cloudinaryPosterImage,
            cloudinaryPosterImageMobile,
            className,
            objectFit,
            style,
            autoPlay,
            muted: initialMuted = true,
            loop,
            playsInline = true,
            preload = 'auto',
            showControls,
            showMuteButton,
            showPauseButton,
            controlsPosition = 'bottom-right',
            controlsBackground = 'black',
            intersectionDisabled = false,
            size,
            showProgressIndicator = true,
            play
        },
        forwardedRef
    ) => {
        const { isAnimationStopped } = useAnimationAndVideosToggle()
        const { isMobile } = useMobile()
        const [isPlaying, setIsPlaying] = useState(
            autoPlay && !isAnimationStopped
        )
        const [wasPausedManually, setWasPausedManually] = useState(false)
        const [isMuted, setIsMuted] = useState(initialMuted)
        const [isEnded, setIsEnded] = useState(false)

        // Load video immediately in order to avoid GSAP animations to prevent re-renders that break animations
        const [shouldLoadVideo, setShouldLoadVideo] = useState(
            intersectionDisabled || preload !== 'none'
        )
        const innerRef = useRef<HTMLVideoElement>(null)
        const containerRef = useRef<HTMLDivElement>(null)

        // Use forwarded ref if provided, otherwise use inner ref
        const videoRef = (forwardedRef ||
            innerRef) as RefObject<HTMLVideoElement>

        const { src, posterImageSrc } = useMedia({
            cloudinaryMedia,
            cloudinaryMobileMedia,
            cloudinaryPosterImage,
            cloudinaryPosterImageMobile
        })

        const hasCustomControls =
            !isAnimationStopped && (showMuteButton || showPauseButton)
        const fileType = src.split('.').pop()

        const pauseVideo = useCallback(() => {
            if (!videoRef.current || isAnimationStopped) return
            videoRef.current.pause()
            setIsPlaying(false)
            setWasPausedManually(true)
        }, [isAnimationStopped, videoRef])

        const playVideo = useCallback(() => {
            if (!videoRef.current || isAnimationStopped) return
            if (isEnded) {
                videoRef.current.currentTime = 0
                setIsEnded(false)
            }
            videoRef.current.play()
            setIsPlaying(true)
            setWasPausedManually(false)
        }, [isAnimationStopped, isEnded, videoRef])

        const handlePlayPause = useCallback(
            (e: MouseEvent) => {
                e.preventDefault()
                e.stopPropagation()
                if (!videoRef.current || isAnimationStopped) return

                const isVideoPlaying = !!(
                    videoRef.current.currentTime > 0 &&
                    !videoRef.current.paused &&
                    !videoRef.current.ended &&
                    videoRef.current.readyState > 2
                )

                if (isVideoPlaying) {
                    pauseVideo()
                } else {
                    playVideo()
                }
            },
            [isAnimationStopped, pauseVideo, playVideo, videoRef]
        )

        const handleMute = (e: React.MouseEvent) => {
            e.preventDefault()
            e.stopPropagation()
            if (!videoRef.current) return
            setIsMuted((muted) => !muted)
        }

        const handleReset = (e: React.MouseEvent) => {
            e.preventDefault()
            e.stopPropagation()
            if (!videoRef.current || isAnimationStopped) return
            videoRef.current.currentTime = 0
            videoRef.current.play()
            setIsEnded(false)
            setIsPlaying(true)
        }

        useEffect(() => {
            if (isAnimationStopped) {
                videoRef.current?.pause()
                setIsPlaying(false)
                return
            }

            if (play !== undefined) {
                if (play) {
                    videoRef.current?.play()
                    setIsPlaying(true)
                } else {
                    videoRef.current?.pause()
                    setIsPlaying(false)
                }
            } else if (autoPlay && !wasPausedManually) {
                videoRef.current?.play()
                setIsPlaying(true)
            } else {
                videoRef.current?.pause()
                setIsPlaying(false)
            }
        }, [autoPlay, wasPausedManually, isAnimationStopped, play, videoRef])

        useEffect(() => {
            const video = videoRef.current
            if (!video) return

            const handleEnded = () => {
                if (!loop) {
                    setIsEnded(true)
                    setIsPlaying(false)
                }
            }

            video.addEventListener('ended', handleEnded)
            return () => {
                video.removeEventListener('ended', handleEnded)
            }
        }, [loop, videoRef])

        useIntersectionObserver(containerRef, (isIntersecting) => {
            if (isAnimationStopped || intersectionDisabled) return

            if (isIntersecting && !wasPausedManually) {
                videoRef.current?.play()
                setIsPlaying(true)
            } else {
                videoRef.current?.pause()
                setIsPlaying(false)
            }
        })
        // Use Intersection Observer to detect when video is near viewport
        useIntersectionObserver(
            containerRef,
            (isIntersecting) => {
                // Lazy load video when it comes into view, but skip if lazy loading is disabled to prevent unnecessary re-renders that could break GSAP animations
                if (intersectionDisabled || preload !== 'none') return

                if (isIntersecting && !shouldLoadVideo) {
                    setShouldLoadVideo(true)
                }
            },
            {
                rootMargin: '300px'
            }
        )

        const buttonSize = isMobile ? 'small' : size

        return (
            <>
                <div
                    ref={containerRef}
                    className={cn({
                        'h-full w-full': objectFit === 'cover'
                    })}
                >
                    {shouldLoadVideo ? (
                        <video
                            ref={videoRef}
                            poster={posterImageSrc}
                            className={cn(className)}
                            muted={isMuted}
                            playsInline={playsInline}
                            loop={loop}
                            preload={preload}
                            autoPlay={
                                play !== undefined
                                    ? play
                                    : autoPlay && !isAnimationStopped
                                    ? true
                                    : undefined
                            }
                            controls={showControls}
                            style={{
                                objectFit: objectFit || 'cover',
                                ...style
                            }}
                        >
                            <track kind="captions" />
                            <source src={src} type={`video/${fileType}`} />
                        </video>
                    ) : (
                        <img
                            src={posterImageSrc}
                            alt=""
                            className={cn(className)}
                            style={{
                                objectFit: objectFit || 'cover',
                                ...style
                            }}
                        />
                    )}
                </div>
                {(hasCustomControls ||
                    (!loop && isEnded && !isAnimationStopped)) && (
                    <div
                        className={cn(
                            'absolute flex items-center justify-center z-10',
                            {
                                'top-8px left-8px md:top-16px md:left-16px':
                                    controlsPosition === 'top-left',
                                'top-8px right-8px md:top-16px md:right-16px':
                                    controlsPosition === 'top-right',
                                'bottom-8px left-8px md:bottom-16px md:left-16px':
                                    controlsPosition === 'bottom-left',
                                'bottom-8px right-8px md:bottom-16px md:right-16px':
                                    controlsPosition === 'bottom-right'
                            }
                        )}
                    >
                        <div className="flex gap-2 text-h2">
                            {showPauseButton && !isAnimationStopped && (
                                <VideoControlButton
                                    videoRef={
                                        showProgressIndicator
                                            ? videoRef
                                            : undefined
                                    }
                                    controlsBackground={controlsBackground}
                                    size={buttonSize}
                                    onClick={
                                        !isEnded ? handlePlayPause : handleReset
                                    }
                                    ariaLabel={
                                        !isEnded
                                            ? isPlaying
                                                ? 'Pause'
                                                : 'Play'
                                            : 'Reset Playback'
                                    }
                                    iconName={
                                        !isEnded
                                            ? isPlaying
                                                ? 'pause'
                                                : 'play'
                                            : 'resetPlayback'
                                    }
                                />
                            )}
                            {showMuteButton && !isAnimationStopped && (
                                <VideoControlButton
                                    onClick={handleMute}
                                    ariaLabel={isMuted ? 'Unmute' : 'Mute'}
                                    controlsBackground={controlsBackground}
                                    iconName={isMuted ? 'volumeMute' : 'volume'}
                                    size={buttonSize}
                                />
                            )}
                        </div>
                    </div>
                )}
            </>
        )
    }
)

ElgatoMediaVideo.displayName = 'ElgatoMediaVideo'

export default ElgatoMediaVideo
