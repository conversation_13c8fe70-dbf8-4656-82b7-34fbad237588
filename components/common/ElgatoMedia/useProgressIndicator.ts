import { RefObject, useEffect, useRef } from 'react'

export const useProgressIndicator = (
    videoRef: RefObject<HTMLVideoElement>,
    circleRef: RefObject<SVGCircleElement>,
    size: 'small' | 'large'
): void => {
    // Stroke thickness is fixed at 4px in both sizes
    const radius = size === 'small' ? 16 : 20
    const circumference = 2 * Math.PI * radius

    const animationFrameRef = useRef<number>()
    const isAnimatingRef = useRef(false)

    useEffect(() => {
        if (!circleRef.current) return

        // Prepare circle stroke values once
        circleRef.current.style.strokeDasharray = `${circumference}`
        circleRef.current.style.strokeDashoffset = `${circumference}`

        const updateProgress = () => {
            if (!videoRef.current || !circleRef.current) return

            const progress = video.ended
                ? 100
                : (video.currentTime / video.duration) * 100

            const offset = circumference - (progress / 100) * circumference
            circleRef.current.style.strokeDashoffset = `${offset}`

            if (!video.ended && isAnimatingRef.current) {
                animationFrameRef.current = requestAnimationFrame(
                    updateProgress
                )
            }
        }

        // If videoRef.current is null, this is a button without progress indicator
        if (!videoRef.current) return

        const video = videoRef.current

        const handlePlay = () => {
            isAnimatingRef.current = true
            animationFrameRef.current = requestAnimationFrame(updateProgress)
        }

        const handlePause = () => {
            isAnimatingRef.current = false
            if (animationFrameRef.current) {
                cancelAnimationFrame(animationFrameRef.current)
            }
        }

        const handleEnded = () => {
            isAnimatingRef.current = false
            if (circleRef.current) {
                circleRef.current.style.strokeDashoffset = '0'
            }
            if (animationFrameRef.current) {
                cancelAnimationFrame(animationFrameRef.current)
            }
        }

        // Initial setup - if video is already playing, start animation
        if (!video.paused) {
            handlePlay()
        }

        video.addEventListener('play', handlePlay)
        video.addEventListener('pause', handlePause)
        video.addEventListener('ended', handleEnded)

        return () => {
            isAnimatingRef.current = false
            if (animationFrameRef.current) {
                cancelAnimationFrame(animationFrameRef.current)
            }
            video.removeEventListener('play', handlePlay)
            video.removeEventListener('pause', handlePause)
            video.removeEventListener('ended', handleEnded)
        }
    }, [
        videoRef.current, // Add videoRef.current as dependency to re-run effect when it changes
        size,
        circleRef,
        circumference
    ])
}
