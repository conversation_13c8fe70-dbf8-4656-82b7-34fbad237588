import { RefObject, useEffect } from 'react'

export const useParallax = (
    mainRef: RefObject<HTMLDivElement>,
    parallaxFactor: number
): void => {
    useEffect(() => {
        if (parallaxFactor && parallaxFactor !== 0 && mainRef.current) {
            const container = mainRef.current
            container.style.overflow = 'hidden'
            container.style.height = 'fit-content'

            // Make media element slightly larger and ensure it fills container
            const mediaElement = container.children[0] as HTMLElement
            const fullScale = 1 + parallaxFactor
            if (mediaElement) {
                mediaElement.style.width = '100%'
                mediaElement.style.height = '100%'
                mediaElement.style.objectFit = 'cover'
                mediaElement.style.transform = `scale(${fullScale}) translateY(0)` // Start at top
                mediaElement.style.transformOrigin = 'top'
            }

            const handleScroll = () => {
                const rect = container.getBoundingClientRect()
                const isVisible =
                    rect.top < window.innerHeight && rect.bottom > 0

                if (isVisible && mediaElement) {
                    // Calculate scroll progress
                    const start = window.innerHeight // When container enters viewport bottom
                    const end = -rect.height // When container bottom hits viewport top
                    const current = rect.top
                    const total = start - end

                    // Clamp scroll progress between 0 and 1
                    const scrollPercent = Math.max(
                        0,
                        Math.min(1, (start - current) / total)
                    )

                    // Total movement is the scaled overflow amount
                    const maxTranslate =
                        (rect.height / fullScale) * parallaxFactor
                    const translateY = -maxTranslate * scrollPercent // Move from 0 to -maxTranslate

                    mediaElement.style.transform = `scale(${fullScale}) translateY(${translateY}px)`
                }
            }

            window.addEventListener('scroll', handleScroll)
            handleScroll() // Initial position

            return () => window.removeEventListener('scroll', handleScroll)
        }
    }, [mainRef, mainRef.current, parallaxFactor])
}
