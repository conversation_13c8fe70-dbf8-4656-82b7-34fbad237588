import { CSSProperties, forwardRef, RefObject, useRef } from 'react'
import cn from 'classnames'
import s from './VideoControlButton.module.scss'
import { useProgressIndicator } from './useProgressIndicator'
import { Icon } from '@components/atoms/Icon/Icon'

type VideoControlButtonProps = {
    controlsBackground: 'black' | 'white'
    size?: 'small' | 'large'
    iconName: 'pause' | 'play' | 'resetPlayback' | 'volume' | 'volumeMute'
    onClick: (e: React.MouseEvent) => void
    ariaLabel?: string
    videoRef?: RefObject<HTMLVideoElement>
    className?: string
    style?: CSSProperties
}

export const VideoControlButton = forwardRef<
    HTMLButtonElement,
    VideoControlButtonProps
>(
    (
        {
            controlsBackground,
            size = 'large',
            iconName,
            onClick,
            ariaLabel,
            videoRef,
            className,
            style
        },
        ref
    ) => {
        const dimensions = size === 'large' ? 48 : 40
        const strokeWidth = 4
        const radius = dimensions / 2 - strokeWidth / 2
        const circumference = 2 * Math.PI * radius

        const circleRef = useRef<SVGCircleElement>(null)

        const progressColor = controlsBackground === 'black' ? 'black' : 'white'
        const restColor =
            controlsBackground === 'black'
                ? 'rgba(0,0,0,0.4)'
                : 'rgba(255,255,255,0.4)'

        useProgressIndicator(
            videoRef ? videoRef : { current: null },
            circleRef,
            size
        )

        return (
            <button
                className={cn(
                    'rounded-full flex items-center justify-center cursor-pointer relative',
                    className
                )}
                aria-label={ariaLabel}
                onClick={onClick}
                style={{ width: dimensions, height: dimensions, ...style }}
                ref={ref}
            >
                {videoRef && (
                    <svg
                        width={dimensions}
                        height={dimensions}
                        className={cn(
                            'absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-20'
                        )}
                    >
                        <circle
                            cx={dimensions / 2}
                            cy={dimensions / 2}
                            r={radius}
                            stroke={restColor}
                            strokeWidth={strokeWidth}
                            fill="transparent"
                        />
                        <circle
                            ref={circleRef}
                            cx={dimensions / 2}
                            cy={dimensions / 2}
                            r={radius}
                            stroke={progressColor}
                            strokeWidth={strokeWidth}
                            fill="transparent"
                            strokeLinecap="round"
                            transform={`rotate(-90 ${dimensions / 2} ${
                                dimensions / 2
                            })`}
                            style={{
                                strokeDasharray: `${circumference}`,
                                strokeDashoffset: `${circumference}`
                            }}
                        />
                    </svg>
                )}
                <div
                    className={cn(
                        'absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 rounded-full flex items-center justify-center transition-colors duration-300'
                    )}
                >
                    <div
                        className={cn(
                            s['video-control'],
                            'absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 rounded-full flex items-center justify-center transition-colors duration-300',
                            {
                                [s['video-control--white']]:
                                    controlsBackground === 'white',
                                [s['video-control--black']]:
                                    controlsBackground === 'black'
                            }
                        )}
                        style={{ width: dimensions, height: dimensions }}
                    />
                    <Icon
                        name={iconName}
                        className={cn(
                            'relative z-30 rounded-full w-24px h-24px',
                            {
                                'text-black': controlsBackground === 'black',
                                'text-white': controlsBackground === 'white'
                            }
                        )}
                    />
                </div>
            </button>
        )
    }
)

VideoControlButton.displayName = 'VideoControlButton'
