import { CloudinaryMedia } from '@components/common/CloudinaryMedia/Cloudinary'
import ElgatoMediaImage from '@components/common/ElgatoMedia/ElgatoMediaImage'
import ElgatoMediaVideo from '@components/common/ElgatoMedia/ElgatoMediaVideo'
import { useParallax } from '@components/common/ElgatoMedia/useParallax'
import {
    PositionContainer,
    PositionContainerProps
} from '@components/common/PositionContainer/PositionContainer'
import { useMedia } from '@lib/hooks/useMedia'
import { useMobile } from '@pylot-data/hooks/use-mobile'
import cn from 'classnames'
import { CSSProperties, forwardRef, ReactNode, Ref, useRef } from 'react'

type MediaSizing = 'auto-height' | 'auto-width' | 'constrain-ratio'

export type ElgatoMediaProps = {
    cloudinaryMedia?: CloudinaryMedia[]
    cloudinaryMobileMedia?: CloudinaryMedia[]
    cloudinaryPosterImage?: CloudinaryMedia[]
    cloudinaryPosterImageMobile?: CloudinaryMedia[]
    className?: string
    style?: CSSProperties
    mobileStyle?: CSSProperties
    objectFit?: 'cover' | 'contain' | 'fill' | 'none'
    autoPlay?: boolean
    muted?: boolean
    loop?: boolean
    playsInline?: boolean
    preload?: 'auto' | 'metadata' | 'none'
    showControls?: boolean
    showMuteButton?: boolean
    showPauseButton?: boolean
    children?: ReactNode
    sizing?: MediaSizing
    controlsPosition?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'
    controlsBackground?: 'black' | 'white'
    overlayContainers?: PositionContainerProps[]
    size?: 'small' | 'large'
    showProgressIndicator?: boolean
    parallaxFactor?: number
    meta?: {
        contentType: 'elgatoMedia'
    }
    // non contentful props
    layers?: string[]
    intersectionDisabled?: boolean
    play?: boolean
}

const ElgatoMedia = forwardRef<
    HTMLImageElement | HTMLVideoElement,
    ElgatoMediaProps
>(
    (
        {
            cloudinaryMedia,
            cloudinaryMobileMedia,
            cloudinaryPosterImage,
            cloudinaryPosterImageMobile,
            className,
            objectFit,
            sizing = 'auto-height',
            overlayContainers,
            style,
            mobileStyle,
            children,
            parallaxFactor = 0,
            ...props
        },
        ref
    ) => {
        const mainRef = useRef<HTMLDivElement>(null)
        const { isMobile } = useMobile()
        const { src, type } = useMedia({
            cloudinaryMedia,
            cloudinaryMobileMedia,
            cloudinaryPosterImage,
            cloudinaryPosterImageMobile
        })
        useParallax(mainRef, parallaxFactor)

        const mediaClasses = cn({
            'object-cover': objectFit === 'cover',
            'object-contain': objectFit === 'contain',
            'object-fill': objectFit === 'fill',
            'h-full w-auto': sizing === 'auto-width',
            'w-full h-auto': sizing === 'auto-height',
            'w-full h-full': sizing === 'constrain-ratio'
        })

        const elementStyle = isMobile ? mobileStyle : style

        return (
            <div
                className={cn('relative h-full w-full', className)}
                ref={mainRef}
            >
                {type === 'video' ? (
                    <ElgatoMediaVideo
                        ref={ref as Ref<HTMLVideoElement>}
                        key={src}
                        cloudinaryMedia={cloudinaryMedia}
                        cloudinaryMobileMedia={cloudinaryMobileMedia}
                        cloudinaryPosterImage={cloudinaryPosterImage}
                        cloudinaryPosterImageMobile={
                            cloudinaryPosterImageMobile
                        }
                        className={mediaClasses}
                        objectFit={objectFit}
                        style={elementStyle}
                        {...props}
                    />
                ) : (
                    <ElgatoMediaImage
                        ref={ref as Ref<HTMLImageElement>}
                        key={src}
                        cloudinaryMedia={cloudinaryMedia}
                        cloudinaryMobileMedia={cloudinaryMobileMedia}
                        cloudinaryPosterImage={cloudinaryPosterImage}
                        cloudinaryPosterImageMobile={
                            cloudinaryPosterImageMobile
                        }
                        className={mediaClasses}
                        objectFit={objectFit}
                        style={elementStyle}
                        sizing={sizing}
                        {...props}
                    />
                )}
                {overlayContainers?.map((container, index) => (
                    <PositionContainer key={index} {...container} />
                ))}
                {children}
            </div>
        )
    }
)

ElgatoMedia.displayName = 'ElgatoMedia'

export default ElgatoMedia
