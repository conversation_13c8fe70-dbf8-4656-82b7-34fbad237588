.carousel-container {
    @apply h-full;
    
    :global {

        .slick-prev, .slick-next {
            z-index: 2;
            width: 80px;
            height: 80px;

            button {
                width: 80px;
                height: 80px;
                border: none;
                outline: none;
                display: flex;
                align-items: center;
                justify-content: center
            }

            &:before {
                content: none ;
            }
            
        }

        .slick-prev {
            left: 0;
        }

        .slick-next {
            right: 0;
        }

        #custom-slick-dots {
            width:100%;
            background: transparent;
            position: absolute;
            height: 30px;
            bottom: 20px;

            ul {
                bottom: auto;
                padding:0;
                position: relative;
                margin: 0 auto;
            }
        }

        .slick-dots {
            width: 50%;
            min-width: 300px;
            background-color: unset;

            li {
                cursor: default;
                margin:auto;
                padding: 1rem .1rem;

                &:after {
                    content:'';
                    display: block;
                    position: relative;
                    width: 100%;
                    background: #99999940;
                    height: 3px;
                }

                &.slick-active:after :local {
                    animation-name: filling;
                    animation-duration: .35s;
                    animation-timing-function: linear;
                    animation-fill-mode: forwards;
                    -webkit-animation-name: filling;
                    -webkit-animation-duration: .35s;
                    -webkit-animation-timing-function: linear;
                    -webkit-animation-fill-mode: forwards;
                    background: #eee;
                }

                button {
                    width:100%;
                    position: relative;
                    z-index: 5;
                    transform: translateY(10px);
                   
                    &:before{
                        content: '';
                    }
                }
            }
        }
    }

    @keyframes filling {
        from {
          width: 0%;
        }
      
        to {
          width: 100%;
        }
    }
    
}