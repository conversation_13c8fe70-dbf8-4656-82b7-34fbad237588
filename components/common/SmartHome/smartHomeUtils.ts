import { CardCurrentPosition, SmartHomeProduct } from './SmartHome.interfaces'

export const generateTransform = (
    activeProduct: SmartHomeProduct | undefined,
    position: CardCurrentPosition
): string => {
    if (activeProduct) {
        const { scalePriority, translateX, translateY, scale } = activeProduct[
            position
        ]

        if (scalePriority) {
            return `scale(${scale}) translateX(${translateX}%) translateY(${translateY}%) `
        }

        return `translateX(${translateX}%) translateY(${translateY}%) scale(${scale}) `
    }

    return `translateX(0%) translateY(0%) scale(1)`
}
