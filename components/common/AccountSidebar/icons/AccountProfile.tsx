import React from 'react'

const AccountProfile = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="18"
            height="20"
            viewBox="0 0 18 20"
        >
            <g
                fill="none"
                fillRule="evenodd"
                stroke="none"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="1"
            >
                <g stroke="currentColor" transform="translate(-105 -474)">
                    <g transform="translate(-1)">
                        <g transform="translate(83 107)">
                            <g transform="translate(24 368)">
                                <g strokeWidth="2">
                                    <path d="M16 18v-2a4 4 0 00-4-4H4a4 4 0 00-4 4v2" />
                                    <circle cx="8" cy="4" r="4" />
                                </g>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </svg>
    )
}

export default React.memo(AccountProfile)
