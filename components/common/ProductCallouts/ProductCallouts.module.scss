.callout {
    padding: 24px 20px 0px 20px;
    &-heading {
        font-size: 32px;
        margin-bottom: 40px;
    }
    &-image {
        max-width: 335px;
        max-height: 335px;
        margin: auto;
    }
}

.accordion {
    @apply border-0 mr-0 #{!important};

    :global {
        .accordion-wrapper {
            @apply p-0;
            padding-bottom: 24px;

            .accordion-header {
                @apply flex flex-row-reverse justify-end;
                &-title {
                    @apply #{!important};
                    font-size: 24px !important;
                    color: var(--color) !important;
                }
            }
        }
        .accordion-body {
            @apply pl-12 font-helveticaRoman;
            font-size: 14px !important;
            padding-top: 13px !important;
            height: fit-content !important;
        }
        .accordion-content {
            color: var(--color) !important;
        }
    }
}
.circle {
    width: 24px;
    height: 24px;
    margin-right: 16px;
    color: var(--circle-color);
    border-color: var(--circle-color);
    &-close {
        top: 4px;
    }
    &-open {
        top: 4px;
        transform: rotate(45deg);
    }
}

@screen md {
    .callout {
        max-width: 1440px;
        @apply m-auto;
        &-list {
            margin-top: 6%;
            width: 291px;
        }
        &-image {
            margin-bottom: 105px;
            &-padding {
                padding-bottom: 30%;
                max-width: 400px;
                max-height: 400px;
                @screen xl {
                    padding-bottom: 36%;
                    max-width: 607px;
                    max-height: 607px;
                }
            }
        }
        &-heading {
            font-size: 60px;
            margin-bottom: 56px;
        }
        &-desktop {
            padding-bottom: 105px;
        }
    }
}
