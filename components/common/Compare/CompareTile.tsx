import React, { Dispatch, SetStateAction, SyntheticEvent } from 'react'
import { ProductPrice } from '../ProductPrice/ProductPrice'
import s from './CompareTile.module.scss'
import styles from '@pagestyles/Plp.module.scss'
import { ProductInterface } from '@pylot-data/fwrdschema'
import Image from '@corsairitshopify/corsair-image'
import Link from 'next/link'
import { useTranslation } from 'next-i18next'

interface CompareTileInterface {
    element: any
    key: number
}

const categoryUrlSuffix = '.html'

export const CompareTile = ({ element, key }: CompareTileInterface) => {
    const {
        uid,
        small_image,
        price_range,
        name,
        url_key: urlKey,
        __typename: typename
    } = element
    const { t } = useTranslation(['common'])

    const url_key = element ? urlKey : null
    const productUrl = `/p/${url_key}${categoryUrlSuffix}`

    /**
     * Handle Remove Compare Item Click
     */
    const handleRemoveCompareItem = (event: SyntheticEvent<EventTarget>) => {
        const { target } = event

        if (!(target instanceof HTMLButtonElement)) return

        const { compareId } = target.dataset
        const $correspondingCheckbox = document.getElementById(
            `checkbox-${compareId}`
        ) as HTMLInputElement

        $correspondingCheckbox.click()
    }

    return (
        <div className={s.compareTitle} key={key}>
            <button
                className={s.close}
                data-compare-id={uid}
                onClick={(event) => handleRemoveCompareItem(event)}
                type="button"
            />
            <Link
                href={productUrl}
                aria-label={`${name} - ${t('ada|Opens in the current Tab')}`}
            >
                <div aria-label={name} className={s.link}>
                    <div className={s.image}>
                        <Image
                            alt={name ? name : 'placeholder'}
                            src={small_image?.url}
                            layout="fill"
                        />
                    </div>

                    <div className={s.content}>
                        <h3 className={s.header}>{name}</h3>
                        <div className={styles['compare-tile-price']}>
                            <ProductPrice
                                className={String(typename).toLowerCase()}
                                priceRange={price_range}
                            />
                        </div>
                    </div>
                </div>
            </Link>
        </div>
    )
}
