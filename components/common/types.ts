import { BannerType } from '@components/common/HeroBanner/HeroBanner'
import { LinkResponse } from '@components/molecules/Link/Link'
import { PDPMessageProps } from '@components/molecules/PDPMessage/PDPMessage'
import {
    CardListProps,
    CardProps
} from '@components/templates/CardList/CardList'
import { ProductInterface } from '@pylot-data/fwrdschema'
import {
    ConfigurableVariant,
    ProductRetailer,
    SimpleProduct
} from '@pylot-data/pylotschema'
import {
    CTAType,
    EmbedVideoType,
    ImageLinkType,
    ImageType,
    URLType,
    VideoType
} from '../../framework/pylot/hooks/contentful/use-content-json'
import { HorizontalLightingGalleryData } from './HorizontalLightingGallery/HorizontalLightingGallery'
import type { IOverlayContent } from './OverlayProductBlock/BlockFullScreen'
import type { MessagesList } from './PDPMessages/types'
import type { IContentItem } from './ProductContentCarousel/ProductContentCarousel'
import { SmartHomeProduct } from './SmartHome/SmartHome.interfaces'
import { VerticalLightingGalleryData } from './VerticalLightingGallery/VerticalLightingGallery'
import { VidGalleryResponse } from './VidGallery/VidGallery'
import { PageTheme } from '@components/layouts/MainLayout/LayoutContext'
import { FeatureListProps } from '@components/templates/FeatureList/FeatureList'
import { MediaProps } from '@components/templates/ProductConfigurator/ProductConfigurator'
import { key } from '@components/atoms/Icon/general'

export interface Meta {
    contentType: string
}

export interface FAQItemType {
    question: string
    answer: string
    title: string
    meta: Meta
}

export interface FAQModuleType {
    title: string
    identifier: string
    heading: string
    defaultNum: number
    meta: Meta
    showMore: string
    showLess: string
    faqItem: FAQItemType[]
    useFaqSchema: boolean
}

export interface FAQModuleProps {
    faqModule: FAQModuleType
}

export interface FAQModuleCollectionType {
    faqModule: FAQModuleType
    title: string
    identifier: string
    heading: string
    defaultNum: number
    showMore: string
    showLess: string
    meta: Meta
    useFaqSchema: boolean
    faqItem: FAQItemType[]
}

export interface FAQModuleCollectionProps {
    faqModuleCollection: FAQModuleCollectionType[]
}

export interface IHeroBannerType extends BannerType {
    meta: IMeta<'heroBanner'>
}

export interface IVidGalleryType extends VidGalleryResponse {
    meta: IMeta<'vidGallery'>
}

export interface IMeta<T = string> {
    contentType: T
}

export interface ITabItem<T> {
    title?: string
    tabName: string
    url: string
    meta: IMeta<T>
    visible?: boolean
}

export interface ICalloutBlockItem {
    heading: string
    meta: IMeta<'calloutBlockItem'>
    subheading: string
    title: string
}

export interface ICalloutBlock {
    title: string
    image: ImageType
    calloutBlockItems: ICalloutBlockItem[]
    backgroundColor: string
    meta: IMeta<'calloutBlock'>
    markup: any
    url: string
    developmentUrl?: string
    script: string
}

export interface ICloudinaryVideo {
    format: string
    duration?: number
    created_at: string
    secure_url: string
    original_secure_url: string
    raw_transformation: string
    context?: {
        custom?: {
            alt?: string
            caption?: string
        }
    }
}

export interface IVideoSchema {
    name: string
    title?: string
    description?: string
    thumbnailTimeFrame?: number
    source: ICloudinaryVideo[]
}

export interface IProductContentfulResponse<T, D = string> {
    name: string
    sku: string
    shortDescription: string
    isEnabled: boolean
    isVisible: boolean
    identifier: string
    useFaqSchema: boolean
    faqModuleCollection: FAQModuleCollectionType[]
    tabs: ITabItem<T>[]
    meta: IMeta<D>
    promoMessage: {
        message: string
    }
    linkedProducts?: IProductContentfulResponse<T, D>[]
    calloutBlock: ICalloutBlock
    carouselContent: ICarouselContent
    embedVideo: EmbedVideoType
    productFeaturesTitle?: string
    features: IProductFeature[]
    verticalLightingGallery: VerticalLightingGalleryData
    horizontalLightingGallery: HorizontalLightingGalleryData
    nonInteractiveProductBlock: IProductBlocks
    packageContents?: IProductContentfulResponse<T, D>
    productMedia: MediaProps[]
    messages?: MessagesList
    videoPosition?: 'First' | 'Second' | 'Last'
    videos?: EmbedVideoType[]
    longDescription: string
    label?: string
    contentModules?: any[]
    socialDefaultMedia: ImageType
    metaDescription?: string
    metaTitle?: string
    productOptionTitle?: string
    accessoriesTitle?: string
    extendedNameAndDesc?: boolean
    youMayAlsoLikeTitle?: string
    bundleAndSaveTitle?: string
    elgatoBundleAndSaveMessage?: string
    elgatoBundleAndSaveTitle?: string
    shouldShowElgatoBundleAndSave?: boolean
    bundleCards?: CardProps[]
    onlineToDate?: string
    videoSchema?: IVideoSchema[]
    enableCanonical?: boolean
    canonicalOverride?: string
    metaRobotsOverride?: boolean
    metaRobotsOverrideFollow?: boolean
    retailerLinks?: ProductRetailer
    pdpMessages?: PDPMessageProps[]
    notSellableButton?: LinkResponse
    usRegionBuyAt?: LinkResponse[]
    notifyMeLink?: LinkResponse
    thirdPartyPromo?: IThirdPartyPromoMessage
    promoCampaign?: IThirdPartyPromoMessage
    pimId?: number
    recommendedBy?: FeatureListProps
    pageTheme?: PageTheme
    showFooter?: boolean
    buyInBulkTable?: BuyInBulkTable
}

export interface TranslationEntry {
    initial: string
    translated: string
}

export interface IGenericProductBlock<T = string> {
    heading?: string
    text?: string
    productImage: ImageType
    mobileImage?: ImageType
    meta: IMeta<T>
}

export interface IProductBlock {
    title: string
    heading: string
    image: ImageLinkType
    cta: CTAType
    meta: IMeta<'productBlock'>
    text: string
    price: number
    specialPrice: number
    link: URLType
}

export interface ILearnMoreBlock {
    title: string
    products: IProductBlock[]
    backgroundImage: ImageLinkType
    meta: IMeta<'addToCartLearnMoreBlock'>
}

export interface ICarouselContent {
    title: string
    subtitle: string
    contents: IContentItem[]
    backgroundColor: string
    headerTitle: string
    meta: IMeta<'productImageContents'>
    markup: any
    url: string
    developmentUrl?: string
    script: string
}

export interface IVerticalLightingGalleryData
    extends VerticalLightingGalleryData {
    meta: IMeta<'verticalLightingGallery'>
}

export interface ICopyBlock {
    title: string
    heading: string
    text: string
    meta: IMeta<'copyBlock'>
}

export interface ICopyBlocksWrapper {
    copyBlocks: ICopyBlock[]
    title: string
    meta: IMeta<'copyBlocksWrapper'>
}

export interface IHorizontalLightingGalleryData
    extends HorizontalLightingGalleryData {
    meta: IMeta<'horizontalLightingGallery'>
}

export interface ISmartHome {
    title: string
    video: VideoType
    backgroundImage: ImageType
    headerTitle: string
    subtitle: string
    headerIcon: ImageType
    ctaLabel: string
    ctaUrl: string
    products: SmartHomeProduct[]
    fullWidth: boolean
    meta: IMeta<'smartHome'>
}

// extend OverviewItem type with more interfaces ICalloutBlock | A | B
export type OverviewItem =
    | ICalloutBlock
    | ICarouselContent
    | ILearnMoreBlock
    | IHeroBannerType
    | IVerticalLightingGalleryData
    | ICopyBlocksWrapper
    | IHorizontalLightingGalleryData
    | IProductBlocks
    | IInteractiveCardsSection
    | ISmartHome
    | IProductCallouts
    | IVidGalleryType
    | ITestimonialCarousel
    | IOverlayContent
    | IStandardCardsSection

export interface IOverviewItems {
    overviewItems: Array<OverviewItem> | undefined
}

export interface IProductBlock {
    heading: string
    text: string
    title: string
    productImage: {
        title: string
        description: string
        file: {
            contentType: string
            details: {
                image: {
                    width: number
                    height: number
                }
                size: number
            }
            fileName: string
            url: string
        }
        meta: Record<string, unknown>
    }
    meta: IMeta<'productBlock'>
}

export interface IProductBlocks {
    title: string
    mainImage: {
        title: string
        description: string
        file: {
            contentType: string
            details: {
                image: {
                    width: number
                    height: number
                }
                size: number
            }
            fileName: string
            url: string
        }
        meta: Record<string, unknown>
    }
    backgroundImage: {
        title: string
        description: string
        file: {
            contentType: string
            details: {
                image: {
                    width: number
                    height: number
                }
                size: number
            }
            fileName: string
            url: string
        }
        meta: Record<string, unknown>
    }
    link: URLType
    heading: string
    secondaryHeading: string
    meta: IMeta<'nonInteractiveProductBlock'>
    block: IProductBlock[]
    secondaryBlock: IProductBlock[]
}

export interface IInteractiveCard {
    title: string
    backgroundImage: ImageLinkType
    subheading: string
    subheadingPlacement: 'bottom' | 'top'
    cta: CTAType
    heading: string
    text: string
    productCards: IProductBlock[]
    meta: IMeta<'interactiveCard'>
    displayAsCta: 'cta' | 'icons'
    socialIcons: ImageLinkType[]
}

export interface IInteractiveCardsSection {
    title: string
    interactiveCardItems: IInteractiveCard[]
    backgroundColor: string
    heading: string
    text: string
    meta: IMeta<'interactiveCardsSection'>
}

export interface IProductCalloutItem {
    title: string
    heading: string
    text: string
    desktopItemSide: string
    meta: IMeta<'productCalloutItem'>
}

export interface IProductCallouts {
    title: string
    heading: string
    theme: string
    image: ImageType
    meta: IMeta<'productCallout'>
    calloutItems: IProductCalloutItem[]
}

export interface ITestimonialBlock {
    title: string
    desktopMainImagePosition: string
    headingTitle: string
    mobileMainImagePosition: string
    paginationTitle: string
    text: string
    desktopTextPosition: string
    meta: IMeta<'testimonialBlock'>
    mainImage: ImageType
    logo?: ImageType
    logoHeading?: string
}

export interface ITestimonialCarousel {
    title: string
    backgroundColorHex: string
    textColor: string
    heading: string
    meta: IMeta<'testimonialCarousel'>
    testimonialBlocks: ITestimonialBlock[]
}

export interface IDarkLightTheme {
    meta: IMeta<'darkAndLightTheme'>
    title: string
    theme: 'dark' | 'light'
    backgroundDark: string
    backgroundLight: string
    colorLight: string
    colorDark: string
}

export interface IStandardCard {
    theme: IDarkLightTheme
    meta: IMeta<'standardContentCard'>
    image: ImageLinkType
    text: string
    heading: string
    title: string
}

export interface IStandardCardsSection {
    title: string
    heading: string
    standardCards: IStandardCard[]
    theme: IDarkLightTheme
    meta: IMeta<'standardCardsSection'>
}

export interface IBackgroundColor {
    colorHex: string
    gradientColor: boolean
    meta: IMeta<'backgroundColor'>
    opacity: number
    title: string
    gradient: string
}

export interface IThemeSelector {
    meta: IMeta<'pageSection'>
    theme: 'Dark' | 'Light'
    title: string
}

export interface IPageSection {
    backgroundColor: IBackgroundColor
    backgroundImage: ImageType
    backgroundImageMobile: ImageType
    meta: IMeta<'pageSection'>
    title: string
    contentModules: Array<{
        identifier: string
        [k: string]: unknown
        meta: IMeta<string>
    }>
    additionalJson: Record<string, unknown>
}

export interface IProductFeature {
    title: string
    copy: string
    image: ImageType
    meta: IMeta<'features'>
    featureTitle: string
    titleColor: string
    isAnimated?: boolean
    mobileIsNotAnimated?: boolean
    customOptions?: any
    mobileImage: ImageType
}

export interface IProductVariant {
    __typename: 'ConfigurableVariant'
    attributes: ConfigurableVariant['attributes']
    product: SimpleProduct
}

export interface IPricesBundleAlgolia {
    code: string
    msrp: number
}

export interface IBundleProductAlgolia {
    sku: string
    name: string
    image: string
    prices: IPricesBundleAlgolia[]
}

export interface IRelatedProductsData {
    relatedAccessoriesData?: ProductInterface[]
    relatedCustomerMayAlsoLikeData?: ProductInterface[]
    relatedBundleAndSaveData?: ProductInterface[]
}

export interface IThirdPartyPromoMessage {
    thirdPartyPromoName: string
    badgeText: string
    title: string
    description: string
    image: ImageType
    titleViewMore: string
    urlViewMore: string
    enable3rdPartyPromoPopup: boolean
    message: string
}

export interface BuyInBulkTable {
    [key: string]: number | boolean
}
