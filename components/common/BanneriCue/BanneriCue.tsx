import { FC } from 'react'
import type { BannerItemType } from '../Carousel/Carousel'
import s from './BanneriCue.module.scss'
import { IcueBannerItem } from './IcueBannerItem'
import cn from 'classnames'

export interface BannerResponse {
    title: string
    identifier: string
    bannerItems: BannerItemType[]
}
interface BanneriCueProps {
    content: BannerResponse | null
}

export const BanneriCue: FC<BanneriCueProps> = ({ content }) => {
    if (!content) {
        return null
    }

    const { bannerItems = [] } = content

    return (
        <div className={cn(s['banner-container'])}>
            <div className={cn(s['content-group'])}>
                {bannerItems.map((bannerItem, index) => (
                    <IcueBannerItem
                        key={index}
                        bannerItem={bannerItem}
                        classNameContainer={s['image-container']}
                    />
                ))}
            </div>
        </div>
    )
}

export default BanneriCue
