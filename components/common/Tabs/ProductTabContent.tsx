import React, { FC } from 'react'
import { SupportTabWrapper } from '@components/common/Tabs/SupportTab'
import type {
    IProductContentfulResponse,
    ITabItem
} from '@components/common/types'
import { OverviewTab } from './OverviewTab'
import DownloadTab from './DownloadsTab/DownloadsTab'
import { PackageContentsTab } from './PackageContentsTab'

export type TypeComponentsTab = keyof typeof componentsTab

interface IProductTabContentProp {
    tab: ITabItem<keyof typeof componentsTab>
    linkedProducts?: IProductContentfulResponse<TypeComponentsTab>[]
    packageContents?: IProductContentfulResponse<string>
}

interface IDynamicComponentProps {
    content: ITabItem<TypeComponentsTab>
    linkedProducts?: IProductContentfulResponse<TypeComponentsTab>[]
    packageContents?: IProductContentfulResponse<string>
}

const PlaceholderTab: FC<IDynamicComponentProps> = ({
    content
}): JSX.Element => {
    return <div>{content?.title}</div>
}

const componentsTab = {
    overviewTab: OverviewTab,
    techSpecsTab: PlaceholderTab,
    compatibilityTab: PlaceholderTab,
    downloadTab: DownloadTab,
    accessoriesTab: PlaceholderTab,
    supportTab: SupportTabWrapper,
    packageContentsTab: PackageContentsTab,
    reviewsTab: PlaceholderTab
}

const ProductTabContent: FC<IProductTabContentProp> = ({
    tab,
    linkedProducts,
    packageContents
}): JSX.Element | null => {
    if (!tab) {
        return null
    }

    const { contentType } = tab.meta
    const Component = componentsTab[
        contentType
    ] as React.FC<IDynamicComponentProps>

    if (!Component) {
        return null
    }
    return (
        <Component
            content={tab}
            linkedProducts={linkedProducts}
            packageContents={packageContents}
        />
    )
}

export default ProductTabContent
