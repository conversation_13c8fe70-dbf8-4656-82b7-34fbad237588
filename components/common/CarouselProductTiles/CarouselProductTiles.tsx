import { ImageType } from '@pylot-data/hooks/contentful/use-content-json'
import cn from 'classnames'
import { ReactChild, ReactFragment, ReactPortal, useState } from 'react'
import Slider from 'react-slick'
import { Arrow } from './Arrows'
import s from './CarouselProductTiles.module.scss'
import Slide from './Slide'

type desktopMobileImage = {
    desktop: imageComponent
    mobile: imageComponent
}

type imageComponent = {
    title: string
    image: ImageType
    url: string
    customClass: string
}

export type CSLObject = {
    identifier: string
    heading: string
    logo: ImageType
    productImages: desktopMobileImage[]
    mainImage: ImageType
}

type CslData = CSLObject[]

export type CslContainer = {
    identifier: string
    productSlide: CSLObject[]
}

export type CarouselProps = {
    content: CslContainer
}

const CarouselProductTiles = ({
    content: carouselContainer
}: CarouselProps): JSX.Element | null => {
    const [currentSlider, setCurrentSlider] = useState<number>(1)
    if (!carouselContainer) return null
    const productSlides: CslData = carouselContainer.productSlide

    const renderCustomDots = (dots: React.ReactNode): JSX.Element => {
        const sliderLength = productSlides.length
        const currentActive = currentSlider === productSlides.length

        return (
            <div className={s['dot-container']}>
                <label className="opacity-100 text-white font-bold">
                    {currentSlider}
                </label>
                <ul
                    className={cn(
                        s['product-carousel-custom-dots'],
                        'w-full flex items-center justify-center'
                    )}
                >
                    {dots}
                </ul>
                <label
                    className={`text-white font-bold ${
                        currentActive ? 'opacity-100' : 'opacity-50'
                    }`}
                >
                    {sliderLength}
                </label>
            </div>
        )
    }

    const defaultSettings = {
        autoplay: false,
        dots: true,
        infinite: true,
        speed: 500,
        slidesToShow: 1,
        slidesToScroll: 1,
        arrows: true,
        draggable: true,
        afterChange: (current: number) => setCurrentSlider(current + 1),
        prevArrow: <Arrow direction="left" s={s} />,
        nextArrow: <Arrow direction="right" s={s} />,
        appendDots: (
            dots:
                | boolean
                | ReactChild
                | ReactFragment
                | ReactPortal
                | null
                | undefined
        ) => renderCustomDots(dots)
    }

    return (
        <div>
            <Slider {...defaultSettings} className={s['slider']}>
                {productSlides.map((prod: CSLObject, key: number) => {
                    return <Slide cslData={prod} key={key} />
                })}
            </Slider>
        </div>
    )
}

export default CarouselProductTiles
