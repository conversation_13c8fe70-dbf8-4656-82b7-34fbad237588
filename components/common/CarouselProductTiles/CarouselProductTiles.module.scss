.csl-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    max-width: 1210px;
    margin: 0px auto;
}

.header-text-container {
    display: flex;
    justify-content: center;
    align-items: center;
}

.heading {
    font-size: 3.75rem;
}

.heading-logo {
    width: 100%;
    height: 100%;
}
.logo-container {
    width: 130px;
    height: 130px;
    margin-right: 10px;
    display: flex;
    justify-content: center;
    align-items: center;
}
.photo-container {
    display: flex;
    justify-content: space-between;
    margin-top: 75px;
    height: 630px;
    max-height: 630px;
}

.main-image {
    max-width: 394px;
    height: 630px;
    object-fit: cover;
    margin-left: 7px;
}

.product-tiles-container {
    max-width: 816px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-evenly;
}

.product-tile {
    height: 308px;
    width: 394px;
    transition: all 0.1s ease-in-out;
    cursor: pointer;
}

.product-tile-image {
    width: 208px;
    height: 208px;
    margin: 0 auto;
}

.product-info {
    width: 100%;
    margin-left: 16px;
    margin-bottom: 16px;
}

.product-description {
    font-size: 18px;
    margin-top: 8px;
}

.product-title {
    font-size: 24px;
}

.nav-arrow {
    border: 1px solid white;
    border-radius: 50%;
    height: 48px;
    width: 48px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
}

.chevron {
    width: 20px;
    height: 20px;
}

.slider {
    width: 90vw;
    max-width: 1440px;
    color: white;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: nowrap;
    margin: 0px auto;
    padding-bottom: 100px;
    position: relative !important;
    .slick-dots {
        li {
            background-color: white !important;
        }
        background: red !important;
    }
    :global {
        .slick-next,
        .slick-prev {
            &::before {
                content: "";
            }
        }
        .slick-dots {
            @apply max-w-none m-auto;
            display: flex !important;
            justify-content: center;
            align-items: ccenter;
            width: 692px;
            bottom: 0px;

            @media screen and (max-width: 768px) {
                padding: 1.5rem 2.5rem !important;
                bottom: 0px;
                width: 350px;
            }

            & > label {
                letter-spacing: 0.0625rem;
                transition-property: opacity;
                transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
                transition-duration: 0.3s;
                font-size: 32px;

                @media screen and (max-width: 768px) {
                    font-size: 24px;
                }
            }

            ul {
                width: 35vw;
                max-width: 549px;
                margin: 0px 24px;

                @media screen and (min-width: 1366px) {
                    width: 100%;
                }

                @media screen and (max-width: 768px) {
                    max-width: 233px;
                    width: 100%;
                }
            }

            li {
                @apply w-full m-0;
                height: 1px;
                max-width: 200px;

                @media screen and (max-width: 768px) {
                    max-width: 25%;
                }

                button {
                    @apply bg-white w-full p-2 bg-transparent flex items-center h-4;
                    background-color: transparent;

                    &::before {
                        @apply opacity-50 bg-white;
                        height: 1px;
                        content: "";
                        width: 100%;
                    }
                }

                &.slick-active {
                    button {
                        &::before {
                            @apply opacity-100 bg-white;
                        }
                    }
                }
            }
        }
    }
}

@media (max-width: 1050px) {
    .photo-container {
        display: flex;
        flex-direction: column;
        margin-top: 30px;
        max-width: 400px;
    }

    .slider {
        width: 90vw;
        padding-bottom: 50px;
    }

    .main-image {
        min-width: 375px !important;
        min-height: 220px !important;
        width: 375px;
        height: 220px;
        object-fit: cover;
        margin-left: 0px;
    }

    .product-tile {
        height: 96px;
        width: 160px;
        transition: all 0.1s ease-in-out;
        cursor: pointer;
    }

    .product-tile-image {
        width: 96px;
        height: 96px;
        margin: 0 auto;
    }
    .chevron {
        width: 16px;
        height: 16px;
    }
    .heading-logo {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
    .logo-container {
        margin-right: 10px;
        width: 55.5px;
        height: 80px;
    }
    .heading {
        font-size: 1.875rem;
    }
    .nav-arrow {
        top: 30% !important;
        width: 32px;
        height: 32px;
    }
}
