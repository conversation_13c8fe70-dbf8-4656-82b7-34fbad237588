import Image from '@corsairitshopify/corsair-image'
import {
    ImageLinkType,
    VideoType
} from '@pylot-data/hooks/contentful/use-content-json'
import cn from 'classnames'
import { useState } from 'react'
import VideoFilePlayer from '../Carousel/VideoFilePlayer'
import s from './HorizontalLightingGallery.module.scss'

const c = {
    controls: `${s['controls']} `,
    controlBtnIconContainer: `relative overflow-hidden rounded-full block w-full h-full`,
    controlBtnIconLabel: `${s['control-btn-icon-label']} hidden absolute -bottom-8 md:-bottom-14`,
    controlBtnIconLabelDefault: `${s['control-btn-icon-label']} text-center pt-3 block md:hidden`,
    controlsBtn: `${s['control-btn']} relative cursor-pointer flex flex-col items-center md:mx-5`,
    controlsBtnIcon: `${s['control-icon']} relative overflow-hidden rounded-full border-2 border-black duration-100 transition-all`,
    controlsContainer: `${s['controls-container']} relative flex-col flex z-2 mb-8`,
    lightProfileVideo: `${s['light-profile-video']} stick w-auto hidden`,
    videoContainer: `${s['video-container']} relative justify-center flex mx-auto`,
    horizontalLightingGallery: `${s['horizontal-lighting-gallery']} bg-black`
}

interface LightingProfile {
    icon: ImageLinkType
    label: string
    title: string
    video: VideoType
}

export interface HorizontalLightingGalleryData {
    controlsMobileTitle: string
    lightingProfiles: LightingProfile[]
}

const HorizontalLightingGallery: React.FC<{
    content: HorizontalLightingGalleryData
}> = ({ content }) => {
    const [
        primaryControlActiveOptionIndex,
        setPrimaryControlActiveOptionIndex
    ] = useState(0)

    const { lightingProfiles, controlsMobileTitle } = content

    return (
        <div className={c.horizontalLightingGallery}>
            <div className={c.controlsContainer}>
                <div className={c.controls}>
                    <div className="justify-center flex w-full">
                        {lightingProfiles.map((control, i) => (
                            <button
                                key={control.title}
                                onClick={() =>
                                    setPrimaryControlActiveOptionIndex(i)
                                }
                                className={cn(c.controlsBtn, {
                                    [s['active']]:
                                        i === primaryControlActiveOptionIndex
                                })}
                            >
                                <div className={c.controlsBtnIcon}>
                                    <span className={c.controlBtnIconContainer}>
                                        <Image
                                            src={control.icon.image.file.url}
                                            alt={
                                                control.icon.image.file.fileName
                                            }
                                            layout="fill"
                                        />
                                    </span>
                                </div>
                                <p className={c.controlBtnIconLabel}>
                                    <span className="hidden md:block whitespace-nowrap">
                                        {control.label}
                                    </span>
                                </p>
                            </button>
                        ))}
                    </div>
                    <p className={c.controlBtnIconLabelDefault}>
                        <span className="whitespace-nowrap">
                            {controlsMobileTitle}
                        </span>
                    </p>
                </div>
            </div>
            <div className={c.videoContainer}>
                <div>
                    {lightingProfiles.map((control, i) => (
                        <VideoFilePlayer
                            key={control.title}
                            videos={[control.video]}
                            className={cn(c.lightProfileVideo, {
                                [s['active']]:
                                    i === primaryControlActiveOptionIndex
                            })}
                        />
                    ))}
                </div>
            </div>
        </div>
    )
}

export default HorizontalLightingGallery
