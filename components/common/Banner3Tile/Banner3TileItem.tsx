import { FC } from 'react'
import type { BannerItemType } from '../Carousel/Carousel'
import s from './Banner3Tile.module.scss'
import ChevronRightBanner from '@components/icons/ChevronRightBanner'
import Image from '@corsairitshopify/corsair-image'
import { useTranslation } from 'next-i18next'

interface Banner3TileImageProps {
    bannerItem: BannerItemType
    classNameContainer?: string
}

const Banner3TileImage: FC<Banner3TileImageProps> = ({
    bannerItem,
    classNameContainer = 'image-container'
}) => {
    const { t } = useTranslation(['common'])
    const desktopUrl =
        bannerItem?.desktopMedia?.backgroundImage?.file?.url || ''
    const mobileUrl = bannerItem?.mobileMedia?.backgroundImage?.file?.url || ''
    const desktopDescription =
        bannerItem?.desktopMedia?.backgroundImage?.description || ''
    const mobileDescription =
        bannerItem?.mobileMedia?.backgroundImage?.description || ''

    const { heading, subHeading, ctaButton, logo } = bannerItem

    const { openInANewTab: newTab, url: link } = ctaButton

    return (
        <a
            className={s[classNameContainer]}
            href={link}
            target={newTab ? '_blank' : '_self'}
            rel="noreferrer"
            aria-label={
                newTab
                    ? t('ada|Opens in a new Tab')
                    : t('ada|Opens in the current Tab')
            }
        >
            <div className={s['extra-info-container']}>
                {logo && logo?.file && logo?.file?.url && (
                    <div className={s['logo-container']}>
                        <Image
                            src={logo?.file?.url}
                            alt={logo?.description || ''}
                            width={165}
                            height={86}
                        />
                    </div>
                )}
                <div className={s['heading']}>{heading}</div>
                <div className={s['sub-heading']}>{subHeading}</div>
                {ctaButton && ctaButton?.displayText && (
                    <div className={s['cta']}>
                        <p>{ctaButton?.displayText}</p>
                        <div className={s['right-arrow-wrapper']}>
                            <ChevronRightBanner />
                        </div>
                    </div>
                )}
            </div>

            {desktopUrl && (
                <div className="hidden md:block">
                    <Image
                        src={desktopUrl}
                        alt={desktopDescription || ''}
                        layout="fill"
                        objectFit="cover"
                    />
                </div>
            )}
            {(mobileUrl || desktopUrl) && (
                <div className="block md:hidden">
                    <Image
                        src={mobileUrl || desktopUrl}
                        alt={mobileDescription || desktopDescription || ''}
                        layout="fill"
                        objectFit="cover"
                    />
                </div>
            )}
        </a>
    )
}

export default Banner3TileImage
