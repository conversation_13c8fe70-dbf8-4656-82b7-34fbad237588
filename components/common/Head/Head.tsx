/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable i18next/no-literal-string */
import { FC } from 'react'
import NextHead from 'next/head'
import { SEO } from '@corsairitshopify/corsair-seo'
import { OneTrust } from '@corsairitshopify/corsair-onetrust'
import { Rollbar } from '@components/common/Rollbar'
import Script from 'next/script'
import { useRouter } from 'next/router'
import { useStoreConfig } from '@config/hooks/useStoreConfig'
import { socialDefaultContent } from '@config/seo/defaultContents'
const URL_NOT_FOUND = '/404'
const URL_PDP = '/p/[product_url]'
const rel = 'alternate'

const Head: FC = () => {
    const config = useStoreConfig()
    const gigyaLocaleSpecialCase: { [key: string]: string | undefined } = {
        pt: 'pt-br',
        zh: 'zh-cn'
    }
    const { locale, asPath, pathname } = useRouter()
    const currentLang = locale?.split('-')[0] || ''
    const currentRegion = locale?.split('-')[1] || ''
    const regionLangPath = `/${currentRegion.toLowerCase()}/${currentLang}`
    const origin = /\/[a-z]{2}\/[a-z]{2}(\/)?/g.test(config.base.url.baseUrl)
        ? config.base.url.baseUrl.replace(regionLangPath, '')
        : config.base.url.baseUrl
    const oneTrustConfig = config.base.oneTrust
    const gigyaLang = gigyaLocaleSpecialCase[currentLang] || currentLang
    const regionMapLanguages = config.base.regionMapLanguages
    let path = asPath

    if (path.includes('?')) {
        path = path.substring(0, path.indexOf('?'))
    }

    if (path.endsWith('/')) {
        path = path.slice(0, -1)
    }

    const getRegionAvailable = () => {
        switch (pathname) {
            case URL_NOT_FOUND:
            case URL_PDP:
                return []
            default:
                return regionMapLanguages
        }
    }

    const noIndex = () => {
        const generatedSet: Set<string | undefined> = new Set(
            regionMapLanguages
                .filter(({ languages }) => languages !== undefined)
                .flatMap(({ languages, redirectRegion, region }) =>
                    languages?.map(
                        (language: any) =>
                            `/${redirectRegion || region}/${language}`
                    )
                )
        )

        const [lang, region] = locale?.split('-') || ['', '']
        if (!generatedSet.has(`/${region.toLowerCase()}/${lang}`)) {
            return true
        } else {
            return false
        }
    }

    return (
        <>
            <NextHead>
                <meta
                    name="viewport"
                    content="width=device-width, initial-scale=1"
                />
                <link
                    rel="manifest"
                    href="/site.webmanifest"
                    key="site-manifest"
                />
                {!noIndex() &&
                    getRegionAvailable()?.map((country: any) =>
                        country?.redirectRegion ? (
                            <link
                                key={`${country?.redirectLanguage}-${country.region}`}
                                rel={rel}
                                hrefLang={`${country.redirectLanguage}-${country.region}`}
                                href={`${origin}${country?.redirectRegion}/${country?.redirectLanguage}${path}`}
                            />
                        ) : (
                            country.languages.map((language: any) => {
                                const href = `${origin}${country.region}/${language}${path}`
                                const hrefLang =
                                    country.region === 'ww'
                                        ? language
                                        : `${language}-${country.region}`
                                return (
                                    <link
                                        key={hrefLang}
                                        rel={rel}
                                        hrefLang={hrefLang}
                                        href={href}
                                    />
                                )
                            })
                        )
                    )}
            </NextHead>
            {oneTrustConfig?.enabled && oneTrustConfig?.dataDomainScript ? (
                <OneTrust dataDomainScript={oneTrustConfig.dataDomainScript} />
            ) : null}
            {config.base.gigya?.enabled && (
                <Script
                    id="gigya"
                    src={`https://cdns.gigya.com/js/gigya.js?apiKey=${config.base.gigya.apiKey}&lang=${gigyaLang}`}
                    type="text/javascript"
                />
            )}
            <SEO socialDefaultContent={socialDefaultContent} />
            <Rollbar />
        </>
    )
}

export default Head
