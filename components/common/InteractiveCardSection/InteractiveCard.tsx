import { CTA } from '@components/common/CTA'
import ChevronRight from '@components/icons/ChevronRight'
import Image from '@corsairitshopify/corsair-image'
import cn from 'classnames'
import React, { useMemo, VFC } from 'react'
import type { IInteractiveCard } from '../types'
import s from './InteractiveCard.module.scss'

export type InteractiveCardProps = {
    card: IInteractiveCard
    onClick?: (card: IInteractiveCard) => void
}

const c = /*tw*/ {
    interactiveCard: `${s['interactive-card']} overflow-hidden relative`,
    interactiveCardOverlay: `${s['interactive-card-overlay']} absolute flex flex-col items-start justify-end lg:flex-row lg:justify-between lg:items-end px-5 py-5 h-full w-full`,
    interactiveCardOverlayCTA: `${s['interactive-card-overlay-cta']} font-helveticaRoman uppercase leading-4`,
    interactiveCardSubheading: 'text-xl text-white uppercase pb-3 lg:pb-0'
}

const CHEVRON_COLOR = '#ECE81A'
const CARD_MOBILE_SIZE = '50vw'

const InteractiveCard: VFC<InteractiveCardProps> = ({
    card,
    onClick = () => {
        // this is intentional
    }
}) => {
    const ctaContent = useMemo(() => {
        if (card.displayAsCta === 'cta') {
            return (
                <CTA
                    cta={card.cta}
                    className={c.interactiveCardOverlayCTA}
                    rightIcon={
                        <ChevronRight
                            width="6"
                            height="11"
                            color={CHEVRON_COLOR}
                        />
                    }
                    rightIconClassName={s['interactive-card-arrow']}
                />
            )
        }

        return (
            <div className="flex pointer-events-auto">
                {card.socialIcons.map((socialIcon) => (
                    <CTA
                        cta={{
                            displayText: '',
                            openInANewTab: socialIcon.newTab,
                            meta: { contentType: 'componentCta' },
                            url: socialIcon.url
                        }}
                        showDisplayText={false}
                        className="relative w-6 h-6 bg-white rounded-full flex justify-center items-center ml-1"
                        key={socialIcon.title}
                    >
                        <Image
                            layout="fill"
                            sizes={CARD_MOBILE_SIZE}
                            src={socialIcon.image.file.url}
                        />
                    </CTA>
                ))}
            </div>
        )
    }, [card])

    const handleClick = () => {
        onClick(card)
        document.getElementById('slider-gallery-target')?.scrollIntoView({
            behavior: 'smooth',
            block: 'start',
            inline: 'start'
        })
    }
    const handleKey = (e: React.KeyboardEvent<HTMLDivElement>) => {
        if (e.code === 'Space') {
            onClick(card)
        }
    }

    return (
        <div
            className={c.interactiveCard}
            onClick={handleClick}
            role="button"
            tabIndex={0}
            onKeyDown={handleKey}
        >
            <Image
                src={card.backgroundImage?.image.file.url}
                layout="fill"
                alt={card?.backgroundImage?.image?.description || ''}
            />
            <div className={c.interactiveCardOverlay}>
                <p
                    className={cn(c.interactiveCardSubheading, {
                        [s['interactive-card-subheading-top']]:
                            card.subheadingPlacement === 'top'
                    })}
                >
                    {card.subheading}
                </p>
                {ctaContent}
            </div>
        </div>
    )
}

export default InteractiveCard
