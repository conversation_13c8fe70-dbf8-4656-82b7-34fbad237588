import React, { VFC, useState } from 'react'
import type { IInteractiveCardsSection } from '@components/common/types'

import s from './InteractiveCardSection.module.scss'
import InteractiveCard from './InteractiveCard'
import SliderGallery from './SliderGallery'

export type InteractiveCardSectionProps = {
    section: IInteractiveCardsSection
}

const c = /*tw*/ {
    interactiveCardSection: 'text-center flex flex-col items-center py-24',
    interactiveCardSectionHeading: `${s['interactive-card-section-heading']} uppercase tracking-widest mb-2`,
    interactiveCardSectionBody: `${s['interactive-card-section-body']} tracking-wider leading-7`,
    interactiveCardSectionGrid:
        'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-6'
}

const InteractiveCardSection: VFC<InteractiveCardSectionProps> = ({
    section
}) => {
    const [galleryIndex, setGalleryIndex] = useState<number>(0)
    const [showGallery, setShowGallery] = useState(false)
    const toggleGallery = (position?: number) => () => {
        if (position !== undefined) {
            setGalleryIndex(position)
        }
        setShowGallery((prev) => !prev)
    }

    return (
        <div className="relative">
            <SliderGallery
                items={section.interactiveCardItems}
                show={showGallery}
                onClose={toggleGallery()}
                slideIndex={galleryIndex}
            />

            <div className={c.interactiveCardSection}>
                <p className={c.interactiveCardSectionHeading}>
                    {section.heading}
                </p>
                <p className={c.interactiveCardSectionBody}>{section.text}</p>
                <div className={c.interactiveCardSectionGrid}>
                    {section.interactiveCardItems.map((card, index) => (
                        <InteractiveCard
                            card={card}
                            key={card.title + index}
                            onClick={toggleGallery(index)}
                        />
                    ))}
                </div>
            </div>
        </div>
    )
}

export default InteractiveCardSection
