import type { ImageType } from '@pylot-data/hooks/contentful/use-content-json'

export type IFontAwesomeIcon = {
    iconName: string
    iconType: string
}

export type Messages = {
    backgroundColorHex?: string
    icon?: ImageType
    fontAwesomeIcon: IFontAwesomeIcon
    content: string
}

export type MessagesList = {
    messages?: Messages[]
}

export interface MessagesModel {
    messages?: MessagesList
}

export interface ProductProps {
    productContentful: MessagesModel | null
}
