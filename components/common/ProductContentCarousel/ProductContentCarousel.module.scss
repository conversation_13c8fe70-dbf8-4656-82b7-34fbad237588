.product-content-slider-container {
    & > div {
        min-height: 50vh;
    }

    .product-content-slider {
        video {
            height: 50vh;
        }

        .slider-content-label {
            background: linear-gradient(
                0deg,
                rgba(0, 0, 0, 0.7) 5%,
                rgba(0, 0, 0, 0.6) 20%,
                rgba(0, 0, 0, 0.5) 40%,
                rgba(0, 0, 0, 0.1) 70%,
                rgba(0, 0, 0, 0.05) 90%,
                transparent
            );

            label {
                background: rgba(55, 55, 55, 0.8);
                letter-spacing: 0.1875rem;
            }
        }
    }

    :global {
        .slick-slider {
            touch-action: manipulation;
            -ms-touch-action: manipulation;
        }

        .slick-slide {
            @apply md:px-4;
            div {
                width: 100% !important;

                div {
                    span {
                        width: 100% !important;
                    }
                }

                img {
                    width: 100% !important;
                    height: 50vh !important;
                }
            }
        }

        .slick-arrow {
            @apply flex items-center justify-center w-2/12 h-full absolute;
            z-index: 8;
            transition-property: opacity;
            transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
            transition-duration: 0.3s;

            &::before {
                display: none;
            }

            &.slick-prev {
                @apply left-0;
                background: linear-gradient(90deg, #000, transparent);
            }

            svg {
                @media screen and (max-width: 768px) {
                    width: 32px;
                    height: 32px;
                }
            }

            &.slick-next {
                @apply right-0;
                background: linear-gradient(270deg, #000, transparent);
            }
        }

        .slick-dots {
            @apply flex items-center justify-center max-w-none w-full md:-bottom-24 -bottom-20 m-auto;
            display: flex !important;

            @media screen and (max-width: 768px) {
                padding: 1.5rem 2.5rem !important;
            }

            & > label {
                letter-spacing: 0.0625rem;
                transition-property: opacity;
                transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
                transition-duration: 0.3s;
                font-size: 32px;

                @media screen and (max-width: 768px) {
                    font-size: 24px;
                }
            }

            ul {
                width: 35vw;
                max-width: 549px;
                margin: 0px 24px;

                @media screen and (min-width: 1366px) {
                    width: 100%;
                }

                @media screen and (max-width: 768px) {
                    max-width: 233px;
                    width: 100%;
                }
            }

            li {
                @apply w-full m-0;
                height: 1px;
                max-width: 200px;

                @media screen and (max-width: 768px) {
                    max-width: 25%;
                }

                button {
                    @apply bg-white w-full p-2 bg-transparent flex items-center h-4;
                    background-color: transparent;

                    &::before {
                        @apply opacity-50 bg-white;
                        height: 1px;
                        content: '';
                        width: 100%;
                    }
                }

                &.slick-active {
                    button {
                        &::before {
                            @apply opacity-100 bg-white;
                        }
                    }
                }
            }
        }
    }
}
