.product {
    @apply relative flex flex-col flex-1;

    & &__cta {
        @apply text-content-blue z-1 absolute  transform -translate-x-1/2 w-fit;
        font-family: Univers55Roman, sans-serif !important;
        left: 50%;
        top: 8px;
        width: calc(100% - 40px);
        line-height: 14px;
        justify-content: center !important;
        font-size: 14px;
        padding: 6px 12px 6px !important;
        gap: 8px !important;
        min-height: 32px;
        letter-spacing: 0.25px;

        @screen md {
            line-height: 16px;
            padding: 8px 12px 8px 12px !important;
            font-size: 16px;
        }
    }

    &__image {
        @apply bg-q-3;
        @apply border border-transparent;
        border-radius: 8px;

        @screen md {
            min-width: 176px;
        }

        img {
            border-radius: 8px;
            overflow: hidden;
        }

        &--selected {
            @apply border border-blue
        }

        &--disabled {
            @apply opacity-50
        }
    }
    & &__checkbox {
        span {
            display: none;
        }

        &--round [type='checkbox'] {
            border-color: var(--light-grey-2);
            &:checked {
                border-color: var(--content-blue);
                background-size: auto;
                @apply bg-white;
                width: 16px;
                height: 16px;
                background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iOCIgaGVpZ2h0PSI4IiB2aWV3Qm94PSIwIDAgOCA4IiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgo8Y2lyY2xlIGN4PSI0IiBjeT0iNCIgcj0iNCIgZmlsbD0iIzFDNDBGRiIvPgo8L3N2Zz4K");
            }
        }
    }

    & &__checkbox, & &__cart-icon {
        @apply absolute top-6px right-6px z-1;
    }

    &__price {
        padding-bottom: 16px;

        @apply flex items-baseline;

        div:nth-child(2) {
            margin-left: 6px;
            font-size: 12px;
            padding-top: 5px;
        }
    }
    &--product-swtach {
        width: 88px;
        text-align: center;
        align-items: center;
    }
    &__colorSwatch {
        width: 42px;
        height: 42px
    }
    &__colorSwatch--disabled {
        opacity: 0.5;
        position: relative;
    }
    &__colorSwatch--disabled:after {
        content: " ";
        position: absolute;
        background-color: #525252;
        height: 2px;
        width: 112%;
        display: block;
        transform: rotate(-45deg);
        top: 87%;
        left: 13%;
        transform-origin: left bottom;
        box-sizing: border-box;
    }
    &__colorSwatch &__colorSwatch__item {
        border: solid 2px #525252;
    }
    &__colorSwatch--selected {
        border: solid 2px var(--content-blue);
    }
    &__colorSwatch__Black &__colorSwatch__item {
        @apply bg-black;
    }
    &__colorSwatch__White &__colorSwatch__item {
        @apply bg-white;
    }
}

.tooltip {
    @apply hidden md:block;
    position: absolute;
    transform: translateY(-6px);
    z-index: 10;
    svg {
        width: 14.67px;
        margin-top: 8px;
        height: 14.67px;
        color: #5f6062;
    }
}

.content-tooltip {
    @apply text-dark-grey-2 absolute font-univers55Roman text-dark-grey-3;
    background-color: #ffff;
    border: 1px solid #666666;
    border-radius: 6px;
    font-size: 12px;
    bottom: 25px;
    padding: 15px 0px;
    width: 160px;
    z-index: 2;

    ul {
        list-style-type: disc;
        padding-left: 27px;
    }
}

/* page theme dark */
.product.page-theme-dark {
    color: var(--primitive-gray-30);

    svg {
        color: var(--white);
    }
}

.product.page-theme-neo.product--product-variant {
    .product__image {
        border-width: 2px;
        border-color: white;

        &--selected {
            border-color: var(--message-colors-info-color-info-blue);
        }


        &--disabled {
            @apply opacity-50
        }
    }
}
