import { PSDStateContext } from '@components/common/PSDStateProvider/PSDStateContext'
import { FC, ReactNode, useState, useCallback } from 'react'
import { buildImageURLWithLayers } from './psdUtils'

export const buildImageURLWithPreload = async (
    url: string,
    baseLayer: string,
    layers: string[]
): Promise<string> => {
    const fullPath = buildImageURLWithLayers(url, [baseLayer, ...layers])
    const fallbackPath = buildImageURLWithLayers(url, [baseLayer])

    const preloadImage = (url: string): Promise<boolean> => {
        return new Promise((resolve) => {
            if (Image) {
                const img = new Image()
                img.onload = () => resolve(true)
                img.onerror = () => resolve(false)
                img.src = url
            }
        })
    }

    const imageExists = await preloadImage(fullPath)
    return imageExists ? fullPath : fallbackPath
}

export const PSDStateProvider: FC<{ children: ReactNode }> = ({ children }) => {
    const [baseLayer, setBaseLayer] = useState<string>('')
    const [layers, setLayers] = useState<string[]>([])

    const toggleLayer = useCallback(
        (layer: string) => {
            if (layers.includes(layer)) {
                setLayers((prev) => prev.filter((l) => l !== layer))
            } else {
                setLayers((prev) => [...prev, layer])
            }
        },
        [layers]
    )

    const addLayer = useCallback((layer: string) => {
        setLayers((prev) => (prev.includes(layer) ? prev : [...prev, layer]))
    }, [])

    const removeLayer = useCallback((layer: string) => {
        setLayers((prev) => prev.filter((l) => l !== layer))
    }, [])

    const resetLayers = useCallback(
        (layers?: string[]) => {
            setLayers(layers ?? [])
        },
        [setLayers]
    )
    const buildImageURL = useCallback(
        (url: string, justBaseLayer = true): string => {
            return buildImageURLWithLayers(
                url,
                justBaseLayer ? [baseLayer] : [baseLayer, ...layers]
            )
        },
        [baseLayer, layers]
    )

    return (
        <PSDStateContext.Provider
            value={{
                baseLayer,
                layers,
                addLayer,
                removeLayer,
                toggleLayer,
                resetLayers,
                buildImageURL,
                setBaseLayer
            }}
        >
            {children}
        </PSDStateContext.Provider>
    )
}
