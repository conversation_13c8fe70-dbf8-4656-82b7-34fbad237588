import { buildImageURLWithLayers } from '@components/common/PSDStateProvider/psdUtils'
import { useContext, useEffect, useState } from 'react'
import { PSDStateContext } from './PSDStateContext'

export const usePSDSource = (src: string, customLayers?: string[]): string => {
    const { baseLayer, layers, buildImageURL } = useContext(PSDStateContext)
    const [actualSrc, setActualSrc] = useState(src)

    const layersToUse = customLayers ?? layers

    useEffect(() => {
        const preloadImage = async () => {
            if (src.endsWith('.psd')) {
                // Try loading with layers
                const withLayers = customLayers
                    ? buildImageURLWithLayers(src, customLayers)
                    : buildImageURL(src, false)

                const img = new Image()
                try {
                    await new Promise((resolve, reject) => {
                        img.onload = resolve
                        img.onerror = reject
                        img.src = withLayers
                    })
                    setActualSrc(withLayers)
                    return
                } catch (e) {
                    // If loading with layers fails and no custom layers, try base layer
                    if (!customLayers) {
                        const justBaseLayer = buildImageURL(src)
                        try {
                            await new Promise((resolve, reject) => {
                                img.onload = resolve
                                img.onerror = reject
                                img.src = justBaseLayer
                            })
                            setActualSrc(justBaseLayer)
                            return
                        } catch (e) {
                            // If both fail, use original URL
                            setActualSrc(src)
                        }
                    } else {
                        // With custom layers, just use original URL if layers fail
                        setActualSrc(src)
                    }
                }
            } else {
                // For non-PSD files, use normal URL building
                setActualSrc(
                    layersToUse
                        ? buildImageURLWithLayers(src, layersToUse)
                        : buildImageURL(src)
                )
            }
        }

        preloadImage()
    }, [src, layersToUse, buildImageURL, customLayers])

    if (src.endsWith('psd') && layersToUse.length === 0 && baseLayer === '') {
        return ''
    }

    return actualSrc
}
