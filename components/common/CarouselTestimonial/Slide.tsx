import s from './CarouselTestimonial.module.scss'
import { CSLTestimonialObject } from './CarouselTestimonial'

interface SliderProps {
    cslData: CSLTestimonialObject
    key: number
}

const Slide = ({ cslData, key }: SliderProps) => {
    return (
        <div className={s['csl-wrapper']} key={key}>
            <img
                className={s['main-image']}
                src={`${cslData.mainImage.file.url}`}
                alt={cslData.mainImage.file.fileName}
            />
            <div className={s['logo-text-container']}>
                <img
                    className={s['logo']}
                    src={`${cslData.logo.file.url}`}
                    alt={cslData.logo.file.fileName}
                />
                <p className={s['testimonial-text']}>{cslData.text}</p>
                <p className={s['testimonial-subheading']}>
                    {cslData.subheading}
                </p>
            </div>
        </div>
    )
}

export default Slide
