import type {
    ConfigurableCartItem,
    ConfigurableProduct,
    GiftCardCartItem,
    SimpleCartItem
} from '@pylot-data/pylotschema'
import { ProductStockStatus } from '@pylot-data/enums/ProductStockStatus.d'
import { CONFIGURABLE_CART_ITEM } from '../CartItem'

export type CartItemData = {
    imageUrl: string
    imageTitle: string
    stockStatus: ProductStockStatus | null
}

export const getCartItemData = (
    item: SimpleCartItem | ConfigurableCartItem | GiftCardCartItem
): CartItemData => {
    const product = item.product

    if (!product) {
        return {
            imageUrl: '',
            imageTitle: '',
            stockStatus: null
        }
    }

    if (item.__typename !== CONFIGURABLE_CART_ITEM) {
        return {
            imageUrl: product.thumbnail?.url || product.image?.url || '',
            imageTitle: product.thumbnail?.label || product.image?.label || '',
            stockStatus: product.stock_status ?? null
        }
    }

    const selectedOptionIds = item.configurable_options.reduce(
        (result: string[], option) => {
            if (option?.configurable_product_option_value_uid) {
                return [...result, option.configurable_product_option_value_uid]
            }
            return result
        },
        []
    )

    const selectedVariant = (product as ConfigurableProduct)!.variants!.find(
        (variant) => {
            if (!variant) {
                return false
            }
            let result = true
            variant.attributes?.forEach((value) => {
                if (!value || !selectedOptionIds.includes(value.uid)) {
                    result = false
                }
            })
            return result
        }
    )
    return {
        imageUrl:
            selectedVariant?.product?.thumbnail?.url ??
            product?.thumbnail?.url!,
        imageTitle:
            selectedVariant?.product?.thumbnail?.label ??
            product?.thumbnail?.label!,
        stockStatus: selectedVariant?.product?.stock_status ?? null
    }
}
