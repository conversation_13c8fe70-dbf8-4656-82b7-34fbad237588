.root {
    @apply fixed inset-0 h-full w-full;
    z-index: 10020;
    pointer-events: none;
    @screen md {
        top: 0px;
    }
}

.sidebar-main {
    @apply absolute inset-0 overflow-hidden;
}
.sidebar-overlay {
    @apply absolute inset-0 transition-opacity bg-secondary opacity-0 duration-100 ease-linear cursor-default;
    backdrop-filter: blur(0.8px);
    -webkit-backdrop-filter: blur(0.8px);
}

.sidebar--pdp {
    background: none;
}

.sidebar--pdp .sidebar-container {
    /* This solutions does not work on pages without above the fold panel */
    @screen lg {
        top: 64px;
        height: calc(100% - 64px);
    }
    @media screen and (min-width: 1200px) {
        top: 125px;
        height: calc(100% - 125px);
    }
}

.sidebar-container {
    @apply fixed top-0 right-0 flex ml-auto;
    border:none;
    filter: drop-shadow(0px 1px 34px rgba(0, 0, 0, 0.8));
    width: 100%;
    height: 100%;
    pointer-events: auto;
    @screen md {
        filter: none;
        width: 513px;
    }

    &-inner-wrapper {
        @apply h-full w-full md:w-full;
    }
    &-inner {
        @apply h-full flex flex-col text-base bg-accents-2 shadow-xl overflow-y-auto overflow-x-hidden;
        -webkit-overflow-scrolling: touch !important;
    }
}
