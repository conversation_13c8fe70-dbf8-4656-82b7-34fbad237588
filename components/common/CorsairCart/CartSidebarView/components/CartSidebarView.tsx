import { useUI } from '@corsairitshopify/pylot-ui/context'

import { useCart } from '@lib/cart-manager'
import cn from 'classnames'
import React, { useEffect, useState } from 'react'
import s from '../CartSidebarView.module.scss'
import CartToast from './CartToast'
import EmptyCartView from './EmptyCartView'
import CartItemsView from './CartItemsView'
import CartSuccessView from './CartSuccessView'
import CartSideBarHeader from './CartSideBarHeader'
import CartErrorView from './CartErrorView'

const CartSidebarView = (): JSX.Element => {
    const { LoadingIndicator, isMinicartLoading } = useUI()
    const { data } = useCart()
    const [innerHeight, setInnerHeight] = useState(0)

    const items = data?.data?.cart?.items ?? []
    const isEmpty = !items?.length

    const error = null
    const success = null

    const handleResize = () => {
        setInnerHeight(window.innerHeight)
    }

    useEffect(() => {
        if (typeof window !== 'undefined') {
            handleResize()
            window.addEventListener('resize', handleResize)
        }

        return () => {
            window.removeEventListener('resize', handleResize)
        }
    }, [])

    return (
        <div
            className={cn(
                s.root,
                {
                    [s.empty]: error,
                    [s.empty]: success,
                    [s.empty]: isEmpty
                },
                'cartSideBar-root'
            )}
            style={{ height: `${innerHeight}px` }}
        >
            <CartToast />
            <CartSideBarHeader isEmpty={isEmpty} />

            {isMinicartLoading ? (
                <LoadingIndicator />
            ) : isEmpty ? (
                <EmptyCartView />
            ) : error ? (
                <CartErrorView />
            ) : success ? (
                <CartSuccessView />
            ) : (
                <CartItemsView />
            )}
        </div>
    )
}

export default CartSidebarView
