import { Button } from '@components/molecules/Button/Button'
import { useTranslation } from 'next-i18next'
import { FC } from 'react'
import { useUI } from '@corsairitshopify/pylot-ui/context'

const EmptyCartView: FC = () => {
    const { t } = useTranslation('common')
    const { closeSidebar } = useUI()

    const handleClose = () => {
        closeSidebar()
    }
    return (
        <div className="bg-bg-grey pt-40 flex flex-col px-16px h-full items-center text-charcoal text-center">
            <h5 className="mb-8px">{t('cart|Your cart is empty')}</h5>
            <div className="mb-24px">
                {t(
                    "cart|Don't know where to start? Check out our latest deals."
                )}
            </div>
            <Button
                onClick={handleClose}
                href="/s/store"
                label={t('cart|See whats new')}
            >
                {t('cart|See whats new')}
            </Button>
        </div>
    )
}

export default EmptyCartView
