/* eslint-disable i18next/no-literal-string */
import { Button } from '@components/molecules/Button/Button'
import React, { FC } from 'react'
import { useAccordion } from '../useAccordion'
import { ChevronUp, ChevronDown } from '@components/icons'
import s from './CountrySelector.module.scss'
import cn from 'classnames'
import { useRegionGroup } from './useRegionGroup'
import { useTranslation } from 'next-i18next'

export type CountrySelectorProps = {
    selectedCountry: string
    onSelectCountry: (country: string) => void
}

export const CountrySelector: FC<CountrySelectorProps> = ({
    selectedCountry = '',
    onSelectCountry = () => {
        //
    }
}) => {
    const { icon, toggle, show } = useAccordion({
        showICon: <ChevronUp />,
        hideIcon: <ChevronDown />
    })

    const REGION_GROUP = useRegionGroup()

    const { t } = useTranslation('common')

    const regionList = Object.entries(REGION_GROUP).map(
        ([region, countries]) => {
            return (
                <ul key={region}>
                    <li
                        className={cn(
                            'body-copy',
                            s['country-selector__country-region'],
                            s['country-selector__wrap']
                        )}
                    >
                        {region}
                    </li>
                    <li>
                        <ul className={s['country-selector__country-list']}>
                            {Object.entries(countries).map(
                                ([countryName, countryCode]) => {
                                    return (
                                        <li
                                            key={countryCode}
                                            className={cn(
                                                s[
                                                    'country-selector__country-item'
                                                ],
                                                s['country-selector__wrap']
                                            )}
                                        >
                                            <Button
                                                variant="tertiary"
                                                size="sm"
                                                color={
                                                    selectedCountry ===
                                                    countryCode
                                                        ? 'blue'
                                                        : 'dark'
                                                }
                                                className={cn(
                                                    'w-full',
                                                    s[
                                                        'country-selector__country-button'
                                                    ]
                                                )}
                                                onClick={() => {
                                                    onSelectCountry(countryCode)
                                                    toggle()
                                                }}
                                                label={countryName}
                                            >
                                                {countryName}
                                            </Button>
                                        </li>
                                    )
                                }
                            )}
                        </ul>
                    </li>
                </ul>
            )
        }
    )

    return (
        <div>
            <div className={s['country-selector__wrap']}>
                <Button
                    variant="tertiary"
                    color="dark"
                    onClick={toggle}
                    className="w-full"
                    label={t('cart|select_your_country')}
                >
                    {t('cart|select_your_country')} {icon}
                </Button>
            </div>

            {show && (
                <div className={s['country-selector__list']}>{regionList}</div>
            )}
        </div>
    )
}
