.container {
    padding: 0 24px;
}

.recommended-products {
    @apply relative w-full transition-all duration-300 transform z-0 bg-black;
    --transform-translate-y: calc(100% - 30px);

    .recommended-area {
        @apply h-0 overflow-hidden;
    }

    &.active {
        .recommended-area {
            @apply h-auto;
        }

        --transform-translate-y: 0;

        .open-recommended-products-btn {
            transform: rotate(0deg);
        }
    }

    .open-recommended-products-btn {
        color: white;
        background-image: url(../../../assets/arrow-down.svg);
        background-position: center center;
        background-repeat: no-repeat;
        font-size: 0;
        padding: 10px;
        transform: rotate(180deg);
    }
}

.title-bar {
    @apply leading-5 text-primary font-bold uppercase;
    letter-spacing: 1.75px;
    font-size: 14px;
    padding: 6px 15px;
    height: 30px;
    max-height: 30px;
}

.container-title {
    @apply flex justify-between    pt-8 pb-8;

    .product-name {
        @apply font-univers67BoldCondensed text-charcoal font-bold uppercase;
        line-height: 120%;
        width: 80%;
        font-size: 18px;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
}

.price-text {
    & div:nth-child(2) {
        font-size: 12px;
        text-align: right;
        margin-top: 4px;
    }
}

.container-description {
    @apply flex flex-col;

    @screen md {
        @apply flex flex-row justify-between pb-6;
    }

    .container-button-add {
        @apply w-full flex justify-end items-center mt-8;
        padding-bottom: 20px;

        button {
            margin-top: 0px;
        }

        svg {
            margin-right: 29px;
        }

        @screen md {
            @apply block mt-0 pb-0;
            width: auto;

            button {
                margin-top: 14px;
            }

            svg {
                margin-right: 0px;
            }
        }
    }

    .container-short-description {
        font-size: 12px;
        line-height: 120%;
        color: #000000;
    }

    ul {
        @apply pl-7;

        li {
            list-style: disc;
            line-height: 140%;
        }
    }
}

.container-button-add {
    .arrow-discover {
        svg {
            width: 11px;
            height: 5px;
        }
    }

    .title-discover {
        @apply font-normal flex justify-end    items-center font-univers55Roman uppercase text-charcoal text-right cursor-pointer;

        font-size: 12px;
        line-height: 12px;
        letter-spacing: 1px;

        svg {
            margin-left: 14px;
        }
    }

    .btn-checkout {
        @apply opacity-100 bg-content-blue text-white rounded-lg border-none font-normal;
        height: 32px;
        width: 134px;
        margin-top: 14px;
        line-height: 16px;

        span {
            @apply font-univers55Roman;
            font-size: 16px;
        }
    }
}
