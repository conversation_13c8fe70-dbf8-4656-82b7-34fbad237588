import { AuthWrapper } from '@components/common/AuthWrapper'
import { useLayoutContext } from '@components/layouts/MainLayout/LayoutContext'
import {
    BlackFridayPopup,
    PATHS_BLACK_FRIDAY_POPUP_HIDDEN
} from '@components/molecules/BlackFridayPopup/BlackFridayPopup'
import {
    DealsBanner,
    PATHS_DEALS_BANNER_HIDDEN
} from '@components/molecules/DealsBanner/DealsBanner'
import { PATHS_DEALS_POPUP_HIDDEN } from '@components/molecules/DealsPopup/DealsPopup'
import { FloatingActionButton } from '@components/molecules/FloatingActionButton/FloatingActionButton'
import Footer from '@components/organisms/Footer/Footer'
import Header from '@components/organisms/Header/Header'
import { HeaderContextProvider } from '@components/organisms/Header/HeaderContext'
import {
    getTotalNavHeight,
    MOBILE_BREAKPOINT
} from '@components/organisms/Header/headerUtils'
import { useStoreConfig } from '@config/index'
import { useUI } from '@corsairitshopify/pylot-ui/context'
import { useCart } from '@lib/cart-manager'
import { useSsoRedirect } from '@pylot-data/hooks/sso-login/use-sso-redirect'
import cn from 'classnames'
import dynamic from 'next/dynamic'
import { useRouter } from 'next/router'
import { FC, useEffect, useRef, useState } from 'react'
import s from './MainLayout.module.scss'
const Sidebar = dynamic(
    () => import('@components/common/CorsairCart/SidebarRootView')
)

const CartSidebarView = dynamic(
    () =>
        import(
            '@components/common/CorsairCart/CartSidebarView/components/CartSidebarView'
        )
)

const MINI_CART_PARAM = 'mini-cart-open'
const MAIN_MENU_HEIGHT = 125
export const MainLayout: FC<any> = ({ children, authRequired, pageProps }) => {
    const { displaySidebar, openSidebar } = useUI()
    const router = useRouter()
    const headerRef = useRef<HTMLDivElement>(null)
    const [mobileMenuActive, setMobileMenuActive] = useState(false)
    const [mainContentPosition, setMainContentPosition] = useState(
        MAIN_MENU_HEIGHT
    )
    const [
        isGeolocationBannerVisible,
        setIsGeolocationBannerVisible
    ] = useState(false)
    const [isMininavUpdated, setIsMininavUpdated] = useState(false)
    const { navHeaderOverlay, pageTheme, showFooter } = useLayoutContext()
    useSsoRedirect()
    const { base } = useStoreConfig()
    useEffect(() => {
        if (base.externalUrls.includes(window.location.pathname)) {
            if (!localStorage.getItem('externalReload')) {
                localStorage.setItem('externalReload', '1')
                window.location.reload()
            } else {
                localStorage.removeItem('externalReload')
            }
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [router])
    const { data } = useCart()
    const isFetched = !!data?.data?.cart ?? false
    useEffect(() => {
        if (
            router.query[MINI_CART_PARAM] === 'true' ||
            router.asPath.includes(MINI_CART_PARAM)
        ) {
            // wait for cart data to be fetched
            if (isFetched) {
                openSidebar()
            }
        }
    }, [isFetched])
    useEffect(() => {
        const mainHeader =
            document?.querySelector('.main-header-wrapper')?.clientHeight || 0
        const overlayNavHeight = navHeaderOverlay ? 0 : mainHeader
        setMainContentPosition(
            window?.innerWidth >= MOBILE_BREAKPOINT
                ? getTotalNavHeight() + overlayNavHeight
                : overlayNavHeight
        )
        const resizeHandler = () => {
            if (document) {
                const mainNav =
                    document?.querySelector('.main-header-wrapper')
                        ?.clientHeight || 0
                const overlayNavResize = navHeaderOverlay ? 0 : mainNav
                setMainContentPosition(
                    window?.innerWidth >= MOBILE_BREAKPOINT
                        ? getTotalNavHeight() + overlayNavResize
                        : overlayNavResize
                )
            }
        }
        window?.addEventListener('resize', resizeHandler)
        return () => window?.removeEventListener('resize', resizeHandler)
    }, [
        isGeolocationBannerVisible,
        isMininavUpdated,
        router.asPath,
        navHeaderOverlay,
        mainContentPosition
    ])
    const isProductOrContentPage =
        router.asPath.includes('/p/') || router.asPath.includes('/s/')

    const pathWithoutLocale = router.asPath
    const pathWithoutHash = pathWithoutLocale.replace(/#.*$/, '')

    const showDealsPopup =
        !PATHS_DEALS_POPUP_HIDDEN.includes(pathWithoutHash) &&
        !PATHS_DEALS_POPUP_HIDDEN.includes(pathWithoutLocale)

    const showBlackFridayPopup =
        !PATHS_BLACK_FRIDAY_POPUP_HIDDEN.includes(pathWithoutHash) &&
        !PATHS_BLACK_FRIDAY_POPUP_HIDDEN.includes(pathWithoutLocale)

    const showDealsBanner =
        !PATHS_DEALS_BANNER_HIDDEN.includes(pathWithoutHash) &&
        !PATHS_DEALS_BANNER_HIDDEN.includes(pathWithoutLocale)

    // const handleCloseDealsBanner = () => {
    //     setShowDealsBanner
    // }

    return (
        <div
            className={cn(s.root, {
                [s[`page-theme-${pageTheme}`]]: pageTheme
            })}
        >
            <HeaderContextProvider subMenu="">
                <div
                    ref={headerRef}
                    className={cn([s.header, 'header-wrapper'])}
                >
                    <Header
                        onMobileMenuOpen={(open: boolean) => {
                            setMobileMenuActive(open)
                        }}
                        isGeolocationBannerVisible={isGeolocationBannerVisible}
                        setIsGeolocationBannerVisible={
                            setIsGeolocationBannerVisible
                        }
                        onMininavUpdated={() => setIsMininavUpdated(true)}
                        mainNavigation={pageProps.mainNavigation}
                        miniNavigation={pageProps.miniNavigation}
                        marketingMessages={pageProps.marketingMessages}
                        brandAssets={pageProps.brandAssets}
                    />
                </div>
            </HeaderContextProvider>
            <div id="cart-sidebar-root" role="dialog">
                <Sidebar open={displaySidebar} headerRef={headerRef}>
                    {displaySidebar && <CartSidebarView />}
                </Sidebar>
            </div>
            {showDealsBanner && <DealsBanner />}
            {!mobileMenuActive && showBlackFridayPopup && <BlackFridayPopup />}
            <main
                id="contentarea"
                className={cn(s.main)}
                style={{
                    marginTop: mainContentPosition
                }}
            >
                <AuthWrapper authRequired={authRequired}>
                    {children}
                </AuthWrapper>
            </main>
            {isProductOrContentPage ? showFooter && <Footer /> : <Footer />}
            {pageTheme !== 'neo' && <FloatingActionButton fixed />}
        </div>
    )
}
