.comparison-content {
    max-width: 285px;
    @apply p-12px h-auto flex flex-col justify-center;
    border-radius: 8px;
    gap: 16px;

    + .comparison-content {
        margin-top: 8px;
    }

    @screen md {
        @apply p-8;
    }

    &__title {
        @apply text-right mt-4px;
    }

    &__text {
        @apply text-center;
        @apply text-xs-copy-md-max;
        @apply leading-tight;

        @screen md {
            @apply font-univers55Roman;
            @apply text-small-copy;
            @apply leading-extra-tight-md-max;
        }
    }

    &__icon {
        @apply mx-auto;
        width: 16px;
        height: 16px;

        @screen md {
            width: 24px;
            height: 24px;
        }
    }

    &__image {
        @apply relative flex justify-center;
        border-radius: 8px;
        overflow: hidden;
    }

    &__logo {
        @apply mx-auto;
        height: 24px;
    }

    &--image {
        padding: 0 0 16px;
    }

    &--title {
        padding-left: 16px;
        padding-right: 16px;
    }

    &--button {
        padding: 0;

        > a {
            @screen md-max {
                width: auto;
                height: 43px;
                padding: 8px;
            }
        }
    }
}
