import { FooterColumn } from '@components/molecules/FooterColumn/FooterColumn'
import { FooterColumnConnect } from '@components/molecules/FooterColumn/FooterColumnConnect'
import { Link, LinkResponse } from '@components/molecules/Link/Link'
import { LinkListProps } from '@components/organisms/LinkList/LinkList'
import { useContentJson } from '@pylot-data/hooks/contentful/use-content-json'
import { useTranslation } from 'next-i18next'
import { FC, useEffect, useState } from 'react'
import s from './FooterWithBackgroundImage.module.scss'

import { RegionSwitcher } from '@components/molecules/RegionSwitcher/RegionSwitcher'
import { SectionBgColor } from '@components/templates/Section/Section'
import { useBaseUrl } from '@config/hooks/useBaseUrl'
import { useStoreConfig } from '@config/index'
import cn from 'classnames'
import { useLayoutContext } from '@components/layouts/MainLayout/LayoutContext'
import { Icon } from '@components/atoms/Icon/Icon'

export interface FooterContainerResponse {
    footerColumns?: LinkListProps[]
    instagramUrl?: string
    youtubeUrl?: string
    twitterUrl?: string
    twitchUrl?: string
    facebookUrl?: string
    discordUrl?: string
    redditUrl?: string
    tiktokUrl?: string
    legalLinks?: LinkResponse[]
    backgroundColor?: SectionBgColor
}

const CONTENT_IDENTIFIER = ['globalFooter']
const CONTENT_TYPE = 'organismFooter'
export const FooterContainer: FC = () => {
    const { t } = useTranslation(['common'])
    const [updateHeightFlag, setUpdateHeightFlag] = useState(false)

    const { host = null } = typeof window !== 'undefined' ? window.location : {}
    const storeConfig = useStoreConfig()
    const baseUrl =
        process.env.NEXT_ENV === 'localhost' || host?.includes('localhost')
            ? storeConfig.base.url.localHostBaseUrl
            : storeConfig.base.url.baseUrl
    const { region: currentRegion } = useBaseUrl(baseUrl)
    const { data } = useContentJson<FooterContainerResponse>(
        {
            identifier: CONTENT_IDENTIFIER,
            contentType: CONTENT_TYPE,
            options: {
                level: 2
            }
        },
        {
            revalidateOnFocus: false,
            revalidateOnMount: true
        }
    )
    const { pageTheme } = useLayoutContext()
    const [theme, setTheme] = useState<'light' | 'dark' | 'neo' | undefined>(
        undefined
    )
    useEffect(() => {
        const currentPage = window.location.href
        if (currentPage.includes('stream-deck-for-audio')) {
            setTheme('dark')
        } else {
            setTheme(pageTheme)
        }
    }, [pageTheme])

    if (!data || !data.length) {
        return null
    }
    const {
        footerColumns,
        instagramUrl = '',
        youtubeUrl = '',
        twitterUrl = '',
        twitchUrl = '',
        facebookUrl = '',
        discordUrl = '',
        redditUrl = '',
        tiktokUrl = '',
        legalLinks
    } = data[0].parsedEntries
    const socialMediaLinks = [] as LinkResponse[]
    if (instagramUrl) {
        socialMediaLinks.push({
            linkTitle: 'Instagram',
            linkUrl: instagramUrl,
            newTab: true,
            icon: 'instagram'
        } as LinkResponse)
    }
    if (youtubeUrl) {
        socialMediaLinks.push({
            linkTitle: 'youtube',
            linkUrl: youtubeUrl,
            newTab: true,
            icon: 'youtube'
        } as LinkResponse)
    }
    if (twitterUrl) {
        socialMediaLinks.push({
            linkTitle: 'twitter',
            linkUrl: twitterUrl,
            newTab: true,
            icon: 'twitter'
        } as LinkResponse)
    }
    if (twitchUrl) {
        socialMediaLinks.push({
            linkTitle: 'twitch',
            linkUrl: twitchUrl,
            newTab: true,
            icon: 'twitch'
        } as LinkResponse)
    }
    if (facebookUrl) {
        socialMediaLinks.push({
            linkTitle: 'facebook',
            linkUrl: facebookUrl,
            newTab: true,
            icon: 'facebook'
        } as LinkResponse)
    }
    if (discordUrl) {
        socialMediaLinks.push({
            linkTitle: 'discord',
            linkUrl: discordUrl,
            newTab: true,
            icon: 'discord'
        } as LinkResponse)
    }
    if (redditUrl) {
        socialMediaLinks.push({
            linkTitle: 'reddit',
            linkUrl: redditUrl,
            newTab: true,
            icon: 'reddit'
        } as LinkResponse)
    }
    if (tiktokUrl) {
        socialMediaLinks.push({
            linkTitle: 'tiktok',
            linkUrl: tiktokUrl,
            newTab: true,
            icon: 'tiktok'
        } as LinkResponse)
    }
    const footerWithBackgroundImage = true
    return (
        <footer
            className={cn(
                s['footer-with-background-image'],
                'z-40 w-full',
                'text-white'
            )}
        >
            <div
                className={cn(
                    s['footer-with-background-image__copyright'],
                    'xs-copy',
                    'text-white'
                )}
            >
                &copy; {new Date().getFullYear()} {t('Elgato')}
            </div>
            <div className={cn(s['footer-with-background-image__elgato-logo'])}>
                <Icon name="elgatoLogo" className="w-40px h-40px" />
            </div>
            <div className={cn('px-64px')}>
                <div
                    className={cn(
                        s['footer-with-background-image__main'],
                        'flex justify-between items-end w-full'
                    )}
                >
                    <div
                        className={cn(
                            'flex flex-row w-full',
                            s['footer-with-background-image__main-content']
                        )}
                    >
                        {footerColumns?.length &&
                            footerColumns.map((column, i) => {
                                if (
                                    column.linkListItems &&
                                    column.linkListItems.length > 0
                                ) {
                                    return (
                                        <FooterColumn
                                            className={cn(
                                                s[
                                                    'footer-with-background-image__main-content-column'
                                                ]
                                            )}
                                            key={i}
                                            headline={column.headline}
                                            linkListItems={column.linkListItems}
                                            textColor="light"
                                        />
                                    )
                                }
                            })}
                    </div>
                    <div
                        className={cn(
                            'flex',
                            s[
                                'footer-with-background-image__main-content-column-connect-container'
                            ]
                        )}
                    >
                        <FooterColumn
                            headline={t('Connect')}
                            updateHeight={updateHeightFlag}
                            textColor="light"
                            className={cn(
                                s[
                                    'footer-with-background-image__main-content-column-connect-container-inner'
                                ]
                            )}
                        >
                            <FooterColumnConnect
                                className={cn(
                                    s[
                                        'footer-with-background-image__main-content-column-connect'
                                    ]
                                )}
                                socialMediaLinks={socialMediaLinks}
                                theme="light"
                                onUpdateContent={() =>
                                    setUpdateHeightFlag(!updateHeightFlag)
                                }
                                footerWithBackgroundImage={
                                    footerWithBackgroundImage
                                }
                            />
                        </FooterColumn>
                    </div>
                </div>
                <div
                    className={cn(
                        s['footer-with-background-image__secondary'],
                        'text-white'
                    )}
                >
                    <div
                        className={cn(
                            s['footer-with-background-image__legal-links']
                        )}
                    >
                        {legalLinks?.length &&
                            legalLinks.map((link, i) => {
                                if (
                                    link &&
                                    link.linkUrl.indexOf('sitemap') !== -1
                                ) {
                                    link.linkUrl = `/${currentRegion.toLowerCase()}-${link.linkUrl.replace(
                                        '/',
                                        ''
                                    )}.xml`
                                }
                                return (
                                    <Link
                                        key={i}
                                        className="xs-copy transition-colors duration-300"
                                        link={link}
                                        aria-label={t(
                                            'ada|Opens in the current Tab'
                                        )}
                                    />
                                )
                            })}
                        <button className="xs-copy transition-colors duration-300 relative">
                            {t('Cookie Settings')}
                            <button className="ot-sdk-show-settings absolute opacity-0 top-0 left-0">
                                {t('Cookie Settings')}
                            </button>
                        </button>
                    </div>
                    <div
                        className={cn(
                            s['footer__region-switcher-wrapper'],
                            'text-white'
                        )}
                    >
                        {/* eslint-disable-next-line i18next/no-literal-string */}
                        <RegionSwitcher
                            openDirection="up"
                            footerWithBackgroundImage={
                                footerWithBackgroundImage
                            }
                        />
                    </div>
                </div>
            </div>
        </footer>
    )
}

export default FooterContainer
