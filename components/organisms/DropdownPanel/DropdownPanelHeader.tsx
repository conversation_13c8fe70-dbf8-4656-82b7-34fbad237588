import React, { FC, KeyboardEventHandler, ReactNode } from 'react'
import cn from 'classnames'
import s from './DropdownPanelHeader.module.scss'
import { LinkResponse } from '@components/molecules/Link/Link'
import { Icon } from '@components/atoms/Icon/Icon'
import { Logo } from '@components/atoms/Logo/Logo'
import { useLayoutContext } from '@components/layouts/MainLayout/LayoutContext'
import { useTranslation } from 'next-i18next'

export type DropdownPanelHeaderProps = {
    title: ReactNode
    bodyCopy?: ReactNode
    disclaimerText?: ReactNode
    link?: LinkResponse
    multipleLinks?: LinkResponse[]
    onClick?: () => void
    isOpen: boolean
    logo?: string
    additionalHeaderContent?: ReactNode
    variant?:
        | 'default'
        | 'camera-check'
        | 'wave-neo'
        | 'teleprompter-camera-check'
    id?: string
    className?: string
    backgroundColor?: string
}

export const DropdownPanelHeader: FC<DropdownPanelHeaderProps> = (props) => {
    const {
        title,
        bodyCopy,
        disclaimerText,
        link,
        multipleLinks,
        onClick,
        isOpen,
        logo,
        additionalHeaderContent,
        variant = 'default',
        id,
        className,
        backgroundColor
    } = props

    const { t } = useTranslation(['common'])
    const { pageTheme } = useLayoutContext()
    const isWaveNeo = variant === 'wave-neo'
    const renderHeadline = (title: React.ReactNode, logo?: string) => {
        return (
            <div className={s['dropdown-panel-header__inner__title']}>
                {title && !logo && !isWaveNeo && (
                    <h4
                        className={cn(
                            s['dropdown-panel-header__inner__title--headline'],
                            'mt-0 text-h4'
                        )}
                    >
                        {title}
                    </h4>
                )}
                {disclaimerText && (
                    <div className="text-xs-copy italic pt-5 pb-4 text-gray-600">
                        {/**promo applies when product is add to cart*/}
                        {disclaimerText}
                    </div>
                )}

                {title && !logo && isWaveNeo && (
                    <h3
                        className={cn(
                            s['dropdown-panel-header__inner__title--headline'],
                            {
                                [s[
                                    'dropdown-panel-header__inner__title--headline--wave-neo'
                                ]]: isWaveNeo
                            }
                        )}
                    >
                        {title}
                    </h3>
                )}

                {bodyCopy && (
                    <p
                        className={cn(
                            s['dropdown-panel-header__title__body-copy'],
                            'text-small-copy mt-4'
                        )}
                    >
                        {bodyCopy}
                    </p>
                )}
            </div>
        )
    }
    const keyPressHandler = (e: React.KeyboardEvent<HTMLDivElement>) => {
        e.preventDefault()
        if ((e.code === 'Space' || e.code === 'Enter') && onClick) {
            onClick()
        }
    }

    return (
        <div
            className={cn(
                s['dropdown-panel-header'],
                {
                    [s['dropdown-panel-header--open']]: isOpen,
                    [s[`dropdown-panel-header--${variant}`]]:
                        variant !== 'default',
                    [s[`page-theme-${pageTheme}`]]: pageTheme,
                    [s[
                        `dropdown-panel-header--backgroundColor`
                    ]]: backgroundColor
                },
                className
            )}
            onClick={onClick}
            onKeyPress={keyPressHandler}
            tabIndex={0}
            role="button"
            aria-expanded={isOpen}
        >
            <div
                className={cn(
                    s['dropdown-panel-header__inner'],
                    {
                        [s['dropdown-panel-header__inner__wave-neo']]: isWaveNeo
                    },
                    'text-h3-md-max'
                )}
            >
                {logo && (
                    <Logo
                        className={s['dropdown-panel-header__logo']}
                        name={logo}
                    />
                )}
                {renderHeadline(title, logo)}
                <div className={s['dropdown-panel-header__toggle']}>
                    {link && !multipleLinks && (
                        <a
                            href={link.linkUrl}
                            className="hidden md:block button-text-underlined"
                            target={link.newTab ? '_blank' : '_self'}
                            rel="noreferrer"
                            aria-label={`${link.linkTitle} - ${
                                link.newTab
                                    ? t('ada|Opens in a new Tab')
                                    : t('ada|Opens in the current Tab')
                            }`}
                        >
                            {link.linkTitle}
                        </a>
                    )}
                    {multipleLinks && !link && (
                        <div className="flex gap-16">
                            {multipleLinks.map((multipleLink, index) => (
                                <a
                                    key={index}
                                    href={multipleLink.linkUrl}
                                    className="hidden md:block button-text-underlined"
                                    target={
                                        multipleLink.newTab ? '_blank' : 'self'
                                    }
                                    rel="noreferrer"
                                    aria-label={`${multipleLink.linkTitle} - ${
                                        multipleLink.newTab
                                            ? t('ada|Opens in a new Tab')
                                            : t('ada|Opens in the current Tab')
                                    }`}
                                >
                                    {multipleLink.linkTitle}
                                </a>
                            ))}
                        </div>
                    )}
                    {additionalHeaderContent && (
                        <div
                            className={
                                s['dropdown-panel-header__additional-content']
                            }
                        >
                            {additionalHeaderContent}
                        </div>
                    )}
                    <div className={s['dropdown-panel-header__icon']}>
                        {/* eslint-disable-next-line i18next/no-literal-string */}
                        <Icon name="chevronDown" className="w-32px h-32px" />
                    </div>
                </div>
            </div>
        </div>
    )
}
