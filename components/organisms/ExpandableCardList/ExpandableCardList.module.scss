.expandable-card-list {
    @apply py-16;

    @screen md {
        @apply py-32;
    }
}

.expandable-card {
    padding: 16px;
    border-radius: 12px;
    @screen md-max {
        padding-bottom: 40px;
        padding-top: 40px;
    }

    @screen md {
        padding: 64px 40px;
    }

    &--title {
        @screen md-max {
            justify-content: space-between;
        }
    }

    &--color-dark {
    }

    &--color-light {
        @apply text-white;
    }

    &__icon {
        @apply rounded-full flex justify-center items-center flex-shrink-0 border-2;
        width: 42px;
        height: 42px;

        @screen md-max {
            width: 24px;
            height: 24px;

            svg {
                width: 16px;
                height: 16px;
            }
        }
    }

    &__text {
        @screen md {
            padding-left: 58px;
            max-width: 480px;
        }
    }

    &__image {
        @apply relative;
        border-radius: 8px;
        overflow: hidden;
        width: 100%;

        @screen md {
            transition: max-width 0.5s ease-in-out;
            max-width: 246px;
        }
    }

    button {
        @screen md-max {
            margin-top: 3px;
        }
        svg {
            transition: 0.5s ease-in-out;
        }
    }
}

.expandable-card--expanded {
    .expandable-card__icon {
        svg {
            transform: rotateZ(45deg);
            transition: 0.5s ease-in-out;
        }
    }
    .expandable-card__image {
        @screen md {
            max-width: 100%;
            transition: max-width 0.5s ease-in-out;
        }
    }
}
