/* eslint-disable i18next/no-literal-string */
import React, { useEffect, useRef, useState } from 'react'
import { Button } from '@components/molecules/Button/Button'
import s from './DownloadModal.module.scss'
import { Icon } from '@components/atoms/Icon/Icon'
import cn from 'classnames'
import { useTranslation } from 'next-i18next'
import ChevronRight from '@components/icons/ChevronRight'
import { SubmitHandler, useForm } from 'react-hook-form'
import parsePhoneNumber, { CountryCode } from 'libphonenumber-js'
import { useStoreConfig } from '@config/index'
import { SmsAvailableRegion } from '@preload/graphql/getConfig.d'
import useSMSRegion from '@config/hooks/useSMSRegion'
import { useRouter } from 'next/router'
import { useMobile } from '@pylot-data/hooks/use-mobile'
const numberOnlyRegex = /^\d+$/

const decodeHTMLEntities = (str: string) => {
    const temp = document.createElement('textarea')
    temp.innerHTML = str
    return temp.value
}

const validatePhoneNumber = (
    phoneNumber?: string,
    smsRegion?: SmsAvailableRegion
) => {
    if (!phoneNumber || !smsRegion) {
        return true
    }

    if (!numberOnlyRegex.test(phoneNumber)) {
        return false
    }

    const { prefixNumber, countryCode } = smsRegion
    const parsedPhoneNumber = parsePhoneNumber(
        `${prefixNumber}${phoneNumber}`,
        {
            defaultCountry: 'US',
            extract: false
        }
    )
    if (!parsedPhoneNumber || parsedPhoneNumber.country !== countryCode) {
        return false
    }
    return parsedPhoneNumber.isValid()
}

const validateEmail = (email: string) => {
    const re = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|.(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
    return re.test(String(email).toLowerCase())
}

export enum DownloadModalStateEnum {
    CLOSED,
    NEWSLETTER_REGISTRATION,
    STARTED_AND_REGISTERED,
    STARTED_AND_NOT_REGISTERED
}

export interface DownloadModalState {
    current: DownloadModalStateEnum
    set: (state: DownloadModalStateEnum) => void
    open: () => void
    close: () => void
}

export interface DownloadModalProps {
    state: DownloadModalState
    newsletterPage: JSX.Element
    downloadPage?: JSX.Element
    registeredContent?: (withPhone: boolean) => JSX.Element
    bottomContent?: React.ReactNode
    onRegisterNewsletter: (
        email: string,
        phone: string | null,
        country: CountryCode
    ) => void
    onStartDownload: (
        userAction:
            | 'Signup Newsletter'
            | 'Skip Newsletter Signup'
            | 'Close Modal'
    ) => void
}

type FormValues = {
    email: string
    phoneNumber: string
    country: CountryCode
}

const DownloadModalContent: React.FC<DownloadModalProps> = (props) => {
    const inputPhoneRef = useRef<string | null>(null)
    const { t } = useTranslation(['common'])
    const { locale } = useRouter()
    const currentRegion = locale?.substring(3) || ''
    const {
        base: { smsAvailableRegions }
    } = useStoreConfig()
    const availableSMSRegion = useSMSRegion()
    const currentState = props.state?.current || DownloadModalStateEnum.CLOSED
    const hasRegistered =
        currentState === DownloadModalStateEnum.STARTED_AND_REGISTERED
    const downloadStarted =
        hasRegistered ||
        currentState === DownloadModalStateEnum.STARTED_AND_NOT_REGISTERED
    const {
        register,
        handleSubmit,
        reset,
        formState: { errors }
    } = useForm<FormValues>()
    const { isMobile } = useMobile()
    if (currentState === DownloadModalStateEnum.NEWSLETTER_REGISTRATION) {
        const onRegister: SubmitHandler<FormValues> = (data) => {
            const { email, phoneNumber, country } = data
            inputPhoneRef.current = phoneNumber
            props.onRegisterNewsletter(
                email,
                availableSMSRegion
                    ? `${availableSMSRegion.prefixNumber}${phoneNumber}`
                    : phoneNumber,
                country
            )
            props.onStartDownload('Signup Newsletter')
            props.state.set(DownloadModalStateEnum.STARTED_AND_REGISTERED)
            reset()
            return false
        }

        const onNotRegister = () => {
            props.onStartDownload('Skip Newsletter Signup')
            props.state.set(DownloadModalStateEnum.STARTED_AND_NOT_REGISTERED)
        }

        return (
            <div
                className={cn(
                    s['download-modal__newsletter-screen'],
                    'flex flex-col items-center w-full m-auto md-max:px-32px'
                )}
            >
                {props.newsletterPage}
                <form
                    onSubmit={handleSubmit(onRegister)}
                    className={cn(
                        s['download-modal__newsletter-form'],
                        'flex flex-col items-center w-full m-auto'
                    )}
                >
                    <div className="flex flex-col md:flex-row justify-center gap-8 w-full md:items-start py-16">
                        <div
                            className={cn(s['download-modal__email'], {
                                [s[
                                    'download-modal__email--wide'
                                ]]: !!availableSMSRegion
                            })}
                        >
                            <input
                                className={cn({
                                    [s[
                                        'download-modal__input-error'
                                    ]]: !!errors.email
                                })}
                                type="email"
                                autoComplete="email"
                                placeholder={t(
                                    'downloadModal|email_placeholder'
                                )}
                                required
                                {...register('email', {
                                    required: 'This field is required',
                                    validate: validateEmail
                                })}
                            />
                            {!!errors.email && (
                                <p
                                    className={
                                        s['download-modal__form-error-message']
                                    }
                                >
                                    {t('invalid email address')}
                                </p>
                            )}
                        </div>

                        {availableSMSRegion && (
                            <div className={cn(s['download-modal__phone'])}>
                                <div
                                    className={cn(
                                        s['download-modal__phone-input'],
                                        {
                                            [s[
                                                'download-modal__input-error'
                                            ]]: !!errors.phoneNumber
                                        }
                                    )}
                                >
                                    <span role="img" aria-label="+1">
                                        +1
                                    </span>
                                    <input
                                        type="tel"
                                        autoComplete="tel"
                                        placeholder={t(
                                            'downloadModal|phone_placeholder'
                                        )}
                                        {...register('phoneNumber', {
                                            validate: (value) =>
                                                validatePhoneNumber(
                                                    value,
                                                    availableSMSRegion
                                                )
                                        })}
                                    />
                                    <span
                                        role="img"
                                        aria-label={availableSMSRegion.countryCode.toLowerCase()}
                                    >
                                        {decodeHTMLEntities(
                                            availableSMSRegion.flag
                                        )}
                                    </span>
                                </div>
                                {!!errors.phoneNumber && (
                                    <p
                                        className={
                                            s[
                                                'download-modal__form-error-message'
                                            ]
                                        }
                                    >
                                        {t('invalid phone number')}
                                    </p>
                                )}
                            </div>
                        )}
                    </div>

                    <Button
                        type="submit"
                        className="mb-4"
                        label={t('downloadModal|newsletter_sign_up')}
                    >
                        {t('downloadModal|newsletter_sign_up')}
                    </Button>
                </form>
                <Button
                    variant="tertiary"
                    className={s['download-modal__skip-button']}
                    onClick={onNotRegister}
                    label={
                        smsAvailableRegions
                            ? isMobile
                                ? t('downloadModal|newsletter_skip_mobile_us')
                                : t('downloadModal|newsletter_skip_us')
                            : t('downloadModal|newsletter_skip')
                    }
                >
                    <span>
                        {smsAvailableRegions
                            ? isMobile
                                ? t('downloadModal|newsletter_skip_mobile_us')
                                : t('downloadModal|newsletter_skip_us')
                            : t('downloadModal|newsletter_skip')}
                        <ChevronRight />
                    </span>
                </Button>

                <div
                    className={cn(
                        s['download-modal__disclaimer'],
                        'text-center'
                    )}
                    dangerouslySetInnerHTML={{
                        __html: t(
                            currentRegion === 'US'
                                ? 'downloadModal|newsletter_disclaimer_us'
                                : 'downloadModal|newsletter_disclaimer_non-us'
                        )
                    }}
                />
            </div>
        )
    }

    if (downloadStarted) {
        return (
            <div className="w-full m-auto">
                <div className="flex flex-col items-center">
                    <div className="flex flex-row items-center mb-12">
                        {props.downloadPage}
                    </div>
                    <h3 className="text-center">
                        {t('downloadModal|download_thank_you')}
                    </h3>
                </div>
                {props.registeredContent && hasRegistered && (
                    <div className={s['download-modal__content-amendment']}>
                        {props.registeredContent(!!inputPhoneRef.current)}
                    </div>
                )}
                {props.bottomContent && (
                    <div className={s['download-modal__content-amendment']}>
                        {props.bottomContent}
                    </div>
                )}
            </div>
        )
    }

    return null
}

export function useDownloadModal(): DownloadModalState {
    const [state, setState] = useState<DownloadModalStateEnum>(
        DownloadModalStateEnum.CLOSED
    )

    return {
        current: state,
        set: setState,
        open() {
            setState(DownloadModalStateEnum.NEWSLETTER_REGISTRATION)
        },
        close() {
            setState(DownloadModalStateEnum.CLOSED)
        }
    }
}

export const DownloadModal: React.FC<DownloadModalProps> = (props) => {
    const content = DownloadModalContent(props)
    const { t } = useTranslation(['common'])
    useEffect(() => {
        const currentState = props.state?.current
        const shouldBeOpen = currentState !== DownloadModalStateEnum.CLOSED
        const isOpen = document.body.classList.contains(s['body--modal-open'])

        if (!isOpen && shouldBeOpen) {
            document.body.classList.add(s['body--modal-open'])
        } else if (isOpen && !shouldBeOpen) {
            document.body.classList.remove(s['body--modal-open'])
        }
    }, [props.state])

    const close = () => {
        if (
            props.state.current ===
            DownloadModalStateEnum.NEWSLETTER_REGISTRATION
        ) {
            props.onStartDownload('Close Modal')
        }
        props.state.close()
    }

    return (
        <div
            role="presentation"
            className={cn(s['download-modal__backdrop'], {
                hidden: !content,
                flex: !!content
            })}
            onClick={close}
        >
            <div
                role="presentation"
                className={cn(s['download-modal'], {
                    [s['download-modal--closed']]: !content
                })}
                onClick={(e) => e.stopPropagation()}
            >
                <div className={s['download-modal__click-catch']}>
                    {content && (
                        <>
                            {content}
                            <Button
                                variant="tertiary"
                                onClick={close}
                                className={s['download-modal__close']}
                                label={t('Close')}
                            >
                                <Icon name="close" />
                            </Button>
                        </>
                    )}
                </div>
            </div>
        </div>
    )
}
