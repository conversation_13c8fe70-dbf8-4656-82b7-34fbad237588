/* eslint-disable i18next/no-literal-string */
import React, { FC, useRef, useState, useCallback, useEffect } from 'react'
import s from './NeoCardWithToggle.module.scss'
import { CardProps } from '@components/templates/CardList/CardList'
import cn from 'classnames'
import { SectionBgColor } from '@components/templates/Section/Section'
import Image from '@corsairitshopify/corsair-image'

import { PrimaryTextProps } from '@components/molecules/PrimaryText/PrimaryText'
import {
    Container,
    ContainerSize
} from '@components/organisms/Container/Container'
import { useTranslation } from 'next-i18next'
import { Icon } from '@components/atoms/Icon/Icon'
import { Toggle } from '@components/organisms/NeoCardWithToggle/Toggle'
import { getImageAspectRatio } from '@config/hooks/useGetImageAspectRatio'
import unescape from 'lodash.unescape'
import { NeoComponent } from '@lib/gtm/neoActions'
import { useAnimationAndVideosToggle } from '@components/common/AnimationAndVideosToggle/AnimationAndVideoContext'

interface NeoCardWithToggleProps {
    cards?: CardProps[]
    id?: string
    textPanel?: PrimaryTextProps
    backgroundColor?: SectionBgColor
}
export const NeoCardWithToggle: FC<NeoCardWithToggleProps> = (props) => {
    const {
        id,
        backgroundColor = SectionBgColor.TRANSPARENT,
        textPanel,
        cards
    } = props
    const { t } = useTranslation(['common'])
    const videoRefNeoMediaDesktop = useRef<HTMLVideoElement>(null)
    const videoRefOtherMediaDesktop = useRef<HTMLVideoElement>(null)
    const videoRefNeoMediaMobile = useRef<HTMLVideoElement>(null)
    const videoRefOtherMediaMobile = useRef<HTMLVideoElement>(null)

    const { isAnimationStopped } = useAnimationAndVideosToggle()

    const [isVideoMuted, setIsVideoMuted] = useState(true)

    const [videoDisplayed, setVideoDisplayed] = useState<
        'neo-desktop' | 'other-desktop' | 'neo-mobile' | 'other-mobile'
    >('neo-desktop')
    const [isVideoPaused, setIsVideoPaused] = useState(false)
    const [windowWidth, setWindowWidth] = useState<number | undefined>(
        undefined
    )
    const isMobile = !!windowWidth && windowWidth < 768
    const isTablet = !!windowWidth && windowWidth > 768 && windowWidth < 1024

    // PAUSE & PLAY REMOVED FOR NOW, MAY BE NEEDED
    const isVideoPlaying = (video: HTMLVideoElement | null) =>
        !!(
            video &&
            video.currentTime > 0 &&
            !video.paused &&
            !video.ended &&
            video.readyState > 2
        )

    const onPauseOrPlayVideo = () => {
        setIsVideoPaused((prevState) => !prevState)
        if (
            isVideoPlaying(videoRefNeoMediaDesktop.current) ||
            isVideoPlaying(videoRefOtherMediaDesktop.current) ||
            isVideoPlaying(videoRefNeoMediaMobile.current) ||
            isVideoPlaying(videoRefOtherMediaMobile.current)
        ) {
            videoRefNeoMediaDesktop.current?.pause()
            videoRefOtherMediaDesktop.current?.pause()
            videoRefNeoMediaMobile.current?.pause()
            videoRefOtherMediaMobile.current?.pause()
        } else if (
            !isVideoPlaying(videoRefNeoMediaDesktop.current) &&
            videoDisplayed === 'neo-desktop'
        ) {
            videoRefNeoMediaDesktop.current?.play()
        } else if (
            !isVideoPlaying(videoRefOtherMediaDesktop.current) &&
            videoDisplayed === 'other-desktop'
        ) {
            videoRefOtherMediaDesktop.current?.play()
        } else if (
            !isVideoPlaying(videoRefNeoMediaMobile.current) &&
            videoDisplayed === 'neo-mobile'
        ) {
            videoRefNeoMediaMobile.current?.play()
        } else if (
            !isVideoPlaying(videoRefOtherMediaMobile.current) &&
            videoDisplayed === 'other-mobile'
        ) {
            videoRefOtherMediaMobile.current?.play()
        }
    }
    useEffect(() => {
        const handleResize = () => {
            setWindowWidth(window.innerWidth)
        }
        handleResize()
        window.addEventListener('resize', handleResize)
        return () => {
            window.removeEventListener('resize', handleResize)
        }
    }, [])

    useEffect(() => {
        if (!isAnimationStopped) {
            if (!isMobile && videoRefNeoMediaDesktop.current) {
                videoRefNeoMediaDesktop.current.play()
            }
            if (isMobile && videoRefNeoMediaMobile.current) {
                videoRefNeoMediaMobile.current.play()
            }
        } else {
            if (
                videoRefNeoMediaDesktop.current ||
                videoRefOtherMediaDesktop.current ||
                videoRefNeoMediaMobile.current ||
                videoRefOtherMediaMobile.current
            ) {
                videoRefNeoMediaDesktop.current?.pause()
                videoRefOtherMediaDesktop.current?.pause()
                videoRefNeoMediaMobile.current?.pause()
                videoRefOtherMediaMobile.current?.pause()
            }
        }
    }, [isMobile, isAnimationStopped])

    const onMuteOrUnmute = useCallback(() => {
        setIsVideoMuted((prevState) => !prevState)
    }, [])

    const onTriggerNeo = () => {
        if (
            isMobile &&
            videoRefNeoMediaMobile.current &&
            videoRefOtherMediaMobile.current
        ) {
            videoRefOtherMediaMobile.current?.pause()
            videoRefNeoMediaMobile.current.currentTime =
                videoRefOtherMediaMobile.current.currentTime
            videoRefNeoMediaMobile.current.play()
            setTimeout(() => {
                setVideoDisplayed('neo-mobile')
            }, 80)
            // setIsVideoMuted(false)
            setIsVideoPaused(false)
        }
        if (
            !isMobile &&
            videoRefNeoMediaDesktop.current &&
            videoRefOtherMediaDesktop.current
        ) {
            videoRefOtherMediaDesktop.current?.pause()
            videoRefNeoMediaDesktop.current.currentTime =
                videoRefOtherMediaDesktop.current.currentTime
            videoRefNeoMediaDesktop.current.play()
            setTimeout(() => {
                setVideoDisplayed('neo-desktop')
            }, 80)
            setIsVideoMuted(false)
            setIsVideoPaused(false)
        }
    }

    const onTriggerOther = () => {
        if (
            isMobile &&
            videoRefNeoMediaMobile.current &&
            videoRefOtherMediaMobile.current
        ) {
            videoRefNeoMediaMobile.current?.pause()
            videoRefOtherMediaMobile.current.currentTime =
                videoRefNeoMediaMobile.current.currentTime
            videoRefOtherMediaMobile.current.play()
            setTimeout(() => {
                setVideoDisplayed('other-mobile')
            }, 80)
            // setIsVideoMuted(false)
            setIsVideoPaused(false)
        }
        if (
            !isMobile &&
            videoRefNeoMediaDesktop.current &&
            videoRefOtherMediaDesktop.current
        ) {
            videoRefNeoMediaDesktop.current?.pause()
            videoRefOtherMediaDesktop.current.currentTime =
                videoRefNeoMediaDesktop.current.currentTime
            videoRefOtherMediaDesktop.current.play()
            setTimeout(() => {
                setVideoDisplayed('other-desktop')
            }, 80)
            setIsVideoMuted(false)
            setIsVideoPaused(false)
        }
    }

    return (
        <div
            id={id}
            className={cn(
                s['neo-card-with-toggle'],
                backgroundColor,
                'relative'
            )}
        >
            <Container
                size={ContainerSize.XLARGE}
                className={cn(s['neo-card-with-toggle__container'])}
            >
                {isMobile && textPanel && (
                    <div
                        className={cn(
                            s[
                                'neo-card-with-toggle__toggle-title-toggle-container'
                            ],
                            'md:hidden'
                        )}
                    >
                        <p
                            className={cn(
                                s['neo-card-with-toggle__toggle-title']
                            )}
                        >
                            {textPanel.disclaimerText}
                        </p>
                        {!!cards?.length &&
                            cards.map((card, index) => {
                                const neoMediaDesktop = card.media
                                const otherMediaDesktop = card.neoMedia
                                const isVideo =
                                    neoMediaDesktop?.file?.contentType?.includes(
                                        'video'
                                    ) ?? false
                                const isVideo2 =
                                    otherMediaDesktop?.file?.contentType?.includes(
                                        'video'
                                    ) ?? false

                                // Only render the toggle button if both videos are present
                                const renderToggleButton = isVideo && isVideo2

                                const neoPosterImage = card.posterImage
                                const neoMediaMobile = card.mobileMedia
                                const otherMediaMobile = card.neoMobileMedia
                                const neoPosterImageMobile =
                                    card.posterImageMobile
                                const aspectRatioDesktop = getImageAspectRatio({
                                    imageOrVideo: neoMediaDesktop,
                                    posterImage: neoPosterImage
                                })
                                const aspectRatioDesktopStyle =
                                    aspectRatioDesktop && neoPosterImage
                                        ? {
                                              aspectRatio: `${aspectRatioDesktop}`
                                          }
                                        : {}

                                const aspectRatioMobile = getImageAspectRatio({
                                    imageOrVideo: neoMediaMobile,
                                    posterImage: neoPosterImageMobile
                                })
                                const aspectRatioMobileStyle =
                                    aspectRatioMobile && neoPosterImageMobile
                                        ? {
                                              aspectRatio: `${aspectRatioMobile}`
                                          }
                                        : {}
                                return (
                                    <React.Fragment key={index}>
                                        {renderToggleButton && (
                                            <Toggle
                                                icon="waveIcon"
                                                component={
                                                    NeoComponent.AUDIO_PANEL
                                                }
                                                text={textPanel?.disclaimerText}
                                                isExpanded={
                                                    !isVideoMuted || isMobile
                                                }
                                                button1Icon="waveIcon"
                                                button1Text={textPanel?.notice}
                                                isButton1Active={
                                                    videoDisplayed ===
                                                        'neo-desktop' ||
                                                    videoDisplayed ===
                                                        'neo-mobile'
                                                }
                                                onClickButton1={onTriggerNeo}
                                                button2Icon="waveIcon"
                                                button2Text={
                                                    textPanel?.richText
                                                }
                                                isButton2Active={
                                                    videoDisplayed ===
                                                        'other-desktop' ||
                                                    videoDisplayed ===
                                                        'other-mobile'
                                                }
                                                onClickButton2={onTriggerOther}
                                            />
                                        )}
                                    </React.Fragment>
                                )
                            })}
                    </div>
                )}
                {!!cards?.length &&
                    cards.map((card, index) => {
                        const neoMediaDesktop = card.media
                        const neoPosterImage = card.posterImage
                        const otherMediaDesktop = card.neoMedia
                        const neoMediaMobile = card.mobileMedia
                        const otherMediaMobile = card.neoMobileMedia
                        const neoPosterImageMobile = card.posterImageMobile
                        const isVideo =
                            card.media?.file?.contentType?.includes('video') ??
                            false
                        const isMobileVideo =
                            card.mobileMedia?.file?.contentType?.includes(
                                'video'
                            ) ?? false
                        const aspectRatioDesktop = getImageAspectRatio({
                            imageOrVideo: neoMediaDesktop,
                            posterImage: neoPosterImage
                        })
                        const aspectRatioDesktopStyle =
                            aspectRatioDesktop && neoPosterImage
                                ? { aspectRatio: `${aspectRatioDesktop}` }
                                : {}
                        const aspectRatioMobile = getImageAspectRatio({
                            imageOrVideo: neoMediaMobile,
                            posterImage: neoPosterImageMobile
                        })
                        const aspectRatioMobileStyle =
                            aspectRatioMobile && neoPosterImageMobile
                                ? { aspectRatio: `${aspectRatioMobile}` }
                                : {}

                        let videoDescAttributes
                        if (card?.customOptions?.videoDescription) {
                            videoDescAttributes = {
                                tabIndex: 0,
                                role: 'img',
                                'aria-describedby': `video-description-${index}`
                            }
                        }
                        return (
                            <div
                                key={index}
                                className={cn(
                                    s['neo-card-with-toggle__video-container']
                                )}
                                style={
                                    isMobile
                                        ? aspectRatioMobileStyle
                                        : aspectRatioDesktopStyle
                                }
                                {...videoDescAttributes}
                            >
                                {isVideo && neoPosterImage && (
                                    <div
                                        className={cn(
                                            s[
                                                'neo-card-with-toggle__poster-image'
                                            ]
                                        )}
                                    >
                                        <Image
                                            src={neoPosterImage.file.url}
                                            objectFit="cover"
                                            layout="fill"
                                            alt={
                                                neoPosterImage.description || ''
                                            }
                                        />
                                    </div>
                                )}
                                {neoMediaDesktop && isVideo && !isMobile && (
                                    <video
                                        ref={videoRefNeoMediaDesktop}
                                        loop
                                        // audio !isVideoMuted && videoDisplayed === 'neo-dekstop'
                                        muted={isVideoMuted}
                                        // muted={
                                        //     !(
                                        //         !isVideoMuted &&
                                        //         videoDisplayed === 'neo-desktop'
                                        //     )
                                        // } // TODO
                                        preload="none"
                                        className={cn(
                                            s['neo-card-with-toggle__video-1'],
                                            {
                                                [s[
                                                    'neo-card-with-toggle__video-1-hidden'
                                                ]]:
                                                    videoDisplayed !==
                                                    'neo-desktop'
                                            }
                                        )}
                                        poster={neoPosterImage?.file.url}
                                    >
                                        <track kind="captions" />
                                        <source src={card.media?.file.url} />
                                    </video>
                                )}
                                {otherMediaDesktop && isVideo && !isMobile && (
                                    <video
                                        ref={videoRefOtherMediaDesktop}
                                        loop
                                        muted={isVideoMuted}
                                        preload="none"
                                        className={cn(
                                            s['neo-card-with-toggle__video-2'],
                                            {
                                                [s[
                                                    'neo-card-with-toggle__video-2-hidden'
                                                ]]:
                                                    videoDisplayed !==
                                                    'other-desktop'
                                            }
                                        )}
                                    >
                                        <track kind="captions" />
                                        <source src={card.neoMedia?.file.url} />
                                    </video>
                                )}

                                {neoMediaMobile && isMobileVideo && isMobile && (
                                    <video
                                        ref={videoRefNeoMediaMobile}
                                        loop
                                        muted={isVideoMuted}
                                        playsInline
                                        preload="auto"
                                        className={cn(
                                            s[
                                                'neo-card-with-toggle__mobile-video-1'
                                            ],
                                            {
                                                [s[
                                                    'neo-card-with-toggle__mobile-video-1-hidden'
                                                ]]:
                                                    videoDisplayed !==
                                                    'neo-mobile'
                                            }
                                        )}
                                        poster={neoPosterImageMobile?.file.url}
                                    >
                                        <track kind="captions" />
                                        <source
                                            src={card.mobileMedia?.file.url}
                                        />
                                    </video>
                                )}

                                {otherMediaMobile && isMobileVideo && isMobile && (
                                    <video
                                        ref={videoRefOtherMediaMobile}
                                        loop
                                        muted={isVideoMuted}
                                        preload="auto"
                                        playsInline
                                        className={cn(
                                            s[
                                                'neo-card-with-toggle__mobile-video-2'
                                            ],
                                            {
                                                [s[
                                                    'neo-card-with-toggle__mobile-video-2-hidden'
                                                ]]:
                                                    videoDisplayed !==
                                                    'other-mobile'
                                            }
                                        )}
                                        poster={neoPosterImageMobile?.file.url}
                                    >
                                        <track kind="captions" />
                                        <source
                                            src={card.neoMobileMedia?.file.url}
                                        />
                                    </video>
                                )}
                                {card?.customOptions?.videoDescription &&
                                    videoDescAttributes && (
                                        <p
                                            className="sr-only"
                                            id={`video-description-${index}`}
                                        >
                                            {
                                                card?.customOptions
                                                    ?.videoDescription
                                            }
                                        </p>
                                    )}
                            </div>
                        )
                    })}

                {textPanel && (
                    <div
                        className={
                            s[
                                'neo-card-with-toggle__text-media-controls-container'
                            ]
                        }
                    >
                        <div
                            className={cn(s['neo-card-with-toggle__text'])}
                            dangerouslySetInnerHTML={{
                                __html: unescape(
                                    textPanel.bodyCopy?.replace(/&quot/g, `'`)
                                )
                            }}
                        >
                            {textPanel.bodyCopy}
                        </div>
                        <div
                            className={
                                s[
                                    'neo-card-with-toggle__toggle-video-control-container'
                                ]
                            }
                        >
                            <div
                                className={cn(
                                    s['neo-card-with-toggle__toggle-group']
                                )}
                            >
                                {!isMobile && textPanel.disclaimerText && (
                                    <Toggle
                                        icon={textPanel?.icon}
                                        component={NeoComponent.AUDIO_PANEL}
                                        text={textPanel?.disclaimerText}
                                        isExpanded={!isVideoMuted || isTablet}
                                        button1Icon={textPanel?.icon}
                                        button1Text={textPanel?.notice}
                                        isButton1Active={
                                            videoDisplayed === 'neo-desktop' ||
                                            videoDisplayed === 'neo-mobile'
                                        }
                                        onClickButton1={onTriggerNeo}
                                        button2Icon={textPanel?.icon2}
                                        button2Text={textPanel?.richText}
                                        isButton2Active={
                                            videoDisplayed ===
                                                'other-desktop' ||
                                            videoDisplayed === 'other-mobile'
                                        }
                                        onClickButton2={onTriggerOther}
                                    />
                                )}

                                <div
                                    className={cn(
                                        s['neo-card-with-toggle__video-control']
                                    )}
                                >
                                    {textPanel.showMuteButton === true && (
                                        <div
                                            className={cn(
                                                s[
                                                    'neo-card-with-toggle__volume-icon'
                                                ],
                                                {
                                                    [s[
                                                        'neo-card-with-toggle__muted'
                                                    ]]: isVideoMuted,
                                                    [s[
                                                        'neo-card-with-toggle__not-muted'
                                                    ]]: !isVideoMuted
                                                }
                                            )}
                                            role="button"
                                            tabIndex={0}
                                            onKeyPress={onMuteOrUnmute}
                                            onClick={onMuteOrUnmute}
                                        >
                                            <Icon name="volumeIcon" />
                                            <Icon name="unmute" />
                                        </div>
                                    )}

                                    {/*MAY BE NEEDED LATER*/}
                                    {textPanel.showPlayButton === true && (
                                        <div
                                            className={cn(
                                                s[
                                                    'neo-card-with-toggle__pause-icon'
                                                ],
                                                {
                                                    [s[
                                                        'neo-card-with-toggle__paused'
                                                    ]]: isVideoPaused
                                                }
                                            )}
                                            role="button"
                                            tabIndex={0}
                                            onKeyPress={onPauseOrPlayVideo}
                                            onClick={onPauseOrPlayVideo}
                                        >
                                            <Icon name="neoPlayIcon" />
                                            <Icon name="neoPauseIcon" />
                                        </div>
                                    )}
                                </div>
                            </div>
                            {isTablet && (
                                <p
                                    className={cn(
                                        s['neo-card-with-toggle__toggle-title']
                                    )}
                                >
                                    {textPanel.disclaimerText}
                                </p>
                            )}
                        </div>
                    </div>
                )}
            </Container>
        </div>
    )
}

export default NeoCardWithToggle
