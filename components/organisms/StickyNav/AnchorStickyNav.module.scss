.anchor-sticky-navigation {
    :global {
        .content-page-holiday-gift-guide-2024 {
            position: relative;
        }
    }

    &__active {
        color: red;
    }

    &__navlinks {
        position: relative;
        width: 100%;
        background-color: #0c2588;
        color: white;
        z-index: 100;
        padding: 5px 20px 10px 20px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease-in-out;
        display: flex;
        justify-content: space-evenly;
        align-items: center;
        min-height: 84px;
        @screen md-max {
            position: sticky;
            top: 0;
            z-index: 100;
            display: flex;
            justify-content: flex-start;
            overflow-x: auto;
            white-space: nowrap;
            min-height: 64px;
            &::-webkit-scrollbar {
                display: none;
            }
        }
    }

    &__navbar {
        display: flex;
        justify-content: space-evenly;
        padding: 0;
        margin: 0;
        @screen md-max {
            flex-direction: row;
            list-style: none;
        }
    }

    &__navbar li {
        margin: 0 10px;
        @screen md-max {
            margin: 0 10px;
            display: inline-block;
        }
    }

    &__navbar button {
        background: none;
        color: white;
        border: none;
        font-size: 16px;
        cursor: pointer;
        padding: 10px;
        transition: opacity 0.2s ease;
        position: relative;
        @screen md-max {
            background: none;
            color: white;
            border: none;
            font-size: 14px;
            cursor: pointer;
            transition: opacity 0.2s ease;
            position: relative;
        }

        &:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            z-index: 100;
            width: 100%;
            height: 1px;
            margin-top: 14px;
            background: var(--white);
            margin-right: 38px;
        }

        &:hover,
        &.anchor-sticky-navigation__active {
            font-weight: 700;

            &::after {
                height: 4px;
            }
        }
    }
}
