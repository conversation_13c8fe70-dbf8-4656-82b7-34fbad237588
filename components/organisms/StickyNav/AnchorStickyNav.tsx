import { FC, useEffect, useRef, useState } from 'react'
import s from './AnchorStickyNav.module.scss'
import cn from 'classnames'
import { pushToDataLayer } from '@corsairitshopify/pylot-gtm/src/utils'
import { getLinkClickEvent } from '@lib/gtm/linkClick'
import { useMobile } from '@pylot-data/hooks/use-mobile'

type LinksData = {
    links: Array<{ buttonText: string; buttonLink: string }>
    meta: { contentType: string }
    title: string
}
export type StickyNavigationContent = {
    links: LinksData
}

export const AnchorStickyNav: FC<StickyNavigationContent> = ({ links }) => {
    const [activeIndex, setActiveIndex] = useState<number | null>(null)
    const sectionsRef = useRef<Array<HTMLElement | null>>([])
    const linkRefs = useRef<(HTMLButtonElement | null)[]>([])
    const isAutoScrolling = useRef(false)

    useEffect(() => {
        sectionsRef.current = links?.links.map((link) =>
            document.getElementById(link.buttonLink)
        )

        const observerOptions = {
            root: null,
            rootMargin: '0px 0px -95%'
        }
        const observerCallback = (entries: IntersectionObserverEntry[]) => {
            entries.forEach((entry) => {
                if (entry.isIntersecting && !isAutoScrolling.current) {
                    const index = sectionsRef.current.findIndex(
                        (section) => section === entry.target
                    )
                    if (index !== -1) {
                        setActiveIndex(index)
                    }
                }
            })
        }
        const observer = new IntersectionObserver(
            observerCallback,
            observerOptions
        )
        sectionsRef.current.forEach((section) => {
            if (section) {
                observer.observe(section)
            }
        })
        return () => {
            observer.disconnect()
        }
    }, [links])

    const scrollToSection = (id: string, index: number) => {
        const section = document.getElementById(id)
        if (section) {
            isAutoScrolling.current = true
            setActiveIndex(index)
            section.scrollIntoView({ behavior: 'smooth', block: 'start' })

            // Reset the auto-scrolling flag after animation completes
            setTimeout(() => {
                isAutoScrolling.current = false
            }, 1000) // Assuming scroll animation takes about 1 second
        }
    }

    function handleLinkClick(link: string, index: number) {
        if (linkRefs.current[index]) {
            pushToDataLayer(
                getLinkClickEvent(
                    { current: linkRefs.current[index] },
                    {
                        overrideClasses: 'elgato-links'
                    }
                )
            )
        }
    }

    const { isMobile } = useMobile()

    return (
        <div className={cn(s['anchor-sticky-navigation'], 'sticky top-0 z-70')}>
            <nav className={cn(s['anchor-sticky-navigation__navlinks'])}>
                <ul className={cn(s['anchor-sticky-navigation__navbar'])}>
                    {links?.links.map((link, index) => (
                        <li key={index}>
                            <button
                                className={cn('elgato-links', {
                                    [s['anchor-sticky-navigation__active']]:
                                        activeIndex === index
                                })}
                                onClick={() => {
                                    scrollToSection(link.buttonLink, index)
                                    handleLinkClick(link.buttonLink, index)
                                }}
                                ref={(el) => {
                                    linkRefs.current[index] = el
                                    // Scroll active button into view on mobile
                                    if (activeIndex === index && isMobile) {
                                        el?.scrollIntoView({
                                            behavior: 'smooth',
                                            block: 'nearest',
                                            inline: 'center'
                                        })
                                    }
                                }}
                            >
                                {link.buttonText}
                            </button>
                        </li>
                    ))}
                </ul>
            </nav>
        </div>
    )
}

export default AnchorStickyNav
