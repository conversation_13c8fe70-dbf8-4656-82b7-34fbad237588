import ChevronDownIcon from '@components/atoms/Icon/general/ChevronDownIcon'
import StickyNavLogoElgato from '@components/atoms/Icon/general/StickyNavLogoElgato'
import { Icon } from '@components/atoms/Icon/Icon'
import { useAnimationAndVideosToggle } from '@components/common/AnimationAndVideosToggle/AnimationAndVideoContext'
import { CloudinaryMedia } from '@components/common/CloudinaryMedia/Cloudinary'
import { usePDPProduct } from '@components/common/ContentPage/ContentPagePDPProductContext'
import { BurgerMenuIcon } from '@components/icons'
import SmallCloseIcon from '@components/icons/SmallCloseIcon'
import { useLayoutContext } from '@components/layouts/MainLayout/LayoutContext'
import { Button } from '@components/molecules/Button/Button'
import { LinkResponse } from '@components/molecules/Link/Link'
import {
    StickyNavigationBanner,
    StickyNavigationBannerProps
} from '@components/molecules/StickyNavigationBanner/StickyNavigationBanner'
import { MenuItemProps } from '@components/organisms/Header/Header'
import { StickyNavBuy } from '@components/organisms/StickyNav/StickyNavBuyProduct'
import { StickyNavProductDropdown } from '@components/organisms/StickyNavProductDropdown/StickyNavProductDropdown'
import { useMediaQuery } from '@config/hooks/useMediaQuery'
import { pushToDataLayer } from '@corsairitshopify/pylot-gtm/src/utils'
import { getChatSimpleEvent } from '@lib/gtm/chatSimple'
import { ButtonLabel } from '@pylot-data/hooks/product/use-product-ui'
import { useMobile } from '@pylot-data/hooks/use-mobile'
import { ProductInterface } from '@pylot-data/pylotschema'
import cn from 'classnames'
import { useTranslation } from 'next-i18next'
import { useRouter } from 'next/router'
import {
    FC,
    ReactNode,
    useCallback,
    useEffect,
    useMemo,
    useRef,
    useState
} from 'react'
import { isNonTransactionalRegion } from '../../../helpers'
import s from './StickyNav.module.scss'
import { StickyNavLinkList } from './StickyNavLinkList'
import StickyNavToggleComponent from './StickyNavToggleComponent'

export type StickyNavCallout = {
    title?: string
    textColor?: string
    fontSize?: number
    media?: CloudinaryMedia[]
    mediaWidth?: number
    mediaHeight: number
    breakpoint?: number
}

export type ChatSimpleConfig = {
    platformId?: string
    userId?: string
    chatbotId?: string
    gtmId?: string
    isLocal?: boolean
    initialPositionLg?: string
    initialPositionMd?: string
}

export type StickyNavProps = {
    className?: string
    active?: boolean
    headline?: string
    toggleTextOne?: string
    toggleTextTwo?: string
    toggleOneUrl?: string
    toggleTwoUrl?: string
    toggleOneSku?: string
    toggleTwoSku?: string
    onToggleButton1Click?: () => void
    onToggleButton2Click?: () => void
    toggleActiveButton?: 'button1' | 'button2'
    dropdownLinks?: MenuItemProps[]
    links?: LinkResponse[]
    buyProduct?: boolean
    price?: string
    priceDiscount?: string
    fixed?: boolean
    addProductHandler?: () => void
    fixedAtPosition?: boolean
    showAiNavigation?: boolean
    showId?: string
    promotionBanner?: StickyNavigationBannerProps
    chatSimpleConfiguration?: ChatSimpleConfig
    callout?: StickyNavCallout
    productLogo?: CloudinaryMedia[]
}
export type StickyNavigationContent = {
    content: StickyNavProps
    children?: ReactNode
}

function useWindowSize() {
    const [windowSize, setWindowSize] = useState({
        width: typeof window !== 'undefined' ? window.innerWidth : 0,
        height: typeof window !== 'undefined' ? window.innerHeight : 0
    })

    useEffect(() => {
        function handleResize() {
            setWindowSize({
                width: typeof window !== 'undefined' ? window.innerWidth : 0,
                height: typeof window !== 'undefined' ? window.innerHeight : 0
            })
        }

        window.addEventListener('resize', handleResize)
        return () => window.removeEventListener('resize', handleResize)
    }, [])

    return windowSize
}

const useSvgContent = (url: string) => {
    const [svgContent, setSvgContent] = useState<string>('')
    const [isLoading, setIsLoading] = useState<boolean>(true)
    const [error, setError] = useState<Error | null>(null)

    useEffect(() => {
        const fetchSvg = async () => {
            if (!url) {
                setIsLoading(false)
                return
            }

            try {
                const response = await fetch(url)
                if (!response.ok) {
                    throw new Error(
                        `Failed to fetch Logo: ${response.statusText}`
                    )
                }
                const text = await response.text()
                setSvgContent(text)
            } catch (err) {
                setError(
                    err instanceof Error ? err : new Error('Unknown error')
                )
            } finally {
                setIsLoading(false)
            }
        }

        fetchSvg()
    }, [url])

    return { svgContent, isLoading, error }
}

const SvgLogo = ({ url }: { url: string }) => {
    const { svgContent } = useSvgContent(url)

    return (
        <div
            dangerouslySetInnerHTML={{ __html: svgContent }}
            className={cn(s['sticky-nav__svg-logo'])}
        />
    )
}

export const StickyNav: FC<StickyNavigationContent> = ({
    content,
    children
}) => {
    const {
        className,
        headline,
        active = false,
        fixed = true,
        dropdownLinks = [],
        links = [],
        buyProduct = false,
        price = '',
        priceDiscount = '',
        addProductHandler,
        fixedAtPosition = false,
        promotionBanner,
        showAiNavigation = false,
        showId = '',
        callout,
        chatSimpleConfiguration,
        productLogo,
        toggleTextOne,
        toggleTextTwo,
        toggleOneUrl,
        toggleTwoUrl,
        toggleOneSku,
        toggleTwoSku
    } = content
    const {
        breakpoint: calloutBreakpoint = 1200,
        fontSize: calloutFontSize = 16,
        textColor: calloutColor = '#000'
    } = callout || {}
    const { isMobile } = useMobile()
    const isDisplayCallout: boolean = useMediaQuery(
        `(min-width: ${calloutBreakpoint}px)`
    )
    const { product } = usePDPProduct()
    const withDropdown = dropdownLinks && dropdownLinks.length > 0
    const { setStickyNav, pageTheme } = useLayoutContext()
    const { t } = useTranslation(['common'])
    const containerRef = useRef<HTMLDivElement>(null)
    const dropdownRef = useRef<HTMLDivElement>(null)
    const toggleRef = useRef<HTMLDivElement>(null)
    const [isDropdownOpen, setIsDropdownOpen] = useState(false)
    const [chatSimpleStarted, setChatSimpleStarted] = useState(false)
    const [isShowAiTool, setIsShowAiTool] = useState(showAiNavigation)
    const router = useRouter()
    const toggleDropdown = useCallback(() => {
        setIsDropdownOpen(!isDropdownOpen)
    }, [isDropdownOpen])
    const [isActive, setIsActive] = useState(active)
    const [isToggleVisible, setIsToggleVisible] = useState(false)
    const [isFixed, setIsFixed] = useState(fixed)
    const [offsetTop, setOffsetTop] = useState(0)
    const { not_sellable, bundle_products } = product as ProductInterface
    const isSellable = useMemo(() => {
        return !not_sellable && !bundle_products?.some((p) => p?.not_sellable)
    }, [not_sellable, bundle_products])
    const getUserLanguage = router.locale || ' '
    const userLanguage = (getUserLanguage && getUserLanguage.includes('-')
        ? getUserLanguage.split('-')[0]
        : getUserLanguage
    ).toLowerCase()
    const isShowPrice = useMemo(() => {
        if (router.locale) {
            return !isNonTransactionalRegion(router.locale)
        }
        return true
    }, [router.locale])
    const { isAnimationStopped } = useAnimationAndVideosToggle()
    const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

    const toggleMobileMenu = useCallback(() => {
        setIsMobileMenuOpen(!isMobileMenuOpen)
    }, [isMobileMenuOpen])

    useEffect(() => {
        const handleScroll = () => {
            if (containerRef.current) {
                if (!fixedAtPosition) {
                    let offsetTop = (document.querySelector(
                        '.contentful-page'
                    ) as HTMLElement)?.offsetTop
                    const panelWithShowId = document.getElementById(showId)
                    if (panelWithShowId) {
                        offsetTop = panelWithShowId.offsetTop
                    }
                    if (
                        window.pageYOffset + 5 >
                        (offsetTop ? offsetTop : window.innerHeight)
                    ) {
                        setIsActive(true)
                        setIsToggleVisible(true)
                        // setTimeout(() => {
                        //     const element = document.getElementById(
                        //         'tooltip-ai'
                        //     )
                        //     //if (element) element.style.display = 'none'
                        // }, 5000)
                    } else {
                        setIsActive(false)
                        setIsDropdownOpen(false)
                        setIsToggleVisible(false)
                    }
                } else if (offsetTop && window.pageYOffset >= offsetTop) {
                    setIsFixed(true)
                } else {
                    setIsFixed(false)
                    setIsDropdownOpen(false)
                    setIsToggleVisible(false)
                }
            }
        }
        if (typeof document !== 'undefined') {
            document.addEventListener('scroll', handleScroll)
        }

        return () => {
            if (typeof document !== 'undefined') {
                document.removeEventListener('scroll', handleScroll)
            }
        }
    }, [containerRef, offsetTop, showId])

    // custom implementation
    const initChatSimpleEvents = () => {
        // only interact once per typing a question
        let chatSimpleInteracted = false

        const coPilot = document.querySelector('co-pilot')
        const input = coPilot?.shadowRoot?.querySelector('input')
        const submitBtn = coPilot?.shadowRoot?.querySelector('button.p-2')
        const closeBtn = coPilot?.shadowRoot?.querySelector(
            'button.rounded-full'
        )

        const keyupFunction = (e: KeyboardEvent) => {
            // enter means submitted
            if (e.key === 'Enter') {
                pushToDataLayer(getChatSimpleEvent('submitted'))
                input?.removeEventListener('keyup', keyupFunction)
            } else {
                // only trigger one event
                if (!chatSimpleInteracted) {
                    const chatSimpleEvent = getChatSimpleEvent('interacted')
                    pushToDataLayer(chatSimpleEvent)
                    // interaction to true
                    chatSimpleInteracted = true
                }
            }
        }
        const submitFunction = (e: Event) => {
            pushToDataLayer(getChatSimpleEvent('submitted'))
            submitBtn?.removeEventListener('click', submitFunction)
        }

        const closeFunction = (e: Event) => {
            pushToDataLayer(getChatSimpleEvent('closed'))
            closeBtn?.removeEventListener('click', closeFunction)
        }

        // handle events
        input?.addEventListener('keyup', keyupFunction)
        submitBtn?.addEventListener('click', submitFunction)
        closeBtn?.addEventListener('click', closeFunction)
    }
    const clickChatSimple = () => {
        const inputField = document
            .querySelector('co-pilot')
            ?.shadowRoot?.querySelector('input')
        if (inputField !== null) {
            setChatSimpleStarted(true)
            pushToDataLayer(getChatSimpleEvent('started'))
            initChatSimpleEvents()
        } else {
            setChatSimpleStarted(false)
        }
    }
    useEffect(() => {
        if (!offsetTop && containerRef.current) {
            const bodyRect = document.body.getBoundingClientRect()
            const elemRect = containerRef.current.getBoundingClientRect()
            const offset = elemRect.top - bodyRect.top
            setOffsetTop(offset)
        }
    }, [containerRef])
    useEffect(() => {
        const handleClickOutside = (e: any): void => {
            if (
                dropdownRef.current &&
                toggleRef.current &&
                !dropdownRef.current.contains(e.target) &&
                !toggleRef.current.contains(e.target)
            ) {
                setIsDropdownOpen(false)
            }
        }

        document.addEventListener('mousedown', handleClickOutside)
        return () => {
            document.removeEventListener('mousedown', handleClickOutside)
        }
    }, [dropdownRef])

    useEffect(() => {
        setStickyNav(true)
    }, [])
    const additionalButtons: LinkResponse[] = useMemo(() => {
        const buttonStyles = ['secondary', 'primary']
        return links.filter((link) => {
            return link.style && buttonStyles.includes(link.style)
        })
    }, [links])
    const listLinks: LinkResponse[] = useMemo(
        () => links.filter((link) => !link.style || link.style === 'link'),
        [links]
    )

    const stickynavbuyVisible =
        (price || (product && product.uid) || addProductHandler) &&
        isSellable &&
        product &&
        isShowPrice
    const buttonsVisible =
        additionalButtons.length > 0 ||
        (isMobile && listLinks && listLinks.length > 0)

    return (
        <div
            className={cn(
                'sticky-nav',
                s['sticky-nav'],
                { [s['sticky-nav--active']]: isActive },
                { [s['sticky-nav--fixed']]: isFixed },
                { [s['sticky-nav--dark']]: pageTheme === 'dark' },
                {
                    [s['sticky-nav--fixed-pos']]: fixedAtPosition && !isFixed
                },
                { [s[`page-theme-${pageTheme}`]]: pageTheme },
                { [s['sticky-nav--mobile-open']]: isMobileMenuOpen },
                className
            )}
            ref={containerRef}
        >
            <div className={s['sticky-nav__inner']}>
                {/* Mobile header layout */}
                <div className={s['sticky-nav__mobile-header']}>
                    <div className={s['sticky-nav__left-section']}>
                        <button
                            className={s['sticky-nav__menu-button']}
                            onClick={toggleMobileMenu}
                            aria-label={t('common:menu')}
                        >
                            {isMobileMenuOpen ? (
                                <SmallCloseIcon />
                            ) : (
                                <BurgerMenuIcon />
                            )}
                        </button>
                    </div>

                    <div className={s['sticky-nav__mobile-cart']}>
                        {(price ||
                            (product && product.uid) ||
                            addProductHandler) &&
                            isSellable &&
                            product &&
                            isShowPrice && (
                                <StickyNavBuy
                                    price={price}
                                    priceDiscount={priceDiscount}
                                    buttonLabel={t(
                                        `cart|${ButtonLabel.ADD_TO_CART}`
                                    )}
                                    product={product}
                                    addProductHandler={addProductHandler}
                                    buttonDesktopSize={
                                        pageTheme === 'neo' ? 'm' : undefined
                                    }
                                />
                            )}
                    </div>
                </div>

                <div
                    className={cn(s['sticky-nav__mobile-menu'], {
                        [s['sticky-nav__mobile-menu--open']]: isMobileMenuOpen
                    })}
                >
                    {listLinks && listLinks.length > 0 && (
                        <StickyNavLinkList
                            linkList={listLinks}
                            className={cn(s['sticky-nav__mobile-links'])}
                            toggleTextOne={toggleTextOne}
                            toggleTextTwo={toggleTextTwo}
                            toggleOneUrl={toggleOneUrl}
                            toggleTwoUrl={toggleTwoUrl}
                        />
                    )}

                    {additionalButtons.length > 0 && (
                        <ul className={s['sticky-nav__mobile-buttons']}>
                            {additionalButtons.map((button, i) => (
                                <li key={`sticky-nav-button-${i}`}>
                                    <Button
                                        href={button.linkUrl}
                                        variant={button.style}
                                        color={button.styleColor}
                                        dataLayer={button.eventTracking}
                                        newTab={button.newTab}
                                        className={cn(s['sticky-nav-button'])}
                                        isNeo={pageTheme === 'neo'}
                                        downloadableContent={
                                            button.downloadableContent
                                        }
                                        desktopSize={
                                            pageTheme === 'neo'
                                                ? 'm'
                                                : undefined
                                        }
                                        label={button.linkTitle}
                                        aria-label={`${button.linkTitle} - ${t(
                                            'ada|Opens in the current Tab'
                                        )}`}
                                    >
                                        {button.icon &&
                                        button.icon !== 'none' ? (
                                            <>
                                                <Icon
                                                    name={button.icon}
                                                    style={{
                                                        width: 'fit-content'
                                                    }}
                                                />
                                                {button.linkTitle}
                                            </>
                                        ) : (
                                            button.linkTitle
                                        )}
                                    </Button>
                                </li>
                            ))}
                        </ul>
                    )}
                </div>

                <div
                    className={cn(
                        s['sticky-nav__title-wrapper'],
                        'font-universLtStd md:absolute z-10',
                        {
                            [s['sticky-nav__title-wrapper--with-buy']]:
                                (price || buyProduct) && product && isSellable,
                            [s[
                                'sticky-nav__title-wrapper-isShowAiTool'
                            ]]: isShowAiTool,
                            [s['sticky-nav--with-toggle']]:
                                toggleTextOne || toggleTextTwo
                        }
                    )}
                    onClick={withDropdown ? toggleDropdown : undefined}
                    onKeyPress={withDropdown ? toggleDropdown : undefined}
                    role={withDropdown ? 'button' : undefined}
                    tabIndex={withDropdown ? 0 : undefined}
                    ref={toggleRef}
                    {...(withDropdown && { 'aria-label': 'Button Dropdown' })}
                >
                    {(headline ||
                        productLogo ||
                        (toggleTextOne && toggleTextTwo)) && (
                        <div
                            className={s['sticky-nav__title-container']}
                            aria-label={headline ?? 'Headline'}
                        >
                            {headline && !productLogo && (
                                <div
                                    className="flex gap-5"
                                    onClick={() => {
                                        if (!toggleTextOne) {
                                            window.scrollTo({
                                                top: 0,
                                                behavior: 'smooth'
                                            })
                                        }
                                    }}
                                    role="button"
                                    tabIndex={0}
                                    onKeyPress={(e) => {
                                        if (
                                            e.key === 'Enter' &&
                                            !toggleTextOne &&
                                            !toggleTextTwo
                                        ) {
                                            window.scrollTo({
                                                top: 0,
                                                behavior: 'smooth'
                                            })
                                        }
                                    }}
                                >
                                    <StickyNavLogoElgato
                                        className={s['sticky-nav__logo']}
                                    />
                                    <h5 className={cn(s['sticky-nav__title'])}>
                                        {headline}
                                    </h5>
                                </div>
                            )}
                            {productLogo && !headline && (
                                <div
                                    onClick={() => {
                                        if (!toggleTextOne) {
                                            window.scrollTo({
                                                top: 0,
                                                behavior: 'smooth'
                                            })
                                        }
                                    }}
                                    role="button"
                                    tabIndex={0}
                                    onKeyPress={(e) => {
                                        if (
                                            e.key === 'Enter' &&
                                            !toggleTextOne &&
                                            !toggleTextTwo
                                        ) {
                                            window.scrollTo({
                                                top: 0,
                                                behavior: 'smooth'
                                            })
                                        }
                                    }}
                                >
                                    <SvgLogo
                                        url={
                                            productLogo?.[0]
                                                ?.original_secure_url || ''
                                        }
                                    />
                                </div>
                            )}
                            {toggleTextOne &&
                                toggleTextTwo &&
                                isToggleVisible && (
                                    <StickyNavToggleComponent
                                        toggleTextOne={toggleTextOne}
                                        toggleTextTwo={toggleTextTwo}
                                        toggleOneUrl={toggleOneUrl}
                                        toggleTwoUrl={toggleTwoUrl}
                                        toggleOneSku={toggleOneSku}
                                        toggleTwoSku={toggleTwoSku}
                                    />
                                )}
                            {showAiNavigation && chatSimpleConfiguration && (
                                <div
                                    id="wrapper"
                                    className={cn(s['sticky-nav__copilot'], {
                                        [s[
                                            'sticky-nav__copilot__show-legal'
                                        ]]: chatSimpleStarted
                                    })}
                                    style={{
                                        display: isActive ? 'block' : 'none'
                                    }}
                                >
                                    <co-pilot
                                        platform-id={
                                            chatSimpleConfiguration.platformId
                                        }
                                        user-id={chatSimpleConfiguration.userId}
                                        chatbot-id={
                                            chatSimpleConfiguration.chatbotId
                                        }
                                        gtm-id={chatSimpleConfiguration.gtmId}
                                        is-local={
                                            chatSimpleConfiguration.isLocal
                                        }
                                        language={userLanguage}
                                        onClick={clickChatSimple}
                                    />
                                    <div
                                        className={cn(
                                            s['sticky-nav__copilot__legal']
                                        )}
                                        dangerouslySetInnerHTML={{
                                            __html: t('common||AI privacy note')
                                                .replace(
                                                    '{',
                                                    '<a target="_blank" rel="noreferrer" href="https://help.corsair.com/hc/en-us/articles/30601855766669-AI-Chat-Privacy-Policy">'
                                                )
                                                .replace('}', '</a>')
                                        }}
                                    />
                                </div>
                            )}
                            {/* <StickyNavLinkList
                                linkList={listLinks}
                                className={cn(
                                    s['sticky-nav__links'],
                                    s['sticky-nav__links--desktop'],
                                    'sticky-nav-chat-helper'
                                )}
                            /> */}
                        </div>
                    )}
                    {dropdownLinks && dropdownLinks.length > 0 && (
                        <div
                            className={cn(s['sticky-nav__dropdown-icon'], {
                                [s[
                                    'sticky-nav__dropdown-icon--rotated'
                                ]]: isDropdownOpen
                            })}
                        >
                            <ChevronDownIcon />
                        </div>
                    )}
                </div>
                {callout?.title && isDisplayCallout && (
                    <p
                        className={s['sticky-nav__callout']}
                        dangerouslySetInnerHTML={{ __html: callout.title }}
                        style={{
                            fontSize: `${calloutFontSize}px`,
                            color: `${calloutColor}`
                        }}
                    />
                )}
                {(headline || productLogo || toggleTextOne || toggleTextTwo) &&
                ((listLinks && listLinks.length > 0) ||
                    price ||
                    buyProduct ||
                    additionalButtons.length > 0) ? (
                    <div className={s['sticky-nav__links-wrapper']}>
                        {listLinks && listLinks.length > 0 && (
                            <StickyNavLinkList
                                linkList={listLinks}
                                className={cn(
                                    s['sticky-nav__links'],
                                    s['sticky-nav__links--desktop'],
                                    'sticky-nav-chat-helper'
                                )}
                            />
                        )}
                        {(stickynavbuyVisible || buttonsVisible) && (
                            <div className="flex flex-row gap-8px items-center">
                                {stickynavbuyVisible && (
                                    <StickyNavBuy
                                        price={price}
                                        priceDiscount={priceDiscount}
                                        buttonLabel={t(
                                            `cart|${ButtonLabel.ADD_TO_CART}`
                                        )}
                                        product={product}
                                        addProductHandler={addProductHandler}
                                        buttonDesktopSize={
                                            pageTheme === 'neo'
                                                ? 'm'
                                                : undefined
                                        }
                                    />
                                )}
                                <div>
                                    {(additionalButtons.length > 0 ||
                                        (listLinks &&
                                            listLinks.length > 0)) && (
                                        <div
                                            className={
                                                s[
                                                    'sticky-nav__links-buttons-wrapper'
                                                ]
                                            }
                                        >
                                            {additionalButtons.length > 0 && (
                                                <ul
                                                    className={cn(
                                                        s[
                                                            'sticky-nav__buttons'
                                                        ],
                                                        {
                                                            [s[
                                                                'sticky-nav__buttons--no-animation'
                                                            ]]: isAnimationStopped
                                                        }
                                                    )}
                                                >
                                                    {additionalButtons.map(
                                                        (button, i) => (
                                                            <li
                                                                key={`sticky-nav-button-${i}`}
                                                            >
                                                                <Button
                                                                    href={
                                                                        button.linkUrl
                                                                    }
                                                                    variant={
                                                                        button.style
                                                                    }
                                                                    color={
                                                                        button.styleColor
                                                                    }
                                                                    dataLayer={
                                                                        button.eventTracking
                                                                    }
                                                                    newTab={
                                                                        button.newTab
                                                                    }
                                                                    className={
                                                                        s[
                                                                            'sticky-nav-button'
                                                                        ]
                                                                    }
                                                                    iconAlignment={
                                                                        button.iconAlignment
                                                                    }
                                                                    download={
                                                                        button.download
                                                                    }
                                                                    isNeo={
                                                                        pageTheme ===
                                                                        'neo'
                                                                    }
                                                                    desktopSize={
                                                                        pageTheme ===
                                                                        'neo'
                                                                            ? 'm'
                                                                            : undefined
                                                                    }
                                                                    label={
                                                                        button.linkTitle
                                                                    }
                                                                    aria-label={`${
                                                                        button.linkTitle
                                                                    } - ${t(
                                                                        'ada|Opens in the current Tab'
                                                                    )}`}
                                                                    downloadableContent={
                                                                        button.downloadableContent
                                                                    }
                                                                >
                                                                    {button.icon &&
                                                                    button.icon !==
                                                                        'none' ? (
                                                                        <>
                                                                            <Icon
                                                                                name={
                                                                                    button.icon
                                                                                }
                                                                                style={{
                                                                                    width:
                                                                                        'fit-content'
                                                                                }}
                                                                            />
                                                                            {
                                                                                button.linkTitle
                                                                            }
                                                                        </>
                                                                    ) : (
                                                                        button.linkTitle
                                                                    )}
                                                                </Button>
                                                            </li>
                                                        )
                                                    )}
                                                </ul>
                                            )}
                                            {listLinks &&
                                                listLinks.length > 0 && (
                                                    <StickyNavLinkList
                                                        linkList={listLinks}
                                                        className={cn(
                                                            s[
                                                                'sticky-nav__links'
                                                            ],
                                                            s[
                                                                'sticky-nav__links--mobile'
                                                            ]
                                                        )}
                                                    />
                                                )}
                                        </div>
                                    )}
                                </div>
                            </div>
                        )}
                    </div>
                ) : null}

                {children}
                {dropdownLinks && dropdownLinks.length > 0 && (
                    <div
                        className={cn(s['sticky-nav__dropdown'], {
                            [s['sticky-nav__dropdown--active']]: isDropdownOpen
                        })}
                        ref={dropdownRef}
                    >
                        <StickyNavProductDropdown items={dropdownLinks} />
                    </div>
                )}
            </div>
            {promotionBanner && (
                <StickyNavigationBanner
                    message={promotionBanner.message}
                    buttons={promotionBanner.buttons}
                />
            )}
        </div>
    )
}

export default StickyNav
