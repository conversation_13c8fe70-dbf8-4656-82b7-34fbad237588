.sticky-nav-toggle {
    height: 50px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    @screen md-max {
        max-height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        /* max-width: 340px; */
        width: 100%;
    }
    &__button {
        height: 40px;
        width: 117px;
        padding: 12px 16px;
        border-radius: 9999px;
        color: white;
        background-color: transparent;
        border: none;
        cursor: pointer;

        &:hover {
            background-color: rgba(255, 255, 255, 0.5);
            color: black;
        }
        @screen md-max {
            width: 100%;
            max-height: 20px;
        }

        &--active {
            background-color: white;
            color: black;
        }
    }
} 