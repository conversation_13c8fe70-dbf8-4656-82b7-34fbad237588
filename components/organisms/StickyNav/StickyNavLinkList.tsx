import { FC } from 'react'
import { Link, LinkResponse } from '@components/molecules/Link/Link'
import { useTranslation } from 'react-i18next'
import cn from 'classnames'
import s from './StickyNav.module.scss'
import { useLayoutContext } from '@components/layouts/MainLayout/LayoutContext'
import StickyNavToggleComponent from '@components/organisms/StickyNav/StickyNavToggleComponent'

type StickyNavLinkListProps = {
    linkList: LinkResponse[]
    className?: string
    toggleTextOne?: string
    toggleTextTwo?: string
    toggleOneUrl?: string
    toggleTwoUrl?: string
}

export const StickyNavLinkList: FC<StickyNavLinkListProps> = ({
    linkList,
    className,
    toggleTextOne,
    toggleTextTwo,
    toggleOneUrl,
    toggleTwoUrl
}) => {
    const { t } = useTranslation()
    const { pageTheme } = useLayoutContext()
    return (
        <ul className={className}>
            {toggleTextOne && toggleTextTwo && (
                <div className={s['sticky-nav__mobile-toggle']}>
                    <StickyNavToggleComponent
                        toggleTextOne={toggleTextOne}
                        toggleTextTwo={toggleTextTwo}
                        toggleOneUrl={toggleOneUrl}
                        toggleTwoUrl={toggleTwoUrl}
                    />
                </div>
            )}
            {linkList.map((link, i) => {
                if (pageTheme === 'neo') {
                    return (
                        <li key={`sticky-nav-link-${i}`}>
                            <Link
                                link={link}
                                className={cn(
                                    s['sticky-nav__link'],
                                    'btn-neo btn-neo-tertiary btn-neo-desktop-m'
                                )}
                                aria-label={`${link.linkTitle} - ${t(
                                    'ada|Opens in the current Tab'
                                )}`}
                            >
                                <div className="btn-neo-inner">
                                    {link.linkTitle}
                                </div>
                                <div className="btn-neo-inner">
                                    {link.linkTitle}
                                </div>
                            </Link>
                        </li>
                    )
                } else {
                    return (
                        <li key={`sticky-nav-link-${i}`}>
                            <Link
                                link={link}
                                aria-label={`${link.linkTitle} - ${t(
                                    'ada|Opens in the current Tab'
                                )}`}
                                className={cn(
                                    s['sticky-nav__link'],
                                    'button-text-underlined'
                                )}
                            />
                        </li>
                    )
                }
            })}
        </ul>
    )
}
