.sticky-nav {
    @apply relative w-full;
    background-color: transparent;
    @screen md {
        padding: 16px;
    }
    @screen md {
        border: none;
        height: auto;
    }

    &__copilot {
        margin: -10px 0;
        height: 48px;
        @media (max-width: 863px) {
            display: none !important;
        }

        @media (min-width: 1791px) {
            left: 80px;
        }

        &__legal {
            background-color: black;
            color: white;
            border: solid 1px rgba(255, 255, 255, 0.3);
            padding: 6px 12px;
            border-radius: 8px;
            position: absolute;
            font-size: 12px;
            opacity: 0;
            visibility: hidden;
            pointer-events: none;
            transition: all 0.3s ease-out;

            a {
                text-decoration: underline;
            }
        }

        &__show-legal &__legal {
            pointer-events: all;
            opacity: 1;
            visibility: visible;
        }
    }

    &__title {
        @apply font-univers67BoldCondensed;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        padding-top: 2px;
    }

    &__callout {
        margin-left: 20px;
        min-height: 29px;
        display: flex;
        align-items: flex-end;
        padding-bottom: 5px;
        line-height: 1;
        color: var(--black);
    }

    &__inner {
        display: flex;
        justify-content: space-between;
        width: 100%;
        flex-wrap: wrap;
        align-items: center;

        @screen md {
            flex-wrap: nowrap;
            // @apply p-16px;
            height: auto;
            position: relative;
        }
        @screen md-max {
            padding: 8px;
        }
        @screen xxl {
            @apply px-16px;
        }
    }

    &__dropdown-icon {
        width: 24px;
        height: 24px;
        flex: 0 0 auto;

        display: flex;
        justify-content: center;
        align-items: center;
        @apply transform rotate-0 transition-transform duration-300;

        &--rotated {
            @apply -rotate-180;
        }
    }

    &__title-wrapper {
        display: flex;
        align-items: center;
        gap: 16px;
        margin-right: 24px;
        background-color: var(--white);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        border-radius: 8px;
        color: var(--black);
        @screen md-max {
            min-height: 36px;
            width: 100vw;
            justify-content: space-between;
        }

        &--with-buy {
            @screen md-max {
                width: calc(100vw - 100px);
            }
        }
    }

    &--with-toggle {
        @screen md-max {
            display: none;
        }
        position: relative;
        height: 65px;
        padding: 8px;
        width: fit-content;

        &.sticky-nav__title-wrapper {
            padding: 8px;
            border-radius: 999px;
        }

        .sticky-nav__title-container {
            padding: 0;
        }
    }

    // &__mobile-toggle {
    //     display: flex;
    //     align-items: center;
    //     justify-content: center;
    //     width: 100%;
    //     height: 100%;
    //     max-height: 40px;
    // }

    &__buy,
    &__links {
        display: flex;
        align-items: center;
        color: var(--black);

        > * {
            flex: 0 0 auto;
        }
    }

    &__links-wrapper {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        flex: 0 0 auto;
        background-color: var(--white);
        padding: 8px 24px;
        border-radius: 12px;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        gap: 24px;
        width: fit-content;
        margin-left: auto;

        @screen md {
            white-space: nowrap;
            height: 60px;
        }
    }

    &__links {
        gap: 24px;
        order: 1;
        flex-wrap: wrap;
        color: var(--black);
        @screen md {
            flex-wrap: nowrap;
            gap: 24px;
            flex: 1 1 auto;
            justify-content: flex-end;
            order: 0;
            margin-top: 0;
            margin-bottom: 0;
        }
    }

    &__links.sticky-nav__links--mobile {
        flex-wrap: nowrap;

        @screen md {
            display: none;
        }
    }

    &__links.sticky-nav__links--desktop {
        @apply relative z-5;

        @screen md-max {
            display: none;
        }
    }

    &__buy + &__links-buttons-wrapper {
        .sticky-nav__buttons {
            padding-top: 0;
        }
    }

    &__links-buttons-wrapper {
        @screen md-max {
            display: flex;
            flex: 1 0 auto;
            flex-direction: row;
            justify-content: space-between;
            max-width: 100%;
            gap: 8px 16px;
            overflow: auto;
        }
    }

    &__buttons {
        gap: 16px;
        display: flex;
        align-items: center;
        justify-content: center;

        .sticky-nav-button {
            white-space: nowrap;
        }

        &--no-animation a {
            animation: none !important;
            transition: none !important;
        }
    }

    &__buy {
        gap: 24px;

        div:nth-child(1) {
            font-size: 18px;
            color: var(--black);
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            @apply font-univers55Roman font-bold;
        }

        div:nth-child(2) {
            text-align: right;
            font-size: 14px;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            @apply font-univers55Roman font-bold;
        }

        :global {
            a.btn,
            button.btn {
                @media (max-width: 1200px) {
                    width: 36px;
                    height: 36px;
                    border-radius: 6px;
                    padding: 0;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    min-height: 0;
                }

                > svg {
                    @media (max-width: 1200px) {
                        width: 18px;
                        height: 18px;
                    }
                }
            }
        }
    }

    &__price {
        @apply hidden lg:block;
        margin-top: 0;
    }

    &__dropdown {
        position: absolute;
        left: 0;
        top: calc(100% + 2px);
        width: 100%;
        opacity: 0;
        pointer-events: none;

        &--active {
            opacity: 1;
            pointer-events: auto;
            z-index: 200;
        }

        @screen md {
            top: 100%;
            padding-left: 16px;
        }
    }

    &--fixed {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        z-index: 149;
        transform: translate(0, -100%);
        transition: 0.3s ease-out;
        will-change: transform;
        background-color: transparent;
        @screen md-max {
            background-color: var(--white);
        }

        &.sticky-nav--active {
            transform: translate(0, 0);
        }
    }

    &--fixed-pos {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
    }

    &__title-container {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 18px 16px;
    }

    &__logo {
        width: 24px;
        height: 24px;
    }

    &__cart-button {
        display: flex !important;
        align-items: center !important;
        gap: 8px !important;

        .sticky-nav__cart-icon {
            width: 24px;
            height: 24px;
        }
    }

    &__mobile-header {
        display: none;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        background: var(--white);

        @screen md-max {
            display: flex;
        }

        .sticky-nav__title-container {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
    }

    &__menu-button {
        background: none;
        cursor: pointer;
        display: flex;
        align-items: center;
        border-radius: 6px;
        border: 1px solid #232323;
        padding: 8px;
    }

    &__mobile-menu {
        display: block;
        position: absolute;
        top: 100%;
        background: var(--white);
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        z-index: 100;
        max-height: 0;
        overflow: hidden;
        width: 100%;
        left: 0;
        transition: all 0.5s ease-in-out;

        &--open {
            max-height: 500px;
        }
    }

    &__mobile-links {
        padding: 16px;
        display: flex;
        flex-direction: column;
        gap: 24px;
        border-top: 1px solid var(--light-grey-1);
        color: var(--black);
    }

    &__mobile-buttons {
        padding: 16px;
        list-style: none;
    }

    @screen md-max {
        &__links-wrapper {
            display: none;
        }

        &__title-wrapper {
            display: none !important;
        }
    }

    &__left-section {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    @screen md-max {
        &__title-container {
            .sticky-nav__logo {
                width: 24px;
                height: auto;
            }

            .sticky-nav__title {
                font-size: 16px;
                margin: 0;
                color: var(--black);
            }
        }
    }

    &__mobile-cart {
        display: flex;
        align-items: center;

        .sticky-nav__price {
            display: block;

            div:nth-child(1) {
                font-size: 16px;
            }

            div:nth-child(2) {
                display: none;
            }
        }
    }
}

/* page theme dark */
.sticky-nav.page-theme-dark {
    $dark-bg: #323232;
    $dark-text: var(--white);
    background-color: transparent;
    color: $dark-text;

    color: var(--white);

    .sticky-nav__mobile-menu {
        background-color: $dark-bg;
    }

    .sticky-nav__inner {
        @screen md-max {
            padding: 0;
        }
    }

    .sticky-nav--fixed {
        background-color: transparent;
    }

    // Common dark theme styles
    .sticky-nav__mobile-links,
    .sticky-nav__title-wrapper,
    .sticky-nav__links-wrapper,
    .sticky-nav__mobile-header {
        background-color: $dark-bg;
        color: $dark-text;
    }

    .sticky-nav__links,
    .sticky-nav__logo,
    .sticky-nav__title,
    .sticky-nav__title-container {
        color: $dark-text;
    }

    .sticky-nav__buy {
        div:nth-child(1),
        div:nth-child(2) {
            color: $dark-text;
        }
    }

    .sticky-nav__mobile-header {
        padding: 8px;

        .sticky-nav__menu-button {
            border-color: $dark-text;
        }

        .sticky-nav__mobile-buttons {
            color: $dark-text;
        }
    }
}

/* page theme neo */
.sticky-nav.page-theme-neo {
    padding: 16px;

    .sticky-nav__buy {
        .sticky-nav__price {
            display: flex;
            flex-direction: column;
            align-items: flex-end;

            div:nth-child(1) {
                line-height: 1;
            }
        }
    }

    // @screen md-max {
    //     padding-left: 0;
    //     padding-right: 0;
    // }

    // @screen md {
    //     padding-left: 3.2rem;
    //     padding-right: 3.2rem;
    // }

    // @screen lg2 {
    //     padding-left: 6.4rem;
    //     padding-right: 6.4rem;
    // }

    .sticky-nav__inner {
        margin: 0;
        max-width: 100%;
        padding: 0;
    }

    .sticky-nav__title-wrapper, .sticky-nav__links-wrapper {
        border-radius: 999px;
    }

    .sticky-nav__links-wrapper {
        padding: 8px;

        .sticky-nav__links {
            gap: 24px;
            padding: 24px;
        }

        :global {
            .btn-neo.btn-neo-tertiary {
                padding: 8px;
                text-decoration: underline;
            }
        }
    }

    .sticky-nav__buy,
    .sticky-nav__links {
        @screen md {
            margin-left: 0;
        }
    }

    .sticky-nav__links {
        @screen md {
            flex-wrap: nowrap;
            gap: 10px 0;
        }
    }
}

.sticky-nav__svg-logo {
    svg {
        height: 24px;
        width: 100%;
    }
}
