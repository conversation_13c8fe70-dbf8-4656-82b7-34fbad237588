import React, { FC } from 'react'
import s from './StickyNavToggleComponent.module.scss'
import cn from 'classnames'
import { useRouter } from 'next/router'
import { usePDPProduct } from '@components/corra/PDPProductProvider'

type StickyNavToggleComponentProps = {
    toggleTextOne: string
    toggleTextTwo: string
    toggleOneUrl?: string
    toggleTwoUrl?: string
    toggleOneSku?: string
    toggleTwoSku?: string
}

const StickyNavToggleComponent: FC<StickyNavToggleComponentProps> = ({
    toggleTextOne,
    toggleTextTwo,
    toggleOneUrl,
    toggleTwoUrl,
    toggleOneSku,
    toggleTwoSku
}) => {
    const router = useRouter()
    const currentPath = router.asPath
    const { product, updateProduct } = usePDPProduct()

    // Use URL to determine active state instead of SKU
    const isButton1Active = toggleOneUrl
        ? currentPath.includes(toggleOneUrl)
        : false
    const isButton2Active = toggleTwoUrl
        ? currentPath.includes(toggleTwoUrl)
        : false

    const handleButton1Click = (e: React.MouseEvent) => {
        e.preventDefault()
        if (toggleOneSku) {
            updateProduct({ ...product, sku: toggleOneSku })
        }
        if (toggleOneUrl) {
            router.push(toggleOneUrl)
        }
    }

    const handleButton2Click = (e: React.MouseEvent) => {
        e.preventDefault()
        if (toggleTwoSku) {
            updateProduct({ ...product, sku: toggleTwoSku })
        }
        if (toggleTwoUrl) {
            router.push(toggleTwoUrl)
        }
    }

    const handleKeyPress1 = (e: React.KeyboardEvent) => {
        if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault()
            if (toggleOneSku) {
                updateProduct({ ...product, sku: toggleOneSku })
            }
            if (toggleOneUrl) {
                router.push(toggleOneUrl)
            }
        }
    }

    const handleKeyPress2 = (e: React.KeyboardEvent) => {
        if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault()
            if (toggleTwoSku) {
                updateProduct({ ...product, sku: toggleTwoSku })
            }
            if (toggleTwoUrl) {
                router.push(toggleTwoUrl)
            }
        }
    }
    return (
        <div
            className={cn(
                s['sticky-nav-toggle'],
                'flex items-center bg-black p-2 w-fit-content rounded-full gap-2'
            )}
        >
            <button
                className={cn(
                    s['sticky-nav-toggle__button'],
                    'font-univers55Roman flex items-center justify-center btn-sm',
                    {
                        [s[
                            'sticky-nav-toggle__button--active'
                        ]]: isButton1Active
                    }
                )}
                onClick={handleButton1Click}
                onKeyPress={handleKeyPress1}
            >
                {toggleTextOne}
            </button>
            <button
                className={cn(
                    s['sticky-nav-toggle__button'],
                    'font-univers55Roman flex items-center justify-center btn-sm',
                    {
                        [s[
                            'sticky-nav-toggle__button--active'
                        ]]: isButton2Active
                    }
                )}
                onClick={handleButton2Click}
                onKeyPress={handleKeyPress2}
            >
                {toggleTextTwo}
            </button>
        </div>
    )
}

export default StickyNavToggleComponent
