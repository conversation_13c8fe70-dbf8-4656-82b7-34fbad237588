.comparison-cards {
    grid-template-columns: repeat(var(--grid-container-cols), 1fr);
    grid-template-rows: auto auto;
    @screen lg {
        max-width: 1190px;
        margin: 0 auto;
    }

    &__text-panel {
        @screen xl {
            max-width: 888px;
        }
    }

    video {
        border-radius: 8px;
    }
}

.comparison-card {
    &__text {
        max-width: 487px;
        margin: 0 auto;
    }

    &__image--on-image {
        & > * {
            @screen md {
                @apply min-h-full h-full;
            }
        }
        video {
            @screen md {
                @apply h-full object-cover;
            }
        }
    }
}
