import ArrowLeftThin from '@components/icons/ArrowLeftThin'
import ArrowThin from '@components/icons/ArrowThin'
import CloseThin from '@components/icons/CloseThin'
import Reset from '@components/icons/Reset'
import ProductQuizView from '@components/organisms/ProductQuizView/ProductQuizView'
import ProductQuizViewResult from '@components/organisms/ProductQuizViewResult/ProductQuizViewResult'
import { pushToDataLayer } from '@corsairitshopify/pylot-gtm/src/utils'
import { getLinkClickEvent } from '@lib/gtm/linkClick'
import { useMobile } from '@pylot-data/hooks/use-mobile'
import cn from 'classnames'
import { useTranslation } from 'next-i18next'
import { FC, MutableRefObject, useEffect, useRef, useState } from 'react'
import s from './ProductQuiz.module.scss'

interface ProductQuizProps {
    content: any
}

const ProductQuiz: FC<ProductQuizProps> = ({ content }) => {
    const { t } = useTranslation(['common'])

    const productQuizViews = content.productQuizView
    const [activeIndex, setActiveIndex] = useState(0)
    const [clickedValues, setClickedValues] = useState<string[]>([])
    const [visitedViews, setVisitedViews] = useState<string[]>([])
    const [selectedValues, setSelectedValues] = useState<string[]>([])
    const [bg, setBg] = useState('')
    const variant = content.variant
    const productResults = content.productResults
    const { isMobile } = useMobile()
    const isBigConfigurator = variant === 'capture'

    const startOverBtnRef = useRef<any>(null)
    const startOverBigBtnRef = useRef<any>(null)
    const backBtnRef = useRef<any>(null)
    const backBigBtnRef = useRef<any>(null)

    const resultVariables: Record<string, string> = {}

    if (variant !== 'capture') {
        const resultEntries = content.translation.entries.filter(
            (entry: { initial: string }) => entry.initial.startsWith('result')
        )

        resultEntries.forEach(
            (entry: { translated: string }, index: number) => {
                resultVariables[`result${index + 1}`] = entry.translated
            }
        )
    }

    const newClickedValues = clickedValues.filter((value) => value !== '')

    useEffect(() => {
        if (variant == '4kX') {
            if (clickedValues.includes('hdr')) {
                if (clickedValues.includes('4k')) {
                    if (
                        clickedValues.includes('120') &&
                        (clickedValues.includes('playStation5') ||
                            clickedValues.includes('playStation5Pro') ||
                            clickedValues.includes('xBoxSeriesX') ||
                            clickedValues.includes('pc') ||
                            clickedValues.includes('xBoxSeriesS'))
                    ) {
                        setSelectedValues(['', resultVariables.result1])
                    }
                    if (
                        clickedValues.includes('120') &&
                        (clickedValues.includes('xBoxOneX') ||
                            clickedValues.includes('xBoxOneS'))
                    ) {
                        setSelectedValues(['', resultVariables.result29])
                    }
                    if (
                        clickedValues.includes('60') &&
                        (clickedValues.includes('xBoxOneX') ||
                            clickedValues.includes('xBoxOneS'))
                    ) {
                        setSelectedValues(['', resultVariables.result28])
                    }
                    if (
                        clickedValues.includes('60') &&
                        (clickedValues.includes('xBoxSeriesX') ||
                            clickedValues.includes('xBoxSeriesS'))
                    ) {
                        setSelectedValues(['', resultVariables.result33])
                    }
                    if (
                        clickedValues.includes('240') &&
                        clickedValues.includes('pc')
                    ) {
                        setSelectedValues(['', resultVariables.result26])
                    }
                    if (
                        clickedValues.includes('144') &&
                        clickedValues.includes('pc')
                    ) {
                        setSelectedValues(['', resultVariables.result7])
                    }

                    if (clickedValues.includes('playStation4Pro')) {
                        setSelectedValues(['', resultVariables.result3])
                    }
                    if (clickedValues.includes('iPad/tablet')) {
                        setSelectedValues(['', resultVariables.result30])
                    }
                } else if (clickedValues.includes('1440p')) {
                    if (
                        clickedValues.includes('120') &&
                        (clickedValues.includes('playStation5') ||
                            clickedValues.includes('playStation5Pro') ||
                            clickedValues.includes('xBoxSeriesX') ||
                            clickedValues.includes('xBoxOneX') ||
                            clickedValues.includes('xBoxOneS') ||
                            clickedValues.includes('pc') ||
                            clickedValues.includes('xBoxSeriesS'))
                    ) {
                        setSelectedValues(['', resultVariables.result3])
                    } else if (
                        clickedValues.includes('240') &&
                        clickedValues.includes('pc')
                    ) {
                        setSelectedValues(['', resultVariables.result8])
                    } else if (
                        clickedValues.includes('144') &&
                        clickedValues.includes('pc')
                    ) {
                        setSelectedValues(['', resultVariables.result8])
                    }
                } else if (clickedValues.includes('1080p')) {
                    if (
                        clickedValues.includes('240') &&
                        clickedValues.includes('pc')
                    ) {
                        setSelectedValues(['', resultVariables.result9])
                    } else if (
                        clickedValues.includes('144') &&
                        clickedValues.includes('pc')
                    ) {
                        setSelectedValues(['', resultVariables.result10])
                    }
                }
            } else if (clickedValues.includes('sdr')) {
                if (clickedValues.includes('4k')) {
                    if (
                        clickedValues.includes('240') &&
                        clickedValues.includes('pc')
                    ) {
                        setSelectedValues(['', resultVariables.result26])
                    }
                    if (
                        clickedValues.includes('playStation4Pro') &&
                        clickedValues.includes('sdr')
                    ) {
                        setSelectedValues(['', resultVariables.result12])
                    }
                    if (
                        clickedValues.includes('120') &&
                        (clickedValues.includes('xBoxOneX') ||
                            clickedValues.includes('xBoxOneS'))
                    ) {
                        setSelectedValues(['', resultVariables.result29])
                    }
                } else if (clickedValues.includes('1440p')) {
                    if (
                        clickedValues.includes('240') &&
                        clickedValues.includes('pc')
                    ) {
                        setSelectedValues(['', resultVariables.result27])
                    }
                }
            } else if (
                clickedValues.includes('playStation4') ||
                clickedValues.includes('xBoxOne')
            ) {
                setSelectedValues(['', resultVariables.result4])
            } else if (clickedValues.includes('8k')) {
                setSelectedValues(['', resultVariables.result11])
            } else if (clickedValues.includes('nintendoSwitch')) {
                setSelectedValues(['', resultVariables.result5])
            } else if (
                (clickedValues.includes('xBoxOneX') ||
                    clickedValues.includes('xBoxOneS')) &&
                clickedValues.includes('1080p')
            ) {
                setSelectedValues(['', resultVariables.result12])
            } else if (
                (clickedValues.includes('playStation4Pro') &&
                    clickedValues.includes('1080p')) ||
                (clickedValues.includes('playStation4Pro') &&
                    clickedValues.includes('1440p'))
            ) {
                setSelectedValues(['', resultVariables.result12])
            } else if (clickedValues.includes('iPad/tablet')) {
                if (!clickedValues.includes('4k')) {
                    setSelectedValues(['', resultVariables.result31])
                }
            }
        } else if (variant == '4kPro') {
            if (clickedValues.includes('hdr')) {
                if (clickedValues.includes('4k')) {
                    if (
                        clickedValues.includes('120') &&
                        (clickedValues.includes('playStation5') ||
                            clickedValues.includes('playStation5Pro') ||
                            clickedValues.includes('xBoxSeriesS') ||
                            clickedValues.includes('xBoxSeriesX'))
                    ) {
                        setSelectedValues(['', resultVariables.result14])
                    }
                    if (
                        clickedValues.includes('120') &&
                        (clickedValues.includes('playStation5') ||
                            clickedValues.includes('playStation5Pro') ||
                            clickedValues.includes('xBoxSeriesS') ||
                            clickedValues.includes('xBoxSeriesX'))
                    ) {
                        setSelectedValues(['', resultVariables.result14])
                    }
                    if (
                        clickedValues.includes('240') &&
                        clickedValues.includes('pc')
                    ) {
                        setSelectedValues(['', resultVariables.result21])
                    }
                    if (
                        clickedValues.includes('144') &&
                        clickedValues.includes('pc')
                    ) {
                        setSelectedValues(['', resultVariables.result22])
                    }
                    if (
                        clickedValues.includes('120') &&
                        clickedValues.includes('pc')
                    ) {
                        setSelectedValues(['', resultVariables.result23])
                    }
                } else if (clickedValues.includes('1440p')) {
                    if (
                        clickedValues.includes('144') &&
                        clickedValues.includes('pc')
                    ) {
                        setSelectedValues(['', resultVariables.result24])
                    }
                    if (
                        clickedValues.includes('240') &&
                        clickedValues.includes('pc')
                    ) {
                        setSelectedValues(['', resultVariables.result32])
                    }
                } else if (
                    clickedValues.includes('8k') &&
                    clickedValues.includes('pc') &&
                    clickedValues.includes('sdr')
                ) {
                    setSelectedValues(['', resultVariables.result20])
                } else if (
                    clickedValues.includes('8k') &&
                    clickedValues.includes('pc') &&
                    clickedValues.includes('hdr')
                ) {
                    setSelectedValues(['', resultVariables.result35])
                }
            } else if (clickedValues.includes('sdr')) {
                if (clickedValues.includes('4k')) {
                    if (
                        clickedValues.includes('120') &&
                        (clickedValues.includes('playStation5') ||
                            clickedValues.includes('playStation5Pro') ||
                            clickedValues.includes('xBoxSeriesS') ||
                            clickedValues.includes('xBoxSeriesX'))
                    ) {
                        setSelectedValues(['', resultVariables.result13])
                    }

                    if (
                        clickedValues.includes('240') &&
                        clickedValues.includes('pc')
                    ) {
                        setSelectedValues(['', resultVariables.result34])
                    }
                    if (
                        clickedValues.includes('144') &&
                        clickedValues.includes('pc')
                    ) {
                        setSelectedValues(['', resultVariables.result15])
                    }
                    if (
                        clickedValues.includes('120') &&
                        clickedValues.includes('pc')
                    ) {
                        setSelectedValues(['', resultVariables.result16])
                    }
                } else if (clickedValues.includes('1440p')) {
                    if (
                        clickedValues.includes('240') &&
                        clickedValues.includes('pc')
                    ) {
                        setSelectedValues(['', resultVariables.result17])
                    }
                } else if (clickedValues.includes('1080p')) {
                    if (
                        clickedValues.includes('144') &&
                        clickedValues.includes('pc')
                    ) {
                        setSelectedValues(['', resultVariables.result18])
                    }
                } else if (
                    clickedValues.includes('8k') &&
                    clickedValues.includes('pc')
                ) {
                    setSelectedValues(['', resultVariables.result19])
                }
            } else if (
                clickedValues.includes('playStation4') ||
                clickedValues.includes('xBoxOne')
            ) {
                setSelectedValues(['', resultVariables.result4])
            } else if (clickedValues.includes('nintendoSwitch')) {
                setSelectedValues(['', resultVariables.result5])
            } else if (
                clickedValues.includes('xBoxOneX') ||
                clickedValues.includes('xBoxOneS') ||
                clickedValues.includes('playStation4Pro') ||
                clickedValues.includes('iPad/tablet')
            ) {
                setSelectedValues(['', resultVariables.result25])
            } else if (clickedValues.includes('8k')) {
                setSelectedValues(['', resultVariables.result11])
            }
        }
    }, [clickedValues])

    const result =
        selectedValues && selectedValues.length > 0
            ? selectedValues
            : clickedValues

    const showProgressBar =
        activeIndex > 0 && activeIndex < productQuizViews.length - 1
    const progressWidth =
        ((activeIndex < productQuizViews.length - 1
            ? activeIndex
            : activeIndex - 1) /
            (productQuizViews.length - 2)) *
        100

    const handleBack = (ref: MutableRefObject<any>) => {
        pushToDataLayer(
            getLinkClickEvent(ref, {
                overrideClasses: 'guide-links-step',
                overrideText: 'Go back'
            })
        )

        const currentIndex = visitedViews.indexOf(activeIndex.toString())

        if (currentIndex === 1 && isBigConfigurator) {
            setVisitedViews(['1'])
        }

        if (currentIndex !== -1 && currentIndex > 0) {
            setClickedValues((prevValues) => prevValues.slice(0, -1))

            const previousIndex = visitedViews[currentIndex - 1]

            setActiveIndex(parseInt(previousIndex, 10))
        }
    }
    useEffect(() => {
        const ESC_KEY_HANDLER = (e: KeyboardEvent) => {
            if (e.key === 'Escape' && activeIndex > 1) {
                handleBack(activeIndex > 1 ? backBigBtnRef : backBtnRef)
            }
        }
        window.addEventListener('keydown', ESC_KEY_HANDLER)
        return () => window.removeEventListener('keydown', ESC_KEY_HANDLER)
    }, [activeIndex, backBigBtnRef, backBtnRef])

    const handleGoToStart = (ref: MutableRefObject<any>) => {
        setClickedValues([])
        setVisitedViews([])
        setSelectedValues([])
        setActiveIndex(0)
        pushToDataLayer(
            getLinkClickEvent(ref, {
                overrideClasses: 'guide-links-step',
                overrideText: 'Start Over'
            })
        )
    }

    const handleButtonClick = (value: string) => {
        const currentView = productQuizViews[activeIndex]
        let nextViewId: string

        /* Logic 4kx | 4kpro */
        if (variant !== 'capture') {
            if (activeIndex === 0) {
                nextViewId = 'viewSource'
            } else if (currentView.id === 'viewSource') {
                if (
                    value == 'playStation5' ||
                    value == 'playStation5Pro' ||
                    value == 'xBoxSeriesS' ||
                    value == 'xBoxSeriesX' ||
                    (value == 'iPad/tablet' && variant == '4kX')
                ) {
                    nextViewId = 'viewResolutionSmall'
                } else if (value == 'playStation4Pro' && variant == '4kX') {
                    nextViewId = 'viewResolutionPlayStation'
                } else if (value == 'pc' && variant != '4kX') {
                    nextViewId = 'viewResolution'
                } else if (value == 'pc' && variant == '4kX') {
                    nextViewId = 'viewResolutionMedium'
                } else if (
                    (value == 'xBoxOneX' || value == 'xBoxOneS') &&
                    variant == '4kX'
                ) {
                    nextViewId = 'viewResolutionSmall'
                } else if (
                    (value == 'xBoxOneX' || value == 'xBoxOneS') &&
                    variant != '4kX'
                ) {
                    nextViewId = 'viewEnd'
                } else {
                    nextViewId = 'viewEnd'
                }
            } else if (currentView.id === 'viewResolutionSmall') {
                if (
                    clickedValues.includes('playStation5') ||
                    clickedValues.includes('playStation5Pro') ||
                    clickedValues.includes('xBoxSeriesS') ||
                    clickedValues.includes('xBoxSeriesX')
                ) {
                    nextViewId = 'viewFramerateSmall'
                } else if (
                    (clickedValues.includes('xBoxOneS') ||
                        clickedValues.includes('xBoxOneX')) &&
                    value == '1080p'
                ) {
                    nextViewId = 'viewEnd'
                } else if (
                    (clickedValues.includes('xBoxOneS') ||
                        clickedValues.includes('xBoxOneX')) &&
                    value == '4k'
                ) {
                    nextViewId = 'viewFramerateXSmall'
                } else if (
                    (clickedValues.includes('xBoxOneS') ||
                        clickedValues.includes('xBoxOneX')) &&
                    value == '1440p'
                ) {
                    nextViewId = 'viewFramerateSmall'
                } else if (
                    (clickedValues.includes('playStation4Pro') &&
                        value == '4k') ||
                    (value == '4k' && clickedValues.includes('iPad/tablet'))
                ) {
                    nextViewId = 'viewHdr'
                } else if (
                    clickedValues.includes('playStation4Pro') &&
                    value != '4k'
                ) {
                    nextViewId = 'viewEnd'
                } else if (
                    value != '4k' &&
                    clickedValues.includes('iPad/tablet')
                ) {
                    nextViewId = 'viewEnd'
                }
            } else if (currentView.id === 'viewResolution') {
                if (value == '8k') {
                    nextViewId = 'viewHdr'
                } else {
                    nextViewId = 'viewFramerate'
                }
            } else if (currentView.id === 'viewResolutionPlayStation') {
                nextViewId = 'viewFramerateSmall'
            } else if (currentView.id === 'viewResolutionMedium') {
                nextViewId = 'viewFramerate'
            } else if (currentView.id === 'viewFramerateXSmall') {
                nextViewId = 'viewHdr'
            } else if (currentView.id === 'viewFramerate') {
                nextViewId = 'viewHdr'
            } else if (currentView.id === 'viewFramerateSmall') {
                if (
                    clickedValues.includes('playStation5') ||
                    clickedValues.includes('playStation5Pro') ||
                    clickedValues.includes('xBoxSeriesS') ||
                    clickedValues.includes('xBoxSeriesX')
                ) {
                    nextViewId = 'viewHdr'
                }
            } else if (currentView.id === 'viewHdr') {
                if (
                    clickedValues.includes('playStation5') ||
                    clickedValues.includes('playStation5Pro') ||
                    clickedValues.includes('iPad/tablet') ||
                    clickedValues.includes('xBoxSeriesS') ||
                    clickedValues.includes('xBoxSeriesX') ||
                    clickedValues.includes('pc')
                ) {
                    nextViewId = 'viewEnd'
                }
            } else {
                const answerMappings: { [key: string]: string } = {}
                nextViewId = answerMappings[value]
            }
        } else {
            if (activeIndex === 0) {
                nextViewId = 'viewSource'
            } else if (currentView.id === 'viewSource') {
                if (value == 'console') {
                    nextViewId = 'viewConsole'
                }
                if (value == 'camera') {
                    nextViewId = 'viewCamera'
                }
                if (value == 'gaming') {
                    nextViewId = 'viewGaming'
                }
                if (value == 'computer') {
                    nextViewId = 'viewComputer'
                }
                if (value == 'mobile') {
                    nextViewId = 'viewDevice'
                }
                if (value == 'iPad') {
                    nextViewId = 'viewEnd'
                }
            } else if (currentView.id === 'viewConsole') {
                nextViewId = 'viewConsoleCapture'
            } else if (
                currentView.id === 'viewCamera' ||
                currentView.id === 'viewGaming' ||
                currentView.id === 'viewComputer' ||
                currentView.id === 'viewDevice' ||
                currentView.id === 'viewConsoleCapture'
            ) {
                nextViewId = 'viewEnd'
            }
        }

        const nextIndex = productQuizViews.findIndex(
            (view: any) => view.id === nextViewId
        )

        setClickedValues((prevValues) => [...prevValues, value.trim()])
        setActiveIndex(nextIndex !== -1 ? nextIndex : activeIndex + 1)
        setVisitedViews((prevVisitedViews) => {
            const newVisitedViews = [...prevVisitedViews]

            // Ensure unique views in the visitedViews array
            if (
                nextIndex !== -1 &&
                !newVisitedViews.includes(nextIndex.toString())
            ) {
                newVisitedViews.push(nextIndex.toString())
            }

            return newVisitedViews
        })
    }

    // Update bg and styleBgImage when productQuizViews changes
    useEffect(() => {
        if (variant === 'capture' && productQuizViews) {
            const viewStart = productQuizViews.find(
                (view: { id: string; background: { file: { url: any } } }) =>
                    view?.id === 'viewStart' &&
                    view.background &&
                    view.background.file.url
            )

            if (viewStart) {
                setBg(viewStart.background.file.url)
            }
        }
    }, [variant, productQuizViews])

    const styleBgImage = {
        backgroundImage: bg ? `url(${bg})` : '',
        backgroundSize: 'cover',
        height: '100%',
        backgroundRepeat: 'no-repeat',
        backgroundPosition: 'top',
        borderRadius: '10px'
    }

    return (
        <div
            id={content.id}
            className={cn(
                s['product-quiz__wrapper'],
                'bg-black',
                isBigConfigurator &&
                    activeIndex !== productQuizViews.length - 1 &&
                    s['product-quiz__wrapper_full-screen'],
                isBigConfigurator &&
                    activeIndex === productQuizViews.length - 1 &&
                    s['product-quiz__wrapper_full']
            )}
        >
            <div
                style={activeIndex === 0 ? styleBgImage : {}}
                className={cn(
                    s['product-quiz'],
                    activeIndex === 0 && s['product-quiz__start-view'],
                    isBigConfigurator && s['product-quiz__configurator']
                )}
            >
                {isBigConfigurator && (
                    <div className={cn(s['product-quiz__top-button'])}>
                        <div
                            className={cn(
                                s['product-quiz__top-button__wrap'],
                                'flex'
                            )}
                        >
                            {activeIndex === productQuizViews.length - 1 && (
                                <button
                                    ref={startOverBigBtnRef}
                                    className={cn(
                                        s['product-quiz__button'],
                                        s['product-quiz__button_refresh']
                                    )}
                                    onClick={() =>
                                        handleGoToStart(startOverBigBtnRef)
                                    }
                                    onKeyPress={() =>
                                        handleGoToStart(startOverBigBtnRef)
                                    }
                                >
                                    <div
                                        className={cn(
                                            s['product-quiz__button__icon']
                                        )}
                                    >
                                        <Reset />
                                    </div>
                                    <div>{t('Restart quiz')}</div>
                                </button>
                            )}
                            {activeIndex > 1 && (
                                <button
                                    ref={backBigBtnRef}
                                    className={cn(
                                        s['product-quiz__button'],
                                        s['product-quiz__button_prev'],
                                        activeIndex ===
                                            productQuizViews.length - 1
                                            ? 'ml-8'
                                            : ''
                                    )}
                                    onClick={() => handleBack(backBigBtnRef)}
                                    onKeyPress={() => handleBack(backBigBtnRef)}
                                >
                                    <div
                                        className={cn(
                                            s['product-quiz__button__icon']
                                        )}
                                    >
                                        <ArrowLeftThin />
                                    </div>
                                    <div>{t('Go back')}</div>
                                </button>
                            )}
                        </div>
                    </div>
                )}
                {showProgressBar && (
                    <div
                        className={cn(
                            s['product-quiz__progress-bar'],
                            isBigConfigurator &&
                                s['product-quiz__progress-bar__big']
                        )}
                    >
                        <div
                            className={
                                s[
                                    'product-quiz__progress-bar__progress-indicator'
                                ]
                            }
                            style={{ width: `${progressWidth}%` }}
                        />
                        {/* <div
                            className={s['product-quiz__progress-bar__number']}
                        >
                            {`${activeIndex}/${productQuizViews.length - 2}`}
                        </div> */}
                    </div>
                )}
                {productQuizViews &&
                    productQuizViews.map((view: any, i: number) => {
                        return (
                            <div
                                className={cn(s['product-quiz-view-wrapper'], {
                                    [s['active']]: activeIndex === i
                                })}
                                key={i}
                            >
                                <ProductQuizView
                                    index={i}
                                    title={view?.title || ''}
                                    subtitle={view?.subtitle || ''}
                                    result={result}
                                    buttonWithMedia={
                                        view?.buttonWithMedia || ''
                                    }
                                    illustartion={
                                        isBigConfigurator &&
                                        i !== 0 &&
                                        activeIndex !==
                                            productQuizViews.length - 1
                                    }
                                    isBigConfigurator={isBigConfigurator}
                                    viewVariant={view?.id || ''}
                                    handleButtonClick={(value: string) =>
                                        handleButtonClick(value)
                                    }
                                />
                            </div>
                        )
                    })}
                {productResults &&
                    productResults.map((result: any, i: number) => {
                        if (
                            JSON.stringify(result.resultValues.sort()) ===
                            JSON.stringify(newClickedValues.sort())
                        ) {
                            return (
                                <div
                                    className={cn(
                                        s['product-quiz__view-result-wrapper']
                                    )}
                                    key={i}
                                >
                                    <ProductQuizViewResult
                                        index={i}
                                        productCards={result.productCard}
                                    />
                                </div>
                            )
                        }
                    })}

                {activeIndex > 1 &&
                    activeIndex !== productQuizViews.length - 1 &&
                    !isBigConfigurator && (
                        <button
                            ref={backBtnRef}
                            className={cn(
                                s['product-quiz__button'],
                                s['product-quiz__button_prev']
                            )}
                            onClick={() => handleBack(backBtnRef)}
                            onKeyPress={() => handleBack(backBtnRef)}
                        >
                            <div
                                className={cn(s['product-quiz__button__icon'])}
                            >
                                <ArrowThin />
                            </div>
                            {isMobile && <div>{t('Previous question')}</div>}
                        </button>
                    )}

                {activeIndex > 0 && !isBigConfigurator && (
                    <button
                        ref={startOverBtnRef}
                        className={cn(
                            s['product-quiz__button'],
                            s['product-quiz__button_refresh']
                        )}
                        onClick={() => handleGoToStart(startOverBtnRef)}
                        onKeyPress={() => handleGoToStart(startOverBtnRef)}
                    >
                        <div className={cn(s['product-quiz__button__icon'])}>
                            <CloseThin />
                        </div>
                    </button>
                )}
            </div>
        </div>
    )
}

export default ProductQuiz
