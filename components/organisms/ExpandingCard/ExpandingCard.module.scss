.expanding-card__media-video-fake-poster {}

.expanding-card {
    position: relative;
    overflow: hidden;

    @screen lg {
        &:hover {
            cursor: pointer;
            flex: 3;

            .expanding-card__media {
                border-radius: 12rem;
            }

            button {
                background-color: var(--primaries-light-blue);
                color: var(--primaries-dark-blue);

                svg:first-child {
                    transform: translateY(100%);
                    opacity: 0
                }

                svg:nth-child(2) {
                    transform: translateY(0);
                    opacity: 1;
                }
            }
        }
    }

    &__media {
        position: relative;
        border-radius: 40px;
        overflow: hidden;
        transition: all ease-out 0.3s;
        padding-bottom: 84%;
        max-height: 300px;
        height: 100%;

        @screen md {
            border-radius: 32px;
            // Using aspect ratio instead here
            height: 0;
            padding-bottom: 83.5%;
        }

        @screen lg {
            border-radius: 64px;
            height: 100%;
            padding-bottom: 0;
        }
    }

    &__media-video,
    &__media-video-fake-poster {
        position: absolute !important;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 0;
    }

    &__media-video {
        z-index: 1;
    }

    &__info-wrapper {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 17.5px;
        gap: 8px;

        padding: 0 1.6rem;
        margin-bottom: 40px;

        @screen lg {
            margin-bottom: 0;
            margin-top: 20px;
            opacity: 0;
            transition: all 0.1s ease-out;
        }

        @media screen and (min-width: 1440px) {
            padding: 0 4.8rem;
        }

        &--expanded {
            @screen lg {
                display: flex;
                opacity: 1;
                transition-delay: 0.3s;
            }
        }

        &__text {
            @apply font-univers55Roman;
            font-size: 2.4rem;
            line-height: 3.2rem;

            @screen md {
                font-size: 3.2rem;
                line-height: 4rem;
            }

            &--light {
                @apply text-white;
            }
        }

        &__button {
            background: var(--black);
            transition: background-color 0.2s linear;
            border-radius: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
            padding: 8px;
            color: var(--white);
            width: 40px;
            height: 40px;

            svg:first-child {
                transform: translateY(0);
                opacity: 1;
                transition: all 0.2s linear;
            }

            svg:nth-child(2) {
                opacity: 0;
                transform: translateY(-100%);
                transition: all 0.2s linear;
                position: absolute;

            }

            @screen md {
                width: 48px;
                height: 48px;
            }

            @screen lg {
                width: 56px;
                height: 56px;
            }
        }
    }
}
