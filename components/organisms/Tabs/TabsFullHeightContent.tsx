import { TextToggleProps } from '@components/atoms/TextToggle/TextToggle'
import { ViewToggleProps } from '@components/atoms/ViewToggle/ViewToggle'
import { ViewToggles } from '@components/molecules/ViewToggles/ViewToggles'
import TextPanel from '@components/organisms/TextPanel/TextPanel'
import { CardProps } from '@components/templates/CardList/CardList'
import { DropdownPanelTabContentProps } from '@components/templates/DropdownPanel/DropdownPanel'
import { TemplateTabsProps } from '@components/templates/Tabs/Tabs'
import cn from 'classnames'
import { FC, useState } from 'react'
import s from './Tabs.module.scss'
import { useLayoutContext } from '@components/layouts/MainLayout/LayoutContext'
import { useMedia } from '@lib/hooks/useMedia'
import ElgatoVideo from '@components/common/ElgatoVideo/ElgatoVideo'
import ElgatoImage from '@components/common/ElgatoImage'
import { decode } from 'he'
import { parseNoBreakLines } from '@config/hooks/useParseNoBreakLines'

export const TabsFullHeightContent: FC<
    Omit<TemplateTabsProps, 'variant'> & {
        tabs: any
    }
> = ({ tabs, textPanel, id }) => {
    const { pageTheme } = useLayoutContext()
    const [activeTabIndex, setActiveTabIndex] = useState(0)
    const toggles: ViewToggleProps[] | TextToggleProps[] =
        tabs?.map((tab: any) => {
            return {
                text: tab.headline,
                thumbnail: tab.thumbnail,
                theme: tab.theme,
                variant: undefined
            } as ViewToggleProps | TextToggleProps
        }) || []

    const setSelectedTab = (i: number) => {
        setActiveTabIndex(i)
    }

    return (
        <div
            className={cn(s['tabs'], s[`tabs--center`], {
                // s[`tabs--${variant}`],
                [s['tabs--spacing']]: !textPanel,
                [s[`tabs--page-theme-${pageTheme}`]]: pageTheme === 'dark'
            })}
            id={id}
            role="tab"
        >
            <div className={s['tabs__inner']} aria-controls={id}>
                {textPanel && (
                    <TextPanel
                        content={{
                            ...textPanel,
                            className: s['tabs__text-panel']
                        }}
                    />
                )}
                <div>
                    <ViewToggles
                        toggles={toggles as ViewToggleProps[]}
                        activeTab={activeTabIndex}
                        variant="center"
                        onChange={setSelectedTab}
                        className={s['tabs__toggles']}
                        mobileVariant="default"
                    />
                    <div>
                        {tabs?.map(
                            (tab: DropdownPanelTabContentProps, i: number) => {
                                const child = tab.children?.[0] as CardProps

                                if (!child) {
                                    return null
                                }

                                return (
                                    <ContentPanel
                                        key={i}
                                        className={cn({
                                            hidden: activeTabIndex !== i
                                        })}
                                        {...child}
                                    />
                                )
                            }
                        )}
                    </div>
                </div>
            </div>
        </div>
    )
}

const ContentPanel: FC<CardProps & { className?: string }> = (props) => {
    const { pageTheme } = useLayoutContext()
    const { className } = props
    const headline = props.textPanel?.headline
    const text = props.textPanel?.bodyCopy

    const { type, src, posterImageSrc, alt } = useMedia({
        cloudinaryMedia: props.cloudinaryMedia,
        cloudinaryMobileMedia: props.cloudinaryMobileMedia,
        cloudinaryPosterImage: props.cloudinaryPosterImage,
        cloudinaryPosterImageMobile: props.cloudinaryPosterImageMobile
    })

    const parsedHeadline = parseNoBreakLines(headline || '')
    const parsedBody = text?.replace(/\n/g, `</br>`).replace(/\s/g, ' ')

    return (
        <div
            className={cn(
                s['tabs__full-height-content'],
                'px-16px md:px-0 flex flex-col',
                className
            )}
        >
            <div
                className={cn('p-16px rounded-t-xxl', {
                    'text-white bg-primitive-gray-120': pageTheme === 'dark',
                    'text-black bg-white': pageTheme !== 'dark'
                })}
            >
                {headline && (
                    <h5
                        className="text-h5 mb-4px"
                        dangerouslySetInnerHTML={{
                            __html: decode(parsedHeadline)
                        }}
                    />
                )}
                {parsedBody && (
                    <div
                        className="text-h5-md-max"
                        dangerouslySetInnerHTML={{
                            __html: decode(parsedBody)
                        }}
                    />
                )}
            </div>
            <div className="overflow-hidden rounded-b-xxl flex-grow flex justify-center items-center">
                {type === 'video' ? (
                    <ElgatoVideo
                        secure_url={src}
                        fallbackImgUrl={posterImageSrc}
                        className="max-h-full max-w-full object-contain"
                        videoClasses="w-full h-full object-contain"
                        options={{
                            autoPlay: true,
                            preload: 'none',
                            muted: true,
                            loop: true
                        }}
                        videoDescription={
                            props?.customOptions?.videoDescription
                        }
                    />
                ) : (
                    <ElgatoImage
                        src={src}
                        alt={alt}
                        className="w-full h-full object-cover"
                    />
                )}
            </div>
        </div>
    )
}
