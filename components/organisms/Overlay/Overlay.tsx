import { useLayoutContext } from '@components/layouts/MainLayout/LayoutContext'
import { OverlayCloseButton } from '@components/organisms/Overlay/OverlayCloseButton'
import { SectionBgColor } from '@components/templates/Section/Section'
import cn from 'classnames'
import { nanoid } from 'nanoid'
import { createPortal } from 'preact/compat'
import React, {
    Dispatch,
    FC,
    ReactNode,
    useCallback,
    useEffect,
    useMemo,
    useRef,
    useState
} from 'react'
import s from './Overlay.module.scss'

export type OverlayProps = {
    isOpen?: boolean
    setIsOpen?: Dispatch<React.SetStateAction<boolean>>
    className?: string
    children?: ReactNode
    onClose?: () => void
    //In order to archieve transition set up value for this field. Example transition={20} is going to make transition translateX(20%)
    transition?: number
    variant?: 'default' | 'animate-bottom'
    theme?: 'light' | 'dark' | 'black'
    closeButtonStyle?:
        | 'default'
        | 'content-center-top'
        | 'hidden'
        | 'popupClose'
    closable?: boolean
    backgroundColor?: SectionBgColor
    fadeOutBackground?: boolean
    id?: string
}

export const Overlay: FC<OverlayProps> = (props) => {
    const {
        isOpen = false,
        setIsOpen,
        children,
        className,
        onClose,
        variant = 'default',
        theme = 'light',
        transition,
        closable = true,
        closeButtonStyle = 'default',
        backgroundColor = SectionBgColor.WHITE,
        fadeOutBackground = false,
        id
    } = props
    const overlayRef = useRef<HTMLDivElement>(null)
    const [container, setContainer] = useState<HTMLElement | null>(null)
    const overlayId = useMemo(() => nanoid(), [])
    const { pageTheme } = useLayoutContext()
    const closeOverlay = useCallback(() => {
        onClose?.()
        setIsOpen?.(false)
    }, [onClose, setIsOpen])

    const handleEscape = useCallback(
        (e) => {
            if (closable) {
                if (
                    isOpen &&
                    (e.key === 'Escape' || e.key === 'Esc' || e.keyCode === 27)
                ) {
                    closeOverlay()
                }
            }
        },
        [closeOverlay, isOpen, closable]
    )

    useEffect(() => {
        let timeoutId: NodeJS.Timeout

        function disableTransition() {
            if (overlayRef.current) {
                overlayRef.current.classList.add(s['overlay--no-transition'])

                // Clear existing timeout if it exists
                if (timeoutId) {
                    clearTimeout(timeoutId)
                }

                // Set new timeout to remove class after 10ms
                timeoutId = setTimeout(() => {
                    overlayRef.current?.classList.remove(
                        s['overlay--no-transition']
                    )
                }, 10)
            }
        }

        if (transition) {
            disableTransition()
        }

        window.addEventListener('resize', disableTransition)

        return () => {
            window.removeEventListener('resize', disableTransition)
            if (timeoutId) {
                clearTimeout(timeoutId)
            }
        }
    }, [transition])

    useEffect(() => {
        if (isOpen) {
            document.addEventListener('keydown', handleEscape)
            document.body.classList.add('noscroll')
        } else {
            document.removeEventListener('keydown', handleEscape)
            document.body.classList.remove('noscroll')
        }

        return () => {
            document.removeEventListener('keydown', handleEscape)
            document.body.classList.remove('noscroll')
        }
    }, [isOpen])

    useEffect(() => {
        const nextElement = document?.getElementById('__next')
        if (nextElement) {
            setContainer(nextElement)
        }
    }, [])

    if (container) {
        return createPortal(
            <>
                {!!transition && fadeOutBackground && (
                    <div
                        style={{
                            backgroundColor: 'rgba(0, 0, 0, 0.5)',
                            zIndex: 150 // sticky nav has 148
                        }}
                        className={cn(
                            s['overlay'],
                            {
                                [s['overlay--open']]: isOpen
                            },
                            'fixed w-screen h-screen overflow-hidden'
                        )}
                    />
                )}
                <div
                    className={cn(
                        s['overlay'],
                        {
                            [s['overlay--dark']]: theme === 'dark',
                            [s['overlay--black']]: theme === 'black',
                            [s['overlay--light-background']]:
                                pageTheme !== 'dark' &&
                                theme !== 'dark' &&
                                pageTheme !== 'neo',
                            [s['overlay--animate-bottom']]:
                                variant === 'animate-bottom',
                            [s['overlay--light']]: theme === 'light',
                            [s['overlay--open']]: isOpen,
                            [s['overlay--transition']]:
                                transition !== undefined && !isNaN(transition),
                            [s[`overlay--open--${transition}`]]:
                                transition !== undefined &&
                                !isNaN(transition) &&
                                isOpen
                        },
                        backgroundColor,
                        className,
                        'overlay-container'
                    )}
                    aria-hidden={!isOpen}
                    ref={overlayRef}
                    id={id ? id : overlayId}
                    role="dialog"
                >
                    <div className={`${s['overlay__content']} content__open`}>
                        {closable &&
                            closeButtonStyle === 'content-center-top' && (
                                <OverlayCloseButton
                                    onClick={closeOverlay}
                                    className={
                                        s['overlay__close--content-center-top']
                                    }
                                />
                            )}
                        {children}
                    </div>
                    {closable && closeButtonStyle === 'default' && (
                        <OverlayCloseButton
                            onClick={closeOverlay}
                            className={s['overlay__close--default']}
                        />
                    )}
                    <div className={cn('md:hidden')}>
                        {closable && closeButtonStyle === 'popupClose' && (
                            <OverlayCloseButton
                                onClick={closeOverlay}
                                className={s['overlay__close--default']}
                            />
                        )}
                    </div>
                </div>
            </>,
            container
        )
    }
    return null
}
