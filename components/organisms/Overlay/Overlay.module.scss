.overlay {
    z-index: -1;
    @apply w-full opacity-0 transition-opacity duration-500;
    @apply fixed top-0 left-0 bottom-0 right-0;
    @apply w-screen overflow-y-auto pointer-events-none;
    height: 100svh;
    height: 100dvh;
    min-height: -webkit-fill-available;

    &--transition {
        transform: translateX(100vw);
        transition: transform 1s ease-out;
        opacity: 1;
        z-index: 10010;
        overflow: visible;
    }

    &--no-transition {
        transition: none !important;
    }

    &:focus-visible {
        outline: auto;
    }

    &--open {
        @apply opacity-100 pointer-events-auto;
        z-index: 10010;

        &--0 {
            transform: translateX(0);

            &:after {
                opacity: 1 !important;
            }
        }

        &--10 {
            transform: translateX(10vw);

            width: 90vw;

            &:after {
                opacity: 1 !important;
            }
        }

        &--20 {
            transform: translateX(20vw);
            width: 80vw;

            &:after {
                opacity: 1 !important;
            }
        }
    }

    .detail-media-text__embed-wrapper:active {
        .detail-media-text__embed {
            position: relative;
            z-index: 1;
        }

        .detail-media-text__cover {
            z-index: -2;
        }
    }

    &__close {
        &--default {
            @apply absolute;
            top: 2%;
            right: 10%;

            @screen xl {
                @apply top-16 right-16;
            }

            svg {
                @screen md-max {
                    width: 30px !important;
                    height: 30px !important;
                }

                @screen md {
                    display: none;
                }
            }
        }

        &--popupClose {
            @apply absolute top-5 right-4;

            @screen xl {
                @apply top-16 right-16;
            }
        }
    }

    &--dark {
        background: rgba(0, 0, 0, 0.8);
    }

    &--black {
        background: var(--primitive-black);
    }

    &--light-background {
        background: rgba(0, 0, 0, 0.25);
    }

    &--light {
        .overlay__close--content-center-top {
            background-color: var(--white) !important;
            color: var(----charcoal) !important;
        }
    }

    &--animate-bottom {
        @apply opacity-100;
        top: 100%;
        transition: top 1s ease 0s;
        z-index: 10010;

        &.overlay--open {
            top: 0;
        }
    }

    &__content {
        max-width: 100%;
        overflow-x: hidden;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        @screen md-max {
            overflow-y: auto;
            height: 100%;
            min-height: 100dvh;
        }
    }
}
