import React, { <PERSON> } from 'react'
import s from './Overlay.module.scss'
import cn from 'classnames'
import { Button } from '@components/molecules/Button/Button'
import CloseIconSimple from '@components/atoms/Icon/general/CloseIconSimple'
import { useTranslation } from 'next-i18next'

export type OverlayCloseButtonProps = {
    onClick?: (() => void) | ((e: any) => void)
    className?: string
}

export const OverlayCloseButton: FC<OverlayCloseButtonProps> = (props) => {
    const { onClick, className } = props
    const { t } = useTranslation('common')
    return (
        <Button
            variant="tertiary"
            onClick={onClick}
            className={cn(s['overlay__close'], className, 'overlay-close')}
            label={t('Close')}
        >
            <CloseIconSimple width="52" height="52" />
        </Button>
    )
}
