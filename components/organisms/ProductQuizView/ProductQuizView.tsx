import React, { FC, useRef } from 'react'
import s from './ProductQuizView.module.scss'
import cn from 'classnames'
import ButtonWithMedia from '@components/organisms/ButtonWithMedia/ButtonWithMedia'
import { useTranslation } from 'next-i18next'
import Illustartion from '@components/icons/Illustartion'
import { getGuideStepEvent } from '@lib/gtm/guideTracking'
import { pushToDataLayer } from '@corsairitshopify/pylot-gtm/src/utils'
import { getLinkClickEvent } from '@lib/gtm/linkClick'

interface ProductQuizViewProps {
    title?: string
    subtitle?: string
    buttonWithMedia?: any
    viewVariant?: string
    handleButtonClick: (value: string) => void
    result?: any
    index?: number
    illustartion?: boolean
    isBigConfigurator?: boolean
}

const ProductQuizView: FC<ProductQuizViewProps> = ({
    title,
    subtitle,
    buttonWithMedia,
    handleButtonClick,
    viewVariant,
    result,
    illustartion,
    isBigConfigurator,
    index
}) => {
    const linkRef = useRef<any>(null)
    const buttonsWithMedia = buttonWithMedia
    const { t } = useTranslation(['common'])
    let updatedResult = ''

    if (result.length > 2) {
        updatedResult = `${result[2]}${result[3]} ${result[4]}`

        if (result[4] === 'hdr') {
            updatedResult = `${updatedResult.replace(
                /(\d+p\d+)\sHDR/,
                '$1 HDR'
            )} / SDR`
        }

        updatedResult = updatedResult.toUpperCase().replace(/P/g, 'p')
    } else {
        updatedResult = result[result.length - 1]
    }

    const handleOnClick = (event: any) => {
        handleButtonClick('')
        pushToDataLayer(
            getLinkClickEvent(linkRef, {
                overrideClasses: 'guide-links-start',
                overrideText: 'Get started'
            })
        )
    }

    return (
        <div
            className={cn(
                s['product-quiz-view'],
                'text-center',
                {
                    [s['product-quiz-view__big']]:
                        !buttonsWithMedia && result && index == 0
                },
                {
                    [s['product-quiz-view__configurator']]: isBigConfigurator
                }
            )}
        >
            {illustartion && (
                <div
                    className={cn(
                        s['product-quiz-view__icon'],
                        'w-full md:mb-8 flex justify-center'
                    )}
                >
                    <Illustartion />
                </div>
            )}

            {viewVariant == 'viewStart' && isBigConfigurator && (
                <h1
                    className={cn(
                        s['product-quiz-view__title'],
                        'univers55Roman'
                    )}
                >
                    {title}
                </h1>
            )}
            {viewVariant != 'viewStart' && isBigConfigurator && (
                <h2
                    className={cn(
                        s['product-quiz-view__title'],
                        'univers55Roman'
                    )}
                >
                    {title}
                </h2>
            )}
            {!isBigConfigurator && (
                <div
                    className={cn(
                        s['product-quiz-view__title'],
                        'univers55Roman'
                    )}
                >
                    {title}
                </div>
            )}

            {viewVariant == 'viewEnd' && !isBigConfigurator && (
                <div
                    className={cn(
                        s['product-quiz-view__title'],
                        'w-full flex justify-center'
                    )}
                >
                    {updatedResult}
                </div>
            )}
            <div
                className={cn(
                    s['product-quiz-view__subtitle'],
                    'univers55Roman'
                )}
            >
                {subtitle}
            </div>

            {buttonsWithMedia && viewVariant != 'viewStart' && (
                <div className="w-full flex justify-center">
                    <div
                        className={cn(
                            s['product-quiz-view__button-wrapper'],
                            isBigConfigurator &&
                                s['product-quiz-view__button-wrapper__big']
                        )}
                    >
                        <div
                            className={
                                s['product-quiz-view__button-wrapper-flex']
                            }
                        >
                            {buttonsWithMedia.map(
                                (
                                    buttonWithMedia: {
                                        image: any
                                        icon: any
                                        key: string | undefined
                                        value: string | undefined
                                        linkUrl: string | undefined
                                    },
                                    i: React.Key | null | undefined
                                ) => {
                                    const handleOnClick = (value: string) => {
                                        handleButtonClick(value)
                                        pushToDataLayer(
                                            getGuideStepEvent({
                                                selection: value,
                                                step: title || 'Undefined step'
                                            })
                                        )
                                    }

                                    return (
                                        <ButtonWithMedia
                                            key={i}
                                            title={buttonWithMedia.key}
                                            value={buttonWithMedia.value}
                                            media={buttonWithMedia.image}
                                            icon={buttonWithMedia.icon}
                                            handleButtonClick={handleOnClick}
                                            isBigConfigurator={
                                                isBigConfigurator
                                            }
                                            linkUrl={buttonWithMedia.linkUrl}
                                        />
                                    )
                                }
                            )}
                        </div>
                    </div>
                </div>
            )}
            {viewVariant == 'viewStart' && (
                <div className="w-full flex justify-center">
                    <div
                        ref={linkRef}
                        className={s['product-quiz-view__start_btn']}
                        role="button"
                        tabIndex={0}
                        onClick={handleOnClick}
                        onKeyPress={handleOnClick}
                    >
                        {t('Get started')}
                    </div>
                </div>
            )}
        </div>
    )
}

export default ProductQuizView
