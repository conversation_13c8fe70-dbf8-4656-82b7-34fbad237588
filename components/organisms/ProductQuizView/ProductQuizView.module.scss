
.product-quiz-view{
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-items: center;
    &__title{
        font-size: 40px;
        font-weight: 700;
        line-height: 50px;
        color: var(--white);
        text-align: center;
        width: 100%;
        text-transform: uppercase;
        font-family: 'Univers67BoldCondensed', 'Arial', sans-serif;
        max-width: 80%;
        margin: 0 auto;
        margin-bottom: 24px;
    }
    &__subtitle{
        font-size: 16px;
        line-height: 24px;
        color: var(--white);        
        text-align: center;
        width: 100%;
        max-width: 740px;
        display: block;
        margin: 0 auto;
        font-family: 'Univers55Roman', 'Arial', sans-serif;

    }
    &__big{
        display: block;
        .product-quiz-view__title{
            font-size: 72px;
            line-height: 88px;
            color: var(--white);     
            font-weight: 700;
            text-align: center;
            width: 100%;
            max-width: 888px;
            margin: 0 auto;
            margin-bottom: 32px;  
        }
        .product-quiz-view__subtitle{
            font-size: 26px;
            line-height: 31px;
            color: var(--white);        
        }
    }
    &__button-wrapper{
        display: flex;
        align-items: center;
        justify-content: center;
        flex-wrap: wrap;
        gap: 16px;
        width: 100%;
        max-width: 1200px;
        margin: 0 auto;
        margin-top: 40px;
        &__big{
            margin-top: 80px;
            .product-quiz-view__button-wrapper-flex{
                gap: 32px;
            }
        }
        &-flex{
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            align-items: center;
            gap: 16px;
            width: 100%;

        }

        &__media{
            margin-top: 0;
        }
    }
   &__start_btn{
        color: var(--white);        
        width: 120px;
        height: 43px;
        background-color: var(--content-blue);
        cursor: pointer;
        margin-top: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 8px;
   }
   @media only screen and (max-width: 1024px) {
        &__title{
            font-size: 40px;
            line-height: 48px;
        }
        &__subtitle{
            font-size: 24px;
            line-height: 28px;
        }
        &__big{
            .product-quiz-view__title{
                font-size: 40px;
                line-height: 48px;
            }
            .product-quiz-view__subtitle{
                font-size: 24px;
                line-height: 28px;
            }
        }
   }
   @media only screen and (max-width: 767px) {
        &__configurator{
            margin: 0 16px;
        }
        &__title{
            font-size: 24px;
            line-height: 29px;
        }
        &__subtitle{
            font-size: 14px;
            line-height: 21px;
        }
        &__button-wrapper{

            &-flex{
                justify-content: flex-start;
                max-width: 500px;
                max-height: 315px;
                overflow-y: scroll;
            }
            &__big{
                .product-quiz-view__button-wrapper-flex{
                    justify-content: center;
                }
            }

            &__big{
                margin-top: 40px;
                .product-quiz-view__button-wrapper-flex{
                    gap: 16px;
                }
            }
        }
        &__icon{
            svg{
                width: 100px;
            }
        }
    }
}