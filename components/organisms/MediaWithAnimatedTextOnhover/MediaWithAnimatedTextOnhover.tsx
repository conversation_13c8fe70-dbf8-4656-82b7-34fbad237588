import { FC, useState } from 'react'
import { PrimaryTextProps } from '@components/molecules/PrimaryText/PrimaryText'
import AnimatedTextOnHover from './AnimatedTextOnHover'
import cn from 'classnames'
import ElgatoMedia from '@components/common/ElgatoMedia/ElgatoMedia'
import { decode } from 'he'
import { useMobile } from '@pylot-data/hooks/use-mobile'
import s from './MediaWithAnimatedTextOnhover.module.scss'
import { CardProps } from '@components/templates/CardList/CardList'
import { CloudinaryMedia } from '@components/common/CloudinaryMedia/Cloudinary'

interface MediaWithAnimatedTextOnhoverProps {
    id?: string
    cards?: CardProps[]
    textPanel?: PrimaryTextProps
    cloudinaryMedia?: CloudinaryMedia[]
}

const MediaWithAnimatedTextOnhover: FC<MediaWithAnimatedTextOnhoverProps> = ({
    id,
    cards,
    textPanel,
    cloudinaryMedia
}) => {
    const { isMobile, isTablet } = useMobile()
    const [activeCardIndex, setActiveCardIndex] = useState(0)

    return (
        <div
            id={id}
            className="flex justify-center px-16px md:px-64px gap-16px py-40px md:py-32"
        >
            <div className="grid grid-cols-12">
                <div className="grid grid-cols-10 col-span-12 lg3:col-start-2 lg3:col-span-10 gap-16px">
                    <div
                        className={cn(
                            s['media-with-animated-text-onhover'],
                            'col-span-10'
                        )}
                    >
                        <div
                            className={cn(
                                'flex flex-col lg:grid lg:grid-cols-12 gap-6 md:gap-40px w-full h-full bg-white'
                            )}
                        >
                            <div
                                className={cn(
                                    'col-span-12 lg:col-span-5 w-full sticky mb-auto bg-white z-10',
                                    s[
                                        'media-with-animated-text-onhover__left-panel'
                                    ]
                                )}
                                style={{
                                    top:
                                        !isMobile && !isTablet ? '100px' : '0px'
                                }}
                            >
                                {textPanel && (
                                    <h2
                                        className="mb-24px md:mb-40px md-max:mobile-headline"
                                        dangerouslySetInnerHTML={{
                                            __html: decode(
                                                textPanel?.bodyCopy || ''
                                            )
                                        }}
                                    />
                                )}
                                <div
                                    className={cn(
                                        s[
                                            'media-with-animated-text-onhover__media-container'
                                        ],
                                        'opacity-100'
                                    )}
                                >
                                    <div className="relative w-full h-full">
                                        {cards?.map((card, index) => (
                                            <div
                                                key={index}
                                                className={cn(
                                                    'transition-opacity duration-500',
                                                    {
                                                        relative: index === 0,
                                                        'absolute top-0 left-0 right-0':
                                                            index !== 0,
                                                        'opacity-0':
                                                            index !==
                                                            activeCardIndex,
                                                        'opacity-100':
                                                            index ===
                                                            activeCardIndex
                                                    }
                                                )}
                                            >
                                                <ElgatoMedia
                                                    cloudinaryMedia={
                                                        cloudinaryMedia
                                                    }
                                                    layers={
                                                        card.sku
                                                            ? [card.sku]
                                                            : []
                                                    }
                                                    className="rounded-xxxl"
                                                    objectFit="cover"
                                                    style={{
                                                        width: '100%',
                                                        height: isTablet
                                                            ? '50vh'
                                                            : '100%'
                                                    }}
                                                    mobileStyle={{
                                                        width: '100%',
                                                        height: '50vh'
                                                    }}
                                                />
                                                {card.cloudinaryNeoMedia && (
                                                    <div className="absolute top-24px right-24px">
                                                        <ElgatoMedia
                                                            cloudinaryMedia={
                                                                card.cloudinaryNeoMedia
                                                            }
                                                            className={cn(
                                                                s[
                                                                    'media-with-animated-text-onhover__badge'
                                                                ]
                                                            )}
                                                            style={{
                                                                width: '140px',
                                                                height: '140px'
                                                            }}
                                                            mobileStyle={{
                                                                width: '100px',
                                                                height: '100px'
                                                            }}
                                                        />
                                                    </div>
                                                )}
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            </div>
                            <div
                                className={cn(
                                    'col-span-12 md:col-start-7 md:col-span-6 h-full'
                                )}
                            >
                                <div
                                    className={cn(
                                        'flex flex-col divide-y divide-gray-30 h-full',
                                        s['animated-text-on-hover']
                                    )}
                                >
                                    {cards?.map((card, index) => (
                                        <AnimatedTextOnHover
                                            key={index}
                                            card={card}
                                            active={index === activeCardIndex}
                                            onCardClick={() =>
                                                setActiveCardIndex(index)
                                            }
                                        />
                                    ))}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}

export default MediaWithAnimatedTextOnhover
