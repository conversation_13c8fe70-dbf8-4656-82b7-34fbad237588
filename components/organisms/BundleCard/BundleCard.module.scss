.bundle-card {
    position: relative;
    width: calc(50% - 32px);
    border-radius: 8px;
    overflow: hidden;
    background-color: #F6F6F6;
    height: 65px;
    display: flex;
    align-items: center;
    padding: 2px 5px;

    &__text {
        @apply font-univers67BoldCondensed;
        word-break: break-word;
        hyphens: auto;
        max-width: 65%;
    }

    /* default */
    &__media-wrapper {
        @apply relative w-full;
        overflow: hidden;
        width: 60px;
        height: 55px;
        max-height: 55px;
        img{
            border-radius: 8px;
        }
    }
    &__link {
        color: #204CFE;
        display: inline-flex;

        p{
            color: #204CFE;
            white-space: nowrap;
            font-weight: 100;
            padding-bottom: 1px;
        }
        svg{
            transform: rotate(0) !important;
        }
    }
    &__product-wrapper{
        p{
            font-size: 14px;
        }
    }
    &__title{
        font-size: 14px;
        text-transform: uppercase;
        word-break: break-word;
        hyphens: auto;
    }

    @media only screen and (max-width:767px) {
        width: auto;
    }

}
