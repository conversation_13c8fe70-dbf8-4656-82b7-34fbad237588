import ElgatoImage from '@components/common/ElgatoImage'
import ElgatoVideo from '@components/common/ElgatoVideo/ElgatoVideo'
import { Plus } from '@components/icons'
import ArrowAngledTopRight from '@components/icons/ArrowAngledTopRight'
import { Button } from '@components/molecules/Button/Button'
import { CardProps } from '@components/templates/CardList/CardList'
import { NeoComponent, useNeoAction } from '@lib/gtm/neoActions'
import { useMedia } from '@lib/hooks/useMedia'
import { useMobile } from '@pylot-data/hooks/use-mobile'
import cn from 'classnames'
import { getAriaLabel } from 'helpers/accessibilityHelper'
import { useTranslation } from 'next-i18next'
import { FC, useState } from 'react'
import s from './ImageCard.module.scss'

export type ImageCardProps = {
    card: CardProps
    index: number
    id?: string
    openOverlay: (index: number) => void
    textColor: 'dark' | 'light' | 'blue'
    isOverlayClosing: boolean
    customConfiguration?: {
        imageBorder: string
        buttonBorder: string
    }
    theme?: 'neo' | ''
}

const ImageCard: FC<ImageCardProps> = (props) => {
    const {
        card,
        index,
        id,
        openOverlay,
        textColor = 'dark',
        isOverlayClosing,
        customConfiguration,
        theme
    } = props

    const { isMobile } = useMobile()

    const { handleNeoCardBtnClick, handleNeoCardMediaClick } = useNeoAction()
    const [isClickedByBtn, setIsClickedByBtn] = useState(false)
    const { t } = useTranslation(['common'])

    const {
        media,
        link,
        mobileMedia,
        posterImage,
        posterImageMobile,
        cloudinaryMedia,
        cloudinaryMobileMedia,
        cloudinaryPosterImage,
        cloudinaryPosterImageMobile
    } = card
    const isVideo = media?.file?.contentType?.includes('video') ?? false

    const { src, alt, type, description, posterImageSrc } = useMedia({
        media: media,
        mobileMedia: mobileMedia,
        posterImage: posterImage,
        posterImageMobile: posterImageMobile,
        cloudinaryMedia: cloudinaryMedia,
        cloudinaryMobileMedia: cloudinaryMobileMedia,
        cloudinaryPosterImage: cloudinaryPosterImage,
        cloudinaryPosterImageMobile: cloudinaryPosterImageMobile
    })

    const handleOnClick = () => {
        if (!link && !isOverlayClosing) {
            openOverlay(index)
        }
    }

    const handleMediaClick = (type: 'video' | 'image') => {
        handleNeoCardMediaClick(type, {
            cta: 'Links',
            component: NeoComponent[link ? 'TILES_TO_PDP' : 'POPUP_TILES'],
            clickText: card?.textPanel?.headline || card.title,
            clickUrl: link
                ? link.linkUrl
                : type === 'video' && isMobile
                ? card?.mobileMedia?.file?.url || card?.media?.file?.url
                : card?.media?.file?.url
        })
    }

    const handleButtonClick = () => {
        setIsClickedByBtn(true)
        handleNeoCardBtnClick(
            {
                cta: 'Links',
                component: NeoComponent[link ? 'TILES_TO_PDP' : 'POPUP_TILES'],
                clickText: card?.textPanel?.headline || card.title,
                clickUrl: link?.linkUrl
            },
            'button'
        )
    }

    const handleOnAreaClick = () => {
        if (isClickedByBtn) {
            setIsClickedByBtn(false)
            return
        }
        handleNeoCardBtnClick(
            {
                cta: 'Links',
                component: NeoComponent[link ? 'TILES_TO_PDP' : 'POPUP_TILES'],
                clickText: card?.textPanel?.headline || card.title,
                clickUrl: link?.linkUrl
            },
            'area'
        )
    }
    const renderImage = () => (
        <>
            {src && type === 'image' && (
                <div
                    className={cn(s['image-card__image-wrapper'], {
                        [s['image-card__custom-border']]:
                            customConfiguration?.imageBorder
                    })}
                    aria-hidden="true"
                >
                    <ElgatoImage
                        onClick={() => handleMediaClick('image')}
                        src={src}
                        alt={description}
                        layout="fill"
                        objectFit="cover"
                        className="w-full h-full rounded-xxl"
                    />
                </div>
            )}
            {src && type === 'video' && (
                <div
                    className={cn(s['image-card__video'], {
                        [s['image-card__custom-border']]:
                            customConfiguration?.imageBorder
                    })}
                    onKeyPress={() => handleMediaClick('video')}
                    onClick={() => handleMediaClick('video')}
                    tabIndex={0}
                    role="button"
                    aria-hidden="true"
                >
                    <ElgatoVideo
                        className={cn('rounded-xxl h-full')}
                        // eslint-disable-next-line i18next/no-literal-string
                        videoClasses="object-cover h-full w-full"
                        secure_url={src}
                        options={{
                            autoPlay: true,
                            preload: 'auto',
                            muted: true,
                            loop: false
                        }}
                        fallbackImgUrl={posterImageSrc}
                        visibleThreshold={0.95}
                        videoDescription={card?.customOptions?.videoDescription}
                    />
                </div>
            )}
            {card.textPanel && (
                <div
                    className="flex justify-between items-center gap-8px px-16px"
                    onClick={handleOnAreaClick}
                    onKeyPress={handleOnAreaClick}
                    tabIndex={0}
                    role="button"
                    aria-hidden="true"
                >
                    <p
                        className={cn(
                            s['image-card__info-wrapper__text'],
                            s[`image-card__info-wrapper__text--${textColor}`]
                        )}
                    >
                        {card.textPanel.headline}
                    </p>
                    <Button
                        className={s['image-card__info-wrapper__button']}
                        variant="icon"
                        onClick={handleButtonClick}
                        style={{
                            borderRadius: customConfiguration
                                ? customConfiguration?.buttonBorder
                                : '100%'
                        }}
                        label={link?.ariaLabel}
                        isNeo={theme === 'neo'}
                    >
                        {link ? <ArrowAngledTopRight /> : <Plus />}
                    </Button>
                </div>
            )}
        </>
    )

    if (link) {
        return (
            // eslint-disable-next-line react/jsx-no-target-blank
            <a
                className={cn(
                    s['image-card'],
                    theme === 'neo' && s['image-card--neo'],
                    card.sizeCard && s[`image-card__size-card-${card.sizeCard}`]
                )}
                href={link.linkUrl}
                target={link.newTab ? '_blank' : '_self'}
                rel={link.newTab ? 'noreferrer' : undefined}
            >
                <span className="sr-only">
                    {getAriaLabel(
                        link?.linkTitle,
                        link.newTab
                            ? t('ada|Opens in a new Tab')
                            : t('ada|Opens in the current Tab')
                    )}
                </span>
                {renderImage()}
            </a>
        )
    }

    return (
        // eslint-disable-next-line jsx-a11y/interactive-supports-focus,jsx-a11y/click-events-have-key-events
        <div
            className={cn(
                s['image-card'],
                theme === 'neo' && s['image-card--neo'],
                card.sizeCard && s[`image-card__size-card-${card.sizeCard}`]
            )}
            onClick={handleOnClick}
            id={id}
            role="button"
            tabIndex={0}
        >
            {renderImage()}
        </div>
    )
}

export default ImageCard
