.sales-card-eol {
    

    &__list {
        .sales-card-eol__item {
            flex: 1 1 auto;
        }
    }

    &__text-wrapper + &__slider-wrapper,
    &__text-wrapper + &__list {
        margin-top: 64px;
    }

    &__toggles {
        background-color: var(--primitive-gray-20);
        width: 100%;
        height: 100%;
        border-radius: 16px;
        padding: 8px;
        display: flex;
        flex-direction: row;
        gap: 16px;
        margin: 24px 0;

        @screen md {
            width: fit-content;
        }
    }

    &__toggle-background {
        background-color: var(--primitive-black);
        color: var(--white);
    }

    &__toggle-inner {
        border-radius: 8px;
        width: fit-content;
        padding: 12px 24px;
    }
    &__toggles {
        background-color: var(--primitive-gray-20);
        width: 100%;
        height: 100%;
        border-radius: 16px;
        padding: 8px;
        display: flex;
        flex-direction: row;
        gap: 16px;
        margin: 24px 0;

        @screen md {
            width: fit-content;
        }
    }

    &__toggle-background {
        background-color: var(--primitive-black);
        color: var(--white);
    }

    &__toggle-inner {
        border-radius: 8px;
        width: fit-content;
        padding: 12px 24px;
    }

    &__slider-wrapper {
        width: 100%;
        height: 100%;
    }

    &__slider {
        max-width: none;
        width: 100%;
        padding-left: 40px;
        padding-bottom: 40px;
        box-shadow: 0px 0px 16px 0px rgba(0, 0, 0, 0.08);
        @screen md-max {
            padding-left: 16px;
        }
        

        :global {
            .swiper-slide {
                height: auto;
                width: 100%;
                max-width: 314px;

                @screen xl {
                    max-width: 386px;
                }
            }

            .swiper {
                overflow: visible;
                padding-right: var(--container-padding);

                @screen md {
                    padding-right: var(--container-padding--md);
                }

                @screen lg {
                    padding-right: var(--container-padding--lg);
                }
            }
        }

        & div[data-nav='prev'],
        & div[data-nav='next'] {
            top: 40% !important;
            display: block;
            button {
                background-color: var(--white);
            }

            svg {
                color: var(--primitive-gray-140);
            }
        }
        & div[data-nav='next'] {
            right: 16px !important;
            @screen md-max {
                right: 44px !important;
                top: 40% !important;
            }
        }
        & div[data-nav='prev'] {
            left: 16px !important;
            @screen md-max {
                left: 16px !important;
                top: 40% !important;
            }
        }

        .sales-card-eol__item {
            height: 100%;
            >div:first-child {
                box-shadow: 0px 0px 16px 0px rgba(0, 0, 0, 0.08);
                @screen 1200c {
                    padding-bottom: calc((230 / 386) * 100%);
                }
                @screen 1920c {
                    padding-bottom: calc((260 / 386) * 100%);
                }
            }
        }
    }

    &__item {
        display: flex;
        flex-direction: column;
    }

    :global {
        & div[data-nav='prev'],
        & div[data-nav='next'] {
            @screen md-max {
                display: none;
            }
        }
        .swiper-pagination {
            position: relative;
            top: 26px;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 16px;
            width: auto !important;
            padding: 8px 0;
        }
        .swiper-button-prev.swiper-button-disabled,
        .swiper-button-next.swiper-button-disabled {
            opacity: 0;
        }

        .swiper-navigation-wrapper {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            z-index: 20;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 16px;
            
            .swiper-button-prev,
            .swiper-button-next {
                display: block !important;
                position: relative;
                width: 24px;
                height: 24px;
                margin: 0;
                top: 24px;
                left: auto;
                right: auto;
                color: #000 !important;

                &::after {
                    font-size: 12px;
                    color: #000;
                }
            }
        }

        .swiper-pagination-bullet {
            width: 8px;
            height: 8px;
            background: rgba(0, 0, 0, 0.5);
            opacity: 1;
            margin: 0 !important;
            cursor: pointer;
            transition: all 0.3s ease;
            border-radius: 100%;
            position: relative;
            border-color: unset !important;

            &-active {
                background: #000;
                opacity: 1;
            }
        }

        @media (max-width: 767px) {
            .swiper-navigation-wrapper .swiper-button-prev {
                display: block !important;
            }
        }
    }
}
