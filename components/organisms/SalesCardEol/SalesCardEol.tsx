import React, { FC, useEffect, useRef, useState } from 'react'
import s from './SalesCardEol.module.scss'
import { CardProps } from '@components/templates/CardList/CardList'
import dynamic from 'next/dynamic'
import getProductsBySkus from '@pylot-data/api/operations/get-products-by-skus'
import { useRouter } from 'next/router'
import cn from 'classnames'
import { SwiperSliderTheme } from '@components/organisms/Slider/SwiperSlider'
import { SwiperSlide } from 'swiper/react'
import { SwiperOptions } from 'swiper/types/swiper-options'
import { A11y, Mousewheel, Navigation, Keyboard, Pagination } from 'swiper'
import {
    SectionBgColor,
    SectionThemeDarkBgColors
} from '@components/templates/Section/Section'

import { PrimaryTextProps } from '@components/molecules/PrimaryText/PrimaryText'
import {
    ConfigurableProduct,
    ConfigurableVariant,
    ProductInterface,
    SimpleProduct
} from '@pylot-data/pylotschema'
import { useIsMobileDevice } from '@config/hooks/useIsMobileDevice'
import { TextBlock } from '@components/molecules/TextBlock/TextBlock'
import {
    ProductStockStatus,
    ProductVariant
} from '@pylot-data/hooks/product/use-product-ui'
import { Icon } from '@components/atoms/Icon/Icon'
import SalesCardItem from './SalesCardItem'

const SwiperSlider = dynamic(
    () => import('@components/organisms/Slider/SwiperSlider'),
    {
        ssr: false
    }
)

interface SalesCardProps extends CardProps {
    eventTracking: any
    product?: null | ProductInterface
}

interface SalesCardEolProps {
    cards?: SalesCardProps[]
    id?: string
    textPanel?: PrimaryTextProps
    backgroundColor?: SectionBgColor
    variant?: 'default' | 'small' | 'bundle'
    mobileSlider?: boolean
    additionalTextPanels?: PrimaryTextProps[]
    className?: string
}

export const SalesCardEol: FC<SalesCardEolProps> = ({
    cards = [],
    id,
    backgroundColor = SectionBgColor.TRANSPARENT,
    textPanel,
    variant = 'default',
    additionalTextPanels
}) => {
    const { locale, events } = useRouter()
    const [salesCards, setSalesCards] = useState<SalesCardProps[]>([])
    const [toggleIsActive, setToggleIsActive] = useState(0)
    const sliderRef = useRef<HTMLDivElement | null>(null)
    const textBlockSize = 'large'
    const isMobile = useIsMobileDevice()
    const textColor = SectionThemeDarkBgColors.includes(backgroundColor)
        ? 'light'
        : 'dark'
    const paginationTheme = SectionThemeDarkBgColors.includes(backgroundColor)
        ? SwiperSliderTheme.LIGHT
        : SwiperSliderTheme.DARK
    const cardVariant = variant === 'small' ? 'small' : 'default'
    const sliderSettings: SwiperOptions = {
        spaceBetween: 16,
        slidesPerView: 'auto',
        centeredSlides: false,
        loop: false,
        modules: [A11y, Navigation, Mousewheel, Keyboard, Pagination],
        initialSlide: 0,
        allowTouchMove: true,
        speed: 300,
        mousewheel: {
            forceToAxis: true
        },
        keyboard: true,
        pagination: {
            clickable: true,
            el: '.swiper-pagination',
            bulletClass: 'swiper-pagination-bullet',
            bulletActiveClass: 'swiper-pagination-bullet-active',
            type: 'bullets'
        },
        navigation: {
            nextEl: !isMobile ? '[data-nav="next"]' : '.swiper-button-next',
            prevEl: !isMobile ? '[data-nav="prev"]' : '.swiper-button-prev'
        }
    }

    useEffect(() => {
        const handleRouteChangeComplete = () => {
            setSalesCards([])
        }
        events.on('routeChangeComplete', handleRouteChangeComplete)
        return () => {
            events.off('routeChangeComplete', handleRouteChangeComplete)
        }
    }, [])
    useEffect(() => {
        const sortProductCardByStockStatus = (
            productSalesCards: SalesCardProps[]
        ) => {
            const isOutOfStock = (product: ProductInterface) => {
                const variantIndex =
                    (product.variants as ConfigurableVariant[])?.findIndex(
                        (variant) =>
                            variant!.product!.sku?.toLowerCase() ===
                            product.sku?.toLowerCase()
                    ) ?? ProductVariant.NOT_SELECTED
                const variant = (product?.variants?.[variantIndex]?.product ??
                    product) as SimpleProduct
                const isOutOfStock =
                    product?.stock_status === ProductStockStatus.OutOfStock ||
                    variant?.stock_status === ProductStockStatus.OutOfStock ||
                    variantIndex === ProductVariant.NOT_EXIST
                return isOutOfStock
            }

            productSalesCards.sort((a, b) => {
                // do not change the order if one of the items does not contain a product
                if (!a.product || !b.product) {
                    return 0
                }

                const aOutOfStock = isOutOfStock(a.product)
                const bOutOfStock = isOutOfStock(b.product)

                // do not change if both Items have the same stock status
                if (aOutOfStock === bOutOfStock) {
                    return 0
                }

                return aOutOfStock ? 1 : -1
            })

            return productSalesCards
        }

        if (cards.length > 0 && !salesCards.length) {
            const skus = cards
                .map((card) => card.sku ?? '')
                .filter((sku) => sku != '')
            if (skus.length) {
                getProductsBySkus(skus, locale ?? '').then((products) => {
                    const productSalesCards: SalesCardProps[] = []
                    if (products && products.length) {
                        cards?.forEach((card) => {
                            const product = products.find(
                                (product) =>
                                    product.sku === card.sku ||
                                    (product?.__typename ===
                                        'ConfigurableProduct' &&
                                        (product as ConfigurableProduct)?.variants?.some(
                                            (variant) =>
                                                variant?.product?.sku ===
                                                card.sku
                                        ))
                            ) as ProductInterface
                            if (product) {
                                card.product = product
                            }
                            productSalesCards.push(card)
                        })
                    }
                    setSalesCards(
                        sortProductCardByStockStatus(productSalesCards)
                    )
                })
            } else {
                setSalesCards(sortProductCardByStockStatus(cards))
            }
        }
    }, [cards, locale, salesCards.length])
    return (
        <div
            id={id}
            className={cn(
                s['sales-card-eol'],
                'max-w-full overflow-x-hidden',
                backgroundColor
            )}
        >
            {textPanel && (
                <div className={s['sales-card-eol__text-wrapper']}>
                    {textPanel && (
                        <TextBlock
                            className={s['sales-card-eol__text']}
                            headline={textPanel.headline}
                            bodyCopy={textPanel.bodyCopy}
                            link={textPanel.link}
                            size={textBlockSize}
                            color={textColor}
                        />
                    )}
                    <div className={cn(s['sales-card-eol__toggles'])}>
                        {additionalTextPanels?.map((toggle, index) => {
                            return (
                                <div key={index}>
                                    <div
                                        className={cn(
                                            'flex gap-2 flex-row items-center justify-center',
                                            {
                                                [s[
                                                    'sales-card-eol__toggle-background'
                                                ]]: toggleIsActive === index
                                            },
                                            s['sales-card-eol__toggle-inner']
                                        )}
                                        role="button"
                                        tabIndex={0}
                                        onKeyPress={() =>
                                            setToggleIsActive(index)
                                        }
                                        onClick={() => setToggleIsActive(index)}
                                    >
                                        <div
                                            className={cn(
                                                s['sales-card-eol__icon']
                                            )}
                                        >
                                            {/*@ts-ignore*/}
                                            <Icon name={toggle.icon} />
                                        </div>
                                        <div
                                            className={cn(
                                                s[
                                                    'sales-card-eol__calloutTitle'
                                                ]
                                            )}
                                        >
                                            {toggle.calloutTitle}
                                        </div>
                                    </div>
                                </div>
                            )
                        })}
                    </div>
                </div>
            )}

            {salesCards && salesCards.length > 0 && (
                <div
                    ref={sliderRef}
                    className={cn(
                        s['sales-card-eol__slider-wrapper'],
                        'w-full'
                    )}
                >
                    <div
                        className={
                            salesCards.length <= 2
                                ? 'flex justify-center gap-4'
                                : ''
                        }
                    >
                        {salesCards.length <= 2 && !isMobile ? (
                            <div
                                className={cn(
                                    s['sales-card-eol__slider'],
                                    'flex justify-center gap-4'
                                )}
                            >
                                {salesCards.map((card, i) => (
                                    <SalesCardItem
                                        key={`sales-card-item-${i}`}
                                        {...card}
                                        variant={cardVariant}
                                    />
                                ))}
                            </div>
                        ) : (
                            <SwiperSlider
                                loop={false}
                                settings={sliderSettings}
                                className={s['sales-card-eol__slider']}
                                paginationTheme={paginationTheme}
                                navigationTheme={SwiperSliderTheme.LIGHT}
                            >
                                {salesCards.map((card, i) => {
                                    if (card.sku && !card.product) return null
                                    return (
                                        <SwiperSlide
                                            key={`sales-card-item-${i}`}
                                        >
                                            <SalesCardItem
                                                {...card}
                                                variant={cardVariant}
                                            />
                                        </SwiperSlide>
                                    )
                                })}
                                <div className="md:hidden">
                                    <div className="swiper-navigation-wrapper">
                                        <div className="swiper-button-prev" />
                                        <div
                                            className="swiper-pagination"
                                            slot="pagination"
                                        />
                                        <div className="swiper-button-next" />
                                    </div>
                                </div>
                            </SwiperSlider>
                        )}
                    </div>
                </div>
            )}
        </div>
    )
}

export default SalesCardEol
