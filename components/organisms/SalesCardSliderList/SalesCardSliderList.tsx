import { CardProps } from '@components/templates/CardList/CardList'
import { FC, useRef } from 'react'
import cn from 'classnames'
import s from './SalesCardSliderList.module.scss'
import { A11y, Keyboard, Mousewheel, Navigation } from 'swiper'
import { SwiperSlide } from 'swiper/react'
import { SwiperOptions } from 'swiper/types/swiper-options'
import dynamic from 'next/dynamic'
import {
    ContainerSize,
    Container
} from '@components/organisms/Container/Container'
import SliderArrows from '@components/molecules/SliderArrows/SliderArrows'
import { SalesCardSlider } from '@components/organisms/SalesCardSliderList/SalesCardSlider'
import { PrimaryTextProps } from '@components/molecules/PrimaryText/PrimaryText'
import { TextBlock } from '@components/molecules/TextBlock/TextBlock'
const SwiperSlider = dynamic(
    () => import('@components/organisms/Slider/SwiperSlider'),
    {
        ssr: false
    }
)

interface SalesCardSliderListProps {
    id?: string
    classNames?: string
    cards?: CardProps[]
    textPanel?: PrimaryTextProps
    backgroundColor?: string
    arrowStyle?: 'normal'
}

export const SalesCardSliderList: FC<SalesCardSliderListProps> = ({
    id,
    cards,
    classNames,
    textPanel,
    backgroundColor
}) => {
    const sliderRef = useRef<HTMLDivElement | null>(null)
    const textBlockSize = 'large'

    const sliderSettings: SwiperOptions = {
        navigation: {
            prevEl: '.product-swiper-button-prev',
            nextEl: '.product-swiper-button-next'
        },
        spaceBetween: 16,
        slidesPerView: 'auto',
        centeredSlides: false,
        loop: false,
        modules: [A11y, Navigation, Mousewheel, Keyboard],
        initialSlide: 0,
        allowTouchMove: true,
        breakpoints: {
            320: {
                slidesPerView: 'auto'
            },
            1920: {
                slidesPerView: 3.02
            }
        },
        mousewheel: {
            forceToAxis: true
        },
        keyboard: true
    }

    return (
        <div
            id={id}
            className={cn(
                'w-full flex justify-center overflow-hidden',
                backgroundColor
            )}
        >
            <div className={cn(s['sales-card-slider-list'], 'max-w-full')}>
                {textPanel && (
                    <TextBlock
                        className={s['sales-card-slider-list__text']}
                        callout={textPanel.calloutTitle}
                        headline={textPanel.headline}
                        bodyCopy={textPanel.bodyCopy}
                        link={textPanel.link}
                        size={textBlockSize}
                    />
                )}
                {cards && cards?.length > 0 && cards.length <= 3 && (
                    <Container size={ContainerSize.LARGE}>
                        <div
                            className={cn(
                                'pt-24px gap-16px flex flex-col md:flex-row md:flex-wrap w-full md:justify-center',
                                s['sales-card-slider-list__list']
                            )}
                        >
                            {cards?.map((card, index) => (
                                <SalesCardSlider
                                    key={`${card}-${index}`}
                                    card={card}
                                    index={index}
                                    classNames={cn(
                                        s['sales-card-slider-list__item'],
                                        'overflow-hidden',
                                        classNames
                                    )}
                                />
                            ))}
                        </div>
                    </Container>
                )}
                {cards && cards?.length > 3 && (
                    <div
                        ref={sliderRef}
                        className={cn(s['sales-card-list__slider-wrapper'])}
                    >
                        <SwiperSlider
                            loop={false}
                            settings={sliderSettings}
                            className={cn(s['sales-card-slider-list__slider'])}
                        >
                            {cards?.map((card, index) => (
                                <SwiperSlide key={`${card}-${index}`}>
                                    <SalesCardSlider
                                        key={`${card}-${index}`}
                                        card={card}
                                        index={index}
                                        classNames={cn(
                                            s['sales-card-slider-list__item'],
                                            'overflow-hidden',
                                            classNames
                                        )}
                                    />
                                </SwiperSlide>
                            ))}
                            <SliderArrows
                                classNameNext="product-swiper-button-next"
                                classNamePrev="product-swiper-button-prev"
                                style="normal"
                            />
                        </SwiperSlider>
                    </div>
                )}
            </div>
        </div>
    )
}

export default SalesCardSliderList
