.pdp-messages {
    padding: 0 16px;

    &__inner {
        padding: 25px 0;
        border-top: 1px solid var(--light-grey-1);

        @screen md {
            padding: 25px 32px;
        }

        @media screen and (min-width: 768px) and (max-height: 1000px) {
            padding: 16px 32px;
        }
    }

    &__item + &__item {
        margin-top: 12px;

        @media screen and (min-width: 768px) and (max-height: 1000px) {
            margin-top: 6px;
        }
    }
}

/* page theme dark */
.pdp-messages.page-theme-dark {
    .pdp-messages__inner {
        border-top-color: var(--primitive-gray-90)
    }
}

/* page theme neo */
.pdp-messages.page-theme-neo {
    padding: 0;

    .pdp-messages__inner {
        border-top-color: var(--primaries-mid-blue);
        padding: 25px 16px;

        @screen md {
            padding: 25px 48px;
        }

        @media screen and (min-width: 768px) and (max-height: 1000px) {
            padding: 16px 48px;
        }
    }
}
