/* eslint-disable react/no-children-prop */
import {
    Container,
    ContainerSize
} from '@components/organisms/Container/Container'
import { FeatureCardProps } from '@components/organisms/FeatureCard/FeatureCard'
import { CardProps } from '@components/templates/CardList/CardList'
import { FeatureListProps } from '@components/templates/FeatureList/FeatureList'
import cn from 'classnames'
import { FC } from 'react'
import PaddedTextCard from '../PaddedTextCard/PaddedTextCard'
import s from './PaddedTextCardList.module.scss'

interface PaddedTextCardListProps {
    cards?: CardProps[]
    headline?: string
    id?: string
    children?: FeatureCardProps[]
    headlineClasses?: string[]
    isAnimated?: boolean
}

export const PaddedTextCardList: FC<PaddedTextCardListProps> = ({
    headline = '',
    cards = [],
    id,
    isAnimated
}) => {
    return (
        <div id={id} className={s['padded-text-card-list']}>
            <Container size={ContainerSize.MEDIUM}>
                <div
                    className={cn('py-16', s['padded-text-card-list__section'])}
                >
                    {headline && (
                        <h2 className="pb-4 border-b-2 border-mid-grey-1 mx-16px text-h4 md-max:text-h4-md-max ">
                            {headline}
                        </h2>
                    )}
                    {cards && cards.length > 0 && (
                        <div className="pt-24px gap-8px md:gap-16px flex flex-wrap w-full">
                            {cards.map((card, index) => {
                                const childrenFeatureList = card.children?.filter(
                                    (el) =>
                                        el.meta?.contentType ===
                                        'organismFeatureList'
                                ) as FeatureListProps[] | undefined
                                return (
                                    <PaddedTextCard
                                        className={cn(
                                            s['padded-text-card-list__item']
                                        )}
                                        key={`padded-text-card-${card.textPanel?.headline}-${index}`}
                                        logoImage={card?.textPanel?.logoImage}
                                        bodyCopy={
                                            card.textPanel?.richText
                                                ? card.textPanel?.richText
                                                : card.textPanel?.bodyCopy
                                        }
                                        headline={card.textPanel?.headline}
                                        link={card.textPanel?.link}
                                        eventTracking={card?.eventTracking}
                                        featureList={childrenFeatureList?.[0]}
                                        headlineClasses={
                                            card.textPanel?.headlineClasses
                                        }
                                        cloudinaryLogoImage={
                                            card?.textPanel?.cloudinaryLogoImage
                                        }
                                    />
                                )
                            })}
                        </div>
                    )}
                </div>
            </Container>
        </div>
    )
}

export default PaddedTextCardList
