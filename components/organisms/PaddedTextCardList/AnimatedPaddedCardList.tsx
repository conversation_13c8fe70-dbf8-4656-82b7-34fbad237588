/* eslint-disable react/no-children-prop */
import AnimatedPaddedTextCard from '@components/organisms/AnimatedPaddedTextCards/AnimatedPaddedTextCard'
import {
    Container,
    ContainerSize
} from '@components/organisms/Container/Container'
import { FeatureCardProps } from '@components/organisms/FeatureCard/FeatureCard'
import { CardProps } from '@components/templates/CardList/CardList'
import cn from 'classnames'
import { decode } from 'he'
import { FC } from 'react'
import s from './PaddedTextCardList.module.scss'

interface PaddedTextCardListProps {
    cards?: CardProps[]
    headline?: string
    id?: string
    children?: FeatureCardProps[]
    headlineClasses?: string[]
    bodyCopy?: string
    disclaimerText?: string
    classes?: string[]
    size?: string
}

export const AnimatedPaddedTextCardList: FC<PaddedTextCardListProps> = ({
    headline = '',
    cards = [],
    id,
    bodyCopy,
    disclaimerText
}) => {
    return (
        <div id={id} className={s['padded-text-card-list']}>
            <Container size={ContainerSize.LARGE}>
                <div
                    className={cn(
                        'pb-80px flex flex-col items-center',
                        s['padded-text-card-list__section']
                    )}
                >
                    {headline && (
                        <h1
                            className={cn(
                                s['padded-text-card-list__headline'],
                                'pt-80px md:py-80px'
                            )}
                        >
                            {headline}
                        </h1>
                    )}
                    {cards && cards.length > 0 && (
                        <div className="py-40px md:pt-16px gap-16px flex flex-wrap w-full">
                            {cards.map((card, index) => {
                                const backgroundColorClass =
                                    card.backgroundColor
                                return (
                                    <AnimatedPaddedTextCard
                                        className={cn(
                                            s['padded-text-card-list__item'],
                                            backgroundColorClass,
                                            card.textColor === 'light'
                                                ? 'text-white'
                                                : undefined
                                        )}
                                        key={`padded-text-card-${card.textPanel?.headline}-${index}`}
                                        bodyCopy={
                                            card.textPanel?.richText
                                                ? card.textPanel?.richText
                                                : card.textPanel?.bodyCopy
                                        }
                                        headline={card.textPanel?.headline}
                                        eventTracking={card?.eventTracking}
                                        headlineClasses={
                                            card.textPanel?.headlineClasses
                                        }
                                        animationImage={card?.animationImage}
                                        backgroundColor={card?.backgroundColor}
                                        textColor={card.textColor}
                                        cloudinaryMedia={card?.cloudinaryMedia}
                                    />
                                )
                            })}
                        </div>
                    )}

                    <div
                        className={cn(
                            s['padded-text-card-list__text'],
                            'flex flex-col gap-24px items-center justify-center'
                        )}
                    >
                        {bodyCopy && (
                            <p
                                className={cn(
                                    s['padded-text-card-list__text-body']
                                )}
                            >
                                {bodyCopy}
                            </p>
                        )}
                        {disclaimerText && (
                            <p
                                className="text-center"
                                dangerouslySetInnerHTML={{
                                    __html: decode(disclaimerText)
                                }}
                            />
                        )}
                    </div>
                </div>
            </Container>
        </div>
    )
}

export default AnimatedPaddedTextCardList
