import s from './HoverGalleryCard.module.scss'
import cn from 'classnames'
import React, { FC } from 'react'
import { GalleryCardProps } from '@components/organisms/GalleryCard/GalleryCard'
import Image from '@corsairitshopify/corsair-image/index'
import PrimaryText, {
    HorizontalAlignmentEnum
} from '@components/molecules/PrimaryText/PrimaryText'
import ElgatoVideo from '@components/common/ElgatoVideo/ElgatoVideo'
import ElgatoImage from '@components/common/ElgatoImage/ElgatoImage'
import { useMobile } from '@pylot-data/hooks/use-mobile'

export enum HoverCardSize {
    THIRD = '33',
    HALF = '50',
    FULL = 'full'
}

const defaultVideoOptions = {
    autoPlay: true,
    preload: 'auto',
    muted: true,
    loop: false
}
export const HoverGalleryCard: FC<GalleryCardProps> = (props) => {
    const {
        headline,
        text,
        textColor,
        mobileTextColor,
        textHoverColor,
        cloudinaryMedia,
        animationImage,
        mobileAnimationImage,
        link,
        size = HoverCardSize.FULL,
        className,
        cloudinaryMobileMedia,
        textPanel,
        sku,
        textHorizontalAlignment = HorizontalAlignmentEnum.LEFT,
        mobileTextHorizontalAlignment = HorizontalAlignmentEnum.LEFT,
        cloudinaryPosterImage,
        videoOptions,
        customOptions
    } = props

    const { isMobile } = useMobile()
    const CardComponent = link && link.linkUrl ? 'a' : 'div'
    const target = link && link.newTab ? '_blank' : undefined
    let textAlignment = HorizontalAlignmentEnum.LEFT

    if (textHorizontalAlignment === HorizontalAlignmentEnum.RIGHT) {
        textAlignment = HorizontalAlignmentEnum.RIGHT
    } else if (textHorizontalAlignment === HorizontalAlignmentEnum.CENTER) {
        textAlignment = HorizontalAlignmentEnum.CENTER
    }

    if (isMobile) {
        textAlignment = HorizontalAlignmentEnum.LEFT
        if (mobileTextHorizontalAlignment === HorizontalAlignmentEnum.RIGHT) {
            textAlignment = HorizontalAlignmentEnum.RIGHT
        } else if (
            mobileTextHorizontalAlignment === HorizontalAlignmentEnum.CENTER
        ) {
            textAlignment = HorizontalAlignmentEnum.CENTER
        }
    }

    let image,
        video = null
    const isVideo = cloudinaryMedia?.[0]?.resource_type == 'video'
    if (isVideo && cloudinaryMedia?.[0]) {
        video = cloudinaryMedia?.[0]
    } else {
        image = cloudinaryMedia?.[0]
    }
    let mobileImage,
        mobileVideo = null
    const isMobileVideo = cloudinaryMobileMedia?.[0]?.resource_type == 'video'
    if (isMobileVideo && cloudinaryMobileMedia?.[0]) {
        mobileVideo = cloudinaryMobileMedia?.[0]
    } else {
        mobileImage = cloudinaryMobileMedia?.[0]
    }

    const buttonBottom = customOptions?.buttonBottom as boolean

    const color = isMobile && mobileTextColor ? mobileTextColor : textColor
    return (
        <CardComponent
            className={cn(
                s['hover-gallery-card'],
                s[`hover-gallery-card--size-${size}`],
                {
                    [s[`hover-gallery-card--hover-${textHoverColor}`]]:
                        textHoverColor && textHoverColor !== textColor
                },
                className
            )}
            href={link && link.linkUrl ? link.linkUrl : undefined}
            target={target}
        >
            <div className={s['hover-gallery-card__inner']}>
                <div
                    className={cn(s['hover-gallery-card__image-wrapper'], {
                        [s['hover-gallery-card__image-wrapper--mobile']]:
                            cloudinaryMobileMedia &&
                            cloudinaryMobileMedia?.[0] &&
                            cloudinaryMobileMedia?.[0]?.secure_url,
                        [s['hover-gallery-card__image-wrapper--mobile-hover']]:
                            mobileAnimationImage &&
                            mobileAnimationImage.file &&
                            mobileAnimationImage.file.url
                    })}
                >
                    {image?.secure_url && (
                        <div className={s['hover-gallery-card__image']}>
                            <ElgatoImage
                                src={image?.secure_url}
                                alt={headline || ''}
                                layout="fill"
                                objectFit="cover"
                            />
                        </div>
                    )}
                    {mobileImage?.secure_url && (
                        <div
                            className={cn(
                                s['hover-gallery-card__image'],
                                s['hover-gallery-card__image--mobile']
                            )}
                        >
                            <ElgatoImage
                                src={mobileImage?.secure_url}
                                alt={
                                    mobileImage?.context?.custom?.alt ||
                                    headline ||
                                    ''
                                }
                                layout="fill"
                                objectFit="cover"
                            />
                        </div>
                    )}
                    {animationImage &&
                        animationImage.file &&
                        animationImage.file.url && (
                            <div
                                className={cn(
                                    s['hover-gallery-card__hover-image']
                                )}
                            >
                                <Image
                                    src={animationImage.file.url}
                                    alt={
                                        animationImage?.description ||
                                        headline ||
                                        ''
                                    }
                                    layout="fill"
                                    objectFit="cover"
                                />
                            </div>
                        )}
                    {mobileAnimationImage &&
                        mobileAnimationImage.file &&
                        mobileAnimationImage.file.url && (
                            <div
                                className={cn(
                                    s['hover-gallery-card__hover-image'],
                                    s['hover-gallery-card__hover-image--mobile']
                                )}
                            >
                                <Image
                                    src={mobileAnimationImage.file.url}
                                    alt={
                                        mobileAnimationImage?.description ||
                                        headline ||
                                        ''
                                    }
                                    layout="fill"
                                    objectFit="cover"
                                />
                            </div>
                        )}
                    {isVideo &&
                        cloudinaryMedia?.[0] &&
                        cloudinaryMedia?.[0]?.secure_url && (
                            <ElgatoVideo
                                className={s['hover-gallery-card__hover-video']}
                                secure_url={
                                    isMobile && mobileVideo?.secure_url
                                        ? mobileVideo.secure_url
                                        : cloudinaryMedia?.[0]?.secure_url
                                }
                                options={{
                                    ...defaultVideoOptions,
                                    ...videoOptions
                                }}
                                fallbackImgUrl={
                                    cloudinaryPosterImage?.[0]?.secure_url
                                }
                                videoDescription={
                                    customOptions?.videoDescription as
                                        | string
                                        | undefined
                                }
                            />
                        )}
                </div>
                <PrimaryText
                    headline={headline}
                    mobileHeadline={textPanel?.mobileHeadline}
                    headlineTag={textPanel?.headlineTag}
                    calloutTag={textPanel?.calloutTag}
                    headlineStyle={textPanel?.headlineStyle}
                    disclaimerText={textPanel?.disclaimerText}
                    badgeText={textPanel?.badgeText}
                    bodyCopy={text}
                    label={textPanel?.label}
                    mobileBodyCopy={textPanel?.mobileBodyCopy}
                    textAlignment={textAlignment}
                    className={s['hover-gallery-card__text']}
                    link={link}
                    textColor={color}
                    sku={sku}
                    buttonBottom={buttonBottom}
                />
            </div>
        </CardComponent>
    )
}
