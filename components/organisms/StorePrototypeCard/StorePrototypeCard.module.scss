.customizable-card__overlay-margin {
    @screen md {
        margin-top: -15px !important;
    }
}
.customizable-card__overlay__margin {
    @screen md {
        margin-top: 3.5px;
    }
}
.customizable-card__overlay__margin-x {
    @screen md {
        margin-top: 15px;
    }
}
.customizable-card {
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 0 16px rgba(0, 0, 0, 0.08);
    border: 2px solid transparent;
    min-height: 686px;

    @media only screen and (max-width: 767px) {
        display: grid !important;
        min-height: 390px;

        &__icons {
            min-height: 330px;
        }
    }

    &__info {
        min-height: initial !important;
        @media only screen and (max-width: 767px) {
            min-height: 508px;
        }
    }

    &__info__overlay {
        min-height: 686px !important;
    }

    &__wrapper {
        position: relative;
        &::after {
            position: absolute;
            content: ' ';
            width: 100%;
            height: 150px;
            display: block;
            bottom: -1px;
            z-index: 10;
            background: linear-gradient(
                to bottom,
                rgba(255, 255, 255, 0) 0%,
                rgba(255, 255, 255, 1) 100%
            );
        }
    }

    &_icons {
        min-height: 766px;
    }

    &__overlay {
        opacity: 0;
        z-index: 9999;
        transform: translateX(1200px);
        transition: all 0.3s ease-out;
        left: 0;
        width: 100%;
        height: 100%;
        overflow-x: hidden;
        overflow-y: scroll;
        pointer-events: none;

        &__content {
            padding: 0;
            top: 0;
            position: absolute;
            width: 100%;
        }

        &.active {
            pointer-events: all;
            opacity: 1;
            transform: translateX(0);
            transition: all 0.3s ease-out;
            background-color: var(--bg-grey);
        }

        &__close {
            background: #000 !important;
            padding: 5px !important;
            border-radius: 9999px !important;
            z-index: 99999;
            margin-right: 0;

            @screen lg {
                margin-right: 4.4rem;
                //margin-top: 0.5rem;
            }

            svg {
                width: 32px !important;
                height: 32px !important;
                @screen md-max {
                    width: 24px !important;
                    height: 24px !important;
                }
                color: #fff;
                margin: 0 !important;
            }
        }
    }

    &__active {
        border: 2px solid #333;
    }

    &__badge-wrapper {
        left: 12%;
        max-width: 471px;

        & > div {
            word-break: break-word;
            hyphens: auto;
        }

        @media only screen and(max-width:767px) {
            &__icons {
                svg {
                    width: 24px;
                }
            }

            & > div {
                margin: 7px 0;
            }
        }

        @screen md {
            & > div {
                margin: 12px 0;
                &:first-child {
                    margin-bottom: 48px;
                }
            }
        }

        @screen lg {
            & > div {
                margin: 24px 0;
            }
        }

        @screen xxl {
            & > div {
                margin: 30px 0;
            }
        }
    }

    &__video {
        &__overlay {
            z-index: 20;
            right: 0;
            left: 0;
            bottom: 0;
        }

        &__play-button {
            svg {
                color: #fff;
            }
        }

        & > div {
            height: 100%;

            video {
                height: 100%;
                object-fit: cover;
            }
        }
    }

    &__slideshow {
        :global {
            .swiper-wrapper {
                margin-bottom: 50px;
            }

            .swiper-scrollbar {
                background: #525252;
                border-radius: 8px;
                height: 8px;
                width: 100%;
                margin-top: 45px;
                max-width: 887px;
                @screen md-max {
                    max-width: 306px;
                }
            }

            .swiper-scrollbar-drag {
                cursor: grab;
                background-color: #fff;
            }
        }

        .sales-card-list__slider-wrapper {
            max-width: 70%;
            margin-left: auto;
            top: 50%;
            position: absolute;
            transform: translateY(-50%);
            right: 0;

            @media only screen and(max-width:1280px) {
                padding-left: 100px;
            }

            &__card-wrapper {
                margin: 0 auto;

                @screen md {
                    max-width: 314px;
                }

                @screen xl {
                    max-width: 386px;
                }
            }

            @media only screen and(max-width:767px) {
                padding-left: 0;

                &__card-wrapper {
                    max-width: 320px;
                }
            }
        }

        .swiper-slide {
            margin: 0 auto !important;
        }

        @media only screen and(min-width:1280px) {
            [data-nav='next'] {
                right: -70px;
            }

            [data-nav='prev'] {
                left: -70px;
            }
        }

        & div[data-nav='prev'],
        & div[data-nav='next'] {
            @screen md-max {
                display: none;
            }
        }

        @media only screen and(max-width:767px) {
            min-height: 690px;

            .sales-card-list__slider-wrapper {
                padding-top: 150px;
                max-width: 100%;
                transform: translate(0);
                top: unset;
                left: 5%;
            }
        }
    }

    &__info {
        &__copy {
            min-height: 766px;

            @media only screen and(max-width:767px) {
                min-height: 580px;
            }
        }

        & > div {
            padding: 0px 30px;
        }

        &__hotspots__dot {
            &__active {
                animation: pulse 1.1s infinite;

                &__inner {
                    animation: pulse 1.1s infinite 0.6s;
                }
            }
            &.no-animation {
                animation: none !important;
                transition: none !important;
            }

            &-text {
                &-title {
                    white-space: nowrap;
                    padding: 3px 10px;
                    text-transform: uppercase;
                    font-weight: bold;
                    font-family: 'Univers65Bold';
                }
            }
        }

        &__button {
            overflow: hidden;
            background-color: rgba(255, 255, 255, 0.8) !important;
            padding: 8px 20px !important;
            transition: all 0.3s ease-out;
            text-transform: uppercase;
            border-radius: 9999px !important;
            font-weight: bold;
            font-family: 'Univers65Bold' !important;

            &_icon {
                width: 0;
                transition: all 0.3s ease-out;

                &.active {
                    width: 34px;
                    transition: all 0.3s ease-out;
                }
            }

            svg {
                width: 34px !important;
                height: 34px !important;
                padding: 5px 7px;
                background-color: #204cfe;
                border-radius: 9999px;
                opacity: 0;
                transition: all 0.3s ease-out;
                color: #fff;
                margin-left: 5px;
            }

            &__active {
                svg {
                    opacity: 1;
                    transition: all 0.3s ease-out;
                }
            }

            @media only screen and (max-width: 767px) {
                width: 100%;

                svg {
                    opacity: 1;
                }
            }
        }

        &__copy-btn {
            cursor: pointer;
            margin-top: 30px;
            border-radius: 999px;
            padding: 8px 24px;
            border: 2px solid #dbdbdb;
            justify-content: center;
            display: flex;
            position: relative;
            white-space: nowrap;

            @media only screen and (min-width: 767px) {
                &:before {
                    content: '';
                    display: none;
                    position: absolute;
                    z-index: 9998;
                    top: 50%;
                    transform: translateY(-50%) rotate(-90deg);
                    right: -18px;
                    width: 0;
                    height: 0;
                    color: #fff;
                    border-left: 5px solid transparent;
                    border-right: 5px solid transparent;
                    border-bottom: 5px solid #fff;
                }

                &:after {
                    content: 'Click to Copy';
                    position: absolute;
                    opacity: 0;
                    z-index: 9999;
                    top: 50%;
                    transform: translateY(-50%);
                    right: -145px;
                    width: 130px;
                    height: 36px;

                    color: #000;
                    font-size: 16px;
                    padding: 8px 14px;
                    line-height: 36px;
                    text-align: center;
                    font-family: Univers55Roman, sans-serif;

                    background: #fff;
                    border-radius: 3px;
                    justify-content: center;
                    align-items: center;
                }

                &:hover {
                    &:before {
                        display: block;
                    }

                    &:after {
                        display: flex;
                        opacity: 1;
                    }
                }

                &:active,
                &:focus {
                    outline: none;

                    &:after {
                        content: 'Copied!';
                    }
                }
            }

            &__icon {
                width: 0;
                transition: all 0.3s ease-out;

                svg {
                    opacity: 0;
                    transition: all 0.3s ease-out;
                }

                &.dark {
                    svg {
                        stroke: #333;
                    }
                }

                &.light {
                    svg {
                        stroke: #fff;
                    }
                }

                &.active {
                    svg {
                        opacity: 1;
                        transition: all 0.3s ease-out;
                    }

                    width: 24px;
                    transition: all 0.3s ease-out;
                }
            }
        }

        @media only screen and (max-width: 767px) {
            &__btn {
                width: calc(100% - 48px);
                left: 50%;
                transform: translateX(-50%);

                a {
                    width: 100%;

                    svg {
                        opacity: 1;
                    }
                }
            }

            &__button_icon {
                width: 34px;
            }
        }
    }

    &--small .customizable-card {
        &__media-wrapper {
            padding-bottom: calc((300 / 386) * 100%);
        }
    }

    &--product .customizable-card {
        min-height: 766px;

        &__media-wrapper {
            padding-bottom: calc((467 / 314) * 100%);

            @screen md {
                padding-bottom: calc((430 / 314) * 100%);
            }

            @screen xl {
                padding-bottom: calc((556 / 436) * 100%);
            }
        }
    }

    .sales-card__text {
        &--merch {
            background: linear-gradient(
                180deg,
                rgba(0, 0, 0, 0) 24.27%,
                rgba(0, 0, 0, 0.5) 100%
            );
            padding: 0 4.5rem 2.25rem 4.5rem;
        }
    }

    &__badge {
        h5 {
            word-break: break-word;
            hyphens: auto;
        }

        @screen md {
            h5 {
                font-size: 24px !important;
            }
        }

        @screen lg2 {
            h5 {
                font-size: 32px !important;
            }
        }
    }

    &__banner-hgg-icon {
        display: flex;
        align-items: center;
        gap: 4px;
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.15);
    }

    100% {
        transform: scale(1);
    }
}
.sales-card-list__item{
    height: 100%;
    display: flex;
    flex-direction: column;
}
.sale-card-slide{
    height: auto;
}
.sales-card-list__slider-wrapper__card-wrapper{
    height: 100%;
}