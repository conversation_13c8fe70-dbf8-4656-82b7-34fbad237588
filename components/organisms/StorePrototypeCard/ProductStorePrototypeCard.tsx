/* eslint-disable i18next/no-literal-string */
import { Badge } from '@components/atoms/Badge/Badge'
import { AnimatedProductContent } from '@components/organisms/AnimatedProductContent/AnimatedProductContent'
import { ProductInterface } from '@pylot-data/pylotschema'
import cn from 'classnames'
import { useTranslation } from 'next-i18next'
import { FC, useEffect, useState } from 'react'
import s from './StorePrototypeCard.module.scss'

import { StorePrototypeCardProps } from '@components/organisms/StorePrototypeCard/StorePrototypeCard'

export const ProductStorePrototypeCard: FC<StorePrototypeCardProps> = (
    props
) => {
    const {
        link,
        product = null,
        media,
        mobileMedia,
        textColor,
        className = '',
        hideDiscover,
        backgroundColor,
        children,
        style,
        shortDescription,
        headline
    } = props

    const { t } = useTranslation(['common'])

    let cardMedia = media?.file.url
    let cardMediaMobile = mobileMedia?.file.url

    if (product && product.image?.url && !media?.file.url) {
        cardMedia = product.image.url.toString()
        cardMediaMobile = product.image.url.toString()
    }
    const txtColor = textColor === 'light' ? 'text-white' : 'text-dark'
    const [isHovered, setIsHovered] = useState(false)
    const [isMobile, setIsMobile] = useState(false)
    const handleMouseEnter = () => {
        setIsHovered(true)
    }

    const handleMouseLeave = () => {
        setIsHovered(false)
    }
    useEffect(() => {
        const handleResize = () => {
            if (window.innerWidth < 767) {
                setIsMobile(true)
                setIsHovered(true)
            } else {
                setIsHovered(false)
            }
        }

        handleResize()

        window.addEventListener('resize', handleResize)

        return () => {
            window.removeEventListener('resize', handleResize)
        }
    }, [])

    const styleBgImage = {
        backgroundImage:
            isMobile && cardMediaMobile
                ? `url(${cardMediaMobile})`
                : cardMedia
                ? `url(${cardMedia})`
                : 'none',
        backgroundSize:
            isMobile && cardMediaMobile
                ? 'cover'
                : cardMedia
                ? 'cover'
                : 'auto',
        height: !isMobile ? '100%' : 'auto',
        backgroundRepeat: 'no-repeat',
        backgroundPosition: 'center',
        borderRadius: '10px'
    }
    return (
        <div
            className={cn(
                s['customizable-card'],
                s['customizable-card__product'],
                { [s[`customizable-card__active`]]: isHovered },
                className,
                backgroundColor,
                txtColor
            )}
            data-sku={product?.sku}
            style={style}
            onMouseEnter={handleMouseEnter}
            onMouseLeave={handleMouseLeave}
        >
            {/* eslint-disable-next-line jsx-a11y/anchor-has-content */}
            <a
                href={link?.linkUrl}
                className="absolute w-full h-full"
                target={`${link?.newTab}` ? '_blank' : '_self'}
                rel="noreferrer"
                aria-label={
                    link?.newTab
                        ? t('ada|Opens in a new Tab')
                        : t('ada|Opens in the current Tab')
                }
            />
            <div style={styleBgImage}>
                <div className="pl-8 pt-8 flex">
                    {children &&
                        children.map((child, i) => {
                            if (child.meta.contentType === 'moleculeBanner') {
                                return (
                                    <div
                                        key={i}
                                        className={i !== 0 ? 'ml-4' : ''}
                                    >
                                        {child?.badgeText && (
                                            <Badge
                                                className={cn(
                                                    s[
                                                        'sales-event-card__banner'
                                                    ],
                                                    child.badgeBackgroundColor,
                                                    child.badgeTextColor
                                                )}
                                                /* eslint-disable-next-line i18next/no-literal-string */
                                                size="medium"
                                                hasDiscount={
                                                    child.badgeHasDiscount
                                                }
                                                sku={product?.sku ?? undefined}
                                                regions={child?.regions}
                                                badgeTextColor={
                                                    child?.badgeTextColor
                                                }
                                            >
                                                {child.badgeHasDiscount ? (
                                                    child.badgeText ||
                                                    child.headline
                                                ) : (
                                                    <h5>
                                                        {child.headline ||
                                                            child.badgeText}
                                                    </h5>
                                                )}
                                            </Badge>
                                        )}
                                    </div>
                                )
                            }
                            return null
                        })}
                </div>

                <AnimatedProductContent
                    product={product}
                    headline={headline || product?.name || ''}
                    subheaderHtml={shortDescription}
                    bundleProducts={
                        product?.bundle_products as ProductInterface[]
                    }
                    link={link}
                    productLink={
                        hideDiscover || link
                            ? undefined
                            : product?.url_key?.toString()
                    }
                    fullHeight={false}
                    reviews={false}
                    backgroundColor={backgroundColor}
                    hoverState={isHovered}
                    textColor={textColor}
                />
            </div>
        </div>
    )
}
