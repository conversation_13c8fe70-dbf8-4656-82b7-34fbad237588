.filter-bar {
    padding-top: 16px;
    padding-bottom: 16px;
    border-top: 1px solid var(--white-smoke);
    border-bottom: 1px solid var(--white-smoke);
    @screen md-max {
        border-bottom: none;
        padding-bottom: 0;
    }
    &__inner {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 0 auto;
        padding: 0 16px;
        max-width: 1490px;
        @screen md {
            gap: 16px;
            padding: 0;
        }

        @screen md-max {
            gap: 8px;
            flex-direction: column;
        }
        &__beta-title {
            gap: 10px;
            justify-content: center;
            align-items: flex-start;
            display: flex;
            flex-direction: row;
            @screen md-max {
                position: absolute;
                top: 50%;
                margin-top: 65px;
            }
        }
    }
    &__inner-special-page {
        @screen md {
            max-width: 100%;
        }
    }
    &__inner-beta {
        @screen md-max {
            flex-direction: column-reverse;
            position: relative;
            border-bottom: none !important;
        }
    }
    &__buttons {
        display: flex;
        max-width: 795px;
        align-items: center;
        gap: 16px;
        @screen md-max {
            order: 2;
            justify-content: center;
            align-items: center;
            flex-wrap: wrap;
            width: 100%;
        }
    }
    &__buttons-hidden {
        @screen md-max {
            display: none;
        }
    }

    &__beta-title {
        gap: 10px;
        justify-content: center;
        align-items: flex-start;
        display: flex;
        flex-direction: row;
        @screen md-max {
            width: 100%;
            border-top: 1px solid var(--white-smoke);
            padding-top: 32px;
        }
    }

    &__search {
        width: 100%;
        position: relative;
        align-self: flex-start;
        flex: 0 0 auto;

        @screen md {
            max-width: 351px;
        }
        @screen lg {
            max-width: 436px;
        }
    }

    &__filters {
        display: flex;
        gap: 32px;

        @screen md-max {
            order: 1;
            gap: 8px;
            width: 100%;
            align-items: flex-end;
            flex-direction: column;
            margin-bottom: 16px;
        }
    }

    & &__dropdown {
        @screen md-max {
            width: 100%;
        }
    }
}
