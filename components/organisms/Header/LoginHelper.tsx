/* eslint-disable i18next/no-literal-string */
import { pushToDataLayer } from '@corsairitshopify/pylot-gtm/src/utils/pushToDataLayer'
import { getAccountLoginOpenEvent } from '@lib/gtm/accountLoginOpen'
import { getAccountRegistrationEvent } from '@lib/gtm/accountRegistration'
import { getAccountRegistrationOpenEvent } from '@lib/gtm/accountRegistrationOpen'
import { getNewsletterSignupEvent } from '@lib/gtm/newsletterSignup'
import { useGigyaLogin } from '@pylot-data/hooks/sso-login/use-gigya-login'
import cn from 'classnames'
import {
    addHiddenFormLabel,
    bindKeypessEventToElement,
    handleSneakPassword,
    registerFormFormater,
    signFormFormater
} from 'helpers/gigyaHelper'
import { useTranslation } from 'next-i18next'
import { FC } from 'react'
import { Button } from '../../molecules/Button/Button'

export const response = {
    STATUS: 'ok'
}
export interface GigyaAccount {
    UID: string
    profile: GigyaProfile
}

interface GigyaProfile {
    email: string
}

/*eslint i18next/no-literal-string: ["error", { "ignoreAttribute": [ "color"] }]*/
export const LoginButtons: FC<{ className?: string; mobile?: boolean }> = (
    props
) => {
    const { gigyaLogin } = useGigyaLogin()
    const { t } = useTranslation()

    const initializeGigya = (screen: string) => {
        let eventAccountVerification = { event: '', accountVerification: {} }
        return gigya?.accounts?.showScreenSet({
            // eslint-disable-next-line i18next/no-literal-string
            screenSet: 'Default-RegistrationLogin',
            startScreen: `gigya-${screen}-screen`,
            onAfterScreenLoad: (eventObj) => {
                if (eventObj?.currentScreen === 'gigya-login-screen') {
                    const event = getAccountLoginOpenEvent()
                    pushToDataLayer(event)
                }
                if (eventObj?.currentScreen === 'gigya-register-screen') {
                    const event = getAccountRegistrationOpenEvent()
                    pushToDataLayer(event)
                }
                handleSneakPassword(
                    t('ada|Show password'),
                    t('ada|Hide password')
                )
                bindKeypessEventToElement('.gigya-screen-dialog-close a')
                addHiddenFormLabel(`.gigya-${screen}-form`)
                if (screen === 'register') {
                    registerFormFormater()
                } else if (screen === 'login') {
                    signFormFormater()
                }
            },
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            onAfterSubmit: (eventObj: any) => {
                const user = eventObj?.response?.user
                const userInfo = eventObj?.response?.userInfo
                const isVerified = eventObj?.response?.isVerified
                const marketingConsent =
                    eventObj?.subscriptions?.marketing?.optin?.email
                        ?.isSubscribed
                const email = eventObj?.response?.profile?.email
                const firstName = eventObj?.response?.profile?.firstName
                const lastName = eventObj?.response?.profile?.lastName
                if (userInfo && eventObj?.form === 'gigya-register-form') {
                    const accountRegistrationEvent = getAccountRegistrationEvent(
                        userInfo
                    )
                    pushToDataLayer(accountRegistrationEvent)
                    if (marketingConsent) {
                        const newsletterSignupEvent = getNewsletterSignupEvent({
                            customerEmail: email,
                            registrationLocation: eventObj.form,
                            marketingConsent,
                            firstName,
                            lastName
                        })
                        pushToDataLayer(newsletterSignupEvent)
                    }
                }
                const eventAccountLogin = {
                    event: 'accountLogin',
                    accountLogin: {
                        customerEmail: eventObj?.response?.profile?.email, //email from successful login
                        customerId: eventObj?.response?.user?.UID //id from successful login
                    }
                }
                eventAccountVerification = {
                    event: 'accountVerification',
                    accountVerification: {
                        customerEmail: eventObj?.response?.profile?.email, //email from successful login
                        customerID: eventObj?.response?.UID, //id from successful login
                        firstName: eventObj?.response?.user?.firstName
                            ? eventObj?.response?.user?.firstName
                            : eventObj?.response?.userInfo?.firstName,
                        lastName: eventObj?.response?.user?.lastName
                            ? eventObj?.response?.user?.lastName
                            : eventObj?.response?.userInfo?.lastName,
                        country: eventObj?.profile?.country,
                        isVerified: eventObj?.response?.isVerified
                            ? eventObj?.response?.isVerified
                            : eventObj.response.status.toLowerCase() ===
                              response.STATUS
                            ? true
                            : false
                    }
                }
                if (eventObj?.form === 'gigya-login-form') {
                    if (
                        eventObj.response.status.toLowerCase() ===
                        response.STATUS
                    ) {
                        pushToDataLayer(eventAccountVerification)
                        pushToDataLayer(eventAccountLogin)
                    }
                }
                if (eventObj?.form === 'gigya-otp-update-form') {
                    if (eventObj?.response?.isVerified) {
                        pushToDataLayer(eventAccountVerification)
                        pushToDataLayer(eventAccountLogin)
                    }
                }
            },
            onError: (eventObj: any) => {
                if (
                    eventObj?.response?.info?.form === 'gigya-otp-update-form'
                ) {
                    pushToDataLayer({
                        ...eventAccountVerification,
                        accountVerification: {
                            ...eventAccountVerification.accountVerification,
                            isVerified: false
                        }
                    })
                }
            },
            onAfterValidation: (eventObj: any) => {
                if (
                    eventObj?.form === 'gigya-otp-update-form' &&
                    eventObj?.formData.code === ''
                ) {
                    pushToDataLayer({
                        ...eventAccountVerification,
                        accountVerification: {
                            ...eventAccountVerification.accountVerification,
                            isVerified: false
                        }
                    })
                    return
                }
            }
        })
    }

    const addGigyaEventHandlers = () => {
        return gigya?.accounts?.addEventHandlers({
            onLogin: async (account: GigyaAccount) => {
                const { UID: uid, profile } = account
                const { email } = profile

                const user = { uid, email }

                await gigyaLogin(user)
            },
            onError: (error: string) => {
                console.error({ error })
            }
        })
    }

    const handleGigyaClick = (screen: string) => {
        const gigyaScriptTag = document.getElementById('gigya')

        if (!gigyaScriptTag) return

        initializeGigya(screen)
        addGigyaEventHandlers()
    }

    const buttonVariant = props.mobile ? 'tertiary-underlined' : 'tertiary'
    const buttonSize = props.mobile ? undefined : 'sm'
    const buttonClassName = cn({ 'w-full': props.mobile })

    return (
        <div
            className={cn('flex', {
                'h-full w-full flex justify-center': props.mobile
            })}
        >
            <Button
                key="login-button"
                className={buttonClassName}
                onClick={() => handleGigyaClick('login')}
                variant={buttonVariant}
                size={buttonSize}
                color="dark"
                label={t('auth|Sign In')}
            >
                <span className={props.className}>{t('auth|Sign In')}</span>
            </Button>
            <div
                className={cn('border-l border-solid', {
                    'border-light-grey-2': props.mobile,
                    'border-mid-grey-2 h-16px max-h-16px my-auto': !props.mobile
                })}
            />
            <Button
                key="signUp-button"
                className={buttonClassName}
                onClick={() => handleGigyaClick('register')}
                variant={buttonVariant}
                size={buttonSize}
                color="dark"
                label={t('auth|Join Us')}
            >
                <span className={props.className}>{t('auth|Join Us')}</span>
            </Button>
        </div>
    )
}
