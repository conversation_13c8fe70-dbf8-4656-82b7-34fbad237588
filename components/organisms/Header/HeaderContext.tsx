import { Dispatch, useState } from 'react'
import { createContext, FC, useContext, useMemo } from 'react'

export type HeaderContextProviderProps = {
    subMenu: string
}

export type HeaderSubMenu = {
    subMenu: string
    updateSubMenu: Dispatch<string>
}

const HeaderContext = createContext<HeaderSubMenu>({
    subMenu: '',
    updateSubMenu: (subMenu: string) => {
        //
    }
})

export const HeaderContextProvider: FC<HeaderContextProviderProps> = ({
    children,
    subMenu
}) => {
    const [currentOpenSubMenu, setCurrentOpenSubMenu] = useState<string>(
        subMenu
    )

    const value = useMemo(
        () => ({
            subMenu: currentOpenSubMenu,
            updateSubMenu: setCurrentOpenSubMenu
        }),
        [currentOpenSubMenu]
    )

    return (
        <HeaderContext.Provider value={value}>
            {children}
        </HeaderContext.Provider>
    )
}

export const useHeaderSubMenu = (): HeaderSubMenu => useContext(HeaderContext)
