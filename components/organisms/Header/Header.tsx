import Elgato<PERSON>ogo from '@components/atoms/Logo/ElgatoLogo'
import { HeaderMenu } from '@components/organisms/HeaderMenu/HeaderMenu'
import { ImageType } from '@pylot-data/hooks/contentful/use-content-json'
import { useRouter } from 'next/router'
import React, { FC, useEffect, useRef, useState } from 'react'

import CandyCane from '@components/atoms/Icon/general/CandyCane'
import CartIcon from '@components/atoms/Icon/general/CartIcon'
import GingerBreadMan from '@components/atoms/Icon/general/GingerBreadMan'
import SearchIcon from '@components/atoms/Icon/general/SearchIcon'
import { Searchbar } from '@components/common'
import { useLayoutContext } from '@components/layouts/MainLayout/LayoutContext'
import { BannerProps } from '@components/molecules/Banner/Banner'
import { BrandAssets } from '@components/molecules/BrandAssets/BrandAssets'
import { GeolocationBanner } from '@components/molecules/GeolocationBanner/GeolocationBanner'
import { Link } from '@components/molecules/Link/Link'
import { PDPMessageProps } from '@components/molecules/PDPMessage/PDPMessage'
import { useHeaderSubMenu } from '@components/organisms/Header/HeaderContext'
import {
    MOBILE_NAV_MAX_WIDTH,
    getTotalNavHeight
} from '@components/organisms/Header/headerUtils'
import { LinkListProps } from '@components/organisms/LinkList/LinkList'
import MiniNav from '@components/organisms/MiniNav/MiniNav'
import { useBaseUrl } from '@config/hooks/useBaseUrl'
import { useAccount } from '@corsairitshopify/pylot-auth-manager'
import { useUI } from '@corsairitshopify/pylot-ui/context'
import { setCartIdCookie, useCart } from '@lib/cart-manager'
import { getSharedLayoutProps } from '@lib/getSharedLayoutProps'
import { useMobile } from '@pylot-data/hooks/use-mobile'
import { SimpleCartItem } from '@pylot-data/pylotschema'
import cn from 'classnames'
import { getCookie, setCookie } from 'helpers'
import { focusController } from 'helpers/AdaHelpers'
import Cookies from 'js-cookie'
import { useTranslation } from 'next-i18next'
import { REGION_CODES_MAP, REGION_EU_CODES_MAP } from '../../../localesConfig'
import s from './Header.module.scss'

export type BrandAssetsItemProp = {
    id: string
    type: string
    title: string
    dark_icon: string
    light_icon: string
    svg?: string
    url?: string
    clickedMessage: string
    label: string
    assetName: string
}

export type BrandAssetsProps = {
    items: BrandAssetsItemProp[]
}

export type MenuItemProps = {
    title?: string
    menuTitle?: string
    url?: string
    image?: ImageType
    mobileImage?: ImageType
    label?: string // e.g. "New"
    colors?: string[] // list of hex colors
    subMenuItems?: MenuItemProps[]
    menuId?: string
    newTab?: boolean
    meta?: { contentType: 'moleculeMenuItem' }
    children?: PDPMessageProps[] | any
    icon?: string
}

export interface HeaderResponse {
    miniNavigation?: LinkListProps[] // mini navigation
    marketingMessages?: BannerProps[] // mini navigation message
    mainNavigation?: MenuItemProps[] // main navigation
    brandAssets: BrandAssetsProps
}

const CONTENT_IDENTIFIER = ['globalHeader']
const CONTENT_TYPE = 'organismHeader'

type HeaderProps = {
    onMobileMenuOpen: (open: boolean) => void
    isGeolocationBannerVisible: boolean
    setIsGeolocationBannerVisible: React.Dispatch<React.SetStateAction<boolean>>
    onMininavUpdated: () => void
    mainNavigation?: MenuItemProps[]
    miniNavigation?: LinkListProps[]
    marketingMessages?: BannerProps[]
    brandAssets?: BrandAssetsProps | undefined
}

export const Header: FC<HeaderProps> = ({
    onMobileMenuOpen,
    isGeolocationBannerVisible,
    setIsGeolocationBannerVisible,
    onMininavUpdated,
    mainNavigation,
    miniNavigation,
    marketingMessages,
    brandAssets
}) => {
    const { events, locale } = useRouter()
    const {
        navHeaderColor: navColor,
        navHeaderOverlay: navOverlay,
        pageTheme
    } = useLayoutContext()
    const { t } = useTranslation(['common'])
    const { region: currentRegion } = useBaseUrl(
        typeof window !== 'undefined' ? window.location.href : ''
    )
    const [searchBarActive, setSearchBarActive] = useState(false)
    const [mobileMenuActive, setMobileMenuActive] = useState(false)
    const [cartItemsCount, setCartItemsCount] = useState('')
    const [isActiveMenu, setIsActiveMenu] = useState(false)
    const [dealsStartDate, setDealsStartDate] = useState<string>()
    const [dealsEndDate, setDealsEndDate] = useState<string>()
    const { openSidebar } = useUI()
    const { subMenu, updateSubMenu } = useHeaderSubMenu()
    const { isMobile } = useMobile()
    const { data: cartData } = useCart()
    const [clicked, setClicked] = useState(false)
    const [headerPosition, setHeaderPosition] = useState<null | number>(null)
    const account = useAccount()
    const [headerMainNavigation, setHeaderMainNavigation] = useState<
        MenuItemProps[]
    >(mainNavigation || [])
    const [headerMiniNavigation, setHeaderMiniNavigation] = useState<
        LinkListProps[]
    >(miniNavigation || [])
    const [headerMarketingMessages, setHeaderMarketingMessages] = useState<
        BannerProps[]
    >(marketingMessages || [])
    const [headerBrandAssets, setHeaderBrandAssets] = useState<
        BrandAssetsProps | undefined
    >(brandAssets)

    // if mainNavigation, miniNavigation, marketingMessages are undefined, fetch data throuth sharedLayoutProps
    useEffect(() => {
        if (
            !mainNavigation ||
            !miniNavigation ||
            !marketingMessages ||
            !brandAssets
        ) {
            getSharedLayoutProps(locale).then((sharedLayoutProps) => {
                setHeaderMainNavigation(sharedLayoutProps.mainNavigation)
                setHeaderMiniNavigation(sharedLayoutProps.miniNavigation)
                setHeaderMarketingMessages(sharedLayoutProps.marketingMessages)
                setHeaderBrandAssets(sharedLayoutProps.brandAssets)
            })
        }
    }, [locale, mainNavigation, marketingMessages, miniNavigation])

    const canBeClosedRef = useRef(true)
    useEffect(() => {
        const handleResize = () => {
            setMobileMenuActive(false)
            if (window && window.innerWidth > MOBILE_NAV_MAX_WIDTH) {
                document.body.classList.remove('noscroll')
            }
        }
        window.addEventListener('resize', handleResize)
        handleResize()
        return () => window.removeEventListener('resize', handleResize)
    }, [])

    useEffect(() => {
        const handleRouteChangeComplete = () => {
            setMobileMenuActive(false)
            document.body.classList.remove('noscroll')
        }
        events.on('routeChangeComplete', handleRouteChangeComplete)
        return () => {
            events.off('routeChangeComplete', handleRouteChangeComplete)
        }
    }, [events, mobileMenuActive])

    useEffect(() => {
        if (
            cartData &&
            cartData.data &&
            cartData.data.cart &&
            cartData.data.cart.items
        ) {
            const statusCheckoutSuccess = getCookie('checkout-success')
            const regionCheckoutSuccess = getCookie('region-success')
            const keyData = `cartId_${currentRegion}`
            const currentBackupData = localStorage.getItem(keyData)
            if (statusCheckoutSuccess === 'true') {
                if (currentBackupData) {
                    const parseData = JSON.parse(currentBackupData)
                    if (regionCheckoutSuccess === currentRegion) {
                        const regionCartId = {
                            cartId: cartData?.data.cart.id,
                            locale: currentRegion
                        }
                        localStorage.setItem('cart_id', cartData?.data.cart.id)
                        localStorage.setItem(
                            keyData,
                            JSON.stringify(regionCartId)
                        )
                        setCookie('checkout-success', 'false')
                        setCookie('region-success', '')
                        setCookie(keyData, JSON.stringify(regionCartId))
                    } else if (regionCheckoutSuccess !== currentRegion) {
                        localStorage.setItem('cart_id', parseData?.cartId)
                        const regionCartId = {
                            cartId: parseData?.cartId,
                            locale: currentRegion
                        }
                        localStorage.setItem(
                            keyData,
                            JSON.stringify(regionCartId)
                        )
                        setCartIdCookie(parseData?.cartId)
                        setCookie('checkout-success', 'false')
                        setCookie('region-success', '')
                        setCookie(keyData, JSON.stringify(regionCartId))
                    }
                }
            }
            const cartItemsCount = cartData?.data?.cart?.items?.reduce(
                (itemCount, item) => {
                    if (item) {
                        return itemCount + (item as SimpleCartItem)?.quantity
                    }
                    return 0
                },
                0
            )
            const cartItemsCountString =
                cartItemsCount > 9 ? '9+' : cartItemsCount.toString()
            setCartItemsCount(cartItemsCountString)

            const productUrls = cartData?.data?.cart?.items?.map((item) => {
                const sku = item?.product.sku || ''
                const url_key = item?.product.url_key || ''
                const productUrl = `${location.pathname}/p/${url_key}`
                return {
                    sku,
                    productUrl
                }
            })
            setCookie('productUrls', JSON.stringify(productUrls))
        }
    }, [cartData])

    const currentRegionCode = locale?.substring(3)
    useEffect(() => {
        const userCountryCodeCookie = Cookies.get('Country_Code')
        const userRegionFromCountryCodeCookie =
            userCountryCodeCookie === 'GB' ? 'UK' : userCountryCodeCookie
        const isRegionValid =
            !!userRegionFromCountryCodeCookie &&
            (Object.values(REGION_CODES_MAP).includes(
                userRegionFromCountryCodeCookie
            ) ||
                Object.values(REGION_EU_CODES_MAP).includes(
                    userRegionFromCountryCodeCookie
                ))
        const geoLocationBannerCookie = Cookies.get('GEOLOCATION_BANNER_HIDE')
        if (
            userRegionFromCountryCodeCookie &&
            isRegionValid &&
            currentRegionCode !== userRegionFromCountryCodeCookie &&
            geoLocationBannerCookie !== '1'
        ) {
            setIsGeolocationBannerVisible(true)
        }
    }, [currentRegionCode])
    useEffect(() => {
        setHeaderPosition(getTotalNavHeight())
        const resizeHandler = () => {
            if (document) {
                setHeaderPosition(getTotalNavHeight())
            }
        }
        window?.addEventListener('resize', resizeHandler)
        return () => window?.removeEventListener('resize', resizeHandler)
    }, [isGeolocationBannerVisible])

    // Add event listener for deals dates
    useEffect(() => {
        const handleUpdateDates = (event: any) => {
            setDealsStartDate(event.detail.dealsStartDate)
            setDealsEndDate(event.detail.dealsEndDate)
        }

        window.addEventListener('updateHeaderDates', handleUpdateDates)
        return () => {
            window.removeEventListener('updateHeaderDates', handleUpdateDates)
        }
    }, [])

    const openSearch = () => {
        if (setSearchBarActive) setSearchBarActive((prev) => !prev)
    }

    const openCart = () => {
        openSidebar()
    }

    const openMobileMenu = () => {
        if (mobileMenuActive) {
            document.body.classList.remove('noscroll')
            updateSubMenu('')
        } else {
            document.body.classList.add('noscroll')
            Promise.resolve().then(() => {
                focusController(`#mobile-menu-button`, `#header-menu-wrapper`)
            })
        }
        setMobileMenuActive(!mobileMenuActive)
        onMobileMenuOpen(!mobileMenuActive)
    }
    const cartBtnKeypess = (e: React.KeyboardEvent<HTMLButtonElement>) => {
        e.preventDefault()
        if (e.key === ' ' || e.key === 'Enter') {
            openCart()
            Promise.resolve(true).then(() => {
                const sidebarContainer = document.querySelector(
                    '.sidebar-container'
                ) as HTMLButtonElement
                sidebarContainer?.focus()
                sidebarContainer?.addEventListener('focusout', () => {
                    const cartCloseBtn = document.querySelector(
                        '.cartSidebar-close-btn'
                    )
                    cartCloseBtn?.addEventListener('focus', () => {
                        focusController(
                            '.header__cart-btn-open',
                            '.cartSideBar-root',
                            '.cartSidebar-close-btn',
                            ['select', 'button']
                        )
                    })
                })
            })
        }
    }

    const loggedIn = !!account.data?.data?.customer
    const showGingerBreadMan = false
    const showCandyCane = false
    return (
        <header>
            <MiniNav
                marketingMessages={headerMarketingMessages}
                miniNavigation={headerMiniNavigation}
                loggedIn={loggedIn}
            />
            <GeolocationBanner
                isVisible={isGeolocationBannerVisible}
                setIsVisible={setIsGeolocationBannerVisible}
            />
            <div
                className={cn(
                    s['header'],
                    {
                        [s[`header--landing--${navColor}`]]: navOverlay,
                        [s['header--landing']]: navOverlay,
                        [s[
                            'header--geolocation-banner'
                        ]]: isGeolocationBannerVisible,
                        [s['header--sub-menu']]:
                            mobileMenuActive || subMenu || isActiveMenu,
                        [s[`page-theme-${pageTheme}`]]: pageTheme,
                        [s[`page-theme-${pageTheme}-overlay`]]:
                            pageTheme && navOverlay
                    },
                    'main-header-wrapper'
                )}
                style={{
                    top: headerPosition ?? undefined
                }}
            >
                <div className={s['header__inner-wrapper']}>
                    <div className={s['header__inner']}>
                        {showGingerBreadMan && (
                            <GingerBreadMan
                                style={{
                                    position: 'absolute',
                                    zIndex: 100,
                                    left: isMobile ? '50%' : '15%',
                                    transform: isMobile
                                        ? 'translateX(-50%)'
                                        : 'translateX(-15%)',
                                    bottom: '0'
                                }}
                                className={s['snow']}
                            />
                        )}
                        {showCandyCane && (
                            <CandyCane
                                style={{
                                    position: 'absolute',
                                    zIndex: 101,
                                    left: 190,
                                    bottom: 0,
                                    display: isMobile ? 'none' : 'block'
                                }}
                                fill={
                                    pageTheme === 'dark'
                                        ? 'var(--primitive-red-80)'
                                        : undefined
                                }
                            />
                        )}
                        <div className={s['header__logo']}>
                            <a
                                href="/"
                                aria-label={t('ada|Opens in the current Tab')}
                                className="header__logo-link"
                                // onTouchStart={(e) => {
                                //     e.preventDefault()
                                //     setClicked(!clicked)
                                // }}
                                onContextMenu={(e) => {
                                    e.preventDefault()
                                    setClicked(!clicked)
                                }}
                                onKeyDown={(e) => {
                                    if (e.shiftKey && e.key === 'F10') {
                                        e.preventDefault()
                                        setClicked(!clicked)
                                        Promise.resolve().then(() => {
                                            focusController(
                                                '.header__logo-link',
                                                '.brand-assets-wrapper',
                                                '.brand-assets-close'
                                            )
                                        })
                                    }
                                }}
                            >
                                <ElgatoLogo />
                            </a>
                        </div>
                        <div className={s['header__buttons']}>
                            <button
                                type="button"
                                onClick={openSearch}
                                className={s['header__search-btn']}
                                aria-label={t('ada|Search')}
                            >
                                <SearchIcon width="24px" height="24px" />
                            </button>
                            {headerMiniNavigation &&
                                headerMiniNavigation[1] &&
                                headerMiniNavigation[1].linkListItems &&
                                headerMiniNavigation[1].linkListItems.length >
                                    0 && (
                                    <ul className="flex gap-24px items-center">
                                        {headerMiniNavigation[1].linkListItems.map(
                                            (footerNavItem, i) => (
                                                <li
                                                    key={`header-menu-footer-menu-${footerNavItem.linkTitle}-${i}`}
                                                >
                                                    <Link
                                                        link={footerNavItem}
                                                        className={cn(
                                                            'button-text flex flex-col justify-center md-max:hidden',
                                                            s['header__link']
                                                        )}
                                                        aria-label={`${
                                                            footerNavItem.linkTitle
                                                        } - ${
                                                            footerNavItem.newTab
                                                                ? t(
                                                                      'ada|Opens in a new Tab'
                                                                  )
                                                                : t(
                                                                      'ada|Opens in the current Tab'
                                                                  )
                                                        }`}
                                                    />
                                                </li>
                                            )
                                        )}
                                    </ul>
                                )}
                            <button
                                type="button"
                                onClick={openCart}
                                className={cn(
                                    s['header__cart-btn'],
                                    'header__cart-btn-open'
                                )}
                                onKeyPress={cartBtnKeypess}
                                aria-haspopup="dialog"
                                aria-controls="cart-sidebar-root"
                            >
                                <CartIcon width="24px" height="24px" />
                                {cartItemsCount && cartItemsCount !== '0' && (
                                    <div
                                        className={cn(
                                            s['header__cart-amount'],
                                            'xs-copy'
                                        )}
                                    >
                                        {cartItemsCount}
                                    </div>
                                )}
                            </button>
                            <button
                                aria-controls="mobile-menu"
                                aria-expanded={mobileMenuActive}
                                type="button"
                                id="mobile-menu-button"
                                aria-haspopup
                                onClick={openMobileMenu}
                                className={cn(s['header__menu-btn'], {
                                    [s[
                                        'header__menu-btn--active'
                                    ]]: mobileMenuActive
                                })}
                                aria-label={t('ada|Menu')}
                            >
                                <div
                                    className={s['header__menu-btn-inner']}
                                    id="mobile-menu"
                                >
                                    <span />
                                    <span />
                                    <span />
                                </div>
                            </button>
                        </div>
                        {/* eslint-disable-next-line jsx-a11y/no-static-element-interactions */}
                        <div
                            className={s['header__menu']}
                            onMouseEnter={() => setIsActiveMenu(true)}
                            onMouseLeave={() => setIsActiveMenu(false)}
                            onKeyPress={() => setIsActiveMenu(!isActiveMenu)}
                            id="header-menu-wrapper"
                            aria-labelledby="mobile-menu-button"
                        >
                            {searchBarActive ? (
                                <Searchbar
                                    setSearchBarActive={setSearchBarActive}
                                />
                            ) : (
                                <HeaderMenu
                                    mainNavigation={headerMainNavigation}
                                    mobileMenuActive={mobileMenuActive}
                                    active={subMenu !== ''}
                                    light={
                                        navOverlay &&
                                        navColor === 'light' &&
                                        !mobileMenuActive &&
                                        !subMenu &&
                                        !isActiveMenu
                                    }
                                    loggedIn={loggedIn}
                                    mobileFooterNavigation={
                                        headerMiniNavigation &&
                                        headerMiniNavigation[1]
                                            ? headerMiniNavigation[1]
                                                  .linkListItems
                                            : undefined
                                    }
                                    canBeClosedRef={canBeClosedRef}
                                    closeMenu={() =>
                                        setMobileMenuActive(!mobileMenuActive)
                                    }
                                />
                            )}
                        </div>
                    </div>
                </div>
                {clicked && headerBrandAssets && (
                    <div>
                        <BrandAssets
                            clicked={clicked}
                            setClicked={setClicked}
                            brandAssets={headerBrandAssets}
                            pageTheme={pageTheme}
                        />
                    </div>
                )}
            </div>
        </header>
    )
}

export default Header
