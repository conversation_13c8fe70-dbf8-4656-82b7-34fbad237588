import s from './ProductConfiguration.module.scss'
import Image from '@corsairitshopify/corsair-image'
import { useTranslation } from 'next-i18next'
import { ProductOverlayProps } from '@components/organisms/ProductOverlay/ProductOverlay'
import { FC } from 'react'

type ProductConfigurationProps = Pick<
    ProductOverlayProps,
    'relatedProductsToAddInCart' | 'productImage'
>

export const ProductConfiguration: FC<ProductConfigurationProps> = ({
    relatedProductsToAddInCart,
    productImage
}) => {
    const { t } = useTranslation(['common', 'pdp'])

    return (
        <div className={s['product-configuration']}>
            <div className="flex flex-col gap-8px">
                <h4>{t('YOUR SETUP')}</h4>
            </div>
            {!!relatedProductsToAddInCart.length && (
                <div className="flex flex-col gap-8px">
                    <h6>{t('SELECTED PRODUCTS')}:</h6>
                    <div className="flex gap-8px">
                        {relatedProductsToAddInCart.map((product) => {
                            if (product.image?.url) {
                                return (
                                    <Image
                                        className="rounded-lg"
                                        src={product.image.url}
                                        width={58}
                                        height={58}
                                        objectFit="cover"
                                        alt={product.name || ''}
                                    />
                                )
                            }
                        })}
                    </div>
                </div>
            )}
            {productImage && productImage.url && (
                <div className={s['product-configuration__image']}>
                    <Image
                        src={productImage.url}
                        layout="fill"
                        objectFit="cover"
                        alt=""
                    />
                </div>
            )}
        </div>
    )
}
