import React, { FC } from 'react'
import { CardText } from '@components/molecules/CardText/CardText'
import { LinkResponse } from '@components/molecules/Link/Link'
import { ImageType } from '@pylot-data/hooks/contentful/use-content-json'
import Image from '@corsairitshopify/corsair-image'
import classNames from 'classnames'
import { CloudinaryMedia } from '@components/common/CloudinaryMedia/Cloudinary'
import ElgatoImage from '@components/common/ElgatoImage'
import s from './ProductTile.module.scss'
import cn from 'classnames'
export interface ProductTileProps {
    title?: string
    text?: string
    link?: LinkResponse
    image?: CloudinaryMedia[]
    appStoreLink?: LinkResponse
    additionalLink?: LinkResponse
    className?: string
    logo?: string
}

export const ProductTile: FC<ProductTileProps> = (props) => {
    const {
        title,
        text,
        link,
        image,
        additionalLink,
        appStoreLink,
        className,
        logo
    } = props
    return (
        <div
            className={classNames(
                'flex flex-col gap-16px py-32px px-16px bg-bg-grey rounded-xxl text-center items-center md:flex-row md:items-start md:text-left md:pr-32px',
                className
            )}
        >
            <div
                className={cn(
                    s['product-tile'],
                    'w-52 h-44 relative flex-shrink-0 flex-grow-0'
                )}
            >
                {image && (
                    <ElgatoImage
                        src={image?.[0]?.secure_url}
                        alt={image?.[0]?.context?.custom?.alt}
                        layout="fill"
                    />
                )}
            </div>
            <CardText
                title={title}
                text={text}
                link={link}
                additionalLink={additionalLink}
                appStoreLink={appStoreLink}
                logo={logo}
            />
        </div>
    )
}

export default ProductTile
