.info-card {
    @apply flex relative flex-col;

    &--light {
        @apply text-white;
    }
    
    &--dark {
        @apply text-black;
    }

    &__fullwidth-video {
        height: 72vh;

        @media screen and (max-width: 1199px) {
            height: 100%;
        }

        @media screen and (min-width: 1199px) and (max-width: 1366px) {
            height: 63vh;
        }

        @media screen and (min-width: 1740px) and (max-width: 2869px) {
            height: 79vh;
        }

        @media screen and (min-width: 2870px) {
            height: 89vh;
        }
    }

    &__image {
        @apply relative block;

        .info-card__video {
            @apply absolute top-0 left-0 w-full;
        }
    }

    &__image-wrapper {
        @apply relative;
        @apply rounded-xxl overflow-hidden;

        .info-card__video-play-btn {
            z-index: 2;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            @apply transition-opacity duration-300;
        }
    }

    &__box {
        width: 100%;
        height: auto;
        opacity: 0;
        transition: 0.5s ease-out;
        flex: 1 0 auto;

        &-text {
            @apply pt-0;

            &--with-link {
                height: calc(
                    100% - 32px
                ); /* remove the padding form height, otherwise it's cut off */
            }
        }
    }

    &__icon {
        @apply bg-content-blue text-white inline-flex justify-center items-center absolute top-0 left-0;
        border-radius: 8.89014px;
        width: 60px;
        height: 60px;
    }

    &--active {
        .info-card__box {
            opacity: 1;
        }
    }

    &:not(.info-card--default, .info-card--view-toggle-content) {
        // Design ask to remove the margin-top for the info card and keep the padding top of 16px
        // .info-card__box {
        //     @apply mt-16px;
        // }
        @screen md-max {
            padding: 0;
        }
    }

    &--text-light {
        .info-card__box {
            @apply text-white;
        }
    }

    &--text-light {
        @apply text-white;
    }

    &--text-dark {
        @apply text-black;
    }

    &--sound {
        .info-card__image-wrapper {
            @apply bg-charcoal;

            @screen md {
                padding: 28px;
            }
        }
    }

    &--view-toggle-content {
        @screen md-max{
            @apply px-16px;
        }
        &.info-card--infobox {
            .info-card__image-wrapper {
                @apply rounded-t-none;
            }
        }

        .info-card__box {
            @apply bg-white rounded-t-xxl;
        }
    }

    /* Inline Video */
    &__video-wrapper {
        position: initial !important;
        opacity: 0;
        pointer-events: none;
        @apply transition-opacity duration-300;
    }

    &__video {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;

        video {
            height: 100%;
            width: 100%;
            object-fit: cover;
        }
    }

    &--video-active {
        .info-card__video-wrapper {
            pointer-events: auto;
            opacity: 1;
            z-index: 3;
        }
        .info-card__video-play-btn {
            opacity: 0;
            z-index: -1;
        }
    }

    &__embed {
        :global {
            iframe {
                @apply w-full h-full;
            }
        }
    }

    /* Hotspots */
    &__hotspots {
        @apply absolute top-0 left-0 w-full h-full;
    }

    /* Text Block */
    &__text-block {
        height: calc(100% - 32px);
    }

    &--full-width {
        @apply flex flex-col gap-16px h-full p-16px lg2:gap-0 lg2:p-0 md-max:pb-0;
    }
    &__full-image {
        @apply h-full w-full;
    }

    &__text-wrapper {
        @apply w-full relative rounded-xxl lg2:absolute lg2:left-0 lg2:right-0 lg2:m-auto lg2:z-1;
        max-width: 100%;
        min-height: 115px;
        @screen lg2 {
            bottom: 3%;
            margin: 0 3%;
            width: auto;
        }
        @media screen and (min-width: 1300px) {
            margin: 0 auto;
            max-width: 810px;
        }
        @screen 9xl {
            margin: 0 auto;
            max-width: 1224px;
        }
        @screen md-max {
            @apply p-24px;
            min-height: 150px;
        }
        &-mobile {
            @screen md-max {
                padding: 0 !important;
                min-height: 100%;
            }
        }
        // TODO: Need to check why this was added
        &--text-default {
            @apply bg-white;
        }
        // ...........................................
       
    }

    &__media-wrapper {
        height: 100%;
        border-radius: 16px !important;
        @screen md {
            height: 500px;
        }
        @screen lg2 {
            border-radius: 24px !important;
            height: 100%;
            > div > div {
                aspect-ratio: 1/1 !important;
            }
        }
    }
}
