import React, { FC } from 'react'
import s from './ButtonWithMedia.module.scss'
import cn from 'classnames'
import Image from 'next/image'
import Apple from '@components/icons/Apple'
import Windows from '@components/icons/Windows'
import Android from '@components/icons/Android'

interface ButtonWithMediaProps {
    title?: string
    value?: string | undefined
    media?: any
    icon?: any
    handleButtonClick: (value: string) => void
    children?: any
    isBigConfigurator?: boolean
    linkUrl?: string
}

const ButtonWithMedia: FC<ButtonWithMediaProps> = ({
    title,
    value = '',
    handleButtonClick,
    media,
    isBigConfigurator,
    icon,
    linkUrl
}) => {
    const isLink = !!linkUrl
    const Component = isLink ? 'a' : 'button'
    return (
        <Component
            className={cn(
                s['button-with-media'],
                isBigConfigurator && s['button-with-media-big']
            )}
            role={!isLink ? 'button' : undefined}
            tabIndex={0}
            onClick={
                !isLink
                    ? () => {
                          handleButtonClick(value)
                      }
                    : undefined
            }
            onKeyPress={
                !isLink
                    ? () => {
                          handleButtonClick(value)
                      }
                    : undefined
            }
            href={linkUrl}
            target={linkUrl ? '_blank' : undefined}
        >
            {media && (
                <div className={cn(s['button-with-media__image'])}>
                    <Image
                        src={String(media.file.url)}
                        alt={media?.description || ''}
                        layout="fill"
                        objectFit="cover"
                    />
                </div>
            )}
            <div className={cn(s['button-with-media__button-wrapper'])}>
                <div
                    className={cn(
                        s['button-with-media__button-wrapper__button']
                    )}
                >
                    {icon && icon == 'Apple' && (
                        <div
                            className={cn(
                                s[
                                    'button-with-media__button-wrapper__button__icon'
                                ]
                            )}
                        >
                            <Apple />
                        </div>
                    )}
                    {icon && icon == 'Windows' && (
                        <div
                            className={
                                s[
                                    'button-with-media__button-wrapper__button__icon'
                                ]
                            }
                        >
                            <Windows />
                        </div>
                    )}
                    {icon && icon == 'Android' && (
                        <div
                            className={
                                s[
                                    'button-with-media__button-wrapper__button__icon'
                                ]
                            }
                        >
                            <Android />
                        </div>
                    )}
                    {title}
                </div>
            </div>
        </Component>
    )
}

export default ButtonWithMedia
