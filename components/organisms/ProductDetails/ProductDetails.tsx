import s from './ProductDetails.module.scss'
import Image from '@corsairitshopify/corsair-image'
import React, { FC, useMemo } from 'react'
import { ProductInterface } from '@pylot-data/fwrdschema'
import unescape from 'lodash.unescape'
import { PriceDiscount } from '@components/molecules/PriceDiscount/PriceDiscount'
import { usePrice } from '@corsairitshopify/pylot-price/index'
import { useProductUrlBuilder } from '@lib/hooks/useBuildProductUrl'
import { ModelTypeEnum } from '@config/base'
import { useTranslation } from 'next-i18next'
import { Button } from '@components/molecules/Button/Button'
import ChevronRightIcon from '@components/atoms/Icon/general/ChevronRightIcon'
import { ProductAddToCart } from '@components/molecules/ProductAddToCart/ProductAddToCart'
import {
    ButtonLabel,
    useProductUI
} from '@pylot-data/hooks/product/use-product-ui'
import cn from 'classnames'

type ProductDetailsProps = {
    product: ProductInterface
    addedToCartHandler?: () => void
    cmsATCLocation?: number
    isPopup?: boolean
}

export const ProductDetails: FC<ProductDetailsProps> = ({
    product,
    cmsATCLocation = 1,
    isPopup = false,
    addedToCartHandler
}) => {
    const { t } = useTranslation(['common'])
    const { subtotal, total } = usePrice(product.price_range)
    const productUrlBuilder = useProductUrlBuilder({
        page: ModelTypeEnum.PRODUCT
    })
    const { isOutOfStock } = useProductUI(product)
    const { not_sellable, bundle_products } = product
    const isSellable = useMemo(() => {
        return !not_sellable && !bundle_products?.some((p) => p?.not_sellable)
    }, [not_sellable, bundle_products])
    const productImage = useMemo(() => {
        if (product.media_gallery) {
            const foundPopupImage = product.media_gallery.find((img) => {
                return img?.type === 'popup'
            })
            if (foundPopupImage && foundPopupImage.url) {
                return foundPopupImage.url
            }
        }
        return product?.image?.url
    }, [product])

    return (
        <div className={cn(s['product-details'], 'product-details-contents')}>
            <h4>{product.name}</h4>
            {product.description && product.description.html && (
                <div
                    dangerouslySetInnerHTML={{
                        __html: unescape(product.description.html.toString())
                    }}
                    className="rich-text text-small-copy"
                />
            )}
            <div className="flex flex-wrap justify-between gap-16px">
                <PriceDiscount
                    price={total}
                    priceDiscount={subtotal !== total ? subtotal : undefined}
                    // eslint-disable-next-line i18next/no-literal-string
                    size="big"
                    totalValue={Number(
                        product?.price_range?.minimum_price?.final_price?.value
                    )}
                    subtotalValue={Number(
                        product?.price_range?.minimum_price?.regular_price
                            ?.value
                    )}
                />
                <div className="flex w-full gap-16px md:w-auto">
                    <Button
                        variant="tertiary-underlined"
                        href={productUrlBuilder({
                            product: product,
                            url_key: product.url_key
                        })}
                        label={t('cart|Discover')}
                    >
                        {t('cart|Discover')}
                        <ChevronRightIcon />
                    </Button>
                    {isSellable ? (
                        <ProductAddToCart
                            id={`product-details-atc-btn-${product.uid}`}
                            addedToCartHandler={addedToCartHandler}
                            // eslint-disable-next-line i18next/no-literal-string
                            buttonVariant="primary"
                            product={product}
                            cmsATCLocation={cmsATCLocation}
                            buttonLabel={
                                isOutOfStock
                                    ? t('Out of Stock')
                                    : t(`cart|${ButtonLabel.ADD_TO_CART}`)
                            }
                            isPopup={isPopup}
                            secondaryFocusElementId={`related-product-tile-${product.uid}`}
                        />
                    ) : null}
                </div>
            </div>
            {productImage && (
                <div className={s['product-details__image']}>
                    <Image
                        src={productImage}
                        layout="fill"
                        objectFit="cover"
                        alt={product?.name || ''}
                    />
                </div>
            )}
        </div>
    )
}
