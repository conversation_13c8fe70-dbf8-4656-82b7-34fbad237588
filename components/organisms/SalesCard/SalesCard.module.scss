.sales-card {
    position: relative;
    width: 100%;
    border-radius: 12px;
    overflow: hidden;
    //background-color: var(--primitive-black);
    box-shadow: 0 0 16px rgba(0, 0, 0, 0.08);

    @screen md {
        max-width: 314px;
    }

    @screen xl {
        max-width: 386px;
    }
    &__banner-eol {
        position: absolute;
        top: 24px;
        left: 24px;
        width: fit-content;
        height: fit-content;
        z-index: 1;
    }

    /* default */
    &__media-wrapper {
        @apply relative w-full;
        padding-bottom: calc((474 / 386) * 100%);
        border-radius: 12px;
        overflow: hidden;

        .sales-card__video {
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            width: 100%;

            video {
                object-fit: cover;
                height: 100%;
                border-radius: 12px;
            }
        }
    }

    &__media-absolute {
        @apply absolute top-0 left-0 w-full h-full;
    }

    &--store-prototype {
        @screen md {
            min-height: 485px;
        }

        @screen xl {
            min-height: 430px;
        }

        @screen xxl {
            min-height: 450px;
        }
    }

    &__banner {
        position: absolute;
        top: 8px;
        left: 8px;
        z-index: 1;
    }

    &__banner-hgg {
        position: absolute;
        top: 30px;
        left: 28px;
        padding: 6px 16px;
        z-index: 1;
        background: linear-gradient(270deg, #fede1e 0%, #f09efb 100%);
    }

    &__banner-hgg-icon {
        svg {
            max-height: 24px;
        }
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 4px;
    }

    &--small {
        .sales-card {
            &__media-wrapper {
                padding-bottom: calc((300 / 386) * 100%);
            }
        }
    }

    &--product {
        .sales-card {
            &__media-wrapper {
                padding-bottom: calc((467 / 314) * 100%);

                @screen md {
                    padding-bottom: calc((430 / 314) * 100%);
                }

                @screen xl {
                    padding-bottom: calc((556 / 436) * 100%);
                }
            }
        }
    }

    /* no longer used */
    &--overlay {
        height: 100%;

        .sales-card__text {
            position: absolute;
            top: auto;
            bottom: 0;
            left: 0;
            width: 100%;
            margin: 16px;
            max-height: calc(100% - 32px);
            overflow: hidden;
            max-width: calc(100% - 32px);
        }

        .sales-card__media-wrapper {
            height: 100%;
            padding-bottom: calc((670 / 314) * 100%);

            @screen md {
                padding-bottom: calc((851 / 436) * 100%);
            }
        }
    }

    /* tall (e.g. auto-scroll-cards) */
    &--tall {
        height: 100%;
        --ratio: 686 / 386;

        .sales-card__media-wrapper {
            height: 100%;
            padding-bottom: calc((var(--ratio)) * 100%);

            @screen md {
                padding-bottom: calc((var(--ratio)) * 100%);
            }
        }
    }
}
