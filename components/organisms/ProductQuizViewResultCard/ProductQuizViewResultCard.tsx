import Elgato from '@components/icons/Elgato'
import { BREAKPOINT_MOBILE_MAX } from '@components/layouts/MainLayout/breakpoints'
import { Button } from '@components/molecules/Button/Button'
import { PriceDiscount } from '@components/molecules/PriceDiscount/PriceDiscount'
import { ProductAddToCart } from '@components/molecules/ProductAddToCart/ProductAddToCart'
import Video from '@components/molecules/Video/Video'
import { usePrice } from '@corsairitshopify/pylot-price'
import getProduct from '@pylot-data/api/operations/get-product'
import { useProductUI } from '@pylot-data/hooks/product/use-product-ui'
import { ConfigurableProduct, SimpleProduct } from '@pylot-data/pylotschema'
import cn from 'classnames'
import { useTranslation } from 'next-i18next'
import Image from 'next/image'
import { useRouter } from 'next/router'
import { FC, useCallback, useEffect, useRef, useState } from 'react'
import s from './ProductQuizViewResultCard.module.scss'

import {
    CaptionFileType,
    DescriptionFileType,
    ImageType,
    VideoType
} from '@pylot-data/hooks/contentful/use-content-json'

type ProductState = SimpleProduct | ConfigurableProduct | null

interface ProductQuizViewResultCardProps {
    index?: number
    image?: ImageType
    video?: VideoType
    title?: string
    productTitle?: string
    productSubtitle?: string
    posterImage?: ImageType
    sku?: string
    variant?: string
    videoCaptionFile?: CaptionFileType
    videoDescriptionFile?: DescriptionFileType
}

const ProductQuizViewResultCard: FC<ProductQuizViewResultCardProps> = ({
    index,
    image,
    video,
    title,
    productTitle,
    productSubtitle,
    sku,
    variant,
    posterImage,
    videoCaptionFile,
    videoDescriptionFile
}) => {
    const { t } = useTranslation(['common'])

    const [productData, setProductData] = useState<ProductState>(null)
    const { locale } = useRouter()
    const { total: productPrice, subtotal, total } = usePrice(
        productData?.price_range
    )
    const [isMobile, setIsMobile] = useState(false)

    const { isOutOfStock } = useProductUI(productData, {
        preselectedOptions: {}
    })
    const urlKeyRef = useRef('')

    useEffect(() => {
        const handleResize = () => {
            setIsMobile(window.innerWidth <= BREAKPOINT_MOBILE_MAX)
        }

        handleResize()

        window.addEventListener('resize', handleResize)

        return () => {
            window.removeEventListener('resize', handleResize)
        }
    }, [])

    const fetchProduct = useCallback(
        async (sku: string) => {
            if (sku) {
                const productData = await getProduct({
                    sku: sku,
                    url_key: urlKeyRef.current,
                    locale: locale ? locale : ''
                })
                if (productData?.productDetail?.items[0]?.sku) {
                    // Ensure that the fetched data matches the expected type
                    setProductData(
                        productData.productDetail.items[0] as ProductState
                    )
                }
                if (productData?.productDetail?.items[0]?.url_key) {
                    urlKeyRef.current =
                        productData?.productDetail?.items[0]?.url_key
                }
            }
        },
        [locale]
    )

    useEffect(() => {
        if (sku != null) {
            void fetchProduct(sku)
        }
    }, [fetchProduct, sku])

    return (
        <div
            className={cn(
                s['product-quiz-view-result-card'],
                variant == '1/2' && s['product-quiz-view-result-card_small']
            )}
        >
            <h2 className={cn(s['product-quiz-view-result-card__title'])}>
                {title}
            </h2>
            <div
                className={cn(
                    s['product-quiz-view-result-card-wrapper'],
                    'flex'
                )}
            >
                <div
                    className={cn(
                        s['product-quiz-view-result-card-wrapper__image'],
                        'w-1/2',
                        image && variant == '1/2' && !isMobile && 'mb-6',
                        image && isMobile && 'mb-6'
                    )}
                >
                    {image && (
                        <Image
                            src={String(image.file.url)}
                            alt={image?.description || ''}
                            layout="fill"
                            objectFit="cover"
                        />
                    )}
                    {video && (
                        <Video
                            className={
                                s[
                                    'product-quiz-view-result-card-wrapper__video'
                                ]
                            }
                            video={video}
                            options={{
                                autoPlay: false,
                                controls: true,
                                preload: 'auto',
                                muted: true,
                                loop: true
                            }}
                            fallbackImgUrl={posterImage?.file.url}
                            caption={videoCaptionFile}
                            descriptionFile={videoDescriptionFile}
                        />
                    )}
                </div>
                {image && (
                    <div
                        className={cn(
                            s['product-quiz-view-result-card-wrapper__text'],
                            'w-1/2 flex flex-wrap'
                        )}
                    >
                        <div>
                            <div
                                className={cn(
                                    s[
                                        'product-quiz-view-result-card-wrapper__product-title'
                                    ]
                                )}
                            >
                                <div className="flex">
                                    <div
                                        className={cn(
                                            s[
                                                'product-quiz-view-result-card-wrapper__product-title_icon'
                                            ]
                                        )}
                                    >
                                        <Elgato />
                                    </div>

                                    <h3
                                        className={cn(
                                            s[
                                                'product-quiz-view-result-card-wrapper__product-title_text'
                                            ]
                                        )}
                                    >
                                        {productTitle}
                                    </h3>
                                </div>
                                <div
                                    className={cn(
                                        s[
                                            'product-quiz-view-result-card-wrapper__price'
                                        ],
                                        'md:hidden block'
                                    )}
                                    // eslint-disable-next-line i18next/no-literal-string
                                >
                                    <div className="flex align-center">
                                        <div className="mr-3">
                                            <PriceDiscount
                                                subtotalValue={Number(
                                                    productData?.price_range
                                                        ?.minimum_price
                                                        ?.regular_price?.value
                                                )}
                                                priceDiscount={
                                                    subtotal !== total
                                                        ? subtotal
                                                        : undefined
                                                }
                                                variant="inline"
                                                // eslint-disable-next-line i18next/no-literal-string
                                                theme="light"
                                                // eslint-disable-next-line i18next/no-literal-string
                                                size="big"
                                                className={
                                                    s[
                                                        'configurator-add-to-cart__total'
                                                    ]
                                                }
                                            />
                                        </div>
                                        <PriceDiscount
                                            price={productPrice}
                                            variant="inline"
                                            // eslint-disable-next-line i18next/no-literal-string
                                            theme="light"
                                            // eslint-disable-next-line i18next/no-literal-string
                                            size="medium"
                                            className={
                                                s[
                                                    'configurator-add-to-cart__total'
                                                ]
                                            }
                                        />
                                    </div>
                                </div>
                            </div>
                            <div
                                className={cn(
                                    s[
                                        'product-quiz-view-result-card-wrapper__product-subtitle'
                                    ]
                                )}
                                dangerouslySetInnerHTML={{
                                    __html: productSubtitle
                                        ? unescape(
                                              productSubtitle.replace(
                                                  /\n/g,
                                                  '</br>'
                                              )
                                          )
                                        : ''
                                }}
                            />
                        </div>

                        <div
                            className={cn(
                                s[
                                    'product-quiz-view-result-card-wrapper__button-wrapper'
                                ],
                                variant == '1/2' && s['small']
                            )}
                        >
                            <div className="flex items-center">
                                <div className="flex gap-8px items-center flex-col xs:flex-row flex-wrap">
                                    {productData && urlKeyRef.current && (
                                        <Button
                                            variant="secondary"
                                            // eslint-disable-next-line i18next/no-literal-string
                                            color="light"
                                            // eslint-disable-next-line i18next/no-literal-string
                                            href={`/p/${urlKeyRef.current}`}
                                            label={t('Learn More')}
                                        >
                                            {t('Learn More')}
                                        </Button>
                                    )}
                                    {productData && (
                                        <ProductAddToCart
                                            id={`product-quiz-view-atc-btn-${productData.uid}`}
                                            className={
                                                s['product-card__buy-btn']
                                            }
                                            product={productData}
                                            // eslint-disable-next-line i18next/no-literal-string
                                            buttonVariant="primary"
                                            // eslint-disable-next-line i18next/no-literal-string
                                            buttonColor="light"
                                            buttonLabel={
                                                isOutOfStock
                                                    ? t('Out of Stock')
                                                    : t('Add to Cart')
                                            }
                                        />
                                    )}
                                </div>
                                <div
                                    className={cn(
                                        s[
                                            'product-quiz-view-result-card-wrapper__price'
                                        ],
                                        'md:block hidden'
                                    )}
                                    // eslint-disable-next-line i18next/no-literal-string
                                >
                                    <PriceDiscount
                                        subtotalValue={Number(
                                            productData?.price_range
                                                ?.minimum_price?.regular_price
                                                ?.value
                                        )}
                                        priceDiscount={
                                            subtotal !== total
                                                ? subtotal
                                                : undefined
                                        }
                                        variant="inline"
                                        // eslint-disable-next-line i18next/no-literal-string
                                        theme="light"
                                        // eslint-disable-next-line i18next/no-literal-string
                                        size="big"
                                        className={
                                            s['configurator-add-to-cart__total']
                                        }
                                    />
                                    <PriceDiscount
                                        price={productPrice}
                                        variant="inline"
                                        // eslint-disable-next-line i18next/no-literal-string
                                        theme="light"
                                        // eslint-disable-next-line i18next/no-literal-string
                                        size="big"
                                        className={
                                            s['configurator-add-to-cart__total']
                                        }
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </div>
    )
}

export default ProductQuizViewResultCard
