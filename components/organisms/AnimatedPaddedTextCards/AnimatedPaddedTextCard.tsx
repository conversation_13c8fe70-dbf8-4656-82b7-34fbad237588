import { useAnimationAndVideosToggle } from '@components/common/AnimationAndVideosToggle/AnimationAndVideoContext'
import { CloudinaryMedia } from '@components/common/CloudinaryMedia/Cloudinary'
import { useLayoutContext } from '@components/layouts/MainLayout/LayoutContext'
import { SectionBgColor } from '@components/templates/Section/Section'
import {
    ImageType,
    VideoType
} from '@pylot-data/hooks/contentful/use-content-json'
import cn from 'classnames'
import { FC } from 'react'
import s from './AnimatedPaddedTextCard.module.scss'

export interface AnimatedPaddedTextCardProps {
    headline?: string
    bodyCopy?: string
    className?: string
    headlineClasses?: string[]
    eventTracking?: any
    animationImage?: ImageType | VideoType
    backgroundColor?: SectionBgColor | string
    textColor?: string
    cloudinaryMedia?: CloudinaryMedia[]
}

export const AnimatedPaddedTextCard: FC<AnimatedPaddedTextCardProps> = ({
    className,
    headline,
    bodyCopy,
    animationImage,
    backgroundColor,
    textColor,
    cloudinaryMedia
}) => {
    const { isAnimationStopped } = useAnimationAndVideosToggle()
    const { pageTheme } = useLayoutContext()
    const image = cloudinaryMedia?.[0]?.secure_url ?? animationImage?.file?.url
    return (
        <div
            className={cn(
                'rounded-2xl gap-12px overflow-hidden',
                s['animated-padded-text-card'],
                className,
                backgroundColor
            )}
        >
            <div className={cn(s['animated-padded-text-card__media'])}>
                <div
                    className={cn(
                        s['animated-padded-text-card__media-animated'],
                        {
                            [s['no-animation']]: isAnimationStopped,
                            [s[
                                'animated-padded-text-card__media-animated-light-page-theme'
                            ]]: pageTheme !== 'dark'
                        }
                    )}
                >
                    <div
                        className={cn(
                            s['animated-padded-text-card__icon-container'],
                            'py-16',
                            {
                                [s[
                                    'animated-padded-text-card__icon-light-page-theme'
                                ]]: pageTheme !== 'dark'
                            }
                        )}
                    >
                        <img
                            src={image}
                            // eslint-disable-next-line i18next/no-literal-string
                            alt="LogoIcon"
                        />
                        <img
                            src={image}
                            // eslint-disable-next-line i18next/no-literal-string
                            alt="LogoIcon"
                        />
                        <img
                            src={image}
                            // eslint-disable-next-line i18next/no-literal-string
                            alt="LogoIcon"
                        />
                        <img
                            src={image}
                            // eslint-disable-next-line i18next/no-literal-string
                            alt="LogoIcon"
                        />
                    </div>
                </div>
            </div>
            <div
                className={cn(
                    s['animated-padded-text-card__text'],
                    'flex flex-col gap-6px md:gap-8px'
                )}
            >
                <h4
                    className={cn(
                        s['animated-padded-text-card__text-headline'],
                        'flex items-center',
                        { 'text-white': textColor === 'light' }
                    )}
                >
                    {headline}
                </h4>
                <p
                    className={cn(
                        s['animated-padded-text-card__text-body'],
                        'small-copy h-full',
                        { 'text-white': textColor === 'light' }
                    )}
                >
                    {bodyCopy}
                </p>
            </div>
        </div>
    )
}

export default AnimatedPaddedTextCard
