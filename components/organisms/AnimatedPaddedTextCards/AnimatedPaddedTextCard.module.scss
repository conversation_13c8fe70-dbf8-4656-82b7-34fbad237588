@keyframes bannermove {
    0% {
        transform: translate(0, 0);
    }

    100% {
        transform: translate3d(-200%, 0, 0);
    }
}
@keyframes bannermove-light-page-theme {
    0% {
        transform: translate(0, 0);
    }

    100% {
        transform: translate3d(-140%, 0, 0);
    }
}

.animated-padded-text-card {
    &__text {
        padding: 16px;

        @screen md {
            padding: 32px;
        }
    }

    &__media {
        @screen md {
            position: relative;
            overflow: hidden;
            font-size: 0;
        }

        &-item {
            display: flex;
            flex-direction: column;

            @screen md {
                max-width: 129px;
            }
        }
    }

    &__media-animated {
        white-space: nowrap;
        animation: bannermove 10s linear infinite;

        &.no-animation {
            animation: none !important;
            transition: none !important;
        }
    }

    &__media-animated-light-page-theme {
        animation: bannermove-light-page-theme 10s linear infinite;
    }

    &__icon-container {
        padding-top: 80px;
        width: 200%;

        @screen md-max {
            padding-top: 35px;
        }

        img {
            display: inline-block;
            height: 100%;
            width: 100%;

            @screen md-max {
                height: 100%;
            }
        }
    }

    &__icon-light-page-theme {
        img {
            width: 70%;
        }
    }
}