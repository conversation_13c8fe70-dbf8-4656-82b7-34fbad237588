import type { FC } from 'react'
import { useTranslation } from 'next-i18next'
import Link from 'next/link'
import { Customer } from '@pylot-data/fwrdschema'

type Props = {
    customer: Customer
    canEditInfo?: boolean
}

export const InformationCard: FC<Props> = ({
    customer,
    canEditInfo = true
}) => {
    const { t } = useTranslation(['account'])
    const { t: trans } = useTranslation(['account'])
    const { firstname, lastname, email } = customer

    return (
        <div className="account-details box-info">
            <div className="mb-6 small-copy">
                {t('account|Contact Information')}
            </div>
            <div className="py-6 px-16px bg-bg-grey">
                <div className="small-copy">{`${firstname} ${lastname}`}</div>
                <div className="small-copy">{email}</div>
            </div>
            {canEditInfo && (
                <div className="action-container">
                    <Link href="/account/account-information">
                        {
                            // eslint-disable-next-line jsx-a11y/anchor-is-valid
                            <a
                                aria-label={`${t('account|Edit')} - ${trans(
                                    'ada|Opens in the current Tab'
                                )}`}
                            >
                                {t('account|Edit')}
                            </a>
                        }
                    </Link>
                    <Link
                        href={{
                            pathname: '/account/account-information',
                            query: { action: 'password' }
                        }}
                    >
                        <a
                            aria-label={`${t(
                                'account|Change Password'
                            )} - ${trans('ada|Opens in the current Tab')}`}
                        >
                            {t('account|Change Password')}
                        </a>
                    </Link>
                </div>
            )}
        </div>
    )
}
