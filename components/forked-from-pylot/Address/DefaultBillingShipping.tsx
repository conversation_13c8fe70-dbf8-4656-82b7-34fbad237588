import type { FC } from 'react'
import { useTranslation } from 'next-i18next'
import { UseFormRegister } from 'react-hook-form'
import { useUser } from '@corsairitshopify/pylot-auth-manager'
import { AddressInputVariableInterface } from '@pylot-data/hooks/melissa/use-address-validation'

export type Props = {
    register: UseFormRegister<AddressInputVariableInterface>
    defaultShipping?: boolean
    defaultBilling?: boolean
}

export const DefaultBillingShipping: FC<Props> = ({
    register,
    defaultShipping = true,
    defaultBilling = true
}) => {
    const { t } = useTranslation(['account'])
    const { addresses } = useUser({
        revalidateOnFocus: false
    })

    //if no address then by default set the address as default
    if (!addresses.length) return null

    return (
        <div className="mt-2">
            {defaultBilling && (
                <label className="flex items-center">
                    <input
                        type="checkbox"
                        {...register('default_billing')}
                        className="form-checkbox h-16px w-16px text-content-blue bg-white mr-4px"
                    />
                    {t(
                        'account||addressform|Use as my default billing address'
                    )}
                </label>
            )}
            {defaultShipping && (
                <label className="flex items-center">
                    <input
                        type="checkbox"
                        {...register('default_shipping')}
                        className="form-checkbox h-16px w-16px text-content-blue bg-white mr-4px"
                    />
                    {t(
                        'account||addressform|Use as my default shipping address'
                    )}
                </label>
            )}
        </div>
    )
}
