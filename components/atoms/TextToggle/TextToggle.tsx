import { Icon } from '@components/atoms/Icon/Icon'
import cn from 'classnames'
import unescape from 'lodash.unescape'
import { nanoid } from 'nanoid'
import { FC, useMemo, useState } from 'react'
import s from './TextToggle.module.scss'
import { useLayoutContext } from '@components/layouts/MainLayout/LayoutContext'
import { NeoComponent, useNeoAction } from '@lib/gtm/neoActions'

export type TextToggleProps = {
    text: string
    active?: boolean
    className?: string
    onChange?: () => void
    variant?: 'tab' | 'system' | 'filter'
    pageVariant?: 'beta' | 'downloads' | 'spare-parts'
    id?: string
}

export const TextToggle: FC<TextToggleProps> = (props) => {
    const {
        text,
        active = true,
        variant = 'tab',
        onChange,
        className,
        id
    } = props

    const { handleNeoOptionClick } = useNeoAction()
    const { pageTheme } = useLayoutContext()
    const [isActive, setIsActive] = useState(active)
    let iconName = ''
    if (text?.toLowerCase() === 'mac' || text?.toLowerCase() === 'apple') {
        iconName = 'apple'
    } else if (text?.toLowerCase().includes('windows')) {
        iconName = 'windows'
    } else if (text?.toLowerCase() === 'android') {
        iconName = 'android'
    }
    let textClass = 'h5'
    if (variant === 'filter') {
        textClass = 'h6'
    } else if (variant === 'system') {
        textClass = 'h3'
    }

    const toggle = () => {
        handleNeoOptionClick(NeoComponent.SYSTEM_REQ, text)
        setIsActive(!isActive)
        if (onChange) {
            onChange()
        }
    }
    const textToggleID = useMemo(() => nanoid(), [])

    const renderContent = () => {
        return (
            <>
                {iconName && <Icon name={iconName} />}
                <span
                    id={textToggleID}
                    className={cn(s['text-toggle__text'], textClass)}
                    dangerouslySetInnerHTML={{ __html: unescape(text) }}
                >
                    {text}
                </span>
            </>
        )
    }

    return (
        // eslint-disable-next-line jsx-a11y/no-noninteractive-element-interactions, jsx-a11y/role-supports-aria-props
        <label
            className={cn(
                s['text-toggle'],
                s[`text-toggle--${variant}`],
                {
                    [s[`text-toggle--active`]]:
                        (onChange && active) || (!onChange && isActive),
                    [s[`page-theme-${pageTheme}`]]: pageTheme,
                    [s[`page-theme-${pageTheme}--active`]]:
                        (pageTheme && onChange && active) ||
                        (!onChange && isActive && pageTheme),
                    [s['text-toggle--active--no-border']]:
                        id === 'wave-neo-system-requirements'
                },
                className
            )}
            onClick={toggle}
            onKeyPress={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    toggle()
                }
            }}
            // eslint-disable-next-line jsx-a11y/no-noninteractive-tabindex
            tabIndex={0}
            aria-pressed={(onChange && active) || (!onChange && isActive)}
            aria-controls={textToggleID}
            htmlFor={textToggleID}
            // eslint-disable-next-line jsx-a11y/no-noninteractive-element-to-interactive-role
            role="button"
            aria-label={`Toggle ${text} softwares`}
        >
            {/* <input
                type="radio"
                name="textToggle"
                className="sr-only"
                id={textToggleID}
            /> */}
            <div
                className={cn(
                    s['text-toggle__buttons'],
                    'flex items-center gap-2',
                    {
                        [s['text-toggle__wave-neo']]:
                            id === 'wave-neo-system-requirements',
                        [s[`text-toggle__wave-neo--active`]]:
                            ((onChange && active) || (!onChange && isActive)) &&
                            id === 'wave-neo-system-requirements'
                    }
                )}
            >
                {' '}
                {id === 'wave-neo-system-requirements' ? (
                    <>
                        <div className={s['text-toggle__wave-neo__content']}>
                            {renderContent()}
                        </div>
                        <div className={s['text-toggle__wave-neo__content']}>
                            {' '}
                            {renderContent()}
                        </div>
                    </>
                ) : (
                    renderContent()
                )}
            </div>
        </label>
    )
}
