import React, { FC } from 'react'
import s from './HighlightCallout.module.scss'
import cn from 'classnames'

export type HighlightCalloutProps = {
    text: string
    color?: 'dark' | 'light'
    className?: string
    mobileVariant?:
        | 'overlay-top'
        | 'overlay-bottom'
        | 'product-hotspot'
        | 'animated-product-hotspot'
}

export const HighlightCallout: FC<HighlightCalloutProps> = (props) => {
    const { text, color = 'dark', mobileVariant } = props
    return (
        <div
            className={cn(
                s['highlight-callout'],
                s[`highlight-callout--${color}`],
                {
                    [s['highlight-callout--hide']]:
                        mobileVariant === 'animated-product-hotspot'
                }
            )}
        >
            <div className="h5">{text}</div>
        </div>
    )
}
