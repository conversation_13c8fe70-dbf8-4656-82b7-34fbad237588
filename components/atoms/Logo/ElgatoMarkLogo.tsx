const El<PERSON><PERSON>MarkLogo = ({ ...props }) => {
    return (
        <svg
            width="48"
            height="48"
            viewBox="0 0 48 48"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <path
                d="M27.4783 16.7928L27.5306 16.832L47.4296 28.2662C46.4525 33.7241 43.59 38.7108 39.3525 42.317C35.0472 45.981 29.5628 48 23.9086 48C17.5234 48 11.5208 45.5039 7.00349 40.9708C2.48793 36.4376 0 30.4089 0 24.0002C0 17.5915 2.48793 11.5628 7.00304 7.02921C11.5181 2.49606 17.5207 0 23.9086 0C28.7875 0 33.4781 1.46668 37.4747 4.23756C41.3797 6.94572 44.3676 10.709 46.1199 15.1221C46.3424 15.688 46.5496 16.2693 46.7301 16.8532L42.4794 19.3254C42.3328 18.7387 42.1572 18.1547 41.9555 17.5866C39.2473 9.92601 31.9929 4.7773 23.9082 4.7773C13.3486 4.7773 4.75835 13.4014 4.75835 24.0002C4.75835 34.5991 13.3486 43.2232 23.9082 43.2232C28.0121 43.2232 31.9248 41.9347 35.2273 39.4937C38.4616 37.1028 40.8345 33.826 42.0891 30.0208L42.1022 29.9369L18.8957 16.6095V31.061L26.8966 26.4331L31.0163 28.7956L19.0239 35.7332L14.7836 33.308V14.3888L19.0185 11.9401L27.4805 16.7955L27.4778 16.7928H27.4783Z"
                fill="currentColor"
            />
        </svg>
    )
}

export default ElgatoMarkLogo
