const BestBuySmallLogo = ({ ...props }) => {
    return (
        <svg
            width="46"
            height="27"
            viewBox="0 0 46 27"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M36.2939 19.4675H45.1186V26.535H36.2939L34.0781 24.472V21.5305L36.2939 19.4675Z"
                fill="#FFEE35"
            />
            <path
                d="M6.99323 9.26769C6.80222 9.4587 6.5348 9.57331 6.19098 9.57331H4.47188V7.54858H6.19098C6.4966 7.54858 6.76402 7.66319 6.99323 7.8542C7.18424 8.04521 7.29885 8.27443 7.29885 8.58005C7.29885 8.84746 7.18424 9.07668 6.99323 9.26769ZM4.51008 3.57554H5.84716C6.11458 3.57554 6.34379 3.65195 6.4966 3.80476C6.64941 3.95756 6.76402 4.14858 6.76402 4.37779C6.76402 4.607 6.68761 4.79802 6.4966 4.95083C6.34379 5.10363 6.11458 5.18004 5.84716 5.18004H4.51008V3.57554ZM8.82694 6.2497L8.97975 6.1733C9.09436 6.1351 9.20897 6.02049 9.36178 5.90588C9.51459 5.79128 9.66739 5.63847 9.8202 5.44746C9.97301 5.29465 10.0876 5.06543 10.2022 4.79802C10.3168 4.5306 10.355 4.22498 10.355 3.91936C10.355 3.11711 9.97301 2.42947 9.24717 1.85644C8.52132 1.2834 7.52806 0.977783 6.26739 0.977783H0.880859V12.1329H6.64941C8.1393 12.1329 11.1955 11.8654 11.1955 8.92387C11.1955 6.70813 8.86515 6.2497 8.82694 6.2497Z"
                fill="white"
            />
            <path
                d="M9.86042 21.6455C9.66941 21.8365 9.40199 21.9511 9.05817 21.9511H7.37727V19.9264H9.05817C9.36379 19.9264 9.63121 20.041 9.86042 20.232C10.0514 20.423 10.166 20.6522 10.166 20.9578C10.166 21.1871 10.0514 21.4545 9.86042 21.6455ZM7.37727 15.9533H8.71435C8.98177 15.9533 9.21098 16.0297 9.36379 16.1826C9.5166 16.3354 9.6312 16.5264 9.6312 16.7556C9.6312 16.9848 9.5548 17.1758 9.36379 17.3286C9.21098 17.4814 8.98177 17.5578 8.71435 17.5578H7.37727V15.9533ZM11.6941 18.5893L11.8469 18.5129C11.9615 18.4747 12.0762 18.3601 12.229 18.2455C12.3818 18.1309 12.5346 17.9781 12.6874 17.7871C12.8402 17.6342 12.9548 17.405 13.0694 17.1376C13.184 16.8702 13.2222 16.5646 13.2222 16.259C13.2222 15.4567 12.8402 14.7691 12.1144 14.196C11.3885 13.623 10.3953 13.3174 9.13457 13.3174H3.74805V24.4725H9.5166C11.0065 24.4725 14.0627 24.205 14.0627 21.2635C14.0627 19.0859 11.7323 18.6275 11.6941 18.5893Z"
                fill="white"
            />
            <path
                d="M20.4396 9.4205H15.0531V7.66319H19.4463V5.14184H15.0531V3.69015H20.4778V0.977783H11.4238V12.1329H20.4396V9.4205Z"
                fill="white"
            />
            <path
                d="M29.6855 0.977783V3.69015H32.6271V12.1329H36.2181V3.69015H39.1979V0.977783H29.6855Z"
                fill="white"
            />
            <path
                d="M25.5974 9.26777C25.4064 9.42058 25.139 9.53519 24.7569 9.53519C24.4131 9.53519 24.0311 9.42058 23.6491 9.22957C23.2671 9.03856 22.9996 8.88575 22.885 8.77114C22.8086 8.69474 22.7322 8.65653 22.694 8.58013L20.5547 10.6049L20.7075 10.7577C20.8221 10.9105 21.0513 11.0633 21.3187 11.2543C21.5862 11.4453 21.8918 11.6363 22.2738 11.8273C22.6176 12.0183 23.076 12.1711 23.6109 12.324C24.1457 12.4386 24.6805 12.515 25.2536 12.515C26.6671 12.515 27.7749 12.1329 28.6154 11.4071C29.4558 10.6813 29.8761 9.8026 29.8761 8.77114C29.8761 8.1599 29.7615 7.66327 29.5322 7.20485C29.303 6.74642 28.9974 6.4026 28.6154 6.17338C28.2716 5.94417 27.8513 5.75316 27.4311 5.56215C27.0491 5.40934 26.7053 5.25653 26.3614 5.18012C26.3232 5.18012 26.285 5.18012 26.2468 5.14192C25.903 5.06552 25.5974 4.95091 25.33 4.7981C25.1008 4.68349 24.9862 4.53068 24.9862 4.33967C24.9862 4.14866 25.0626 3.95765 25.2536 3.84304C25.4064 3.69023 25.6738 3.65203 25.9794 3.65203C26.7053 3.65203 27.1637 3.99585 27.3929 4.22506L29.5322 2.20034L29.494 2.16214C29.4176 2.04753 29.2266 1.89472 28.9974 1.74191C28.7682 1.5891 28.5008 1.43629 28.1952 1.28348C27.8895 1.13067 27.5075 0.977866 27.0109 0.863259C26.5143 0.748652 26.0176 0.710449 25.4828 0.710449C24.1457 0.710449 23.076 1.05427 22.2356 1.78011C21.4333 2.50596 21.0131 3.30821 21.0131 4.26327C21.0131 4.7599 21.0895 5.21832 21.2805 5.63855C21.4715 6.05878 21.7008 6.36439 21.9682 6.59361C22.2356 6.82282 22.5794 7.05204 22.9232 7.24305C23.2289 7.39586 23.4963 7.51046 23.8019 7.62507C23.8401 7.66327 23.9165 7.66327 23.9547 7.70148C24.2985 7.81608 24.6041 7.93069 24.9098 8.00709C25.1772 8.1217 25.4064 8.19811 25.5974 8.31271C25.7884 8.42732 25.8648 8.54193 25.8648 8.69474C25.903 8.88575 25.7884 9.07676 25.5974 9.26777Z"
                fill="white"
            />
            <path
                d="M20.4772 19.9642C20.4772 20.6901 19.866 21.3013 19.1401 21.3013C18.3761 21.3013 17.803 20.6901 17.803 19.9642V13.3552H14.1738V19.8114C14.1738 22.4856 16.3514 24.6249 18.9873 24.6249H19.2165C21.8907 24.6249 24.03 22.4474 24.03 19.8114V13.3552H20.4772V19.9642Z"
                fill="white"
            />
            <path
                d="M30.2204 17.1373L27.8519 13.3552H24.2227L28.4249 20.5373V24.5103H32.0159V20.5373L36.2182 13.3552H32.589L30.2204 17.1373Z"
                fill="white"
            />
            <path
                d="M35.8346 22.9825C35.8346 23.2117 35.6436 23.4027 35.4144 23.4027C35.1852 23.4027 34.9941 23.2117 34.9941 22.9825C34.9941 22.7533 35.1852 22.5623 35.4144 22.5623C35.6436 22.5623 35.8346 22.7533 35.8346 22.9825Z"
                fill="white"
            />
            <path
                d="M36.8296 16.4497H36.9824L37.2498 17.0991L37.5172 16.4497H37.67V17.2902H37.5554V16.5643L37.2498 17.2902H37.2116L36.906 16.5643V17.2902H36.8296V16.4497V16.4497ZM36.2948 16.5643H36.0273V16.4879H36.6386V16.5643H36.3712V17.3284H36.2566V16.5643H36.2948Z"
                fill="white"
            />
        </svg>
    )
}

export default BestBuySmallLogo
