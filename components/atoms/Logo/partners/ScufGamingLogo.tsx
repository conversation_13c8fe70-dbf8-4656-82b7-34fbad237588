// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
const <PERSON><PERSON><PERSON>aming<PERSON>ogo = ({ ...props }) => {
    return (
        <svg
            width="60"
            height="21"
            viewBox="0 0 60 21"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M8.81984 11.643C8.81984 12.0299 8.87671 12.3632 8.99162 12.6417C9.10596 12.9203 9.25275 13.1577 9.4308 13.3552C9.60944 13.5521 9.81023 13.7084 10.0327 13.8237C10.2551 13.9385 10.4787 14.0309 10.7016 14.0983C10.9247 14.1668 11.1351 14.2107 11.332 14.2312C11.5294 14.2518 11.6915 14.2621 11.8189 14.2621C12.2012 14.2621 12.5801 14.2193 12.9561 14.1348C13.3321 14.0498 13.6695 13.9105 13.9687 13.7165C14.2679 13.523 14.5103 13.2667 14.6946 12.9471C14.8795 12.6281 14.9711 12.2308 14.9711 11.7548C14.9711 11.4221 14.9176 11.1385 14.8101 10.9039C14.7014 10.6699 14.5563 10.473 14.3749 10.3132C14.1934 10.1539 13.988 10.0227 13.7588 9.92109C13.5295 9.81892 13.2934 9.73104 13.0511 9.65628C12.8099 9.58834 12.5721 9.52559 12.3394 9.46679C12.1074 9.4097 11.8991 9.34807 11.7143 9.28416C11.5294 9.21909 11.3786 9.14433 11.2603 9.05984C11.1425 8.97483 11.0805 8.86808 11.0743 8.7391C11.0743 8.63694 11.0993 8.5536 11.1505 8.48913C11.2017 8.42463 11.2654 8.37556 11.3416 8.34129C11.4179 8.30762 11.5009 8.28365 11.5902 8.26996C11.679 8.25628 11.7586 8.24942 11.8286 8.24942C11.9367 8.24942 12.0453 8.2631 12.154 8.29051C12.2621 8.31791 12.3605 8.36527 12.4498 8.4332C12.5391 8.49428 12.608 8.57247 12.6552 8.66777C12.7029 8.76252 12.7172 8.87781 12.6978 9.0142H14.6855C14.6724 8.57247 14.5865 8.20035 14.4266 7.89784C14.2679 7.59593 14.058 7.35111 13.7969 7.16389C13.5358 6.97783 13.236 6.84315 12.8992 6.76209C12.5613 6.68047 12.2075 6.63998 11.8388 6.63998C11.4873 6.63998 11.1477 6.68506 10.816 6.77693C10.4849 6.86825 10.1919 7.01322 9.93767 7.21013C9.6828 7.4076 9.47745 7.65701 9.321 7.95948C9.16455 8.26139 9.0872 8.62325 9.0872 9.04444C9.0872 9.41086 9.15717 9.71221 9.2971 9.94618C9.43762 10.1802 9.61738 10.3742 9.83697 10.5272C10.0566 10.6802 10.3052 10.8023 10.5822 10.8942C10.8592 10.9855 11.1312 11.0722 11.3991 11.1539C11.6915 11.2423 11.9338 11.3188 12.125 11.3833C12.3161 11.4478 12.4657 11.5083 12.5744 11.5665C12.6825 11.6247 12.7587 11.6858 12.8036 11.7497C12.848 11.8148 12.8702 11.8935 12.8702 11.9889C12.8702 12.1252 12.8412 12.2354 12.7843 12.3204C12.7269 12.4055 12.6535 12.4734 12.5641 12.5242C12.4754 12.575 12.3798 12.6092 12.278 12.6263C12.175 12.6435 12.0834 12.652 12.001 12.652C11.8036 12.652 11.605 12.6178 11.4088 12.5499C11.2114 12.482 11.0743 12.3398 10.998 12.1218C10.9469 11.9791 10.9212 11.8193 10.9212 11.643H8.81984ZM12.6967 2.79999C16.5713 3.20121 19.592 6.48689 19.592 10.4797C19.592 14.472 16.5713 17.7571 12.6967 18.1583V16.5317C15.6799 16.1379 17.9827 13.5794 17.9827 10.4797C17.9827 7.37897 15.6799 4.82096 12.6967 4.42598V2.79999ZM4.20001 10.4797C4.20001 6.48689 7.22016 3.20121 11.0948 2.79999V4.42598C8.11158 4.82096 5.80879 7.37897 5.80879 10.4797C5.80879 13.5794 8.11158 16.1379 11.0948 16.5317V18.1583C9.77894 18.0225 8.56157 17.5522 7.52792 16.8359L4.24324 18.2L5.59945 14.9172C4.71826 13.6615 4.20001 12.1314 4.20001 10.4797ZM48.3882 15.2887H51.0297V11.4837H54.4502V9.33088H51.0297V8.01478H55.0141V5.71129H48.3882V15.2887ZM47.5328 5.71129H44.8924V10.9095C44.8924 11.1401 44.8858 11.3792 44.8739 11.6269C44.8623 11.8741 44.8165 12.1006 44.736 12.3049C44.6558 12.5104 44.5319 12.6782 44.3643 12.8112C44.1963 12.943 43.9562 13.0092 43.6433 13.0092C43.3237 13.0092 43.0756 12.943 42.8999 12.8112C42.7235 12.6782 42.5954 12.5104 42.5152 12.3049C42.4344 12.1006 42.3896 11.8741 42.3777 11.6269C42.3658 11.3792 42.3595 11.1401 42.3595 10.9095V5.71129H39.7187V11.5367C39.7187 12.8089 40.04 13.7517 40.6851 14.3658C41.3291 14.9811 42.3115 15.2887 43.6321 15.2887C44.9523 15.2887 45.9326 14.9811 46.5724 14.3658C47.2129 13.7517 47.5328 12.8089 47.5328 11.5367V5.71129ZM38.8266 9.33487C38.7146 8.18257 38.3369 7.29224 37.6922 6.66556C37.0486 6.03777 36.1347 5.7193 34.951 5.71129C34.286 5.71129 33.6875 5.82658 33.1562 6.05716C32.6232 6.28718 32.1675 6.61191 31.7869 7.03026C31.4069 7.44802 31.1151 7.95199 30.9109 8.54041C30.7072 9.12998 30.6048 9.78287 30.6048 10.4997C30.6048 11.1743 30.7004 11.8038 30.8927 12.3888C31.0849 12.9733 31.3671 13.4789 31.7391 13.9058C32.1117 14.3327 32.5651 14.67 33.1021 14.9172C33.6375 15.1649 34.2541 15.2887 34.951 15.2887C35.5751 15.2887 36.1302 15.1774 36.6184 14.956C37.107 14.734 37.5165 14.4406 37.8487 14.0782C38.1812 13.7158 38.4325 13.3083 38.6054 12.8557C38.7772 12.4037 38.863 11.9465 38.863 11.4859H36.2702C36.231 11.7 36.1764 11.8997 36.1081 12.0881C36.0409 12.2747 35.9527 12.4379 35.8442 12.5743C35.7368 12.7102 35.6041 12.8174 35.4487 12.8934C35.2923 12.971 35.1019 13.0092 34.8787 13.0092C34.5659 13.0092 34.3053 12.9367 34.0982 12.7918C33.89 12.6468 33.7245 12.4551 33.5999 12.2159C33.4759 11.9768 33.386 11.7079 33.3297 11.4089C33.274 11.1098 33.2461 10.8068 33.2461 10.4997C33.2461 10.1927 33.274 9.88962 33.3297 9.59111C33.386 9.29207 33.4759 9.02323 33.5999 8.78411C33.7245 8.54555 33.89 8.35379 34.0982 8.20766C34.3053 8.06329 34.5659 7.9908 34.8787 7.9908C35.1502 7.9908 35.3721 8.04614 35.545 8.15688C35.7168 8.26758 35.8551 8.39544 35.9583 8.54041C36.0626 8.6865 36.1347 8.83378 36.1739 8.98274C36.2149 9.1317 36.2422 9.24982 36.259 9.33487H38.8266ZM24.6595 11.9979C24.6595 12.2199 24.6914 12.4202 24.7557 12.5994C24.8518 12.8734 25.0236 13.052 25.2716 13.1376C25.5191 13.2232 25.7683 13.2655 26.0157 13.2655C26.1198 13.2655 26.2359 13.2546 26.3639 13.2335C26.4913 13.2118 26.6125 13.169 26.724 13.1057C26.8361 13.0412 26.9282 12.9561 27.0004 12.8494C27.0721 12.7427 27.1074 12.6046 27.1074 12.4334C27.1074 12.3135 27.0801 12.2136 27.0243 12.132C26.968 12.0515 26.8725 11.9745 26.7365 11.902C26.5999 11.8289 26.4122 11.7525 26.1722 11.6714C25.9321 11.591 25.6277 11.4939 25.2597 11.3832C24.9241 11.2805 24.5816 11.1721 24.234 11.0568C23.8853 10.9415 23.573 10.7879 23.2971 10.5962C23.0212 10.4038 22.7953 10.1601 22.619 9.86621C22.4432 9.57172 22.355 9.19333 22.355 8.73273C22.355 8.20367 22.4529 7.74881 22.6491 7.36927C22.8448 6.98973 23.1031 6.67582 23.4234 6.42812C23.7431 6.18043 24.1111 5.99895 24.5276 5.88366C24.9434 5.76837 25.3718 5.71129 25.8121 5.71129C26.2757 5.71129 26.72 5.76267 27.1444 5.86483C27.5682 5.967 27.9442 6.1365 28.2725 6.36992C28.6001 6.60564 28.8647 6.9127 29.0649 7.29224C29.2646 7.67234 29.3732 8.13976 29.3891 8.69451H26.8924C26.9168 8.52385 26.898 8.37832 26.8383 8.25904C26.7786 8.13976 26.6921 8.04103 26.5806 7.96455C26.4674 7.8795 26.344 7.81958 26.208 7.78532C26.072 7.75165 25.9361 7.73397 25.7996 7.73397C25.7114 7.73397 25.6118 7.74251 25.4998 7.75966C25.3877 7.77734 25.2842 7.80702 25.1874 7.84982C25.0919 7.89207 25.0117 7.9537 24.9479 8.03473C24.8831 8.11635 24.8518 8.22079 24.8518 8.3492C24.8598 8.51129 24.9377 8.64541 25.0856 8.75272C25.2335 8.85887 25.4235 8.95246 25.6556 9.03408C25.8877 9.11514 26.15 9.19162 26.4424 9.2641C26.7342 9.33715 27.0323 9.4159 27.3367 9.50095C27.6404 9.59569 27.9362 9.70584 28.2246 9.83425C28.5125 9.96211 28.7708 10.1265 28.9989 10.3274C29.227 10.5277 29.409 10.7748 29.5456 11.0693C29.681 11.3638 29.7492 11.7205 29.7492 12.1389C29.7492 12.7364 29.6332 13.2358 29.4011 13.6364C29.169 14.0382 28.8647 14.3601 28.4892 14.6033C28.1126 14.847 27.6888 15.0222 27.216 15.1284C26.7439 15.2351 26.2677 15.2881 25.7876 15.2881C25.6277 15.2881 25.4235 15.2756 25.1755 15.2505C24.928 15.2242 24.6635 15.1688 24.383 15.0832C24.1032 14.9982 23.8233 14.8829 23.5434 14.738C23.2635 14.593 23.0109 14.3967 22.7868 14.1495C22.5632 13.9013 22.3789 13.6033 22.235 13.2529C22.0911 12.9031 22.0188 12.4841 22.0188 11.9979H24.6595Z"
                fill="currentColor"
            />
        </svg>
    )
}

export default ScufGamingLogo
