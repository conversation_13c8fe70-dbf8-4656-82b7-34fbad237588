const TesaLogo = ({ ...props }) => {
    return (
        <svg
            width="168"
            height="60"
            viewBox="0 0 168 60"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <path
                d="M110.625 52.0003H12.2598V8H147.347H155.405V52.0003"
                fill="white"
            />
            <path
                d="M110.625 52.0003H12.2598V8H147.347H155.405V52.0003H110.625Z"
                stroke="white"
                strokeWidth="0.000148552"
                strokeMiterlimit="3.8637"
            />
            <path
                d="M15.3857 11.1411H143.662C143.662 11.1411 136.621 27.4324 120.318 28.7474C122.053 31.724 119.056 48.8594 109.449 48.8594H15.3857"
                fill="#EC1A2D"
            />
            <path
                d="M144.565 11.1411H152.265V48.8594H113.478C131.645 39.0411 144.565 11.1411 144.565 11.1411Z"
                fill="#00ACEF"
            />
            <path
                d="M33.5529 15.4722H39.2884L38.2704 20.2969H48.4654L45.7123 25.0017H37.0803L34.9293 33.7893C34.9293 33.7893 33.7535 37.0014 36.0621 37.8277C40.4929 39.276 44.3357 36.0595 44.3357 36.0595L45.3251 40.6488C45.3251 40.6488 41.2815 43.8253 35.8757 43.2788C31.6027 43.1589 28.3478 40.4533 28.7349 36.4549C29.1651 33.2384 31.359 24.9261 31.359 24.9261H22.6553L25.2792 20.2569H32.377"
                fill="white"
            />
            <path
                d="M51.2757 33.2385C51.2757 33.2385 50.0281 37.8279 55.3192 38.2988C59.9937 38.6098 60.6964 35.3577 60.725 35.3577C66.6183 35.3577 66.6183 35.3577 66.6183 35.3577C66.6183 35.3577 65.0841 43.7899 54.3012 43.239C48.9671 43.239 44.2639 39.0807 45.5975 31.3549C45.5975 31.3549 46.4147 19.3552 59.119 19.7462C65.127 20.1771 67.6364 23.2381 68.0665 26.9655C68.382 29.8266 67.3209 33.2385 67.3209 33.2385"
                fill="white"
            />
            <path
                d="M67.952 35.3581H73.6733C73.6733 35.3581 72.6551 39.0278 77.516 38.8056C81.8607 38.6102 80.6563 35.8646 80.6133 35.9045C80.2261 33.9453 73.2431 33.4744 70.7768 28.8051C68.8124 22.2966 76.1825 20.1775 76.498 20.2175C83.352 18.6892 87.1948 21.2793 87.1948 21.2793C90.3351 22.9231 89.7472 26.6105 89.7472 26.6105H84.069C84.069 26.6105 84.4991 24.0604 80.4986 24.0204C76.3833 24.0604 77.1289 26.5305 77.1289 26.5305C77.6308 28.4142 83.9112 29.6715 85.5889 31.8262C90.0913 37.7305 84.5995 43.1061 78.9643 43.3549C70.3036 43.6704 69.8448 41.5334 69.1852 41.3957C66.4895 39.0544 67.952 35.3581 67.952 35.3581Z"
                fill="white"
            />
            <path
                d="M103.627 28.6499C103.627 28.6499 95.196 27.828 91.7547 30.5336C87.3526 34.0255 88.213 38.6104 88.213 38.6104C89.3601 43.4351 94.6081 43.3551 94.6081 43.3551C98.9241 43.2396 101.319 41.3159 101.319 41.3159L101.548 42.8487H106.925C106.925 42.8487 106.567 40.5562 106.925 39.6322C106.81 39.4767 109.707 27.0816 109.707 27.0816C109.707 27.0816 111.198 23.0432 106.724 21.044C103.355 19.2403 99.5837 19.9822 99.5837 19.9822C99.5837 19.9822 93.0021 20.2177 91.6686 26.6507H97.8631C97.8631 26.6507 98.3362 23.9851 101.749 24.1805C106.065 24.0606 103.627 28.6499 103.627 28.6499Z"
                fill="white"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M113.952 21.2571C113.55 21.6658 113.349 22.15 113.349 22.7142C113.349 23.2962 113.55 23.7894 113.952 24.1937C114.367 24.6024 114.855 24.8023 115.428 24.8023C115.988 24.8023 116.475 24.598 116.877 24.1892C117.292 23.7805 117.493 23.2873 117.493 22.7142C117.493 22.1545 117.292 21.6702 116.891 21.2571C116.475 20.8395 115.988 20.6306 115.428 20.6306C114.841 20.6306 114.353 20.8395 113.952 21.2571ZM117.178 24.5136C116.69 24.9889 116.102 25.2244 115.428 25.2244C114.711 25.2244 114.124 24.98 113.636 24.4958C113.149 24.0115 112.905 23.4206 112.905 22.7142C112.905 21.9812 113.177 21.3681 113.693 20.8839C114.181 20.4307 114.754 20.1997 115.428 20.1997C116.117 20.1997 116.705 20.4485 117.192 20.9372C117.68 21.4303 117.938 22.0212 117.938 22.7142C117.938 23.4251 117.68 24.0249 117.178 24.5136ZM115.643 21.8746C115.543 21.8346 115.414 21.8168 115.228 21.8168H115.056V22.6254H115.328C115.5 22.6254 115.629 22.5899 115.73 22.5232C115.83 22.4522 115.873 22.3455 115.873 22.19C115.873 22.039 115.801 21.9323 115.643 21.8746ZM114.324 24.1092V21.3281C114.496 21.3281 114.755 21.3281 115.099 21.3281C115.443 21.3281 115.629 21.3326 115.672 21.3326C115.887 21.3503 116.074 21.3948 116.217 21.4747C116.461 21.6125 116.59 21.8346 116.59 22.1411C116.59 22.3722 116.518 22.5454 116.389 22.6476C116.26 22.7542 116.102 22.8164 115.916 22.8342C116.088 22.8698 116.217 22.923 116.303 22.9941C116.475 23.1274 116.547 23.3362 116.547 23.6206V23.8693C116.547 23.896 116.547 23.9227 116.561 23.9493C116.561 23.976 116.561 24.0026 116.576 24.0293L116.604 24.1092H115.902C115.887 24.0204 115.873 23.8916 115.859 23.7227C115.859 23.5583 115.844 23.4428 115.816 23.3851C115.787 23.2873 115.715 23.2163 115.615 23.1807C115.557 23.1585 115.471 23.1408 115.357 23.1363L115.199 23.123H115.056V24.1092"
                fill="white"
            />
            <path
                d="M52.1506 28.4939H62.1449C62.1449 28.4939 63.4784 24.1312 58.0726 24.2201C53.2834 24.1801 52.1506 28.4939 52.1506 28.4939Z"
                fill="#EC1A2D"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M102.695 32.7284C102.724 32.7684 93.9051 31.6311 94.1776 37.2022C95.6258 40.6897 102.652 37.9841 102.695 32.7284Z"
                fill="#EC1A2D"
            />
        </svg>
    )
}

export default TesaLogo
