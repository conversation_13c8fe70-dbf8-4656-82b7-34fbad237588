import { useTranslation } from 'next-i18next'

const <PERSON><PERSON><PERSON><PERSON><PERSON> = ({ ...props }) => {
    const { t } = useTranslation(['common'])

    return (
        <svg
            width="174"
            height="48"
            viewBox="0 0 174 48"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            role="img"
            aria-labelledby="Elgato-Logo-title"
            {...props}
        >
            <title id="Elgato-Logo-title">{t('Elgato Logo')}</title>
            <path
                d="M26.978 16.055L27.028 16.0926L46.0527 27.0246C45.1187 32.2427 42.382 37.0102 38.3307 40.458C34.2142 43.961 28.9711 45.8915 23.5651 45.8915C17.4606 45.8915 11.7217 43.5053 7.40246 39.171C3.08574 34.8368 0.707031 29.0728 0.707031 22.9457C0.707031 16.8187 3.08574 11.0547 7.40246 6.72047C11.7192 2.38622 17.4581 0 23.5651 0C28.2299 0 32.7144 1.40218 36.5354 4.05131C40.2687 6.64035 43.1256 10.2385 44.8007 14.4575C45.0136 14.9984 45.2114 15.5542 45.3841 16.1126L41.3203 18.4763C41.1801 17.9154 41.0123 17.357 40.8195 16.8137C38.2305 9.48978 31.2947 4.56712 23.5651 4.56712C13.4694 4.56712 5.25662 12.8125 5.25662 22.9457C5.25662 33.079 13.4694 41.3244 23.5651 41.3244C27.4888 41.3244 31.2296 40.0925 34.387 37.7588C37.4793 35.4728 39.7479 32.3404 40.9472 28.7022L40.9597 28.6221L18.7727 15.8797V29.6963L26.4221 25.2719L30.3607 27.5304L18.8954 34.1632L14.8416 31.8446V13.7564L18.8904 11.4153L26.9805 16.0575L26.978 16.055Z"
                fill="currentColor"
            />
            <path
                d="M142.627 4.4595L146.841 2.02445V9.70504H151.847V13.3821H146.841V31.2889C146.838 32.482 147.687 33.0837 148.895 32.9705C149.891 32.8754 151.847 32.6388 151.847 32.6388V36.249C151.847 36.249 149.723 36.7581 148.44 36.7581C145.473 36.7581 142.619 36.0741 142.619 30.8723L142.624 29.0827V13.3821H138.42V9.70504H142.624V4.4595H142.627Z"
                fill="currentColor"
            />
            <path
                d="M82.4134 36.6115L86.6278 34.1765V3.11212H82.4134V36.6115Z"
                fill="currentColor"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M71.5982 27.8021C71.5519 28.751 71.4979 29.8772 70.5774 31.2092C69.8188 32.3071 68.7312 33.0811 66.489 33.0811C64.2467 33.0811 63.3596 32.0654 62.7374 31.2272C61.6446 29.7589 61.4157 27.3907 61.4157 24.3591H75.7664C75.7586 23.7729 75.7561 23.1917 75.7561 22.6158C75.7406 18.5428 75.7278 14.804 73.861 12.2738C72.1279 9.92362 69.698 9.18307 66.4941 9.18307C63.2902 9.18307 61.1586 9.83362 59.2429 12.3047C57.1473 15.0072 57.1756 19.4376 57.1833 20.5767C57.1833 20.6333 57.1833 20.6821 57.1833 20.7207C57.1833 20.9855 57.1833 21.5769 57.1833 22.2918C57.1833 23.3872 57.1807 24.7731 57.1807 25.7245C57.1807 30.0675 57.7978 31.9883 59.1246 33.6674C61.0223 36.0664 63.7222 36.717 66.4941 36.717C69.266 36.717 72.2693 36.0613 73.9613 33.6802C75.1209 32.0474 75.7406 30.1035 75.7843 27.5502H71.6136C71.6085 27.635 71.6059 27.7199 71.6008 27.8073L71.5982 27.8021ZM62.1485 15.0612C62.9277 13.5004 64.1516 12.8087 66.4607 12.8087C68.7697 12.8087 69.9911 13.5004 70.7702 15.0612C71.5545 16.6322 71.5519 19.7359 71.5519 20.6513V20.6924H61.4003V20.5587C61.4003 19.543 61.3926 16.5834 62.1511 15.0612H62.1485Z"
                fill="currentColor"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M101.169 9.16762C102.93 9.16762 105.193 9.87217 106.903 11.5615V9.69217H110.899V37.4627C110.899 41.1937 110.703 43.2893 108.677 45.4415C106.581 47.6683 104.396 48 102.264 48C100.132 48 97.0495 47.316 95.3575 44.935C94.1978 43.3022 93.5781 41.3505 93.5781 38.8023H97.7411C97.8671 40.5174 98.0677 41.3505 98.7285 42.2968C99.4922 43.3896 100.881 44.0736 102.238 44.0736C103.596 44.0736 105.069 43.2996 105.864 42.2171C106.651 41.1448 106.715 39.476 106.679 37.2801V34.1225C105.031 35.6036 103.077 36.1847 101.166 36.1847C98.6977 36.1847 96.3063 35.547 94.5578 33.1917C93.3339 31.5434 92.763 29.6612 92.763 25.3979C92.763 24.4645 92.763 23.1043 92.7656 22.0295C92.7656 21.3275 92.7656 20.7464 92.7656 20.4867C92.7656 20.4481 92.7656 20.3992 92.7656 20.3452C92.7579 19.2293 92.7322 14.8786 94.6633 12.2275C96.4298 9.80274 99.0782 9.16505 101.161 9.16505L101.169 9.16762ZM101.791 32.5462C103.748 32.5462 105.19 31.4329 105.892 29.7538C106.677 27.869 106.664 24.4517 106.659 23.112C106.659 23.0194 106.659 22.9371 106.659 22.8652V22.4846C106.659 22.356 106.659 22.194 106.664 22.0063C106.684 20.5689 106.723 17.5682 105.982 15.7863C105.283 14.1072 103.925 12.8035 101.791 12.8035C99.7777 12.8035 98.4843 13.6572 97.6897 15.6448C96.9492 17.5013 96.9517 19.1316 96.9595 21.7415C96.9595 21.9806 96.9595 22.2275 96.9595 22.4846V22.8652C96.9595 25.9199 97.016 27.9101 97.8234 29.9389C98.6154 31.9265 99.7776 32.5488 101.788 32.5488L101.791 32.5462Z"
                fill="currentColor"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M171.248 33.5927C173.326 30.8902 173.298 26.4547 173.29 25.3181C173.29 25.2616 173.29 25.2127 173.29 25.1742V20.7154C173.29 20.6769 173.29 20.628 173.29 20.5715C173.298 19.4349 173.326 14.9994 171.248 12.2969C169.348 9.82582 166.563 9.1727 164.018 9.1727C161.472 9.1727 158.769 9.82325 156.869 12.2969C154.792 14.9994 154.82 19.4324 154.828 20.5715C154.828 20.628 154.828 20.6769 154.828 20.7154V25.1742C154.828 25.2127 154.828 25.2616 154.828 25.3181C154.82 26.4572 154.792 30.8902 156.869 33.5927C158.769 36.0638 161.472 36.7143 164.018 36.7143C166.563 36.7143 169.348 36.0638 171.248 33.5927ZM169.06 25.1819C169.06 26.0407 168.913 29.008 168.155 30.6048C167.412 32.1682 166.334 33.0682 164.066 33.0682C161.799 33.0682 160.708 32.1656 159.965 30.6048C159.204 29.008 159.06 26.0381 159.06 25.1819V20.6872C159.06 19.8283 159.207 16.861 159.965 15.2642C160.708 13.7008 161.799 12.8009 164.066 12.8009C166.334 12.8009 167.412 13.7034 168.155 15.2642C168.913 16.861 169.06 19.8309 169.06 20.6872V25.1819Z"
                fill="currentColor"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M126.047 9.1727C123.275 9.1727 120.456 9.82582 118.577 12.2095C117.533 13.5337 117.168 14.9222 117.049 17.1413H121.302C121.367 16.0973 121.542 15.1254 122.033 14.3977C122.778 13.292 124.19 12.8034 125.823 12.8034C128.065 12.8034 129.227 13.4257 129.85 15.0508C130.351 16.3622 130.323 18.301 130.307 19.3784C130.307 19.5378 130.302 19.6792 130.302 19.7949C130.302 19.7949 128.389 19.8129 127.713 19.8232C127.661 19.8232 127.6 19.8232 127.535 19.8232C125.383 19.8489 116.358 19.9518 116.358 28.3626C116.358 33.9604 119.538 36.7066 123.887 36.7066C127.093 36.7066 129.209 35.3927 130.521 34.0916V36.1821H134.532V18.6995C134.553 15.3748 134.511 13.7574 133.424 12.2223C131.513 9.52755 128.824 9.1727 126.052 9.1727H126.047ZM124.679 33.0785C121.68 33.0785 120.726 30.736 120.726 28.4603C120.726 23.7188 125.44 23.6005 126.435 23.5748H126.502C128.075 23.5285 130.294 23.4694 130.294 23.4694C130.294 23.4694 130.325 25.1124 130.325 27.2775C130.325 29.4426 128.469 33.0785 124.676 33.0785H124.679Z"
                fill="currentColor"
            />
        </svg>
    )
}

export default ElgatoLogo
