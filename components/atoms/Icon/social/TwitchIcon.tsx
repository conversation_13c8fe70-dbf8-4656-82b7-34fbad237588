import { useTranslation } from 'next-i18next'

const TwitchIcon = ({ ...props }) => {
    const { t } = useTranslation(['common'])
    return (
        <svg
            width="34"
            height="34"
            viewBox="0 0 34 34"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <path
                d="M24.4374 10.0938H22.3124V16.4688H24.4374V10.0938Z"
                fill="currentColor"
            />
            <path
                d="M16.4689 10.0938H18.5939V16.4688H16.4689V10.0938Z"
                fill="currentColor"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M4.25 9.5625L9.5625 4.25H29.75V19.125L20.1875 28.6875H15.9375L10.625 34V28.6875H4.25V9.5625ZM23.375 22.3125L27.625 18.0625V6.375H10.625L10.6249 22.3125H15.4061V26.0313L19.125 22.3125L23.375 22.3125Z"
                fill="currentColor"
            />
        </svg>
    )
}

export default TwitchIcon
