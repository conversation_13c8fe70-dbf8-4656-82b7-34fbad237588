const JogwheelIcon = ({ ...props }) => {
    return (
        <svg
            width="72"
            height="72"
            viewBox="0 0 72 72"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <path
                d="M33 21C32.4477 21 32 21.4477 32 22V24.3629C30.8477 24.5747 29.7267 24.8762 28.6445 25.26L27.464 23.2153C27.3314 22.9856 27.113 22.818 26.8568 22.7494C26.6006 22.6808 26.3277 22.7167 26.098 22.8493L20.9019 25.8493C20.4236 26.1254 20.2597 26.737 20.5358 27.2153L21.719 29.2647C20.838 30.0172 20.0171 30.8381 19.2646 31.7191L17.2153 30.536C16.737 30.2598 16.1254 30.4237 15.8493 30.902L12.8493 36.0981C12.7167 36.3278 12.6808 36.6008 12.7494 36.857C12.8181 37.1131 12.9856 37.3316 13.2153 37.4642L15.2599 38.6446C14.8972 39.6673 14.608 40.7248 14.3986 41.8106C14.294 42.3529 14.6488 42.8773 15.1911 42.9819C15.7334 43.0865 16.2578 42.7317 16.3624 42.1894C16.6035 40.9389 16.9612 39.7301 17.4235 38.5745C17.6082 38.1129 17.4257 37.5856 16.9951 37.337L15.0814 36.2321L17.0814 32.768L18.9992 33.8753C19.4295 34.1237 19.9769 34.0185 20.2845 33.6284C21.2635 32.3866 22.3865 31.2635 23.6283 30.2846C24.0184 29.977 24.1236 29.4295 23.8752 28.9993L22.7679 27.0814L26.232 25.0814L27.3369 26.9951C27.5855 27.4257 28.1128 27.6083 28.5744 27.4236C30.0209 26.8448 31.5509 26.4301 33.1416 26.2026C33.6342 26.1321 34 25.7102 34 25.2127V23H38V25.2127C38 25.7102 38.3658 26.1321 38.8584 26.2026C40.4491 26.4301 41.979 26.8448 43.4255 27.4235C43.8871 27.6082 44.4144 27.4257 44.663 26.9951L45.7679 25.0814L49.232 27.0814L48.1247 28.9992C47.8763 29.4295 47.9815 29.9769 48.3717 30.2845C49.6134 31.2635 50.7365 32.3865 51.7155 33.6283C52.0231 34.0184 52.5705 34.1236 53.0008 33.8752L54.9187 32.7679L56.9187 36.232L55.0049 37.3369C54.5743 37.5855 54.3917 38.1128 54.5764 38.5744C55.0388 39.73 55.3965 40.9389 55.6376 42.1894C55.7422 42.7317 56.2666 43.0865 56.8089 42.9819C57.3512 42.8773 57.706 42.3529 57.6014 41.8106C57.392 40.7248 57.1028 39.6673 56.74 38.6445L58.7847 37.464C59.0144 37.3314 59.182 37.113 59.2506 36.8568C59.3192 36.6006 59.2833 36.3277 59.1507 36.098L56.1507 30.9019C56.0181 30.6722 55.7997 30.5046 55.5435 30.4359C55.2873 30.3673 55.0144 30.4032 54.7847 30.5358L52.7353 31.719C51.9828 30.838 51.1619 30.0171 50.2809 29.2646L51.464 27.2153C51.7402 26.737 51.5763 26.1254 51.098 25.8493L45.9019 22.8493C45.6722 22.7167 45.3992 22.6808 45.143 22.7494C44.8869 22.818 44.6684 22.9856 44.5358 23.2153L43.3554 25.2599C42.2733 24.8762 41.1523 24.5747 40 24.3629V22C40 21.4477 39.5523 21 39 21H33Z"
                fill="currentColor"
            />
            <path
                d="M36 34C30.6623 34 26.1362 37.4856 24.5785 42.3074C24.4088 42.8329 23.8451 43.1214 23.3196 42.9516C22.794 42.7818 22.5056 42.2181 22.6754 41.6926C24.4919 36.0693 29.7694 32 36 32C42.2305 32 47.508 36.0693 49.3245 41.6926C49.4943 42.2181 49.2059 42.7818 48.6804 42.9516C48.1548 43.1214 47.5912 42.8329 47.4214 42.3074C45.8637 37.4856 41.3376 34 36 34Z"
                fill="currentColor"
            />
            <path
                d="M6 46C6 45.4477 6.44772 45 7 45H65C65.5523 45 66 45.4477 66 46C66 46.5523 65.5523 47 65 47H7C6.44772 47 6 46.5523 6 46Z"
                fill="currentColor"
            />
        </svg>
    )
}

export default JogwheelIcon
