const StereoLineIn = ({ ...props }) => {
    return (
        <svg
            width="31"
            height="30"
            viewBox="0 0 31 30"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <path
                d="M12 5C6.47715 5 2 9.47715 2 15C2 20.5228 6.47715 25 12 25C16.658 24.9999 20.5719 21.8147 21.6836 17.5039L20.8154 17.501C20.6577 17.5013 20.5077 17.5019 20.3867 17.5029C19.31 21.1156 15.9626 23.7499 12 23.75C7.16751 23.75 3.25 19.8325 3.25 15C3.25 10.1675 7.16751 6.25 12 6.25C15.9662 6.25007 19.3162 8.88899 20.3896 12.5068L20.8164 12.502C21.1313 12.5001 21.4783 12.5007 21.6855 12.5029C20.5765 8.18842 16.6606 5.00007 12 5ZM24.3174 9.55762C24.0733 9.31354 23.6767 9.31354 23.4326 9.55762C23.2157 9.77454 23.1917 10.1119 23.3604 10.3555L23.4326 10.4424L27.3662 14.375H18.875C18.5298 14.375 18.25 14.6548 18.25 15C18.25 15.3068 18.4711 15.5623 18.7627 15.6152L18.875 15.625H27.3662L23.4326 19.5576C23.2157 19.7745 23.1917 20.1119 23.3604 20.3555L23.4326 20.4424C23.6495 20.6593 23.9869 20.6833 24.2305 20.5146L24.3174 20.4424L29.3691 15.3828L29.417 15.3125L29.4531 15.2383L29.4795 15.1602L29.4932 15.0928L29.498 15.0537L29.5 15L29.4932 14.9072L29.4795 14.8398L29.4531 14.7607L29.3896 14.6445L29.332 14.5732L24.3174 9.55762ZM12 10C9.23858 10 7 12.2386 7 15C7 17.7614 9.23858 20 12 20C14.7614 20 17 17.7614 17 15C17 12.2386 14.7614 10 12 10ZM12 11.25C14.0711 11.25 15.75 12.9289 15.75 15C15.75 17.0711 14.0711 18.75 12 18.75C9.92893 18.75 8.25 17.0711 8.25 15C8.25 12.9289 9.92893 11.25 12 11.25ZM12 12.5C10.6193 12.5 9.5 13.6193 9.5 15C9.5 16.3807 10.6193 17.5 12 17.5C13.3807 17.5 14.5 16.3807 14.5 15C14.5 13.6193 13.3807 12.5 12 12.5Z"
                fill="currentColor"
            />
        </svg>
    )
}

export default StereoLineIn
