const MultiplePurposeIcon = ({ ...props }) => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="72"
            height="72"
            viewBox="0 0 72 72"
            fill="none"
            {...props}
        >
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M18.8284 10C18.5632 10 18.3089 10.1054 18.1213 10.2929L16.2929 12.1213C15.7303 12.6839 14.9672 13 14.1716 13H11C10.4477 13 10 13.4477 10 14V27C10 27.5523 10.4477 28 11 28H31C31.5523 28 32 27.5523 32 27V14C32 13.4477 31.5523 13 31 13H27.8284C27.0328 13 26.2697 12.6839 25.7071 12.1213L23.8787 10.2929C23.6911 10.1054 23.4368 10 23.1716 10H18.8284ZM16.7071 8.87868C17.2697 8.31607 18.0328 8 18.8284 8H23.1716C23.9672 8 24.7303 8.31607 25.2929 8.87868L27.1213 10.7071C27.3089 10.8946 27.5632 11 27.8284 11H31C32.6569 11 34 12.3431 34 14V27C34 27.8084 33.6803 28.5421 33.1604 29.0816L36.5774 35H46V47C46 48.6569 47.3431 50 49 50H59C60.6569 50 62 48.6569 62 47V36C62 35.4477 62.4477 35 63 35C63.5523 35 64 35.4477 64 36V47C64 49.7614 61.7614 52 59 52H49C46.2386 52 44 49.7614 44 47V37H36.5774L33.0534 43.1036C35.4774 45.2993 37 48.4718 37 52C37 58.6274 31.6274 64 25 64C18.3726 64 13 58.6274 13 52C13 45.3726 18.3726 40 25 40C27.3744 40 29.5878 40.6896 31.4507 41.8795L34.8453 36L31.3683 29.9776C31.2476 29.9924 31.1247 30 31 30H11C9.34315 30 8 28.6569 8 27V14C8 12.3431 9.34315 11 11 11H14.1716C14.4368 11 14.6911 10.8946 14.8787 10.7071L16.7071 8.87868ZM21 16C18.7909 16 17 17.7909 17 20C17 22.2091 18.7909 24 21 24C23.2091 24 25 22.2091 25 20C25 17.7909 23.2091 16 21 16ZM15 20C15 16.6863 17.6863 14 21 14C24.3137 14 27 16.6863 27 20C27 23.3137 24.3137 26 21 26C17.6863 26 15 23.3137 15 20ZM47 21C47 19.3431 48.3431 18 50 18H58C59.6569 18 61 19.3431 61 21V46C61 47.6569 59.6569 49 58 49H50C48.3431 49 47 47.6569 47 46V21ZM50 20C49.4477 20 49 20.4477 49 21V36H59V21C59 20.4477 58.5523 20 58 20H50ZM59 38H49V46C49 46.5523 49.4477 47 50 47H58C58.5523 47 59 46.5523 59 46V38ZM54 41C53.4477 41 53 41.4477 53 42C53 42.5523 53.4477 43 54 43C54.5523 43 55 42.5523 55 42C55 41.4477 54.5523 41 54 41ZM51 42C51 40.3431 52.3431 39 54 39C55.6569 39 57 40.3431 57 42C57 43.6569 55.6569 45 54 45C52.3431 45 51 43.6569 51 42ZM25 42C19.4772 42 15 46.4772 15 52C15 57.5228 19.4772 62 25 62C30.5228 62 35 57.5228 35 52C35 46.4772 30.5228 42 25 42ZM25 47C22.2386 47 20 49.2386 20 52C20 54.7614 22.2386 57 25 57C27.7614 57 30 54.7614 30 52C30 49.2386 27.7614 47 25 47ZM18 52C18 48.134 21.134 45 25 45C28.866 45 32 48.134 32 52C32 55.866 28.866 59 25 59C21.134 59 18 55.866 18 52Z"
                fill="currentColor"
            />
        </svg>
    )
}

export default MultiplePurposeIcon
