const UsbCSmallIcon = ({ ...props }) => {
    return (
        <svg
            width="31"
            height="30"
            viewBox="0 0 31 30"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <rect
                x="5.25"
                y="13.75"
                width="20"
                height="2.5"
                rx="1.25"
                fill="currentColor"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M24.0007 12.9163H6.50065C5.35006 12.9163 4.41732 13.8491 4.41732 14.9997C4.41732 16.1503 5.35006 17.083 6.50065 17.083H24.0007C25.1512 17.083 26.084 16.1503 26.084 14.9997C26.084 13.8491 25.1512 12.9163 24.0007 12.9163ZM6.50065 12.083C4.88982 12.083 3.58398 13.3888 3.58398 14.9997C3.58398 16.6105 4.88982 17.9163 6.50065 17.9163H24.0007C25.6115 17.9163 26.9173 16.6105 26.9173 14.9997C26.9173 13.3888 25.6115 12.083 24.0007 12.083H6.50065Z"
                fill="currentColor"
            />
        </svg>
    )
}

export default UsbCSmallIcon
