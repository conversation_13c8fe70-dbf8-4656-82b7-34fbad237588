const IconGame = ({ ...props }) => {
    return (
        <svg
            width="32"
            height="32"
            viewBox="0 0 32 32"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M2.96086 29.0405C2.57034 28.65 2.57034 28.0168 2.96086 27.6263L27.6275 2.95964C28.0181 2.56912 28.6512 2.56912 29.0417 2.95964C29.4323 3.35017 29.4323 3.98333 29.0417 4.37385L23.9783 9.43726C26.4116 11.2328 28.0919 13.4795 28.9659 14.824C29.4352 15.5459 29.4352 16.4542 28.9659 17.1761C27.3354 19.6846 22.8979 25.3334 16.0013 25.3334C13.617 25.3334 11.5266 24.6582 9.74453 23.6711L4.37508 29.0405C3.98455 29.431 3.35139 29.431 2.96086 29.0405ZM13.0037 20.4119C13.858 20.9935 14.89 21.3334 16.0013 21.3334C18.9468 21.3334 21.3347 18.9456 21.3347 16.0001C21.3347 14.8887 20.9947 13.8567 20.4132 13.0024L18.9574 14.4582C19.1984 14.9193 19.3347 15.4438 19.3347 16.0001C19.3347 17.841 17.8423 19.3334 16.0013 19.3334C15.445 19.3334 14.9205 19.1971 14.4595 18.9561L13.0037 20.4119Z"
                fill="currentColor"
            />
            <path
                d="M10.7617 17.0009L6.48019 21.2824C4.86779 19.7912 3.71175 18.2146 3.03675 17.1761C2.5675 16.4542 2.5675 15.5459 3.03675 14.824C4.66729 12.3156 9.10476 6.66675 16.0013 6.66675C17.5703 6.66675 19.012 6.95911 20.3223 7.44033L17.0021 10.7605C16.678 10.699 16.3434 10.6667 16.0013 10.6667C13.0558 10.6667 10.668 13.0546 10.668 16.0001C10.668 16.3422 10.7002 16.6767 10.7617 17.0009Z"
                fill="currentColor"
            />
        </svg>
    )
}

export default IconGame
