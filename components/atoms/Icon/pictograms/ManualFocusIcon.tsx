const ManualFocusIcon = ({ ...props }) => {
    return (
        <svg
            width="72"
            height="72"
            viewBox="0 0 72 72"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <path
                d="M36 7.99997C35.2605 7.99997 34.5275 8.02866 33.8021 8.08502C33.2514 8.12779 32.8397 8.60884 32.8825 9.15947C32.9253 9.71009 33.4063 10.1218 33.957 10.079C34.6309 10.0267 35.3122 9.99997 36 9.99997C36.6878 9.99997 37.3691 10.0267 38.043 10.079C38.5937 10.1218 39.0747 9.71009 39.1175 9.15947C39.1603 8.60884 38.7486 8.12779 38.198 8.08502C37.4725 8.02866 36.7395 7.99997 36 7.99997Z"
                fill="currentColor"
            />
            <path
                d="M27.9625 11.2658C28.4878 11.0953 28.7753 10.5312 28.6047 10.0059C28.4342 9.4806 27.8701 9.19304 27.3448 9.36362C25.9394 9.81998 24.5828 10.3844 23.2849 11.0471C22.793 11.2982 22.5978 11.9005 22.8489 12.3924C23.1001 12.8843 23.7024 13.0795 24.1943 12.8283C25.3992 12.2132 26.6583 11.6893 27.9625 11.2658Z"
                fill="currentColor"
            />
            <path
                d="M44.6552 9.36362C44.13 9.19305 43.5658 9.4806 43.3953 10.0059C43.2247 10.5312 43.5123 11.0953 44.0375 11.2658C45.3417 11.6893 46.6008 12.2132 47.8057 12.8283C48.2976 13.0795 48.8999 12.8843 49.1511 12.3924C49.4022 11.9005 49.207 11.2982 48.7151 11.0471C47.4172 10.3844 46.0606 9.81999 44.6552 9.36362Z"
                fill="currentColor"
            />
            <path
                d="M19.1145 16.2286C19.5343 15.8697 19.5837 15.2385 19.2248 14.8187C18.8659 14.3989 18.2347 14.3495 17.8149 14.7084C16.7002 15.6613 15.6613 16.7002 14.7084 17.8149C14.3496 18.2347 14.3989 18.8659 14.8187 19.2248C15.2385 19.5837 15.8698 19.5343 16.2286 19.1145C17.1139 18.079 18.079 17.1138 19.1145 16.2286Z"
                fill="currentColor"
            />
            <path
                d="M54.1851 14.7084C53.7653 14.3495 53.1341 14.3989 52.7752 14.8187C52.4163 15.2385 52.4657 15.8697 52.8855 16.2286C53.921 17.1138 54.8861 18.079 55.7714 19.1145C56.1302 19.5343 56.7615 19.5837 57.1813 19.2248C57.6011 18.8659 57.6504 18.2347 57.2916 17.8149C56.3387 16.7002 55.2998 15.6613 54.1851 14.7084Z"
                fill="currentColor"
            />
            <path
                d="M12.8284 24.1943C13.0795 23.7024 12.8843 23.1 12.3925 22.8489C11.9006 22.5978 11.2982 22.793 11.0471 23.2848C10.3845 24.5828 9.82002 25.9393 9.36365 27.3447C9.19308 27.87 9.48063 28.4341 10.0059 28.6047C10.5312 28.7753 11.0953 28.4877 11.2659 27.9624C11.6894 26.6582 12.2132 25.3991 12.8284 24.1943Z"
                fill="currentColor"
            />
            <path
                d="M60.9529 23.2848C60.7018 22.793 60.0994 22.5978 59.6076 22.8489C59.1157 23.1 58.9205 23.7024 59.1716 24.1943C59.7868 25.3991 60.3106 26.6582 60.7341 27.9624C60.9047 28.4877 61.4688 28.7753 61.9941 28.6047C62.5194 28.4341 62.8069 27.87 62.6364 27.3447C62.18 25.9393 61.6156 24.5828 60.9529 23.2848Z"
                fill="currentColor"
            />
            <path
                d="M10.079 33.9569C10.1218 33.4063 9.71012 32.9253 9.1595 32.8825C8.60887 32.8397 8.12782 33.2514 8.08505 33.802C8.02869 34.5275 8 35.2605 8 36C8 36.7395 8.02869 37.4725 8.08505 38.1979C8.12782 38.7485 8.60887 39.1602 9.1595 39.1175C9.71012 39.0747 10.1218 38.5936 10.079 38.043C10.0267 37.3691 10 36.6877 10 36C10 35.3122 10.0267 34.6309 10.079 33.9569Z"
                fill="currentColor"
            />
            <path
                d="M11.2659 44.0375C11.0953 43.5122 10.5312 43.2247 10.0059 43.3952C9.48063 43.5658 9.19308 44.1299 9.36365 44.6552C9.82001 46.0606 10.3845 47.4172 11.0471 48.7151C11.2982 49.207 11.9006 49.4022 12.3925 49.151C12.8843 48.8999 13.0795 48.2976 12.8284 47.8057C12.2132 46.6008 11.6894 45.3417 11.2659 44.0375Z"
                fill="currentColor"
            />
            <path
                d="M62.6364 44.6552C62.8069 44.1299 62.5194 43.5658 61.9941 43.3952C61.4688 43.2247 60.9047 43.5122 60.7341 44.0375C60.3106 45.3417 59.7868 46.6008 59.1716 47.8057C58.9205 48.2976 59.1157 48.8999 59.6076 49.151C60.0994 49.4022 60.7018 49.207 60.9529 48.7151C61.6156 47.4172 62.18 46.0606 62.6364 44.6552Z"
                fill="currentColor"
            />
            <path
                d="M16.2286 52.8855C15.8698 52.4657 15.2385 52.4163 14.8187 52.7752C14.3989 53.134 14.3496 53.7653 14.7084 54.1851C15.6613 55.2997 16.7002 56.3386 17.8149 57.2915C18.2347 57.6504 18.8659 57.601 19.2248 57.1812C19.5837 56.7614 19.5343 56.1302 19.1145 55.7713C18.079 54.8861 17.1139 53.921 16.2286 52.8855Z"
                fill="currentColor"
            />
            <path
                d="M57.2916 54.1851C57.6504 53.7653 57.6011 53.134 57.1813 52.7752C56.7615 52.4163 56.1302 52.4657 55.7714 52.8855C54.8861 53.921 53.921 54.8861 52.8855 55.7713C52.4657 56.1302 52.4163 56.7614 52.7752 57.1812C53.1341 57.601 53.7653 57.6504 54.1851 57.2915C55.2998 56.3386 56.3387 55.2997 57.2916 54.1851Z"
                fill="currentColor"
            />
            <path
                d="M24.1943 59.1716C23.7024 58.9205 23.1001 59.1156 22.8489 59.6075C22.5978 60.0994 22.793 60.7017 23.2849 60.9529C24.5828 61.6155 25.9394 62.18 27.3448 62.6363C27.8701 62.8069 28.4342 62.5193 28.6047 61.9941C28.7753 61.4688 28.4878 60.9047 27.9625 60.7341C26.6583 60.3106 25.3992 59.7867 24.1943 59.1716Z"
                fill="currentColor"
            />
            <path
                d="M48.7151 60.9529C49.207 60.7017 49.4022 60.0994 49.1511 59.6075C48.8999 59.1156 48.2976 58.9205 47.8057 59.1716C46.6008 59.7867 45.3417 60.3106 44.0375 60.7341C43.5123 60.9047 43.2247 61.4688 43.3953 61.9941C43.5658 62.5194 44.1299 62.8069 44.6552 62.6363C46.0606 62.18 47.4172 61.6155 48.7151 60.9529Z"
                fill="currentColor"
            />
            <path
                d="M33.957 61.9209C33.4063 61.8782 32.9253 62.2899 32.8825 62.8405C32.8397 63.3911 33.2514 63.8722 33.8021 63.9149C34.5275 63.9713 35.2605 64 36 64C36.7395 64 37.4725 63.9713 38.1979 63.9149C38.7486 63.8722 39.1603 63.3911 39.1175 62.8405C39.0747 62.2899 38.5937 61.8782 38.043 61.9209C37.3691 61.9733 36.6878 62 36 62C35.3122 62 34.6309 61.9733 33.957 61.9209Z"
                fill="currentColor"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M36 29C32.134 29 29 32.134 29 36C29 39.866 32.134 43 36 43C39.866 43 43 39.866 43 36C43 32.134 39.866 29 36 29ZM31 36C31 33.2386 33.2386 31 36 31C38.7614 31 41 33.2386 41 36C41 38.7614 38.7614 41 36 41C33.2386 41 31 38.7614 31 36Z"
                fill="currentColor"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M25 36C25 29.9248 29.9249 25 36 25C42.0751 25 47 29.9248 47 36C47 42.0751 42.0751 47 36 47C29.9249 47 25 42.0751 25 36ZM36 27C31.0294 27 27 31.0294 27 36C27 40.9705 31.0294 45 36 45C40.9706 45 45 40.9705 45 36C45 31.0294 40.9706 27 36 27Z"
                fill="currentColor"
            />
            <path
                d="M50.4049 39.9998H48.9349V32.7798H51.3649L52.4949 37.6898H52.5149L53.7149 32.7798H56.0649V39.9998H54.5949V34.4298H54.5749L53.1849 39.9998H51.7449L50.4249 34.4298H50.4049V39.9998Z"
                fill="currentColor"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M36 12C22.7452 12 12 22.7451 12 36C12 49.2548 22.7452 60 36 60C49.2548 60 60 49.2548 60 36C60 22.7451 49.2548 12 36 12ZM14 36C14 23.8497 23.8497 14 36 14C48.1503 14 58 23.8497 58 36C58 48.1502 48.1503 58 36 58C23.8497 58 14 48.1502 14 36Z"
                fill="currentColor"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M70 31C70 30.612 69.7755 30.259 69.4242 30.0944C69.0728 29.9298 68.6579 29.9834 68.3598 30.2318L62.3598 35.2318C62.1318 35.4218 62 35.7032 62 36C62 36.2968 62.1318 36.5782 62.3598 36.7682L68.3598 41.7682C68.6579 42.0166 69.0728 42.0702 69.4242 41.9056C69.7755 41.741 70 41.388 70 41V31ZM68 38.8649L64.5621 36L68 33.135V38.8649Z"
                fill="currentColor"
            />
        </svg>
    )
}

export default ManualFocusIcon
