const UsbCGen1 = ({ ...props }) => {
    return (
        <svg
            width="120"
            height="120"
            viewBox="0 0 120 120"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <rect
                x="39.1666"
                y="57.8334"
                width="41.6667"
                height="4.33333"
                rx="2.16667"
                stroke="currentColor"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M78.6666 55.5556H41.3333C38.8787 55.5556 36.8889 57.5455 36.8889 60.0001C36.8889 62.4547 38.8787 64.4445 41.3333 64.4445H78.6666C81.1212 64.4445 83.1111 62.4547 83.1111 60.0001C83.1111 57.5455 81.1212 55.5556 78.6666 55.5556ZM41.3333 53.7778C37.8969 53.7778 35.1111 56.5636 35.1111 60.0001C35.1111 63.4365 37.8969 66.2223 41.3333 66.2223H78.6666C82.1031 66.2223 84.8889 63.4365 84.8889 60.0001C84.8889 56.5636 82.1031 53.7778 78.6666 53.7778H41.3333Z"
                fill="currentColor"
            />
        </svg>
    )
}

export default UsbCGen1
