const PressIcon = ({ ...props }) => {
    return (
        <svg
            width="72"
            height="72"
            viewBox="0 0 72 72"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <path
                d="M17 8C15.3431 8 14 9.34315 14 11V53C14 54.6569 15.3431 56 17 56H19C19.5523 56 20 55.5523 20 55C20 54.4477 19.5523 54 19 54H17C16.4477 54 16 53.5523 16 53V11C16 10.4477 16.4477 10 17 10H47C47.5523 10 48 10.4477 48 11V13C48 13.5523 48.4477 14 49 14C49.5523 14 50 13.5523 50 13V11C50 9.34315 48.6569 8 47 8H17Z"
                fill="currentColor"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M30 21C28.3431 21 27 22.3431 27 24V28C27 29.6569 28.3431 31 30 31H34C35.6569 31 37 29.6569 37 28V24C37 22.3431 35.6569 21 34 21H30ZM29 24C29 23.4477 29.4477 23 30 23H34C34.5523 23 35 23.4477 35 24V28C35 28.5523 34.5523 29 34 29H30C29.4477 29 29 28.5523 29 28V24Z"
                fill="currentColor"
            />
            <path
                d="M39 22C39 21.4477 39.4477 21 40 21L52 21C52.5523 21 53 21.4477 53 22C53 22.5523 52.5523 23 52 23L40 23C39.4477 23 39 22.5523 39 22Z"
                fill="currentColor"
            />
            <path
                d="M28 35C27.4477 35 27 35.4477 27 36C27 36.5523 27.4477 37 28 37H52C52.5523 37 53 36.5523 53 36C53 35.4477 52.5523 35 52 35H28Z"
                fill="currentColor"
            />
            <path
                d="M39 26C39 25.4477 39.4477 25 40 25L52 25C52.5523 25 53 25.4477 53 26C53 26.5523 52.5523 27 52 27L40 27C39.4477 27 39 26.5523 39 26Z"
                fill="currentColor"
            />
            <path
                d="M28 39C27.4477 39 27 39.4477 27 40C27 40.5523 27.4477 41 28 41H52C52.5523 41 53 40.5523 53 40C53 39.4477 52.5523 39 52 39H28Z"
                fill="currentColor"
            />
            <path
                d="M27 48C27 47.4477 27.4477 47 28 47H52C52.5523 47 53 47.4477 53 48C53 48.5523 52.5523 49 52 49H28C27.4477 49 27 48.5523 27 48Z"
                fill="currentColor"
            />
            <path
                d="M28 55C27.4477 55 27 55.4477 27 56C27 56.5523 27.4477 57 28 57H45C45.5523 57 46 56.5523 46 56C46 55.4477 45.5523 55 45 55H28Z"
                fill="currentColor"
            />
            <path
                d="M39 30C39 29.4477 39.4477 29 40 29L52 29C52.5523 29 53 29.4477 53 30C53 30.5523 52.5523 31 52 31L40 31C39.4477 31 39 30.5523 39 30Z"
                fill="currentColor"
            />
            <path
                d="M28 43C27.4477 43 27 43.4477 27 44C27 44.5523 27.4477 45 28 45H52C52.5523 45 53 44.5523 53 44C53 43.4477 52.5523 43 52 43H28Z"
                fill="currentColor"
            />
            <path
                d="M27 52C27 51.4477 27.4477 51 28 51H52C52.5523 51 53 51.4477 53 52C53 52.5523 52.5523 53 52 53H28C27.4477 53 27 52.5523 27 52Z"
                fill="currentColor"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M25 16C23.3431 16 22 17.3431 22 19V61C22 62.6569 23.3431 64 25 64H55C56.6569 64 58 62.6569 58 61V19C58 17.3431 56.6569 16 55 16H25ZM24 19C24 18.4477 24.4477 18 25 18H55C55.5523 18 56 18.4477 56 19V61C56 61.5523 55.5523 62 55 62H25C24.4477 62 24 61.5523 24 61V19Z"
                fill="currentColor"
            />
        </svg>
    )
}

export default PressIcon
