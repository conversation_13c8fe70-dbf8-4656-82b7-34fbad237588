const PowerKeyIcon = ({ ...props }) => {
    return (
        <svg
            width="72"
            height="72"
            viewBox="0 0 72 72"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <path
                d="M28.2218 26.8076C28.6124 27.1981 28.6124 27.8313 28.2218 28.2218C26.6834 29.7602 25.6358 31.7202 25.2114 33.854C24.7869 35.9878 25.0048 38.1995 25.8373 40.2095C26.6699 42.2195 28.0798 43.9375 29.8887 45.1462C31.6977 46.3549 33.8244 47 36 47C38.1756 47 40.3023 46.3549 42.1113 45.1462C43.9202 43.9375 45.3301 42.2195 46.1627 40.2095C46.9952 38.1995 47.2131 35.9878 46.7886 33.854C46.3642 31.7202 45.3166 29.7602 43.7782 28.2218C43.3876 27.8313 43.3876 27.1981 43.7782 26.8076C44.1687 26.4171 44.8019 26.4171 45.1924 26.8076C47.0105 28.6257 48.2486 30.9421 48.7502 33.4638C49.2518 35.9856 48.9944 38.5994 48.0104 40.9749C47.0265 43.3503 45.3603 45.3806 43.2224 46.8091C41.0846 48.2376 38.5712 49 36 49C33.4288 49 30.9154 48.2376 28.7776 46.8091C26.6397 45.3806 24.9735 43.3503 23.9896 40.9749C23.0056 38.5994 22.7482 35.9856 23.2498 33.4638C23.7514 30.9421 24.9895 28.6257 26.8076 26.8076C27.1981 26.4171 27.8313 26.4171 28.2218 26.8076Z"
                fill="currentColor"
            />
            <path
                d="M37 22C37 21.4477 36.5523 21 36 21C35.4477 21 35 21.4477 35 22V36C35 36.5523 35.4477 37 36 37C36.5523 37 37 36.5523 37 36V22Z"
                fill="currentColor"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M12 20C12 15.5817 15.5817 12 20 12H52C56.4183 12 60 15.5817 60 20V52C60 56.4183 56.4183 60 52 60H20C15.5817 60 12 56.4183 12 52V20ZM20 14C16.6863 14 14 16.6863 14 20V52C14 55.3137 16.6863 58 20 58H52C55.3137 58 58 55.3137 58 52V20C58 16.6863 55.3137 14 52 14H20Z"
                fill="currentColor"
            />
        </svg>
    )
}

export default PowerKeyIcon
