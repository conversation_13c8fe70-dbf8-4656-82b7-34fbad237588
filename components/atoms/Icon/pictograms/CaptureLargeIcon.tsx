const CaptureLargeIcon = ({ ...props }) => {
    return (
        <svg
            width="120"
            height="120"
            viewBox="0 0 120 120"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M26.6667 38.3335C27.5871 38.3335 28.3333 39.0797 28.3333 40.0002V80.0002C28.3333 80.9206 27.5871 81.6668 26.6667 81.6668C25.7462 81.6668 25 80.9206 25 80.0002V40.0002C25 39.0797 25.7462 38.3335 26.6667 38.3335Z"
                fill="currentColor"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M93.3337 38.3335C94.2541 38.3335 95.0003 39.0797 95.0003 40.0002V80.0002C95.0003 80.9206 94.2541 81.6668 93.3337 81.6668C92.4132 81.6668 91.667 80.9206 91.667 80.0002V40.0002C91.667 39.0797 92.4132 38.3335 93.3337 38.3335Z"
                fill="currentColor"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M13.3335 30.0002C13.3335 29.0797 14.0797 28.3335 15.0002 28.3335H105C105.921 28.3335 106.667 29.0797 106.667 30.0002C106.667 30.9206 105.921 31.6668 105 31.6668H15.0002C14.0797 31.6668 13.3335 30.9206 13.3335 30.0002Z"
                fill="currentColor"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M13.3335 35.0002C13.3335 34.0797 14.0797 33.3335 15.0002 33.3335H18.2144C19.1349 33.3335 19.8811 34.0797 19.8811 35.0002C19.8811 35.9206 19.1349 36.6668 18.2144 36.6668H15.0002C14.0797 36.6668 13.3335 35.9206 13.3335 35.0002ZM22.9764 35.0002C22.9764 34.0797 23.7225 33.3335 24.643 33.3335H31.0716C31.9921 33.3335 32.7383 34.0797 32.7383 35.0002C32.7383 35.9206 31.9921 36.6668 31.0716 36.6668H24.643C23.7225 36.6668 22.9764 35.9206 22.9764 35.0002ZM35.8335 35.0002C35.8335 34.0797 36.5797 33.3335 37.5002 33.3335H43.9287C44.8492 33.3335 45.5954 34.0797 45.5954 35.0002C45.5954 35.9206 44.8492 36.6668 43.9287 36.6668H37.5002C36.5797 36.6668 35.8335 35.9206 35.8335 35.0002ZM48.6906 35.0002C48.6906 34.0797 49.4368 33.3335 50.3573 33.3335H56.7859C57.7064 33.3335 58.4525 34.0797 58.4525 35.0002C58.4525 35.9206 57.7064 36.6668 56.7859 36.6668H50.3573C49.4368 36.6668 48.6906 35.9206 48.6906 35.0002ZM61.5478 35.0002C61.5478 34.0797 62.294 33.3335 63.2145 33.3335H69.643C70.5635 33.3335 71.3097 34.0797 71.3097 35.0002C71.3097 35.9206 70.5635 36.6668 69.643 36.6668H63.2145C62.294 36.6668 61.5478 35.9206 61.5478 35.0002ZM74.4049 35.0002C74.4049 34.0797 75.1511 33.3335 76.0716 33.3335H82.5002C83.4206 33.3335 84.1668 34.0797 84.1668 35.0002C84.1668 35.9206 83.4206 36.6668 82.5002 36.6668H76.0716C75.1511 36.6668 74.4049 35.9206 74.4049 35.0002ZM87.2621 35.0002C87.2621 34.0797 88.0083 33.3335 88.9287 33.3335H95.3573C96.2778 33.3335 97.024 34.0797 97.024 35.0002C97.024 35.9206 96.2778 36.6668 95.3573 36.6668H88.9287C88.0083 36.6668 87.2621 35.9206 87.2621 35.0002ZM100.119 35.0002C100.119 34.0797 100.865 33.3335 101.786 33.3335H105C105.921 33.3335 106.667 34.0797 106.667 35.0002C106.667 35.9206 105.921 36.6668 105 36.6668H101.786C100.865 36.6668 100.119 35.9206 100.119 35.0002Z"
                fill="currentColor"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M13.3335 40.0002C13.3335 39.0797 14.0797 38.3335 15.0002 38.3335H105C105.921 38.3335 106.667 39.0797 106.667 40.0002C106.667 40.9206 105.921 41.6668 105 41.6668H15.0002C14.0797 41.6668 13.3335 40.9206 13.3335 40.0002Z"
                fill="currentColor"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M13.3335 80.0002C13.3335 79.0797 14.0797 78.3335 15.0002 78.3335H105C105.921 78.3335 106.667 79.0797 106.667 80.0002C106.667 80.9206 105.921 81.6668 105 81.6668H15.0002C14.0797 81.6668 13.3335 80.9206 13.3335 80.0002Z"
                fill="currentColor"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M13.3335 85.0002C13.3335 84.0797 14.0797 83.3335 15.0002 83.3335H18.2144C19.1349 83.3335 19.8811 84.0797 19.8811 85.0002C19.8811 85.9206 19.1349 86.6668 18.2144 86.6668H15.0002C14.0797 86.6668 13.3335 85.9206 13.3335 85.0002ZM22.9764 85.0002C22.9764 84.0797 23.7225 83.3335 24.643 83.3335H31.0716C31.9921 83.3335 32.7383 84.0797 32.7383 85.0002C32.7383 85.9206 31.9921 86.6668 31.0716 86.6668H24.643C23.7225 86.6668 22.9764 85.9206 22.9764 85.0002ZM35.8335 85.0002C35.8335 84.0797 36.5797 83.3335 37.5002 83.3335H43.9287C44.8492 83.3335 45.5954 84.0797 45.5954 85.0002C45.5954 85.9206 44.8492 86.6668 43.9287 86.6668H37.5002C36.5797 86.6668 35.8335 85.9206 35.8335 85.0002ZM48.6906 85.0002C48.6906 84.0797 49.4368 83.3335 50.3573 83.3335H56.7859C57.7064 83.3335 58.4525 84.0797 58.4525 85.0002C58.4525 85.9206 57.7064 86.6668 56.7859 86.6668H50.3573C49.4368 86.6668 48.6906 85.9206 48.6906 85.0002ZM61.5478 85.0002C61.5478 84.0797 62.294 83.3335 63.2145 83.3335H69.643C70.5635 83.3335 71.3097 84.0797 71.3097 85.0002C71.3097 85.9206 70.5635 86.6668 69.643 86.6668H63.2145C62.294 86.6668 61.5478 85.9206 61.5478 85.0002ZM74.4049 85.0002C74.4049 84.0797 75.1511 83.3335 76.0716 83.3335H82.5002C83.4206 83.3335 84.1668 84.0797 84.1668 85.0002C84.1668 85.9206 83.4206 86.6668 82.5002 86.6668H76.0716C75.1511 86.6668 74.4049 85.9206 74.4049 85.0002ZM87.2621 85.0002C87.2621 84.0797 88.0083 83.3335 88.9287 83.3335H95.3573C96.2778 83.3335 97.024 84.0797 97.024 85.0002C97.024 85.9206 96.2778 86.6668 95.3573 86.6668H88.9287C88.0083 86.6668 87.2621 85.9206 87.2621 85.0002ZM100.119 85.0002C100.119 84.0797 100.865 83.3335 101.786 83.3335H105C105.921 83.3335 106.667 84.0797 106.667 85.0002C106.667 85.9206 105.921 86.6668 105 86.6668H101.786C100.865 86.6668 100.119 85.9206 100.119 85.0002Z"
                fill="currentColor"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M13.3335 90.0002C13.3335 89.0797 14.0797 88.3335 15.0002 88.3335H105C105.921 88.3335 106.667 89.0797 106.667 90.0002C106.667 90.9206 105.921 91.6668 105 91.6668H15.0002C14.0797 91.6668 13.3335 90.9206 13.3335 90.0002Z"
                fill="currentColor"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M51.667 52.9439C51.667 50.353 54.4936 48.7526 56.7153 50.0856L68.4759 57.142C70.6337 58.4367 70.6337 61.5639 68.4759 62.8586L56.7153 69.915C54.4936 71.248 51.667 69.6476 51.667 67.0566V52.9439ZM55.0003 52.944L55.0003 52.9439V67.0566L66.7609 60.0003L55.0003 52.944Z"
                fill="currentColor"
            />
        </svg>
    )
}

export default CaptureLargeIcon
