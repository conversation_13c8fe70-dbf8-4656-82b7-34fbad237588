const UserFriendlyIcon = ({ ...props }) => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="72"
            height="72"
            viewBox="0 0 72 72"
            fill="none"
            {...props}
        >
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M28.5107 48.8525C27.6459 46.4615 29.9615 44.1459 32.3525 45.0107L44.6543 49.4609C47.2911 50.4148 47.2943 54.1422 44.6592 55.1006L40.6553 56.5566C40.3773 56.6577 40.1577 56.8773 40.0566 57.1553L38.6006 61.1592C37.6422 63.7943 33.9148 63.7911 32.9609 61.1543L28.5107 48.8525ZM31.6719 46.8916C30.875 46.6036 30.1036 47.375 30.3916 48.1719L34.8408 60.4736L34.9111 60.6289C35.3109 61.3498 36.4222 61.2991 36.7217 60.4756L38.1777 56.4717C38.481 55.6377 39.1377 54.981 39.9717 54.6777L43.9756 53.2217C44.7991 52.9222 44.8498 51.8109 44.1289 51.4111L43.9736 51.3408L31.6719 46.8916Z"
                fill="currentColor"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M22.1543 30.0039C23.7394 30.0842 25 31.3949 25 33V34L24.9961 34.1543C24.9184 35.6883 23.6883 36.9184 22.1543 36.9961L22 37H21L20.8457 36.9961C19.3117 36.9184 18.0816 35.6883 18.0039 34.1543L18 34V33C18 31.3949 19.2606 30.0842 20.8457 30.0039L21 30H22L22.1543 30.0039ZM21 32C20.4477 32 20 32.4477 20 33V34C20 34.5523 20.4477 35 21 35H22C22.5523 35 23 34.5523 23 34V33C23 32.4477 22.5523 32 22 32H21Z"
                fill="currentColor"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M30.1543 30.0039C31.7394 30.0842 33 31.3949 33 33V34L32.9961 34.1543C32.9184 35.6883 31.6883 36.9184 30.1543 36.9961L30 37H29L28.8457 36.9961C27.3117 36.9184 26.0816 35.6883 26.0039 34.1543L26 34V33C26 31.3949 27.2606 30.0842 28.8457 30.0039L29 30H30L30.1543 30.0039ZM29 32C28.4477 32 28 32.4477 28 33V34C28 34.5523 28.4477 35 29 35H30C30.5523 35 31 34.5523 31 34V33C31 32.4477 30.5523 32 30 32H29Z"
                fill="currentColor"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M38.1543 30.0039C39.7394 30.0842 41 31.3949 41 33V34L40.9961 34.1543C40.9184 35.6883 39.6883 36.9184 38.1543 36.9961L38 37H37L36.8457 36.9961C35.3117 36.9184 34.0816 35.6883 34.0039 34.1543L34 34V33C34 31.3949 35.2606 30.0842 36.8457 30.0039L37 30H38L38.1543 30.0039ZM37 32C36.4477 32 36 32.4477 36 33V34C36 34.5523 36.4477 35 37 35H38C38.5523 35 39 34.5523 39 34V33C39 32.4477 38.5523 32 38 32H37Z"
                fill="currentColor"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M22.1543 22.0039C23.7394 22.0842 25 23.3949 25 25V26L24.9961 26.1543C24.9184 27.6883 23.6883 28.9184 22.1543 28.9961L22 29H21L20.8457 28.9961C19.3117 28.9184 18.0816 27.6883 18.0039 26.1543L18 26V25C18 23.3949 19.2606 22.0842 20.8457 22.0039L21 22H22L22.1543 22.0039ZM21 24C20.4477 24 20 24.4477 20 25V26C20 26.5523 20.4477 27 21 27H22C22.5523 27 23 26.5523 23 26V25C23 24.4477 22.5523 24 22 24H21Z"
                fill="currentColor"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M30.1543 22.0039C31.7394 22.0842 33 23.3949 33 25V26L32.9961 26.1543C32.9184 27.6883 31.6883 28.9184 30.1543 28.9961L30 29H29L28.8457 28.9961C27.3117 28.9184 26.0816 27.6883 26.0039 26.1543L26 26V25C26 23.3949 27.2606 22.0842 28.8457 22.0039L29 22H30L30.1543 22.0039ZM29 24C28.4477 24 28 24.4477 28 25V26C28 26.5523 28.4477 27 29 27H30C30.5523 27 31 26.5523 31 26V25C31 24.4477 30.5523 24 30 24H29Z"
                fill="currentColor"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M38.1543 22.0039C39.7394 22.0842 41 23.3949 41 25V26L40.9961 26.1543C40.9184 27.6883 39.6883 28.9184 38.1543 28.9961L38 29H37L36.8457 28.9961C35.3117 28.9184 34.0816 27.6883 34.0039 26.1543L34 26V25C34 23.3949 35.2606 22.0842 36.8457 22.0039L37 22H38L38.1543 22.0039ZM37 24C36.4477 24 36 24.4477 36 25V26C36 26.5523 36.4477 27 37 27H38C38.5523 27 39 26.5523 39 26V25C39 24.4477 38.5523 24 38 24H37Z"
                fill="currentColor"
            />
            <path
                d="M28 18C28.5523 18 29 18.4477 29 19C29 19.5523 28.5523 20 28 20H19C18.4477 20 18 19.5523 18 19C18 18.4477 18.4477 18 19 18H28Z"
                fill="currentColor"
            />
            <path
                d="M40 18C40.5523 18 41 18.4477 41 19C41 19.5523 40.5523 20 40 20H38C37.4477 20 37 19.5523 37 19C37 18.4477 37.4477 18 38 18H40Z"
                fill="currentColor"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M57 12C58.6569 12 60 13.3431 60 15V49L59.9961 49.1543C59.9158 50.7394 58.6051 52 57 52H49.3945C48.9635 51.9999 48.6145 51.6624 48.5273 51.2402C48.4055 50.6492 48.8087 50 49.4121 50H57C57.5523 50 58 49.5523 58 49V20H47V46.8096C47 47.4877 46.135 47.9242 45.5107 47.6592C45.2087 47.5309 45.0002 47.2403 45 46.9121V43H14V49C14 49.5523 14.4477 50 15 50H26.0977C26.5186 50.0002 26.8939 50.2643 27.0371 50.6602C27.2728 51.3119 26.7906 51.9997 26.0977 52H15C13.3949 52 12.0842 50.7394 12.0039 49.1543L12 49V15C12 13.3431 13.3431 12 15 12H57ZM15 14C14.4477 14 14 14.4477 14 15V41H45V14H15ZM47 18H58V15C58 14.4477 57.5523 14 57 14H47V18Z"
                fill="currentColor"
            />
            <path
                d="M55.1025 46.0049C55.6067 46.0562 56 46.4823 56 47C56 47.5177 55.6067 47.9438 55.1025 47.9951L55 48H50C49.4477 48 49 47.5523 49 47C49 46.4477 49.4477 46 50 46H55L55.1025 46.0049Z"
                fill="currentColor"
            />
            <path
                d="M55.1025 42.0049C55.6067 42.0562 56 42.4823 56 43C56 43.5177 55.6067 43.9438 55.1025 43.9951L55 44H50C49.4477 44 49 43.5523 49 43C49 42.4477 49.4477 42 50 42H55L55.1025 42.0049Z"
                fill="currentColor"
            />
            <path
                d="M55.1025 38.0049C55.6067 38.0562 56 38.4823 56 39C56 39.5177 55.6067 39.9438 55.1025 39.9951L55 40H50C49.4477 40 49 39.5523 49 39C49 38.4477 49.4477 38 50 38H55L55.1025 38.0049Z"
                fill="currentColor"
            />
            <path
                d="M55.1025 34.0049C55.6067 34.0562 56 34.4823 56 35C56 35.5177 55.6067 35.9438 55.1025 35.9951L55 36H50C49.4477 36 49 35.5523 49 35C49 34.4477 49.4477 34 50 34H55L55.1025 34.0049Z"
                fill="currentColor"
            />
            <path
                d="M55.1025 30.0049C55.6067 30.0562 56 30.4823 56 31C56 31.5177 55.6067 31.9438 55.1025 31.9951L55 32H50C49.4477 32 49 31.5523 49 31C49 30.4477 49.4477 30 50 30H55L55.1025 30.0049Z"
                fill="currentColor"
            />
            <path
                d="M55.1025 26.0049C55.6067 26.0562 56 26.4823 56 27C56 27.5177 55.6067 27.9438 55.1025 27.9951L55 28H50C49.4477 28 49 27.5523 49 27C49 26.4477 49.4477 26 50 26H55L55.1025 26.0049Z"
                fill="currentColor"
            />
            <path
                d="M55.1025 22.0049C55.6067 22.0562 56 22.4823 56 23C56 23.5177 55.6067 23.9438 55.1025 23.9951L55 24H50C49.4477 24 49 23.5523 49 23C49 22.4477 49.4477 22 50 22H55L55.1025 22.0049Z"
                fill="currentColor"
            />
        </svg>
    )
}

export default UserFriendlyIcon
