const weightedBase = ({ ...props }) => {
    return (
        <svg
            width="32"
            height="32"
            viewBox="0 0 32 32"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <path
                d="M25 2.4375H10.913C7.78261 2.4375 7 5.12501 7 6.375C7 9.375 7 12.4375 7 13.6875"
                stroke="#0C2588"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
            />
            <path
                d="M25 24.9375H10.913C7.78261 24.9375 7 22.25 7 21C7 18 7 14.9375 7 13.6875"
                stroke="#0C2588"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
            />
            <path
                d="M12 19H25"
                stroke="#0C2588"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
            />
            <path
                d="M18.5 19L18.5 30"
                stroke="#0C2588"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
            />
            <path
                d="M15.5 30.5H21.5"
                stroke="#0C2588"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
            />
        </svg>
    )
}

export default weightedBase
