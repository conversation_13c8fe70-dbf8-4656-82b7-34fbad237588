const Facecam = ({ ...props }) => {
    return (
        <svg
            width="32"
            height="32"
            viewBox="0 0 32 32"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <path
                d="M15.9987 19.3332C17.8396 19.3332 19.332 17.8408 19.332 15.9998C19.332 14.1589 17.8396 12.6665 15.9987 12.6665C14.1577 12.6665 12.6654 14.1589 12.6654 15.9998C12.6654 17.8408 14.1577 19.3332 15.9987 19.3332Z"
                fill="#0C2588"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M3.31186 24.2549C3.70373 24.9639 4.47027 25.3332 5.28036 25.3332L26.717 25.3332C27.5271 25.3332 28.2937 24.964 28.6857 24.255C29.4758 22.8263 30.6654 20.0111 30.6654 16.0116V15.9881C30.6654 11.9886 29.4758 9.17341 28.6857 7.74463C28.2937 7.03572 27.5271 6.6665 26.717 6.6665H5.28036C4.47027 6.6665 3.70373 7.03573 3.31186 7.74473C2.52175 9.17428 1.33203 11.9922 1.33203 15.9998C1.33203 20.0075 2.52175 22.8254 3.31186 24.2549ZM15.9987 21.3332C18.9442 21.3332 21.332 18.9454 21.332 15.9998C21.332 13.0543 18.9442 10.6665 15.9987 10.6665C13.0532 10.6665 10.6654 13.0543 10.6654 15.9998C10.6654 18.9454 13.0532 21.3332 15.9987 21.3332ZM4.33203 16.9998H6.9987C7.55098 16.9998 7.9987 16.5521 7.9987 15.9998C7.9987 15.4476 7.55098 14.9998 6.9987 14.9998H4.33203C3.77975 14.9998 3.33203 15.4476 3.33203 15.9998C3.33203 16.5521 3.77975 16.9998 4.33203 16.9998Z"
                fill="#0C2588"
            />
        </svg>
    )
}

export default Facecam
