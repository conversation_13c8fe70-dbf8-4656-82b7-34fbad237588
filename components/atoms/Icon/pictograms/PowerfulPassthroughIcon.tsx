const PowerfulPassthroughIcon = ({ ...props }) => {
    return (
        <svg
            width="72"
            height="72"
            viewBox="0 0 72 72"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M50.7156 13.2901C50.8533 12.8359 50.6528 12.3476 50.2357 12.1212C49.8186 11.8948 49.2999 11.9927 48.994 12.3556L26.2354 39.3556C25.9847 39.653 25.9292 40.0689 26.0932 40.4217C26.2573 40.7744 26.611 41.0001 27 41.0001H38.652L33.2844 58.71C33.1467 59.1642 33.3472 59.6525 33.7643 59.8789C34.1814 60.1053 34.7001 60.0074 35.006 59.6446L57.7646 32.6446C58.0154 32.3471 58.0708 31.9312 57.9068 31.5784C57.7428 31.2257 57.389 31.0001 57 31.0001H45.348L50.7156 13.2901ZM40 39.0001H29.1508L47.3927 17.3585L43.043 31.71C42.9512 32.013 43.0082 32.3416 43.1969 32.5958C43.3855 32.8501 43.6834 33.0001 44 33.0001H54.8492L36.6073 54.6417L40.957 40.2901C41.0489 39.9871 40.9918 39.6585 40.8032 39.4043C40.6145 39.15 40.3166 39.0001 40 39.0001Z"
                fill="currentColor"
            />
            <path
                d="M15 18C14.4477 18 14 18.4477 14 19C14 19.5523 14.4477 20 15 20L38 20C38.5523 20 39 19.5523 39 19C39 18.4477 38.5523 18 38 18L15 18Z"
                fill="currentColor"
            />
            <path
                d="M14 24C14 23.4477 14.4477 23 15 23L34 23C34.5523 23 35 23.4477 35 24C35 24.5523 34.5523 25 34 25L15 25C14.4477 25 14 24.5523 14 24Z"
                fill="currentColor"
            />
            <path
                d="M15 28C14.4477 28 14 28.4477 14 29C14 29.5523 14.4477 30 15 30L30 30C30.5523 30 31 29.5523 31 29C31 28.4477 30.5523 28 30 28L15 28Z"
                fill="currentColor"
            />
        </svg>
    )
}

export default PowerfulPassthroughIcon
