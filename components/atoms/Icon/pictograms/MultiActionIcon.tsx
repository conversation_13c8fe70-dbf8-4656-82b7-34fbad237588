const MultiActionIcon = ({ ...props }) => {
    return (
        <svg
            width="72"
            height="72"
            viewBox="0 0 72 72"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <path
                d="M27.3 36.1C26.1 36.1 25.1 35.6 24.2 34.8L14.5 25C12.8 23.3 12.8 20.6 14.5 18.9L24.3 9.10002C25.1 8.30002 26.2 7.80002 27.4 7.80002C28.6 7.80002 29.6 8.30002 30.5 9.10002L40.3 18.9C42 20.6 42 23.3 40.3 25L30.5 34.8C29.5 35.7 28.4 36.1 27.3 36.1ZM27.3 9.50002C26.6 9.50002 26 9.80002 25.5 10.3L15.7 20.1C14.7 21.1 14.7 22.8 15.7 23.8L25.5 33.6C26.5 34.6 28.2 34.6 29.2 33.6L39 23.8C40 22.8 40 21.1 39 20.1L29.2 10.3C28.6 9.80002 28 9.50002 27.3 9.50002Z"
                fill="currentColor"
            />
            <path
                d="M35.9999 36.1C35.2999 36.1 34.5999 35.9 33.9999 35.6C33.5999 35.4 33.3999 34.9 33.5999 34.4C33.7999 34 34.2999 33.8 34.7999 34C35.7999 34.5 36.9999 34.3 37.7999 33.5L47.5999 23.7C48.5999 22.7 48.5999 21 47.5999 20L37.7999 10.2C36.9999 9.40001 35.7999 9.20001 34.7999 9.70001C34.3999 9.90001 33.8999 9.80001 33.5999 9.30001C33.4999 9.00001 33.5999 8.50001 33.9999 8.30001C35.6999 7.40001 37.6999 7.70001 38.9999 9.10001L48.7999 18.9C50.4999 20.6 50.4999 23.3 48.7999 25L38.9999 34.8C38.1999 35.7 37.1999 36.1 35.9999 36.1Z"
                fill="currentColor"
            />
            <path
                d="M44.5999 36.1C43.8999 36.1 43.1999 35.9 42.5999 35.6C42.1999 35.4 41.9999 34.9 42.1999 34.4C42.3999 34 42.8999 33.8 43.3999 34C44.3999 34.5 45.5999 34.3 46.3999 33.5L56.1999 23.7C57.1999 22.7 57.1999 21 56.1999 20L46.3999 10.2C45.5999 9.40001 44.3999 9.20001 43.3999 9.70001C42.9999 10 42.4999 9.90001 42.1999 9.50001C41.9999 9.00001 42.1999 8.50001 42.5999 8.30001C44.2999 7.40001 46.2999 7.70001 47.5999 9.10001L57.3999 18.9C59.0999 20.6 59.0999 23.3 57.3999 25L47.5999 34.8C46.7999 35.7 45.6999 36.1 44.5999 36.1Z"
                fill="currentColor"
            />
            <path
                d="M20 52V42.5H23.1L24.6 48.9L26.1 42.5H29.1V52H27.2V44.7L25.5 52H23.7L22 44.7V52C21.9 52 20 52 20 52Z"
                fill="currentColor"
            />
            <path
                d="M37 42.6V49.3C37 49.7 36.9 50 36.8 50.4C36.7 50.7 36.5 51.1 36.3 51.3C36.1 51.6 35.7 51.8 35.3 52C34.9 52.2 34.4 52.3 33.8 52.3C32.7 52.3 31.9 52 31.4 51.5C30.9 51 30.6 50.3 30.6 49.4V42.7H32.5V49.3C32.5 49.9 32.6 50.3 32.8 50.5C33 50.7 33.3 50.9 33.7 50.9C34.1 50.9 34.4 50.8 34.6 50.5C34.8 50.2 34.9 49.8 34.9 49.3V42.7L37 42.6Z"
                fill="currentColor"
            />
            <path
                d="M38.6 52V42.5H40.5V50.4H43.4V52H38.6Z"
                fill="currentColor"
            />
            <path
                d="M49.1 42.6V44.2H46.9001V52H45V44.1H42.8V42.5H49.1V42.6Z"
                fill="currentColor"
            />
            <path d="M50 52V42.5H51.9V52H50Z" fill="currentColor" />
            <path
                d="M15.7 63.9L18.3 54.6H20.8L23.3 63.9H21.3L20.8 61.8H18.2L17.7 63.9H15.7ZM19.5 56.4L18.6 60.4H20.4L19.5 56.4Z"
                fill="currentColor"
            />
            <path
                d="M27.3999 64C26.5999 64 25.9999 63.9 25.5999 63.6C25.1999 63.3 24.7999 63 24.6999 62.5C24.4999 62 24.3999 61.5 24.3999 61C24.3999 60.4 24.2999 59.8 24.2999 59.2C24.2999 58.6 24.2999 58 24.3999 57.4C24.3999 56.8 24.4999 56.3 24.6999 55.9C24.8999 55.5 25.1999 55.1 25.5999 54.8C25.9999 54.5 26.5999 54.4 27.3999 54.4C28.3999 54.4 29.0999 54.7 29.4999 55.2C29.8999 55.7 30.0999 56.5 30.0999 57.5H28.1999C28.1999 56.9 28.0999 56.4 27.9999 56.2C27.8999 56 27.6999 55.8 27.3999 55.8C27.0999 55.8 26.8999 55.9 26.6999 56C26.4999 56.1 26.3999 56.3 26.2999 56.6C26.1999 56.9 26.1999 57.2 26.0999 57.7C26.0999 58.1 26.0999 58.6 26.0999 59.2C26.0999 59.8 26.0999 60.3 26.0999 60.7C26.0999 61.1 26.1999 61.5 26.2999 61.8C26.3999 62.1 26.4999 62.3 26.6999 62.4C26.8999 62.5 27.0999 62.6 27.3999 62.6C27.5999 62.6 27.7999 62.5 27.8999 62.4C27.9999 62.3 28.0999 62.1 28.1999 61.9C28.2999 61.7 28.2999 61.5 28.2999 61.3C28.2999 61.1 28.2999 60.9 28.2999 60.7H30.1999C30.1999 61.3 30.0999 61.8 29.9999 62.3C29.8999 62.7 29.6999 63.1 29.4999 63.3C29.2999 63.6 28.9999 63.7 28.5999 63.8C28.1999 64 27.7999 64 27.3999 64Z"
                fill="currentColor"
            />
            <path
                d="M36.8999 54.6V56.1H34.7999V63.9H32.8999V56.1H30.7999V54.6H36.8999Z"
                fill="currentColor"
            />
            <path
                d="M37.7999 63.9V54.6H39.6999V63.9H37.7999Z"
                fill="currentColor"
            />
            <path
                d="M44.3999 64C43.5999 64 42.9999 63.9 42.5999 63.6C42.1999 63.3 41.7999 63 41.6999 62.5C41.4999 62 41.3999 61.5 41.3999 61C41.3999 60.4 41.2999 59.8 41.2999 59.2C41.2999 58.6 41.2999 58 41.3999 57.4C41.3999 56.8 41.4999 56.3 41.6999 55.9C41.8999 55.5 42.1999 55.1 42.5999 54.8C42.9999 54.5 43.5999 54.4 44.3999 54.4C45.1999 54.4 45.7999 54.5 46.1999 54.8C46.5999 55.1 46.9999 55.4 47.0999 55.9C47.2999 56.3 47.3999 56.9 47.3999 57.4C47.3999 58 47.4999 58.6 47.4999 59.2C47.4999 59.8 47.4999 60.4 47.3999 61C47.3999 61.6 47.2999 62.1 47.0999 62.5C46.8999 63 46.5999 63.3 46.1999 63.6C45.7999 63.9 45.1999 64 44.3999 64ZM44.3999 62.6C44.6999 62.6 44.8999 62.5 45.0999 62.4C45.2999 62.3 45.3999 62.1 45.4999 61.8C45.5999 61.5 45.5999 61.2 45.6999 60.7C45.6999 60.3 45.6999 59.8 45.6999 59.2C45.6999 58.6 45.6999 58.1 45.6999 57.7C45.6999 57.3 45.5999 56.9 45.4999 56.6C45.3999 56.3 45.2999 56.1 45.0999 56C44.8999 55.9 44.6999 55.8 44.3999 55.8C44.0999 55.8 43.8999 55.9 43.6999 56C43.4999 56.1 43.3999 56.3 43.2999 56.6C43.1999 56.9 43.1999 57.2 43.0999 57.7C43.0999 58.1 43.0999 58.6 43.0999 59.2C43.0999 59.8 43.0999 60.3 43.0999 60.7C43.0999 61.1 43.1999 61.5 43.2999 61.8C43.3999 62.1 43.4999 62.3 43.6999 62.4C43.8999 62.6 44.1999 62.6 44.3999 62.6Z"
                fill="currentColor"
            />
            <path
                d="M49.2 63.9V54.6H51.7999L54.2999 61V54.6H56.0999V63.9H53.5999L51 57.1V63.9H49.2Z"
                fill="currentColor"
            />
        </svg>
    )
}

export default MultiActionIcon
