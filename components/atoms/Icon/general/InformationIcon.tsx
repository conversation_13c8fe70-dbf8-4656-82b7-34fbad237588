const InformationIcon = ({ ...props }) => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="32"
            height="33"
            viewBox="0 0 32 33"
            fill="none"
            {...props}
        >
            <path
                d="M16 23.1667C15.4477 23.1667 15 22.719 15 22.1667V15.5C15 14.9477 15.4477 14.5 16 14.5C16.5523 14.5 17 14.9477 17 15.5V22.1667C17 22.719 16.5523 23.1667 16 23.1667Z"
                fill="currentColor"
            />
            <path
                d="M16 9.83333C15.2636 9.83333 14.6667 10.4303 14.6667 11.1667C14.6667 11.903 15.2636 12.5 16 12.5C16.7364 12.5 17.3333 11.903 17.3333 11.1667C17.3333 10.4303 16.7364 9.83333 16 9.83333Z"
                fill="currentColor"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M16 28.5C22.6274 28.5 28 23.1274 28 16.5C28 9.87258 22.6274 4.5 16 4.5C9.37258 4.5 4 9.87258 4 16.5C4 23.1274 9.37258 28.5 16 28.5ZM16 26.5C21.5228 26.5 26 22.0228 26 16.5C26 10.9772 21.5228 6.5 16 6.5C10.4772 6.5 6 10.9772 6 16.5C6 22.0228 10.4772 26.5 16 26.5Z"
                fill="currentColor"
            />
        </svg>
    )
}

export default InformationIcon
