const AlertIcon = ({ ...props }) => {
    return (
        <svg
            width="24"
            height="25"
            viewBox="0 0 24 25"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <path
                d="M8.04183 8.22851C8.78981 8.22851 9.39616 8.83486 9.39616 9.58284C9.39616 10.1842 9.00418 10.6941 8.46178 10.8708L8.46178 12.9972C8.46178 13.327 8.59551 13.6426 8.8324 13.872L11.5274 16.4819L11.5274 15.1161C11.5274 15.0397 11.5309 14.9636 11.5379 14.8881L11.5379 2.97917L10.467 2.97917L12.0103 0.3125L13.5641 2.97917L12.4618 2.97917L12.4618 13.1606L15.1276 10.5653C15.3631 10.3361 15.4959 10.0214 15.4959 9.69272L15.4959 7.61959L14.6455 7.61959L14.6455 4.95292L17.3122 4.95292L17.3122 7.61959L16.4198 7.61959L16.4198 9.69272C16.4198 10.2707 16.1862 10.8241 15.7721 11.2273L12.9212 14.0027C12.6642 14.253 12.503 14.583 12.4618 14.9357L12.4618 19.8199C13.5031 20.0251 14.2886 20.9432 14.2886 22.0448C14.2886 23.2972 13.2733 24.3125 12.0208 24.3125C10.7684 24.3125 9.75312 23.2972 9.75312 22.0448C9.75312 20.9581 10.5175 20.0499 11.5379 19.8286L11.5379 17.7782L8.18968 14.5357C7.77308 14.1323 7.53789 13.5771 7.53789 12.9972L7.53789 10.8403C7.03948 10.6404 6.6875 10.1527 6.6875 9.58284C6.6875 8.83486 7.29385 8.22851 8.04183 8.22851Z"
                fill="currentColor"
            />
        </svg>
    )
}

export default AlertIcon
