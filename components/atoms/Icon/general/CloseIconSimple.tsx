import React from 'react'

import { useTranslation } from 'next-i18next'
const CloseIconSimple = ({ ...props }) => {
    const { t } = useTranslation(['common'])

    return (
        <svg
            width="32"
            height="32"
            viewBox="0 0 32 32"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M6.95989 23.6261C6.56936 24.0166 6.56936 24.6498 6.95989 25.0403C7.35041 25.4308 7.98357 25.4308 8.3741 25.0403L16.0003 17.4141L23.6266 25.0403C24.0171 25.4308 24.6502 25.4308 25.0408 25.0403C25.4313 24.6498 25.4313 24.0166 25.0408 23.6261L17.4145 15.9998L25.0408 8.37361C25.4313 7.98309 25.4313 7.34992 25.0408 6.9594C24.6502 6.56887 24.0171 6.56887 23.6266 6.9594L16.0003 14.5856L8.3741 6.9594C7.98357 6.56887 7.35041 6.56887 6.95989 6.9594C6.56936 7.34992 6.56936 7.98309 6.95989 8.37361L14.5861 15.9998L6.95989 23.6261Z"
                fill="currentColor"
            />
        </svg>
    )
}

export default CloseIconSimple
