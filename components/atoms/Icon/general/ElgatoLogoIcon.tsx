const ElgatoLogoIcon = ({ ...props }) => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="25"
            viewBox="0 0 24 25"
            fill="none"
            {...props}
        >
            <path
                d="M13.4493 9.49698L13.4711 9.51334L21.7623 14.2776C21.3552 16.5517 20.1625 18.6295 18.3969 20.1321C16.603 21.6587 14.3178 22.5 11.9619 22.5C9.30141 22.5 6.80035 21.46 4.91812 19.5712C3.03664 17.6823 2 15.1704 2 12.5001C2 9.82981 3.03664 7.31784 4.91793 5.42884C6.79922 3.54002 9.30028 2.5 11.9619 2.5C13.9948 2.5 15.9492 3.11111 17.6145 4.26565C19.2415 5.39405 20.4865 6.96208 21.2166 8.80088C21.3093 9.03667 21.3957 9.27886 21.4709 9.52218L19.6998 10.5522C19.6387 10.3078 19.5655 10.0645 19.4815 9.82774C18.3531 6.63584 15.3304 4.49054 11.9617 4.49054C7.5619 4.49054 3.98264 8.0839 3.98264 12.5001C3.98264 16.9163 7.5619 20.5096 11.9617 20.5096C13.6717 20.5096 15.302 19.9728 16.678 18.9557C18.0257 17.9595 19.0144 16.5942 19.5371 15.0087L19.5426 14.9737L9.87323 9.42064V15.4421L13.2069 13.5138L14.9235 14.4982L9.92663 17.3888L8.15985 16.3783V8.49532L9.92437 7.47504L13.4502 9.49811L13.4493 9.49698Z"
                fill="currentColor"
            />
        </svg>
    )
}

export default ElgatoLogoIcon
