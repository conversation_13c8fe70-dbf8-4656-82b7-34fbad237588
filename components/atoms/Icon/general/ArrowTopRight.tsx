import { FC, SVGProps } from 'react'

type Props = SVGProps<SVGSVGElement>

const ArrowTopRight: FC<Props> = (props) => {
    return (
        <svg
            viewBox="0 0 14 14"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <path
                d="M11.8699 8.67965C11.8699 9.09386 12.2057 9.42965 12.6199 9.42965C13.0341 9.42965 13.3699 9.09386 13.3699 8.67965L13.3699 1.25503C13.3699 0.840815 13.0341 0.505029 12.6199 0.505028L5.19528 0.505029C4.78107 0.505029 4.44528 0.840815 4.44528 1.25503C4.44528 1.66924 4.78107 2.00503 5.19528 2.00503L10.8092 2.00503L0.599086 12.2152C0.306193 12.5081 0.306193 12.983 0.599086 13.2758C0.891979 13.5687 1.36685 13.5687 1.65975 13.2758L11.8699 3.06569L11.8699 8.67965Z"
                fill="#151515"
            />
        </svg>
    )
}

export default ArrowTopRight
