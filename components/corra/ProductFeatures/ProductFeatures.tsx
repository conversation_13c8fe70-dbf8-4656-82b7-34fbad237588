import { IProductFeature } from '@components/common/types'
import React from 'react'
import Feature from './Feature'
import s from './ProductFeatures.module.scss'
import cn from 'classnames'
import { useTranslation } from 'next-i18next'

export type ProductFeaturesProps = {
    productFeaturesTitle?: string
    features?: IProductFeature[]
    customOptions?: any
}

const checkIfAnimated = (features: IProductFeature[]) => {
    return features?.[0]?.isAnimated || false
}

const ProductFeatures: React.VFC<ProductFeaturesProps> = ({
    productFeaturesTitle,
    features = [],
    customOptions
}) => {
    const { t } = useTranslation(['pdp'])
    const background = customOptions?.backgroundColor
    const isAnimated = checkIfAnimated(features)
    return (
        <div className={cn('py-16px', s['product-features__container'])}>
            {features.length > 0 && (
                <div
                    className={(s['product-features'], 'md-max:px-16px')}
                    style={{ background: background }}
                >
                    <div
                        className={cn(s['product-features__inner'], {
                            [s['product-features__inner--animated']]: isAnimated
                        })}
                        style={{ background }}
                    >
                        <h4
                            className={cn(
                                s['product-features-title'],
                                'md:mb-24px text-black',
                                {
                                    ['block md:hidden']: isAnimated
                                }
                            )}
                        >
                            {productFeaturesTitle ? productFeaturesTitle : null}
                        </h4>
                        <div className="text-black">
                            {features.map((feature) => {
                                return (
                                    <Feature
                                        feature={feature}
                                        key={feature.title}
                                    />
                                )
                            })}
                        </div>
                    </div>
                </div>
            )}
        </div>
    )
}

export default ProductFeatures
