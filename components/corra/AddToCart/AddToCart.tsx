import InfoFilledIcon from '@components/atoms/Icon/general/InfoFilledIcon'
import InfoIcon from '@components/atoms/Icon/general/InfoIcon'
import { Icon } from '@components/atoms/Icon/Icon'
import { Logo } from '@components/atoms/Logo/Logo'
import { Tooltip } from '@components/atoms/Tooltip/Tooltip'
import { DeliverBy } from '@components/common/DeliverBy'
import { Button } from '@components/molecules/Button/Button'
import { LinkResponse } from '@components/molecules/Link/Link'
import { ProductsPrice } from '@components/molecules/ProductPrices/ProductPrices'
import { TooltipWrapper } from '@components/molecules/TooltipWrapper/TooltipWrapper'
import { ToastType, useUI } from '@corsairitshopify/pylot-ui/context'
import s from '@pagestyles/PDP.module.scss'
import { useAddToCart } from '@pylot-data/hooks/cart/use-add-to-cart'
import { processEnteredOptions } from '@pylot-data/hooks/cart/utils/processEnteredOptions'
import { useProductUI } from '@pylot-data/hooks/product/use-product-ui'
import {
    BundleProducts,
    Maybe,
    ProductInterface,
    RelatedProductInterface
} from '@pylot-data/pylotschema'
import cn from 'classnames'
import {
    DELIVERYBY_DATE_FORMAT_BY_REGION,
    getDeliveryByDateLocal
} from 'helpers'
import { handleSidebarCartFocus } from 'helpers/AdaHelpers'
import { useCartItemHelper } from 'helpers/cartItemHelper'
import { calculateElgBdsPrice } from 'helpers/elgatoBDSHelper'
import { useTranslation } from 'next-i18next'
import { useRouter } from 'next/router'
import { FC, useEffect, useMemo, useRef, useState } from 'react'

const TIMEOUT_AFTER_OPENING_SIDEBAR = 300

export type AddToCartPropsType = {
    shouldApplyElgBDSPrice?: boolean
    buttonLabel: string
    selectedOptionUIDs: string[]
    isGiftCard: boolean
    product: ProductInterface
    selectedElgatoBDSItems: RelatedProductInterface[]
    relatedProducts: RelatedProductInterface[]
    showPrice?: boolean
    hasRetailersOverlay: boolean
    openRetailersOverlay?: () => void
    onAddToCart?: () => void
    clickFromTab?: string
    showCTA?: boolean
    showPriceNextToCTA?: boolean
    notSellableButton?: LinkResponse
    sku?: string
    bundle?: Maybe<Maybe<BundleProducts>[]>
    deliverBy?: Maybe<string>
    excludedDeliveryDate?: Maybe<string[]>
    isBackorder?: Maybe<boolean>
    usRegionBuyAt?: LinkResponse[]
    notifyMeLink?: LinkResponse
    quantity?: number
    pageTheme?: string
}

const FIND_RETAILER_LABEL = 'Buy at retailer' // replace later with translations
const FIND_RETAILER_SIZE = 'sm'

type UseClickFrom = (
    cmsATCLocation?: number,
    options?: {
        isPopup?: boolean
        isMainATC?: boolean
    }
) => string

export const useClickFrom: UseClickFrom = (
    cmsATCLocation,
    { isPopup, isMainATC } = {
        isPopup: false,
        isMainATC: false
    }
) => {
    const router = useRouter()
    const { asPath } = router
    const [clickFrom, setClickFrom] = useState('')
    useEffect(() => {
        const isCMSPage = asPath.includes('/s/') && cmsATCLocation
        const isPDP = asPath.includes('/p/')

        if (isCMSPage) {
            setClickFrom(`location-${cmsATCLocation}`)
        } else if (isPDP) {
            setClickFrom(
                isPopup
                    ? 'extra'
                    : isMainATC
                    ? 'product'
                    : `location-${cmsATCLocation}`
            )
        }
    }, [asPath, cmsATCLocation, isPopup, isMainATC])

    return clickFrom
}

export const AddToCart: FC<AddToCartPropsType> = (props) => {
    const {
        selectedOptionUIDs,
        isGiftCard,
        product,
        relatedProducts,
        selectedElgatoBDSItems,
        showPrice = true,
        hasRetailersOverlay,
        openRetailersOverlay,
        onAddToCart,
        buttonLabel,
        clickFromTab = '',
        showCTA = false,
        showPriceNextToCTA = false,
        shouldApplyElgBDSPrice = false,
        notSellableButton,
        deliverBy,
        excludedDeliveryDate,
        isBackorder,
        usRegionBuyAt,
        notifyMeLink,
        quantity,
        pageTheme
    } = props
    const { elgato_bundle_and_save_skus } = product
    const isElgatoBundleAndSave = !!(
        elgato_bundle_and_save_skus && elgato_bundle_and_save_skus.length > 0
    )
    const { t } = useTranslation(['common', 'pdp'])
    const updateCartItemsLocalStorage = useCartItemHelper()
    const { openSidebar, openCartToast } = useUI()
    const { isAdding, addToCart } = useAddToCart()
    const containerRef = useRef<HTMLDivElement>(null)
    const { variant, isOutOfStock } = useProductUI(product)
    const label = isAdding ? t('Adding...') : t(buttonLabel)
    const clickFrom = useClickFrom(undefined, { isMainATC: true })

    //moved the add to cart logic to component from the PDP page
    const addProductHandler = async () => {
        const cartItemsFromSelectedRelatedProductsData = relatedProducts.map(
            (productData) => {
                const entered_options = processEnteredOptions(productData)

                return {
                    sku: productData.sku as string,
                    uid: productData.uid,
                    quantity: quantity || 1,
                    entered_options,
                    max_quantity: productData.max_allowed_quantity,
                    bundle_and_save_items:
                        productData?.elgato_bundle_and_save_skus || [],
                    deliver_by: productData?.deliverBy || 0,
                    excluded_delivery_dates:
                        productData?.excludedDeliveryDate?.join('-') || ''
                }
            }
        )
        const elgatoBDSCartItems = selectedElgatoBDSItems.map((item) => {
            const entered_options = processEnteredOptions(item)

            return {
                sku: item?.sku as string,
                uid: item?.uid,
                quantity: quantity || 1,
                max_quantity: item.max_allowed_quantity,
                entered_options,
                bundle_and_save_items: item?.elgato_bundle_and_save_skus || [],
                deliver_by: item?.deliverBy || 0,
                excluded_delivery_dates:
                    item?.excludedDeliveryDate?.join('-') || ''
            }
        })
        const relatedItems = [...relatedProducts, ...selectedElgatoBDSItems]

        updateCartItemsLocalStorage(product, clickFromTab || clickFrom)
        relatedItems?.forEach((product) => {
            updateCartItemsLocalStorage(product, product?.from)
        })
        // 'variant' of a SimpleProduct is the same as 'product'
        const entered_options = processEnteredOptions(product)
        const response = await addToCart(
            [
                {
                    sku: variant.sku,
                    uid: variant.uid,
                    ...(variant.sku !== product.sku && {
                        parent_sku: product.sku
                    }),
                    selected_options: selectedOptionUIDs,
                    quantity: quantity || 1,
                    max_quantity: variant.max_allowed_quantity,
                    entered_options,
                    bundle_and_save_items:
                        product?.elgato_bundle_and_save_skus || [],
                    deliver_by: product?.deliverBy || 0,
                    excluded_delivery_dates:
                        product?.excludedDeliveryDate?.join('-') || ''
                },
                ...cartItemsFromSelectedRelatedProductsData,
                ...elgatoBDSCartItems
            ],
            [product, ...relatedProducts, ...selectedElgatoBDSItems],
            undefined,
            {
                isMainItemElgatoBundleAndSave: isElgatoBundleAndSave
            }
        )

        const errorMessage =
            response?.errors?.[0]?.message ??
            response?.user_errors?.[0]?.message

        openSidebar()
        if (onAddToCart) {
            onAddToCart()
        }

        if (errorMessage) {
            setTimeout(
                () => openCartToast(errorMessage, ToastType.Warning),
                TIMEOUT_AFTER_OPENING_SIDEBAR
            )
        }
    }

    const {
        price_range,
        not_sellable,
        bundle_products
    } = product as ProductInterface
    const relatedProductsPricesRange = relatedProducts.map((p) => p.price_range)
    const elgBDSPriceRange =
        calculateElgBdsPrice(product, selectedElgatoBDSItems[0]) || price_range
    const pricesRange = shouldApplyElgBDSPrice
        ? [elgBDSPriceRange, ...relatedProductsPricesRange]
        : [
              price_range,
              ...relatedProductsPricesRange,
              ...selectedElgatoBDSItems.map((item) => item?.price_range)
          ]
    const isSellable = useMemo(() => {
        return !not_sellable && !bundle_products?.some((p) => p?.not_sellable)
    }, [not_sellable, bundle_products])
    const showNotSellableButton = !isSellable && notSellableButton
    const deliverByDate = getDeliveryByDateLocal({
        dateFormatByRegion: DELIVERYBY_DATE_FORMAT_BY_REGION,
        deliverBy,
        excludedDeliveryDate,
        withYear: false
    })
    const containerPadding =
        !isOutOfStock && !isBackorder && deliverByDate
            ? 'add-to-cart-container-deliver-by'
            : 'add-to-cart-container-padding'

    const priceDiscountTheme =
        pageTheme === 'dark' ? 'light' : pageTheme === 'neo' ? 'neo' : undefined

    //check if alternative link for out of stock should be shown
    const { locale } = useRouter()
    const currentRegionCode = locale?.substring(3) || ''

    const usRegionBuyAtLink =
        usRegionBuyAt?.[0] != undefined && currentRegionCode == 'US'
            ? usRegionBuyAt?.[0]
            : undefined

    const showNotifyMeLink = !!notifyMeLink && (isOutOfStock || !isSellable)

    const handleOnKeyPress = () => {
        addProductHandler()
        handleSidebarCartFocus(`#add-to-cart-button-${product.uid}`)
    }
    return (
        <div
            className={cn(s['add-to-cart-container'], s[containerPadding], {
                [s['add-to-cart-container--no-price']]: !showPrice,
                [s[`page-theme-${pageTheme}`]]: pageTheme,
                [s['add-to-cart-container--notify-me']]:
                    showNotifyMeLink && !usRegionBuyAtLink
            })}
            ref={containerRef}
        >
            {showNotSellableButton && notSellableButton && (
                <div className="flex justify-center md:justify-end">
                    <Button
                        variant={notSellableButton.style}
                        color={notSellableButton.styleColor}
                        iconAlignment={notSellableButton.iconAlignment}
                        href={notSellableButton.linkUrl}
                        newTab={notSellableButton.newTab}
                        isNeo={pageTheme === 'neo'}
                        label={notSellableButton.linkTitle}
                        desktopSize={pageTheme === 'neo' ? 'm' : undefined}
                    >
                        {notSellableButton.linkTitle}
                    </Button>
                </div>
            )}
            {showPrice && !showNotSellableButton && (
                <div
                    className={cn(s['price-add-to-cart'], s['pdp-price'], {
                        [s['notify-me']]: showNotifyMeLink && !usRegionBuyAtLink
                    })}
                >
                    {isSellable && (
                        <>
                            {showPriceNextToCTA && (
                                <ProductsPrice
                                    pricesRange={pricesRange}
                                    showCurrencyCode
                                    splitSymbol
                                    isGiftCard={isGiftCard}
                                    theme={priceDiscountTheme}
                                    size={
                                        pageTheme === 'neo' ? 'medium' : 'big'
                                    }
                                />
                            )}
                            <div
                                className={cn(
                                    'flex justify-end',
                                    s['add-to-cart-wrapper'],
                                    pageTheme === 'neo'
                                        ? 'items-stretch'
                                        : 'items-center'
                                )}
                            >
                                {usRegionBuyAtLink && (
                                    <div className={s['add-to-cart-discover']}>
                                        <Button
                                            variant={
                                                usRegionBuyAtLink.logo
                                                    ? 'secondary'
                                                    : pageTheme === 'neo'
                                                    ? 'tertiary'
                                                    : 'tertiary-underlined'
                                            }
                                            className={cn(
                                                s['add-to-cart-buy-at-btn'],
                                                {
                                                    [s[
                                                        'add-to-cart-buy-at-btn--logo'
                                                    ]]:
                                                        usRegionBuyAtLink.linkUrl,
                                                    'tracking-wide text-black':
                                                        pageTheme !== 'neo'
                                                }
                                            )}
                                            size={FIND_RETAILER_SIZE}
                                            href={usRegionBuyAtLink.linkUrl}
                                            newTab={usRegionBuyAtLink.newTab}
                                            color={
                                                pageTheme === 'dark'
                                                    ? 'light'
                                                    : undefined
                                            }
                                            isNeo={pageTheme === 'neo'}
                                            desktopSize={
                                                pageTheme === 'neo'
                                                    ? 'm'
                                                    : undefined
                                            }
                                            label={usRegionBuyAtLink.linkTitle}
                                        >
                                            {usRegionBuyAtLink.logo ? (
                                                <Logo
                                                    name={
                                                        usRegionBuyAtLink.logo
                                                    }
                                                />
                                            ) : (
                                                usRegionBuyAtLink.linkTitle
                                            )}
                                        </Button>
                                    </div>
                                )}

                                {!usRegionBuyAtLink &&
                                    isOutOfStock &&
                                    hasRetailersOverlay && (
                                        <div
                                            className={
                                                s['add-to-cart-discover']
                                            }
                                        >
                                            <Button
                                                variant={
                                                    pageTheme === 'neo'
                                                        ? 'tertiary'
                                                        : 'tertiary-underlined'
                                                }
                                                className={cn({
                                                    'tracking-wide text-black':
                                                        pageTheme !== 'neo'
                                                })}
                                                size={FIND_RETAILER_SIZE}
                                                onClick={openRetailersOverlay}
                                                color={
                                                    pageTheme === 'dark'
                                                        ? 'light'
                                                        : undefined
                                                }
                                                isNeo={pageTheme === 'neo'}
                                                desktopSize={
                                                    pageTheme === 'neo'
                                                        ? 'm'
                                                        : undefined
                                                }
                                                label={t(FIND_RETAILER_LABEL)}
                                            >
                                                {t(FIND_RETAILER_LABEL)}
                                            </Button>
                                        </div>
                                    )}
                                {showCTA && (
                                    <div className="flex flex-col items-center">
                                        <Button
                                            onClick={addProductHandler}
                                            onKeyPress={(
                                                e: React.KeyboardEvent<HTMLElement>
                                            ) => {
                                                if (
                                                    e.key === 'Enter' ||
                                                    e.key === ' '
                                                ) {
                                                    e.preventDefault()
                                                    handleOnKeyPress()
                                                }
                                            }}
                                            variant="primary"
                                            isNeo={pageTheme === 'neo'}
                                            // eslint-disable-next-line i18next/no-literal-string
                                            Component="button"
                                            disabled={isOutOfStock}
                                            desktopSize={
                                                pageTheme === 'neo'
                                                    ? 'm'
                                                    : undefined
                                            }
                                            className={cn(
                                                s['add-to-cart-button'],
                                                'self-end',
                                                {
                                                    [s[
                                                        'add-to-cart-button--sold-out'
                                                    ]]: isOutOfStock
                                                }
                                            )}
                                            label={label}
                                            aria-haspopup="dialog"
                                            aria-controls="cart-sidebar-root"
                                            id={`add-to-cart-button-${product.uid}`}
                                        >
                                            {label}
                                        </Button>
                                        {!isOutOfStock && !isBackorder && (
                                            <DeliverBy
                                                deliverByDate={deliverByDate}
                                                color={
                                                    pageTheme === 'dark'
                                                        ? 'dark'
                                                        : 'light'
                                                }
                                            />
                                        )}
                                    </div>
                                )}
                            </div>
                        </>
                    )}
                    {!isSellable && showNotifyMeLink && notifyMeLink && (
                        <Button
                            href={notifyMeLink.linkUrl}
                            newTab={notifyMeLink.newTab}
                            isNeo={pageTheme === 'neo'}
                            desktopSize={pageTheme === 'neo' ? 'm' : undefined}
                            variant={
                                !usRegionBuyAtLink ? 'primary' : 'tertiary'
                            }
                            className={cn(s['add-to-cart-notify-me-btn'], {
                                [s[
                                    'add-to-cart-notify-me-btn--primary'
                                ]]: !usRegionBuyAtLink
                            })}
                            label={notifyMeLink.linkTitle}
                        >
                            {notifyMeLink.icon && (
                                <Icon name={notifyMeLink.icon} />
                            )}
                            {notifyMeLink.linkTitle}
                        </Button>
                    )}
                    {!usRegionBuyAtLink && !isSellable && hasRetailersOverlay && (
                        <div className="flex justify-end flex-1">
                            <Button
                                onClick={openRetailersOverlay}
                                isNeo={pageTheme === 'neo'}
                                desktopSize={
                                    pageTheme === 'neo' ? 'm' : undefined
                                }
                                label={t(FIND_RETAILER_LABEL)}
                            >
                                {t(FIND_RETAILER_LABEL)}
                            </Button>
                        </div>
                    )}
                    {usRegionBuyAtLink && !isSellable && (
                        <div className="flex justify-end flex-1">
                            <Button
                                className={cn(s['add-to-cart-buy-at-btn'], {
                                    [s['add-to-cart-buy-at-btn--logo']]:
                                        usRegionBuyAtLink.linkUrl
                                })}
                                href={usRegionBuyAtLink.linkUrl}
                                newTab={usRegionBuyAtLink.newTab}
                                isNeo={pageTheme === 'neo'}
                                variant={
                                    usRegionBuyAtLink.logo
                                        ? 'secondary'
                                        : 'primary'
                                }
                                desktopSize={
                                    pageTheme === 'neo' ? 'm' : undefined
                                }
                                label={usRegionBuyAtLink.linkTitle}
                            >
                                {usRegionBuyAtLink.logo ? (
                                    <Logo name={usRegionBuyAtLink.logo} />
                                ) : (
                                    usRegionBuyAtLink.linkTitle
                                )}
                            </Button>
                        </div>
                    )}
                </div>
            )}
            {!showPrice && hasRetailersOverlay && !showNotSellableButton && (
                <div className="flex items-center justify-between h-full xs-copy text-charcoal">
                    <TooltipWrapper
                        className="-ml-3.5"
                        tooltip={
                            <Tooltip
                                text={t(
                                    'Our web store is not available in your region, but our partners are.'
                                )}
                                transparent={false}
                                color="light"
                            />
                        }
                        ariaUid="tooltip-non-transactional"
                    >
                        <Button
                            variant="icon-tertiary"
                            onClick={(e) => {
                                e?.preventDefault()
                            }}
                            color={pageTheme === 'dark' ? 'light' : undefined}
                            isNeo={pageTheme === 'neo'}
                            desktopSize={pageTheme === 'neo' ? 'm' : undefined}
                            className={s['add-to-cart-tooltip-btn']}
                            label={t('Info')}
                        >
                            {pageTheme === 'neo' ? (
                                <InfoFilledIcon />
                            ) : (
                                <InfoIcon />
                            )}
                        </Button>
                    </TooltipWrapper>

                    <Button
                        onClick={openRetailersOverlay}
                        isNeo={pageTheme === 'neo'}
                        desktopSize={pageTheme === 'neo' ? 'm' : undefined}
                        label={t(FIND_RETAILER_LABEL)}
                    >
                        {t(FIND_RETAILER_LABEL)}
                    </Button>
                </div>
            )}
        </div>
    )
}
