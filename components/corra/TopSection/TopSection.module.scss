.top-section {
    @apply text-black;
    padding: 32px 16px;

    @screen md {
        padding: 32px 48px;
    }

    @media (min-width: 1080px) {
        padding: 40px 48px 32px;
    }

    @media screen and (min-width: 1024px) and (max-height: 1000px) {
        padding: 24px 48px 16px;
    }

    h1 {
        p {
            text-transform: none;
        }
    }

    h2 {
        text-transform: none;
    }

    &__price-wrapper {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    &__label {
        display: flex;
        justify-content: center;
        align-items: center;
    }
    border-bottom: 1px solid var(--light-grey-1);

    &__description {
        @screen md-max {
            max-height: 75px;
            -webkit-line-clamp: 3;
            overflow: hidden;
            display: -webkit-box;
            -webkit-box-orient: vertical;
        }
        &--expanded {
            @screen md-max {
                max-height: initial;
                -webkit-line-clamp: initial;
                overflow: initial;
                display: initial;
                -webkit-box-orient: initial;
            }
        }
    }

    &__bulk {
        &__table-wrapper {
            display: grid;
            grid-template-rows: 0fr;
            transition: grid-template-rows 0.3s ease-in-out;
            overflow: hidden;

            &--expanded {
                grid-template-rows: 1fr;
            }
        }

        &__table-content {
            min-height: 0;
            overflow: hidden;
        }

        &__table {
            width: 100%;
            margin-top: 1rem;
        }

        &__quantity-select {
            padding: 8px !important;
            width: 80px !important;
            height: 32px;
            border: none;
            margin-top: 0 !important;
            appearance: none;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='18 15 12 9 6 15'%3E%3C/polyline%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 8px center;
            background-size: 16px;
            padding-right: 32px !important;
        }
    }
}

.top-section.page-theme-dark {
    border-bottom: 1px solid var(--primitive-gray-90);
    color: var(--white);

    :global {
        .rich-text {
            color: var(--primitive-gray-30);
        }
    }
}

.top-section.page-theme-neo {
    padding: 0 16px 24px 16px;

    @screen md {
        padding: 0 48px 32px 48px;
    }

    @media screen and (min-width: 1024px) and (max-height: 1000px) {
        padding: 24px 48px 16px;
    }

    @media (min-width: 1080px) {
        padding: 40px 48px 32px;
    }

    .top-section__label {
        padding: 8px 16px;
        border-radius: 9999px;
        background-color: var(--primaries-mid-blue);
    }

    .top-section__name {
        color: var(--greyscale-black-65);
        text-transform: none;
        letter-spacing: normal;
        @apply font-univers55Roman;
        font-size: 24px;
        font-style: normal;
        line-height: 150%;
        @screen md {
            font-size: 32px;
        }
    }

    .top-section__short-description {
        @apply font-univers55Roman;
        font-size: 16px;
        font-style: normal;
        line-height: 24px;
        margin-top: 12px;

        @screen md {
            font-size: 20px;
            line-height: 32px;
            margin-top: 16px;
        }

        @media (min-width: 1080px) {
            font-size: 20px;
            line-height: 20px;
        }
    }

    h4 {
        color: var(--black);
    }

    .top-section__review {
        margin-top: 16px;
        margin-bottom: 16px;

        @media (min-width: 1080px) {
            margin-top: 24px;
            margin-bottom: 24px;
        }
    }

    :global {
        .rich-text {
            color: var(--greyscale-black-65);
        }
    }
}
