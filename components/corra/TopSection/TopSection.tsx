import { Badge } from '@components/atoms/Badge/Badge'
import ChevronDownIcon from '@components/atoms/Icon/general/ChevronDownIcon'
import ChevronUpIcon from '@components/atoms/Icon/general/ChevronUpIcon'
import { Icon } from '@components/atoms/Icon/Icon'
import { BuyInBulkTable } from '@components/common/types'
import { ReviewWrapper } from '@components/molecules/BazaarComponents/ReviewWrapper'
import { Button } from '@components/molecules/Button/Button'
import { PriceDiscount } from '@components/molecules/PriceDiscount/PriceDiscount'
import { useMobile } from '@pylot-data/hooks/use-mobile'
import cn from 'classnames'
import { isNonTransactionalRegion } from 'helpers/transactionalInfoHelper'
import { useTranslation } from 'next-i18next'
import { useRouter } from 'next/router'
import React, { Dispatch, SetStateAction, useState, VFC } from 'react'
import s from './TopSection.module.scss'

export type TopSectionProps = {
    priceVisible: boolean
    name?: string
    description?: string
    shortDescription?: string
    extendedNameAndDesc?: boolean
    label?: string
    pimId?: number
    price?: string
    priceDiscount?: string
    subtotalValue?: number
    totalValue?: number
    maxAllowedQuantity?: number
    quantity?: number
    setQuantity: Dispatch<SetStateAction<number>>
    buyInBulkTable?: BuyInBulkTable
    pageTheme?: string
}

const TopSection: VFC<TopSectionProps> = ({
    name = '',
    description = '',
    shortDescription = '',
    extendedNameAndDesc = false,
    pimId,
    price = '',
    priceDiscount = '',
    subtotalValue,
    totalValue,
    priceVisible = true,
    label,
    maxAllowedQuantity,
    quantity = 1,
    setQuantity,
    buyInBulkTable,
    pageTheme
}) => {
    const { t } = useTranslation(['common', 'pdp'])

    const priceDiscountTheme =
        pageTheme === 'dark' ? 'light' : pageTheme === 'neo' ? 'neo' : undefined
    const [isExpanded, setIsExpanded] = useState(false)
    const { isMobile } = useMobile()
    const [isBulkExpanded, setIsBulkExpanded] = useState(false)
    const [copiedCode, setIsCopiedCode] = useState<string>('')
    const { locale } = useRouter()

    const buyInBulkTableArray = buyInBulkTable
        ? Object.keys(buyInBulkTable)
        : []

    const copyCode = (code: string) => {
        navigator.clipboard.writeText(code)
        setIsCopiedCode(code)
        setTimeout(() => {
            setIsCopiedCode('')
        }, 750)
    }

    return (
        <div
            className={cn(s['top-section'], {
                [s[`page-theme-${pageTheme}`]]: pageTheme
            })}
        >
            <div className={s['top-section__price-wrapper']}>
                <PriceDiscount
                    variant="inline"
                    price={price ?? 0}
                    priceDiscount={priceDiscount}
                    /* eslint-disable-next-line i18next/no-literal-string */
                    size="medium"
                    className={label ? 'mb-0' : 'mb-8px'}
                    totalValue={totalValue}
                    subtotalValue={subtotalValue}
                    theme={priceDiscountTheme}
                    pageTheme={pageTheme}
                />
                {label && (
                    <div className={s['top-section__label']}>
                        <span className="neo-label-text">{label}</span>
                    </div>
                )}
            </div>
            {extendedNameAndDesc ? (
                <h1
                    className={
                        pageTheme === 'neo' ? 'h2-neo leading-none' : 'h4'
                    }
                >
                    {`${name} `}
                    <p className="small-copy font-univers65Bold">
                        {shortDescription}
                    </p>
                </h1>
            ) : (
                <>
                    <h1
                        className={
                            pageTheme === 'neo' ? s['top-section__name'] : 'h4'
                        }
                    >
                        {name}
                    </h1>
                    <h2
                        className={
                            pageTheme === 'neo'
                                ? s['top-section__short-description']
                                : 'small-copy font-univers65Bold'
                        }
                    >
                        {shortDescription}
                    </h2>
                </>
            )}
            {pimId && (
                <ReviewWrapper
                    className={s['top-section__review']}
                    pimId={pimId}
                    pageTheme={pageTheme}
                />
            )}
            <div className="flex flex-col items-start">
                <div
                    dangerouslySetInnerHTML={{ __html: description }}
                    className={cn(
                        s['top-section__description'],
                        'rich-text',
                        pageTheme === 'neo'
                            ? 'small-copy-neo'
                            : 'text-small-copy',
                        {
                            [s[
                                'top-section__description--expanded'
                            ]]: isExpanded
                        }
                    )}
                />
                {description && isMobile && (
                    <Button
                        variant="tertiary"
                        className="text-small-copy underline mt-8px"
                        onClick={() => setIsExpanded(!isExpanded)}
                        aria-expanded={isExpanded}
                        aria-label={
                            isExpanded ? t('show less') : t('view more')
                        }
                    >
                        {isExpanded ? t('show less') : t('view more')}
                        <Icon
                            name={isExpanded ? 'chevronUp' : 'chevronDown'}
                            className="ml-4px"
                        />
                    </Button>
                )}
            </div>
            {buyInBulkTable &&
                buyInBulkTableArray.length > 0 &&
                typeof locale !== 'undefined' &&
                !isNonTransactionalRegion(locale) && (
                    <div className={cn(s['top-section__bulk'], 'py-6')}>
                        <div
                            className={cn(
                                s['top-section__bulk__wrapper'],
                                'bg-white mb-4 overflow-hidden px-8 py-4 rounded-xl text-black'
                            )}
                        >
                            <div
                                className={cn(
                                    'flex items-center justify-between',
                                    s['top-section__bulk__wrapper__header']
                                )}
                                onClick={() =>
                                    setIsBulkExpanded(!isBulkExpanded)
                                }
                                onKeyDown={(e) => {
                                    if (e.key === 'Enter') {
                                        setIsBulkExpanded(!isBulkExpanded)
                                    }
                                }}
                                role="button"
                                tabIndex={0}
                                aria-expanded={isBulkExpanded}
                                aria-label={t('Buy in bulk')}
                            >
                                <label
                                    htmlFor="quantity"
                                    className={cn(
                                        'form-label font-bold normal-case text-primitive-gray-100 font-univers65Bold text-base'
                                    )}
                                >
                                    {t('Buy in bulk')}
                                </label>
                                <div className="flex items-center">
                                    <Badge
                                        className="bg-purple-plum-1 mr-4"
                                        size="medium"
                                    >
                                        <p>
                                            {t('Save in bulk SD', {
                                                ns: 'pdp'
                                            })}
                                        </p>
                                    </Badge>
                                    {isBulkExpanded ? (
                                        <ChevronUpIcon />
                                    ) : (
                                        <ChevronDownIcon />
                                    )}
                                </div>
                            </div>

                            <div
                                className={cn(
                                    s['top-section__bulk__table-wrapper'],
                                    'grid',
                                    {
                                        [s[
                                            'top-section__bulk__table-wrapper--expanded'
                                        ]]: isBulkExpanded
                                    }
                                )}
                            >
                                <div
                                    className={
                                        s['top-section__bulk__table-content']
                                    }
                                >
                                    <p className="mt-4">
                                        {t(
                                            'Select quantity, add to cart, then enter the respective coupon at checkout.',
                                            { ns: 'pdp' }
                                        )}
                                    </p>
                                    <table
                                        className={cn(
                                            s['top-section__bulk__table'],
                                            'table table-borderless'
                                        )}
                                    >
                                        <thead>
                                            <tr>
                                                <th scope="col">
                                                    {t('Quantity', {
                                                        ns: 'pdp'
                                                    })}
                                                </th>
                                                <th scope="col">
                                                    {t('Discount', {
                                                        ns: 'pdp'
                                                    })}
                                                </th>
                                                <th scope="col">
                                                    {t('Use code', {
                                                        ns: 'pdp'
                                                    })}
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {buyInBulkTableArray.map(
                                                (index: string) => {
                                                    const item =
                                                        buyInBulkTable[index]
                                                    const currentKey = buyInBulkTableArray.findIndex(
                                                        (key) => key === index
                                                    )
                                                    const nextKey =
                                                        item !== false
                                                            ? parseInt(
                                                                  buyInBulkTableArray[
                                                                      currentKey +
                                                                          1
                                                                  ].toString()
                                                              )
                                                            : false
                                                    return (
                                                        <tr key={index}>
                                                            <td>
                                                                {index}{' '}
                                                                {nextKey &&
                                                                    `- ${
                                                                        nextKey -
                                                                        1
                                                                    }`}
                                                            </td>
                                                            <td>
                                                                {item !==
                                                                    false && (
                                                                    <>{item}%</>
                                                                )}
                                                                {item ===
                                                                    false && (
                                                                    <a
                                                                        href="#buy-in-bulk"
                                                                        className="flex items-center gap-2"
                                                                    >
                                                                        <Icon name="mail" />
                                                                        {t(
                                                                            'Contact us'
                                                                        )}
                                                                    </a>
                                                                )}
                                                            </td>
                                                            <td>
                                                                {typeof item ===
                                                                    'number' && (
                                                                    <span
                                                                        className={cn(
                                                                            'flex gap-2 cursor-pointer items-center relative',
                                                                            {
                                                                                ['text-blue']:
                                                                                    copiedCode ===
                                                                                    `SD${item}`
                                                                            }
                                                                        )}
                                                                        onClick={() =>
                                                                            copyCode(
                                                                                `SD${item}`
                                                                            )
                                                                        }
                                                                        onKeyPress={() =>
                                                                            copyCode(
                                                                                `SD${item}`
                                                                            )
                                                                        }
                                                                        role="button"
                                                                        tabIndex={
                                                                            0
                                                                        }
                                                                        aria-label={t(
                                                                            'Copy code'
                                                                        )}
                                                                    >
                                                                        {item >
                                                                            0 && (
                                                                            <>
                                                                                <span>
                                                                                    SD
                                                                                    {
                                                                                        item
                                                                                    }
                                                                                </span>
                                                                                <Icon
                                                                                    name="copyIcon"
                                                                                    style={{
                                                                                        width: 15,
                                                                                        height: 15
                                                                                    }}
                                                                                />
                                                                            </>
                                                                        )}
                                                                    </span>
                                                                )}
                                                            </td>
                                                        </tr>
                                                    )
                                                }
                                            )}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div className={cn('flex items-center gap-8')}>
                            <label
                                htmlFor="quantity"
                                className={cn(
                                    'text-primitive-gray-100 font-univers65Bold text-base',
                                    'w-min-content'
                                )}
                            >
                                {t('Quantity', { ns: 'pdp' })}
                            </label>
                            <select
                                className={cn(
                                    'form-select',
                                    'text-primitive-gray-100 font-univers65Bold text-base',
                                    s['top-section__bulk__quantity-select']
                                )}
                                name="quantity"
                                data-testid={quantity}
                                defaultValue={quantity}
                                onBlur={(e) =>
                                    setQuantity(parseInt(e.target.value))
                                }
                            >
                                {maxAllowedQuantity &&
                                    Array.from(
                                        {
                                            length: maxAllowedQuantity
                                        },
                                        (_, i) => (
                                            <option
                                                key={i}
                                                value={i + 1}
                                                className="text-black"
                                            >
                                                {i + 1}
                                            </option>
                                        )
                                    )}
                            </select>
                        </div>
                    </div>
                )}
        </div>
    )
}

export default React.memo(TopSection)
